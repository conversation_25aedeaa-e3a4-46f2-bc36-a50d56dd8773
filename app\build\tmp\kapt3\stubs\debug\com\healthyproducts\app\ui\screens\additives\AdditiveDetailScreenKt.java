package com.healthyproducts.app.ui.screens.additives;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a,\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a\u0018\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\nH\u0007\u00a8\u0006\u0012"}, d2 = {"AdditiveDetail", "", "additive", "Lcom/healthyproducts/app/data/model/Additive;", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "AdditiveDetailScreen", "navController", "Landroidx/navigation/NavController;", "additiveCode", "", "viewModel", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "DetailItem", "title", "value", "app_debug"})
public final class AdditiveDetailScreenKt {
    
    /**
     * Katkı maddesi detay ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AdditiveDetailScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String additiveCode, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.AdditiveViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * Katkı maddesi detayı
     */
    @androidx.compose.runtime.Composable()
    public static final void AdditiveDetail(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.Additive additive, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
    }
    
    /**
     * Detay öğesi
     */
    @androidx.compose.runtime.Composable()
    public static final void DetailItem(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
    }
}