package com.healthyproducts.app.data.repository

import com.healthyproducts.app.model.Product
import com.healthyproducts.app.model.ScanHistory
import com.healthyproducts.app.model.ScanType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Tarama geçmişini yöneten repository sınıfı
 * Not: Şu an için mock veri kullanıyoruz, gerçek Firebase entegrasyonu daha sonra eklenecek
 */
@Singleton
class ScanHistoryRepository @Inject constructor() {

    // Mock tarama geçmişi
    private val mockScanHistory = mutableMapOf<String, MutableList<ScanHistory>>()

    // Mock ürünler (FavoritesRepository ile aynı ürünleri kullanabiliriz)
    private val mockProducts = mutableMapOf<String, Product>()

    init {
        // Örnek ürünler ekle
        val product1 = Product(
            id = "1",
            barcode = "1234567890123",
            name = "Örnek Ürün 1",
            brand = "Örnek Marka",
            ingredients = emptyList(),
            createdAt = Date()
        )

        val product2 = Product(
            id = "2",
            barcode = "2234567890123",
            name = "Örnek Ürün 2",
            brand = "Örnek Marka",
            ingredients = emptyList(),
            createdAt = Date()
        )

        mockProducts["1"] = product1
        mockProducts["2"] = product2
    }

    /**
     * Kullanıcının tarama geçmişini getirme
     */
    suspend fun getScanHistory(userId: String, limit: Int = 20): Result<List<ScanHistoryWithProduct>> = withContext(Dispatchers.IO) {
        try {
            // Kullanıcının tarama geçmişini al
            val scanHistory = mockScanHistory[userId] ?: emptyList()

            // Tarama geçmişini sırala ve limitle
            val limitedScanHistory = scanHistory
                .sortedByDescending { it.scanDate }
                .take(limit)

            // Tarama geçmişi ve ürünleri birleştir
            val result = limitedScanHistory.map { scan ->
                ScanHistoryWithProduct(
                    scanHistory = scan,
                    product = scan.productId?.let { mockProducts[it] }
                )
            }

            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Tarama geçmişine ekleme
     */
    suspend fun addToScanHistory(
        userId: String,
        productId: String? = null,
        barcode: String? = null,
        imageUrl: String? = null,
        scanType: ScanType
    ): Result<ScanHistory> = withContext(Dispatchers.IO) {
        try {
            // Yeni tarama geçmişi oluştur
            val scanHistory = ScanHistory(
                id = UUID.randomUUID().toString(),
                userId = userId,
                productId = productId,
                barcode = barcode,
                imageUrl = imageUrl,
                scanDate = Date(),
                scanType = scanType
            )

            // Kullanıcının tarama geçmişi listesini al veya oluştur
            val userScanHistory = mockScanHistory.getOrPut(userId) { mutableListOf() }

            // Tarama geçmişine ekle
            userScanHistory.add(scanHistory)

            Result.success(scanHistory)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Tarama geçmişini temizleme
     */
    suspend fun clearScanHistory(userId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Kullanıcının tarama geçmişini temizle
            mockScanHistory.remove(userId)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Tarama geçmişi ve ürün bilgisini birleştiren veri sınıfı
     */
    data class ScanHistoryWithProduct(
        val scanHistory: ScanHistory,
        val product: Product? = null
    )
}
