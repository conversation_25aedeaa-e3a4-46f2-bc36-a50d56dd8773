package com.healthyproducts.app.ui.viewmodel

import android.app.Activity
import android.app.Application
import android.content.Intent
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.preferences.LanguagePreferences
import com.healthyproducts.app.data.repository.FirestoreRepository
import com.healthyproducts.app.util.LocaleHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Dil ayarlarını yöneten ViewModel
 */
@HiltViewModel
class LanguageViewModel @Inject constructor(
    application: Application,
    private val firestoreRepository: FirestoreRepository
) : AndroidViewModel(application) {

    // Aktivite referansı
    private var activity: Activity? = null

    /**
     * Aktivite referansını ayarlar
     */
    fun setActivity(activity: Activity) {
        this.activity = activity
    }

    private val languagePreferences = LanguagePreferences(application)

    /**
     * Mevcut dil
     */
    val currentLanguage: StateFlow<LocaleHelper.Language> = languagePreferences.languageCode
        .map { code -> LocaleHelper.Language.fromCode(code) }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = LocaleHelper.getCurrentLocale()
        )

    /**
     * Desteklenen diller
     */
    val supportedLanguages = LocaleHelper.Language.values().toList()

    /**
     * Dili değiştirme
     */
    fun setLanguage(language: LocaleHelper.Language) {
        viewModelScope.launch {
            try {
                // DataStore'da dil kodunu kaydet
                languagePreferences.saveLanguageCode(language.code)

                // Uygulama dilini değiştir
                LocaleHelper.setLocale(language.code)

                // Firestore'da kullanıcı dilini güncelle
                val supportedLanguage = LocaleHelper.languageToSupportedLanguage(language)
                android.util.Log.d("LanguageViewModel", "Updating user language to: ${supportedLanguage.code}")
                firestoreRepository.updateUserLanguage(supportedLanguage)

                // Firestore'dan güncel kullanıcı tercihlerini al
                val userPreferences = firestoreRepository.userPreferences.value
                android.util.Log.d("LanguageViewModel", "Current user preferences from Firestore: language=${userPreferences.language}, aiModel=${userPreferences.aiModel}")

                // Sadece dil değerini güncelle, diğer değerleri koru
                val updatedPreferences = userPreferences.copy(language = language.code)
                android.util.Log.d("LanguageViewModel", "Updated preferences: language=${updatedPreferences.language}, aiModel=${updatedPreferences.aiModel}")

                // FirestoreRepository'yi güncelle
                android.util.Log.d("LanguageViewModel", "Updating user preferences in Firestore...")
                firestoreRepository.updateUserPreferences(updatedPreferences)

                android.util.Log.d("LanguageViewModel", "Language changed to: ${language.code}")

                // Aktiviteyi yeniden başlat
                activity?.let { act ->
                    android.util.Log.d("LanguageViewModel", "Recreating activity to apply language changes")

                    // Yeni bir intent oluştur ve flags ekle
                    val intent = android.content.Intent(act, act::class.java)
                    intent.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP or
                                   android.content.Intent.FLAG_ACTIVITY_CLEAR_TASK or
                                   android.content.Intent.FLAG_ACTIVITY_NEW_TASK)

                    // Dil değişikliğini intent'e ekle
                    intent.putExtra("LANGUAGE_CHANGED", true)
                    intent.putExtra("LANGUAGE_CODE", language.code)

                    // Aktiviteyi yeniden başlat
                    act.finish()
                    act.startActivity(intent)
                    act.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
                }
            } catch (e: Exception) {
                android.util.Log.e("LanguageViewModel", "Error changing language", e)
            }
        }
    }
}
