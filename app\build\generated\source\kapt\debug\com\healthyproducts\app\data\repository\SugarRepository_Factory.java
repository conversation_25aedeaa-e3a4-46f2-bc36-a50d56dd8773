package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SugarRepository_Factory implements Factory<SugarRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public SugarRepository_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public SugarRepository get() {
    return newInstance(firestoreProvider.get());
  }

  public static SugarRepository_Factory create(Provider<FirebaseFirestore> firestoreProvider) {
    return new SugarRepository_Factory(firestoreProvider);
  }

  public static SugarRepository newInstance(FirebaseFirestore firestore) {
    return new SugarRepository(firestore);
  }
}
