package com.healthyproducts.app.ui.screens.barcode;

/**
 * Repository'leri inject etmek için EntryPoint
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bg\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&J\b\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0006\u001a\u00020\u0007H&\u00a8\u0006\b"}, d2 = {"Lcom/healthyproducts/app/ui/screens/barcode/RepositoryEntryPoint;", "", "firebaseAuth", "Lcom/google/firebase/auth/FirebaseAuth;", "imageUploadRepository", "Lcom/healthyproducts/app/data/repository/ImageUploadRepository;", "productRepository", "Lcom/healthyproducts/app/data/repository/ProductRepository;", "app_debug"})
@dagger.hilt.EntryPoint()
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract interface RepositoryEntryPoint {
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.healthyproducts.app.data.repository.ProductRepository productRepository();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.healthyproducts.app.data.repository.ImageUploadRepository imageUploadRepository();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.google.firebase.auth.FirebaseAuth firebaseAuth();
}