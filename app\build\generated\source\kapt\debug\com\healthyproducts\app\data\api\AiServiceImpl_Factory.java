package com.healthyproducts.app.data.api;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AiServiceImpl_Factory implements Factory<AiServiceImpl> {
  private final Provider<DeepSeekApiService> deepSeekApiServiceProvider;

  private final Provider<GeminiApiService> geminiApiServiceProvider;

  public AiServiceImpl_Factory(Provider<DeepSeekApiService> deepSeekApiServiceProvider,
      Provider<GeminiApiService> geminiApiServiceProvider) {
    this.deepSeekApiServiceProvider = deepSeekApiServiceProvider;
    this.geminiApiServiceProvider = geminiApiServiceProvider;
  }

  @Override
  public AiServiceImpl get() {
    return newInstance(deepSeekApiServiceProvider.get(), geminiApiServiceProvider.get());
  }

  public static AiServiceImpl_Factory create(
      Provider<DeepSeekApiService> deepSeekApiServiceProvider,
      Provider<GeminiApiService> geminiApiServiceProvider) {
    return new AiServiceImpl_Factory(deepSeekApiServiceProvider, geminiApiServiceProvider);
  }

  public static AiServiceImpl newInstance(DeepSeekApiService deepSeekApiService,
      GeminiApiService geminiApiService) {
    return new AiServiceImpl(deepSeekApiService, geminiApiService);
  }
}
