package com.healthyproducts.app.ui.screens.scan;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a&\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a \u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\u0007\u001a$\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u00a8\u0006\u0011"}, d2 = {"BarcodeScanContent", "", "scanViewModel", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel;", "FrameSizeOption", "text", "", "selected", "", "onClick", "Lkotlin/Function0;", "OcrScanContent", "foodAnalysisViewModel", "Lcom/healthyproducts/app/ui/viewmodel/FoodAnalysisViewModel;", "navController", "Landroidx/navigation/NavController;", "ScanScreen", "app_debug"})
public final class ScanScreenKt {
    
    /**
     * Tarama ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ScanScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.ScanViewModel scanViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel foodAnalysisViewModel) {
    }
    
    /**
     * Barkod tarama içeriği
     */
    @androidx.compose.runtime.Composable()
    public static final void BarcodeScanContent(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.ScanViewModel scanViewModel) {
    }
    
    /**
     * Çerçeve boyutu seçeneği bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void FrameSizeOption(@org.jetbrains.annotations.NotNull()
    java.lang.String text, boolean selected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * OCR tarama içeriği
     */
    @androidx.compose.runtime.Composable()
    public static final void OcrScanContent(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.ScanViewModel scanViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel foodAnalysisViewModel, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
}