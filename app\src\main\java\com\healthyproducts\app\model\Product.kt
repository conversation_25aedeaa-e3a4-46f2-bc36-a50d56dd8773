package com.healthyproducts.app.model

import java.util.Date

/**
 * Ürün içeriğinin durumunu temsil eden enum sınıfı
 */
enum class IngredientStatus {
    HALAL,      // Helal
    HARAM,      // Haram
    SUSPICIOUS, // Şüpheli
    VEGAN,      // Vegan
    HARMFUL,    // Zararlı
    UNHEALTHY   // Sağlıksız
}

/**
 * Ürün içeriğini temsil eden veri sınıfı
 */
data class Ingredient(
    val id: String,
    val name: String,
    val status: List<IngredientStatus>,
    val description: String? = null
)

/**
 * Ser<PERSON><PERSON>ka türünü temsil eden enum sınıfı
 */
enum class CertificateType {
    HALAL,      // Helal sertifikası
    VEGAN,      // Vegan sertifikası
    KOSHER,     // Koşer sertifikası
    ORGANIC,    // Organik sertifikası
    NON_GMO     // GDO içermez sertifikası
}

/**
 * Sertifikayı temsil eden veri sınıfı
 */
data class Certificate(
    val id: String,
    val type: CertificateType,
    val issuer: String,
    val validUntil: Date? = null,
    val imageUrl: String? = null
)

/**
 * Ürünü temsil eden veri sınıfı
 */
data class Product(
    val id: String,
    val barcode: String? = null,
    val name: String,
    val brand: String? = null,
    val ingredients: List<Ingredient> = emptyList(),
    val certificates: List<Certificate> = emptyList(),
    val imageUrl: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) {
    /**
     * Ürünün helal olup olmadığını kontrol eder
     */
    fun isHalal(): Boolean {
        return !ingredients.any { IngredientStatus.HARAM in it.status }
    }

    /**
     * Ürünün vegan olup olmadığını kontrol eder
     */
    fun isVegan(): Boolean {
        return ingredients.all { IngredientStatus.VEGAN in it.status }
    }

    /**
     * Ürünün zararlı içerik içerip içermediğini kontrol eder
     */
    fun hasHarmfulIngredients(): Boolean {
        return ingredients.any { IngredientStatus.HARMFUL in it.status }
    }

    /**
     * Ürünün sağlıksız içerik içerip içermediğini kontrol eder
     */
    fun hasUnhealthyIngredients(): Boolean {
        return ingredients.any { IngredientStatus.UNHEALTHY in it.status }
    }

    /**
     * Ürünün şüpheli içerik içerip içermediğini kontrol eder
     */
    fun hasSuspiciousIngredients(): Boolean {
        return ingredients.any { IngredientStatus.SUSPICIOUS in it.status }
    }
}
