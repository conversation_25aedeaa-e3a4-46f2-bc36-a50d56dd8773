package com.healthyproducts.app.data.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.UserProfileChangeRequest
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase Authentication işlemlerini yöneten repository sınıfı
 */
@Singleton
class FirebaseAuthRepository @Inject constructor(
    private val auth: FirebaseAuth
) {
    /**
     * Mevcut oturum açmış kullanıcıyı döndürür
     */
    fun getCurrentUser(): FirebaseUser? = auth.currentUser

    /**
     * Kullanıcının oturum açıp açmadığını kontrol eder
     */
    fun isUserLoggedIn(): Boolean = auth.currentUser != null

    /**
     * E-posta ve şifre ile kayıt olma
     */
    suspend fun signUp(email: String, password: String): FirebaseUser {
        val result = auth.createUserWithEmailAndPassword(email, password).await()
        return result.user ?: throw Exception("Kullanıcı oluşturulamadı")
    }

    /**
     * E-posta ve şifre ile giriş yapma
     */
    suspend fun signIn(email: String, password: String): FirebaseUser {
        val result = auth.signInWithEmailAndPassword(email, password).await()
        return result.user ?: throw Exception("Giriş yapılamadı")
    }

    /**
     * Google ile giriş yapma
     */
    suspend fun signInWithGoogle(idToken: String): FirebaseUser {
        val credential = GoogleAuthProvider.getCredential(idToken, null)
        val result = auth.signInWithCredential(credential).await()
        return result.user ?: throw Exception("Google ile giriş yapılamadı")
    }

    /**
     * Kullanıcı profilini güncelleme
     */
    suspend fun updateProfile(displayName: String?, photoUrl: String?) {
        val user = getCurrentUser() ?: throw Exception("Kullanıcı oturum açmamış")
        val profileUpdates = UserProfileChangeRequest.Builder().apply {
            displayName?.let { setDisplayName(it) }
            photoUrl?.let { setPhotoUri(android.net.Uri.parse(it)) }
        }.build()
        
        user.updateProfile(profileUpdates).await()
    }

    /**
     * Şifre sıfırlama e-postası gönderme
     */
    suspend fun sendPasswordResetEmail(email: String) {
        auth.sendPasswordResetEmail(email).await()
    }

    /**
     * Oturumu kapatma
     */
    fun signOut() {
        auth.signOut()
    }
}
