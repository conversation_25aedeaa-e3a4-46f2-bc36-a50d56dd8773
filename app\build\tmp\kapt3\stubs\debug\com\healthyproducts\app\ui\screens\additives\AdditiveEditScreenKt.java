package com.healthyproducts.app.ui.screens.additives;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a0\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a$\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u00052\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a$\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00052\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a$\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00052\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a$\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00052\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\rH\u0007\u00a8\u0006\u0013"}, d2 = {"AdditiveEditScreen", "", "navController", "Landroidx/navigation/NavController;", "additiveCode", "", "viewModel", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "CategoryDropdown", "selectedCategory", "onCategorySelected", "Lkotlin/Function1;", "HalalStatusDropdown", "selectedStatus", "onStatusSelected", "KosherStatusDropdown", "VeganStatusDropdown", "app_debug"})
public final class AdditiveEditScreenKt {
    
    /**
     * Katkı maddesi ekleme/düzenleme ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AdditiveEditScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.Nullable()
    java.lang.String additiveCode, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.AdditiveViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * Kategori dropdown
     */
    @androidx.compose.runtime.Composable()
    public static final void CategoryDropdown(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedCategory, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onCategorySelected) {
    }
    
    /**
     * Helal durumu dropdown
     */
    @androidx.compose.runtime.Composable()
    public static final void HalalStatusDropdown(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
    
    /**
     * Vegan durumu dropdown
     */
    @androidx.compose.runtime.Composable()
    public static final void VeganStatusDropdown(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
    
    /**
     * Koşer durumu dropdown
     */
    @androidx.compose.runtime.Composable()
    public static final void KosherStatusDropdown(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
}