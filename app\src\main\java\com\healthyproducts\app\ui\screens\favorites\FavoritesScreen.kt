package com.healthyproducts.app.ui.screens.favorites

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.model.Product
import com.healthyproducts.app.navigation.Screen
import com.healthyproducts.app.ui.components.BottomNavBar
import com.healthyproducts.app.ui.screens.home.RecentScanItem
import com.healthyproducts.app.ui.viewmodel.FavoritesViewModel
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Favoriler ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoritesScreen(
    navController: NavController,
    favoritesViewModel: FavoritesViewModel = viewModel(),
    userViewModel: UserViewModel = viewModel()
) {
    val userState by userViewModel.userState.collectAsState()
    val favoritesState by favoritesViewModel.favoritesState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Kullanıcı giriş yapmışsa favorileri getir
    LaunchedEffect(userState) {
        if (userState is UserViewModel.UserState.LoggedIn) {
            val userId = (userState as UserViewModel.UserState.LoggedIn).user.id
            favoritesViewModel.getFavoriteProducts(userId)
        }
    }

    // Hata durumunda snackbar göster
    LaunchedEffect(favoritesState) {
        if (favoritesState is FavoritesViewModel.FavoritesState.Error) {
            snackbarHostState.showSnackbar(
                (favoritesState as FavoritesViewModel.FavoritesState.Error).message
            )
        }
    }

    Scaffold(
        bottomBar = { BottomNavBar(navController = navController) },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { innerPadding ->
        when {
            // Kullanıcı giriş yapmamışsa
            userState !is UserViewModel.UserState.LoggedIn -> {
                NotLoggedInContent(
                    paddingValues = innerPadding,
                    onLoginClick = { navController.navigate("login") }
                )
            }

            // Favoriler yükleniyorsa
            favoritesState is FavoritesViewModel.FavoritesState.Loading -> {
                LoadingContent(paddingValues = innerPadding)
            }

            // Favori ürün yoksa
            favoritesState is FavoritesViewModel.FavoritesState.Empty -> {
                EmptyFavoritesContent(paddingValues = innerPadding)
            }

            // Favori ürünler varsa
            favoritesState is FavoritesViewModel.FavoritesState.Success -> {
                val products = (favoritesState as FavoritesViewModel.FavoritesState.Success).products
                FavoritesContent(
                    navController = navController,
                    paddingValues = innerPadding,
                    favoriteProducts = products
                )
            }

            // Hata durumunda
            favoritesState is FavoritesViewModel.FavoritesState.Error -> {
                EmptyFavoritesContent(paddingValues = innerPadding)
            }
        }
    }
}

/**
 * Yükleme içeriği
 */
@Composable
fun LoadingContent(paddingValues: PaddingValues) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

/**
 * Giriş yapmamış kullanıcı için içerik
 */
@Composable
fun NotLoggedInContent(
    paddingValues: PaddingValues,
    onLoginClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "Favorilerinizi görmek için giriş yapın",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(16.dp)
        )
    }
}

/**
 * Boş favoriler içeriği
 */
@Composable
fun EmptyFavoritesContent(paddingValues: PaddingValues) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "Henüz favori ürün eklenmedi",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(16.dp)
        )
    }
}

/**
 * Favoriler ekranı içeriği
 */
@Composable
fun FavoritesContent(
    navController: NavController,
    paddingValues: PaddingValues,
    favoriteProducts: List<Product>
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues),
        contentPadding = PaddingValues(vertical = 8.dp)
    ) {
        item {
            Text(
                text = "Favori Ürünleriniz",
                style = MaterialTheme.typography.headlineSmall,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                textAlign = TextAlign.Center
            )
        }

        items(favoriteProducts) { product ->
            RecentScanItem(
                product = product,
                onClick = { navController.navigate(Screen.ProductDetail.createRoute(product.id)) }
            )
        }
    }
}
