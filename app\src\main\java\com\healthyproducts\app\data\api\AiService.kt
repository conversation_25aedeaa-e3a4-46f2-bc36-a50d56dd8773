package com.healthyproducts.app.data.api

import com.healthyproducts.app.data.model.AiModel
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI servisi arayüzü
 */
interface AiService {
    /**
     * AI modeline istek gönderir ve metin üretir
     */
    suspend fun generateText(prompt: String, language: String): String
}

/**
 * AI servisi implementasyonu
 */
@Singleton
class AiServiceImpl @Inject constructor(
    private val deepSeekApiService: DeepSeekApiService,
    private val geminiApiService: GeminiApiService
) : AiService {

    /**
     * Seçilen AI modeline göre istek gönderir ve metin üretir
     * Not: DeepSeek API bypass edilmiştir, tüm istekler Gemini'ye yönlendirilir
     */
    override suspend fun generateText(prompt: String, language: String): String {
        android.util.Log.d("AiService", "generateText called with language: $language")

        // DeepSeek'i bypass et, her zaman Gemini 2.0 Flash kullan
        android.util.Log.d("AiService", "Bypassing DeepSeek, using Gemini 2.0 Flash instead")

        // Dil tercihine göre prompt'u güçlendir
        val enhancedPrompt = enhancePromptWithLanguage(prompt, language)

        // Gemini 2.0 Flash modelini kullan
        val result = generateTextWithGemini(enhancedPrompt, "gemini-1.5-flash")

        android.util.Log.d("AiService", "Used Gemini 2.0 Flash model with language: $language")

        android.util.Log.d("AiService", "AI response received, length: ${result.length}")
        return result
    }

    /**
     * DeepSeek API'sine istek gönderir ve metin üretir
     */
    private suspend fun generateTextWithDeepSeek(prompt: String): String {
        val request = com.healthyproducts.app.data.model.DeepSeekRequest(
            model = AiModel.DEEPSEEK.apiName,
            messages = listOf(
                com.healthyproducts.app.data.model.Message(
                    role = "user",
                    content = prompt
                )
            )
        )

        val response = deepSeekApiService.getCompletion(
            authorization = "Bearer ${com.healthyproducts.app.BuildConfig.DEEPSEEK_API_KEY}",
            request = request
        )

        return response.choices.firstOrNull()?.message?.content ?: ""
    }

    /**
     * Gemini API'sine istek gönderir ve metin üretir
     */
    private suspend fun generateTextWithGemini(prompt: String, modelName: String = "gemini-1.5-flash"): String {
        android.util.Log.d("AiService", "Sending request to Gemini API with model: $modelName and prompt: $prompt")
        try {
            val response = geminiApiService.generateText(prompt, modelName)
            val result = response.text ?: ""
            android.util.Log.d("AiService", "Received response from Gemini API: $result")
            return result
        } catch (e: Exception) {
            android.util.Log.e("AiService", "Error calling Gemini API", e)
            throw e
        }
    }

    /**
     * Dil tercihine göre prompt'u güçlendirir
     */
    private fun enhancePromptWithLanguage(prompt: String, language: String): String {
        val languageInstruction = when (language) {
            "tr" -> {
                """
                ÖNEMLİ: Yanıtını tamamen Türkçe olarak ver. Hiçbir İngilizce kelime kullanma.
                Sağlık puanını "X/10" formatında ver (örnek: "4/10", "7/10").

                """.trimIndent()
            }
            "en" -> {
                """
                IMPORTANT: Respond entirely in English. Do not use any Turkish words.
                Provide the health score in "X/10" format (example: "4/10", "7/10").

                """.trimIndent()
            }
            else -> {
                """
                IMPORTANT: Respond entirely in English. Do not use any Turkish words.
                Provide the health score in "X/10" format (example: "4/10", "7/10").

                """.trimIndent()
            }
        }

        return languageInstruction + prompt
    }
}
