package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName
import com.google.firebase.firestore.ServerTimestamp
import java.util.Date

/**
 * Kullanıcının gıda tercihleri modeli
 */
data class UserFoodPreference(
    @DocumentId
    val id: String = "",
    
    @get:PropertyName("user_id")
    @set:PropertyName("user_id")
    var userId: String = "",
    
    @get:PropertyName("type")
    @set:PropertyName("type")
    var type: String = "", // "allergen", "fat", "sugar", "certificate", "preservative"
    
    @get:PropertyName("item_id")
    @set:PropertyName("item_id")
    var itemId: String = "",
    
    @get:PropertyName("item_name")
    @set:PropertyName("item_name")
    var itemName: String = "",
    
    @get:PropertyName("is_custom")
    @set:PropertyName("is_custom")
    var isCustom: Boolean = false,
    
    @ServerTimestamp
    @get:PropertyName("created_at")
    @set:PropertyName("created_at")
    var createdAt: Date = Date()
) {
    // Firestore için boş constructor
    constructor() : this("", "", "", "", "", false)
}

/**
 * Kullanıcının gıda tercihleri türleri
 */
enum class FoodPreferenceType(val value: String) {
    ALLERGEN("allergen"),
    FAT("fat"),
    SUGAR("sugar"),
    CERTIFICATE("certificate"),
    PRESERVATIVE("preservative")
}
