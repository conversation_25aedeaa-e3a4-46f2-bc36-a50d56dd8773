  Manifest android  SuppressLint android.annotation  Activity android.app  Application android.app  ActivityResultLauncher android.app.Activity  Bundle android.app.Activity  
Configuration android.app.Activity  FirebaseAuth android.app.Activity  GoogleSignInAccount android.app.Activity  GoogleSignInClient android.app.Activity  Inject android.app.Activity  Intent android.app.Activity  LanguagePreferences android.app.Activity  String android.app.Activity  Task android.app.Activity  CoroutineScope android.app.Application  Dispatchers android.app.Application  FirestoreRepository android.app.Application  Inject android.app.Application  
SupervisorJob android.app.Application  Context android.content  Intent android.content  SharedPreferences android.content  ActivityResultLauncher android.content.Context  Bundle android.content.Context  
Configuration android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  FirebaseAuth android.content.Context  FirestoreRepository android.content.Context  GoogleSignInAccount android.content.Context  GoogleSignInClient android.content.Context  Inject android.content.Context  Intent android.content.Context  LanguagePreferences android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  Task android.content.Context  	dataStore android.content.Context  getDATAStore android.content.Context  getDataStore android.content.Context  ActivityResultLauncher android.content.ContextWrapper  Bundle android.content.ContextWrapper  
Configuration android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  FirebaseAuth android.content.ContextWrapper  FirestoreRepository android.content.ContextWrapper  GoogleSignInAccount android.content.ContextWrapper  GoogleSignInClient android.content.ContextWrapper  Inject android.content.ContextWrapper  Intent android.content.ContextWrapper  LanguagePreferences android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  Task android.content.ContextWrapper  PackageManager android.content.pm  
Configuration android.content.res  Bitmap android.graphics  
BitmapFactory android.graphics  ImageDecoder android.graphics  Matrix android.graphics  Uri android.net  Build 
android.os  Bundle 
android.os  
MediaStore android.provider  Settings android.provider  Log android.util  Size android.util  ActivityResultLauncher  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  
Configuration  android.view.ContextThemeWrapper  FirebaseAuth  android.view.ContextThemeWrapper  GoogleSignInAccount  android.view.ContextThemeWrapper  GoogleSignInClient  android.view.ContextThemeWrapper  Inject  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LanguagePreferences  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Task  android.view.ContextThemeWrapper  Toast android.widget  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  ActivityResultLauncher #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  
Configuration #androidx.activity.ComponentActivity  FirebaseAuth #androidx.activity.ComponentActivity  GoogleSignInAccount #androidx.activity.ComponentActivity  GoogleSignInClient #androidx.activity.ComponentActivity  Inject #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LanguagePreferences #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Task #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultLauncher androidx.activity.result  ActivityResultContracts !androidx.activity.result.contract  AppCompatDelegate androidx.appcompat.app  CameraSelector androidx.camera.core  
Composable androidx.camera.core  ExperimentalGetImage androidx.camera.core  
ImageAnalysis androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  androidx androidx.camera.core  com androidx.camera.core  Analyzer "androidx.camera.core.ImageAnalysis  OnImageCapturedCallback !androidx.camera.core.ImageCapture  ProcessCameraProvider androidx.camera.lifecycle  PreviewView androidx.camera.view  BorderStroke androidx.compose.foundation  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalLayoutApi "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FlowRow "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Favorite ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  
QrCodeScanner ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	Analytics &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  	CameraAlt &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FavoriteBorder &androidx.compose.material.icons.filled  
FileUpload &androidx.compose.material.icons.filled  FlashOff &androidx.compose.material.icons.filled  FlashOn &androidx.compose.material.icons.filled  History &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  Logout &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  PhotoLibrary &androidx.compose.material.icons.filled  
QrCodeScanner &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  	Sanitizer &androidx.compose.material.icons.filled  Save &androidx.compose.material.icons.filled  Science &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Spa &androidx.compose.material.icons.filled  Verified &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  AlertDialog androidx.compose.material3  
AssistChip androidx.compose.material3  Button androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  Checkbox androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  Divider androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  ElevatedCard androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  ListItem androidx.compose.material3  
MaterialTheme androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  RadioButton androidx.compose.material3  Scaffold androidx.compose.material3  Slider androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  Tab androidx.compose.material3  TabRow androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  	TextField androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  android androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  DisposableEffect androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
ImageProxy androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
SideEffect androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  getValue androidx.compose.runtime  key androidx.compose.runtime  mutableFloatStateOf androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableStateListOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	clickable androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  clip androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Color androidx.compose.ui.graphics  
PathEffect androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  Stroke &androidx.compose.ui.graphics.drawscope  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  LocalConfiguration androidx.compose.ui.platform  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  ActivityResultLauncher #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
Configuration #androidx.core.app.ComponentActivity  FirebaseAuth #androidx.core.app.ComponentActivity  GoogleSignInAccount #androidx.core.app.ComponentActivity  GoogleSignInClient #androidx.core.app.ComponentActivity  Inject #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LanguagePreferences #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Task #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  LocaleListCompat androidx.core.os  WindowCompat androidx.core.view  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  Preferences #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  
hiltViewModel  androidx.hilt.navigation.compose  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  Activity #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  Barcode #androidx.lifecycle.AndroidViewModel  Bitmap #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  CameraPermissionState #androidx.lifecycle.AndroidViewModel  Context #androidx.lifecycle.AndroidViewModel  CorrectionState #androidx.lifecycle.AndroidViewModel  FirestoreRepository #androidx.lifecycle.AndroidViewModel  	FrameSize #androidx.lifecycle.AndroidViewModel  Inject #androidx.lifecycle.AndroidViewModel  LanguagePreferences #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  LocaleHelper #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  OcrCorrectionRepository #androidx.lifecycle.AndroidViewModel  OcrScanState #androidx.lifecycle.AndroidViewModel  ScanHistoryRepository #androidx.lifecycle.AndroidViewModel  	ScanState #androidx.lifecycle.AndroidViewModel  SharingStarted #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  SupportedLanguage #androidx.lifecycle.AndroidViewModel  Unit #androidx.lifecycle.AndroidViewModel  asStateFlow #androidx.lifecycle.AndroidViewModel  com #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  invoke #androidx.lifecycle.AndroidViewModel  map #androidx.lifecycle.AndroidViewModel  stateIn #androidx.lifecycle.AndroidViewModel  toList #androidx.lifecycle.AndroidViewModel  viewModelScope #androidx.lifecycle.AndroidViewModel  Activity androidx.lifecycle.ViewModel  Additive androidx.lifecycle.ViewModel  AdditiveRepository androidx.lifecycle.ViewModel  	AiService androidx.lifecycle.ViewModel  Allergen androidx.lifecycle.ViewModel  AllergenRepository androidx.lifecycle.ViewModel  AllergensState androidx.lifecycle.ViewModel  
AnalysisState androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  ApplicationContext androidx.lifecycle.ViewModel  Barcode androidx.lifecycle.ViewModel  Bitmap androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  CameraPermissionState androidx.lifecycle.ViewModel  CertificatesState androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  CorrectionState androidx.lifecycle.ViewModel  Fat androidx.lifecycle.ViewModel  
FatRepository androidx.lifecycle.ViewModel  	FatsState androidx.lifecycle.ViewModel  FavoritesRepository androidx.lifecycle.ViewModel  FavoritesState androidx.lifecycle.ViewModel  FirebaseAuthRepository androidx.lifecycle.ViewModel  FirestoreRepository androidx.lifecycle.ViewModel  FoodAnalysisRepository androidx.lifecycle.ViewModel  FoodAnalysisService androidx.lifecycle.ViewModel  FoodCertificate androidx.lifecycle.ViewModel  FoodCertificateRepository androidx.lifecycle.ViewModel  FoodPreferenceType androidx.lifecycle.ViewModel  	FrameSize androidx.lifecycle.ViewModel  ImportStatus androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  LanguagePreferences androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LocaleHelper androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  OcrCorrectionRepository androidx.lifecycle.ViewModel  OcrScanState androidx.lifecycle.ViewModel  PreservativeRepository androidx.lifecycle.ViewModel  PreservativeState androidx.lifecycle.ViewModel  PreservativesState androidx.lifecycle.ViewModel  Product androidx.lifecycle.ViewModel  ScanHistoryRepository androidx.lifecycle.ViewModel  ScanHistoryState androidx.lifecycle.ViewModel  	ScanState androidx.lifecycle.ViewModel  ScanType androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Sugar androidx.lifecycle.ViewModel  SugarRepository androidx.lifecycle.ViewModel  SugarsState androidx.lifecycle.ViewModel  SupportedLanguage androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  User androidx.lifecycle.ViewModel  UserFoodPreference androidx.lifecycle.ViewModel  UserFoodPreferenceRepository androidx.lifecycle.ViewModel  UserFoodPreferencesState androidx.lifecycle.ViewModel  UserPreferences androidx.lifecycle.ViewModel  UserRepository androidx.lifecycle.ViewModel  	UserState androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  emptyMap androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  map androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  toList androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  
NavController androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  NavHostController androidx.navigation  NavType androidx.navigation  navArgument androidx.navigation  	Companion "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  	Companion androidx.navigation.NavGraph  findStartDestination &androidx.navigation.NavGraph.Companion  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  
AsyncImage coil.compose  GenerativeModel !com.google.ai.client.generativeai  GenerateContentResponse &com.google.ai.client.generativeai.type  GoogleSignIn &com.google.android.gms.auth.api.signin  GoogleSignInAccount &com.google.android.gms.auth.api.signin  GoogleSignInClient &com.google.android.gms.auth.api.signin  GoogleSignInOptions &com.google.android.gms.auth.api.signin  ApiException !com.google.android.gms.common.api  Task com.google.android.gms.tasks  FirebaseApp com.google.firebase  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  GoogleAuthProvider com.google.firebase.auth  UserProfileChangeRequest com.google.firebase.auth  auth com.google.firebase.auth.ktx  
DocumentId com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  PropertyName com.google.firebase.firestore  Query com.google.firebase.firestore  ServerTimestamp com.google.firebase.firestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  	firestore !com.google.firebase.firestore.ktx  Firebase com.google.firebase.ktx  FirebaseStorage com.google.firebase.storage  StorageReference com.google.firebase.storage  storage com.google.firebase.storage.ktx  FieldNamingPolicy com.google.gson  Gson com.google.gson  GsonBuilder com.google.gson  SerializedName com.google.gson.annotations  	TypeToken com.google.gson.reflect  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  
InputImage com.google.mlkit.vision.common  Text com.google.mlkit.vision.text  TextRecognition com.google.mlkit.vision.text  TextRecognizer com.google.mlkit.vision.text  	getClient ,com.google.mlkit.vision.text.TextRecognition  TextRecognizerOptions "com.google.mlkit.vision.text.latin  DEFAULT_OPTIONS 8com.google.mlkit.vision.text.latin.TextRecognizerOptions  BuildConfig com.healthyproducts.app  CoroutineScope com.healthyproducts.app  Dispatchers com.healthyproducts.app  HealthyProductsApp com.healthyproducts.app  HealthyProductsAppUI com.healthyproducts.app  MainActivity com.healthyproducts.app  R com.healthyproducts.app  String com.healthyproducts.app  
SupervisorJob com.healthyproducts.app  CoroutineScope *com.healthyproducts.app.HealthyProductsApp  Dispatchers *com.healthyproducts.app.HealthyProductsApp  FirestoreRepository *com.healthyproducts.app.HealthyProductsApp  Inject *com.healthyproducts.app.HealthyProductsApp  
SupervisorJob *com.healthyproducts.app.HealthyProductsApp  ActivityResultLauncher $com.healthyproducts.app.MainActivity  Bundle $com.healthyproducts.app.MainActivity  
Configuration $com.healthyproducts.app.MainActivity  FirebaseAuth $com.healthyproducts.app.MainActivity  GoogleSignInAccount $com.healthyproducts.app.MainActivity  GoogleSignInClient $com.healthyproducts.app.MainActivity  Inject $com.healthyproducts.app.MainActivity  Intent $com.healthyproducts.app.MainActivity  LanguagePreferences $com.healthyproducts.app.MainActivity  String $com.healthyproducts.app.MainActivity  Task $com.healthyproducts.app.MainActivity  string com.healthyproducts.app.R  	favorites  com.healthyproducts.app.R.string  home  com.healthyproducts.app.R.string  profile  com.healthyproducts.app.R.string  scan  com.healthyproducts.app.R.string  	AiService  com.healthyproducts.app.data.api  
AiServiceImpl  com.healthyproducts.app.data.api  DeepSeekApi  com.healthyproducts.app.data.api  DeepSeekApiService  com.healthyproducts.app.data.api  GeminiApiService  com.healthyproducts.app.data.api  String  com.healthyproducts.app.data.api  String *com.healthyproducts.app.data.api.AiService  DeepSeekApiService .com.healthyproducts.app.data.api.AiServiceImpl  GeminiApiService .com.healthyproducts.app.data.api.AiServiceImpl  Inject .com.healthyproducts.app.data.api.AiServiceImpl  String .com.healthyproducts.app.data.api.AiServiceImpl  Body ,com.healthyproducts.app.data.api.DeepSeekApi  DeepSeekRequest ,com.healthyproducts.app.data.api.DeepSeekApi  DeepSeekResponse ,com.healthyproducts.app.data.api.DeepSeekApi  POST ,com.healthyproducts.app.data.api.DeepSeekApi  Body 3com.healthyproducts.app.data.api.DeepSeekApiService  DeepSeekRequest 3com.healthyproducts.app.data.api.DeepSeekApiService  DeepSeekResponse 3com.healthyproducts.app.data.api.DeepSeekApiService  Header 3com.healthyproducts.app.data.api.DeepSeekApiService  POST 3com.healthyproducts.app.data.api.DeepSeekApiService  String 3com.healthyproducts.app.data.api.DeepSeekApiService  Body =com.healthyproducts.app.data.api.DeepSeekApiService.Companion  DeepSeekRequest =com.healthyproducts.app.data.api.DeepSeekApiService.Companion  DeepSeekResponse =com.healthyproducts.app.data.api.DeepSeekApiService.Companion  Header =com.healthyproducts.app.data.api.DeepSeekApiService.Companion  POST =com.healthyproducts.app.data.api.DeepSeekApiService.Companion  String =com.healthyproducts.app.data.api.DeepSeekApiService.Companion  GenerateContentResponse 1com.healthyproducts.app.data.api.GeminiApiService  Inject 1com.healthyproducts.app.data.api.GeminiApiService  String 1com.healthyproducts.app.data.api.GeminiApiService  Additive "com.healthyproducts.app.data.model  AdditiveCategory "com.healthyproducts.app.data.model  AiModel "com.healthyproducts.app.data.model  Allergen "com.healthyproducts.app.data.model  Boolean "com.healthyproducts.app.data.model  Choice "com.healthyproducts.app.data.model  DeepSeekRequest "com.healthyproducts.app.data.model  DeepSeekResponse "com.healthyproducts.app.data.model  Double "com.healthyproducts.app.data.model  Fat "com.healthyproducts.app.data.model  FoodCertificate "com.healthyproducts.app.data.model  FoodPreferenceType "com.healthyproducts.app.data.model  HalalStatus "com.healthyproducts.app.data.model  Int "com.healthyproducts.app.data.model  Intolerance "com.healthyproducts.app.data.model  KosherStatus "com.healthyproducts.app.data.model  List "com.healthyproducts.app.data.model  Long "com.healthyproducts.app.data.model  Message "com.healthyproducts.app.data.model  Preservative "com.healthyproducts.app.data.model  Product "com.healthyproducts.app.data.model  ProductMatchType "com.healthyproducts.app.data.model  ProductSearchResult "com.healthyproducts.app.data.model  SaveProductRequest "com.healthyproducts.app.data.model  String "com.healthyproducts.app.data.model  Sugar "com.healthyproducts.app.data.model  SupportedLanguage "com.healthyproducts.app.data.model  Usage "com.healthyproducts.app.data.model  User "com.healthyproducts.app.data.model  UserFoodPreference "com.healthyproducts.app.data.model  UserSettings "com.healthyproducts.app.data.model  VeganStatus "com.healthyproducts.app.data.model  any "com.healthyproducts.app.data.model  contains "com.healthyproducts.app.data.model  filter "com.healthyproducts.app.data.model  listOf "com.healthyproducts.app.data.model  Boolean +com.healthyproducts.app.data.model.Additive  
DocumentId +com.healthyproducts.app.data.model.Additive  Int +com.healthyproducts.app.data.model.Additive  PropertyName +com.healthyproducts.app.data.model.Additive  String +com.healthyproducts.app.data.model.Additive  SupportedLanguage +com.healthyproducts.app.data.model.Additive  AdditiveCategory 3com.healthyproducts.app.data.model.AdditiveCategory  String 3com.healthyproducts.app.data.model.AdditiveCategory  AiModel *com.healthyproducts.app.data.model.AiModel  String *com.healthyproducts.app.data.model.AiModel  Boolean +com.healthyproducts.app.data.model.Allergen  Int +com.healthyproducts.app.data.model.Allergen  List +com.healthyproducts.app.data.model.Allergen  PropertyName +com.healthyproducts.app.data.model.Allergen  String +com.healthyproducts.app.data.model.Allergen  SupportedLanguage +com.healthyproducts.app.data.model.Allergen  Int )com.healthyproducts.app.data.model.Choice  Message )com.healthyproducts.app.data.model.Choice  SerializedName )com.healthyproducts.app.data.model.Choice  String )com.healthyproducts.app.data.model.Choice  Double 2com.healthyproducts.app.data.model.DeepSeekRequest  Int 2com.healthyproducts.app.data.model.DeepSeekRequest  List 2com.healthyproducts.app.data.model.DeepSeekRequest  Message 2com.healthyproducts.app.data.model.DeepSeekRequest  SerializedName 2com.healthyproducts.app.data.model.DeepSeekRequest  String 2com.healthyproducts.app.data.model.DeepSeekRequest  Choice 3com.healthyproducts.app.data.model.DeepSeekResponse  List 3com.healthyproducts.app.data.model.DeepSeekResponse  Long 3com.healthyproducts.app.data.model.DeepSeekResponse  SerializedName 3com.healthyproducts.app.data.model.DeepSeekResponse  String 3com.healthyproducts.app.data.model.DeepSeekResponse  Usage 3com.healthyproducts.app.data.model.DeepSeekResponse  Int &com.healthyproducts.app.data.model.Fat  List &com.healthyproducts.app.data.model.Fat  PropertyName &com.healthyproducts.app.data.model.Fat  String &com.healthyproducts.app.data.model.Fat  SupportedLanguage &com.healthyproducts.app.data.model.Fat  filter &com.healthyproducts.app.data.model.Fat  functionalType &com.healthyproducts.app.data.model.Fat  	getFILTER &com.healthyproducts.app.data.model.Fat  	getFilter &com.healthyproducts.app.data.model.Fat  
DocumentId 2com.healthyproducts.app.data.model.FoodCertificate  List 2com.healthyproducts.app.data.model.FoodCertificate  PropertyName 2com.healthyproducts.app.data.model.FoodCertificate  SerializedName 2com.healthyproducts.app.data.model.FoodCertificate  String 2com.healthyproducts.app.data.model.FoodCertificate  SupportedLanguage 2com.healthyproducts.app.data.model.FoodCertificate  any 2com.healthyproducts.app.data.model.FoodCertificate  contains 2com.healthyproducts.app.data.model.FoodCertificate  filter 2com.healthyproducts.app.data.model.FoodCertificate  functionalType 2com.healthyproducts.app.data.model.FoodCertificate  getANY 2com.healthyproducts.app.data.model.FoodCertificate  getAny 2com.healthyproducts.app.data.model.FoodCertificate  getCONTAINS 2com.healthyproducts.app.data.model.FoodCertificate  getContains 2com.healthyproducts.app.data.model.FoodCertificate  	getFILTER 2com.healthyproducts.app.data.model.FoodCertificate  	getFilter 2com.healthyproducts.app.data.model.FoodCertificate  	getLISTOf 2com.healthyproducts.app.data.model.FoodCertificate  	getListOf 2com.healthyproducts.app.data.model.FoodCertificate  listOf 2com.healthyproducts.app.data.model.FoodCertificate  FoodPreferenceType 5com.healthyproducts.app.data.model.FoodPreferenceType  String 5com.healthyproducts.app.data.model.FoodPreferenceType  HalalStatus .com.healthyproducts.app.data.model.HalalStatus  String .com.healthyproducts.app.data.model.HalalStatus  
DocumentId .com.healthyproducts.app.data.model.Intolerance  Int .com.healthyproducts.app.data.model.Intolerance  List .com.healthyproducts.app.data.model.Intolerance  PropertyName .com.healthyproducts.app.data.model.Intolerance  String .com.healthyproducts.app.data.model.Intolerance  SupportedLanguage .com.healthyproducts.app.data.model.Intolerance  KosherStatus /com.healthyproducts.app.data.model.KosherStatus  String /com.healthyproducts.app.data.model.KosherStatus  String *com.healthyproducts.app.data.model.Message  Boolean /com.healthyproducts.app.data.model.Preservative  Int /com.healthyproducts.app.data.model.Preservative  List /com.healthyproducts.app.data.model.Preservative  PropertyName /com.healthyproducts.app.data.model.Preservative  String /com.healthyproducts.app.data.model.Preservative  SupportedLanguage /com.healthyproducts.app.data.model.Preservative  Boolean *com.healthyproducts.app.data.model.Product  Date *com.healthyproducts.app.data.model.Product  
DocumentId *com.healthyproducts.app.data.model.Product  Int *com.healthyproducts.app.data.model.Product  List *com.healthyproducts.app.data.model.Product  String *com.healthyproducts.app.data.model.Product  Product 6com.healthyproducts.app.data.model.ProductSearchResult  ProductMatchType 6com.healthyproducts.app.data.model.ProductSearchResult  Int 5com.healthyproducts.app.data.model.SaveProductRequest  List 5com.healthyproducts.app.data.model.SaveProductRequest  String 5com.healthyproducts.app.data.model.SaveProductRequest  Int (com.healthyproducts.app.data.model.Sugar  List (com.healthyproducts.app.data.model.Sugar  PropertyName (com.healthyproducts.app.data.model.Sugar  String (com.healthyproducts.app.data.model.Sugar  SupportedLanguage (com.healthyproducts.app.data.model.Sugar  filter (com.healthyproducts.app.data.model.Sugar  functionalType (com.healthyproducts.app.data.model.Sugar  	getFILTER (com.healthyproducts.app.data.model.Sugar  	getFilter (com.healthyproducts.app.data.model.Sugar  
glycemicIndex (com.healthyproducts.app.data.model.Sugar  String 4com.healthyproducts.app.data.model.SupportedLanguage  SupportedLanguage 4com.healthyproducts.app.data.model.SupportedLanguage  TURKISH 4com.healthyproducts.app.data.model.SupportedLanguage  String >com.healthyproducts.app.data.model.SupportedLanguage.Companion  SupportedLanguage >com.healthyproducts.app.data.model.SupportedLanguage.Companion  TURKISH >com.healthyproducts.app.data.model.SupportedLanguage.Companion  Int (com.healthyproducts.app.data.model.Usage  SerializedName (com.healthyproducts.app.data.model.Usage  
SerialName 'com.healthyproducts.app.data.model.User  String 'com.healthyproducts.app.data.model.User  
SerialName 1com.healthyproducts.app.data.model.User.Companion  String 1com.healthyproducts.app.data.model.User.Companion  Boolean 5com.healthyproducts.app.data.model.UserFoodPreference  Date 5com.healthyproducts.app.data.model.UserFoodPreference  
DocumentId 5com.healthyproducts.app.data.model.UserFoodPreference  PropertyName 5com.healthyproducts.app.data.model.UserFoodPreference  ServerTimestamp 5com.healthyproducts.app.data.model.UserFoodPreference  String 5com.healthyproducts.app.data.model.UserFoodPreference  Boolean /com.healthyproducts.app.data.model.UserSettings  
SerialName /com.healthyproducts.app.data.model.UserSettings  String /com.healthyproducts.app.data.model.UserSettings  Boolean 9com.healthyproducts.app.data.model.UserSettings.Companion  
SerialName 9com.healthyproducts.app.data.model.UserSettings.Companion  String 9com.healthyproducts.app.data.model.UserSettings.Companion  String .com.healthyproducts.app.data.model.VeganStatus  VeganStatus .com.healthyproducts.app.data.model.VeganStatus  
LANGUAGE_CODE (com.healthyproducts.app.data.preferences  LanguagePreferences (com.healthyproducts.app.data.preferences  LocaleHelper (com.healthyproducts.app.data.preferences  String (com.healthyproducts.app.data.preferences  map (com.healthyproducts.app.data.preferences  preferencesDataStore (com.healthyproducts.app.data.preferences  provideDelegate (com.healthyproducts.app.data.preferences  stringPreferencesKey (com.healthyproducts.app.data.preferences  Context <com.healthyproducts.app.data.preferences.LanguagePreferences  	DataStore <com.healthyproducts.app.data.preferences.LanguagePreferences  Flow <com.healthyproducts.app.data.preferences.LanguagePreferences  
LANGUAGE_CODE <com.healthyproducts.app.data.preferences.LanguagePreferences  LocaleHelper <com.healthyproducts.app.data.preferences.LanguagePreferences  Preferences <com.healthyproducts.app.data.preferences.LanguagePreferences  String <com.healthyproducts.app.data.preferences.LanguagePreferences  context <com.healthyproducts.app.data.preferences.LanguagePreferences  	dataStore <com.healthyproducts.app.data.preferences.LanguagePreferences  getMAP <com.healthyproducts.app.data.preferences.LanguagePreferences  getMap <com.healthyproducts.app.data.preferences.LanguagePreferences  languageCode <com.healthyproducts.app.data.preferences.LanguagePreferences  map <com.healthyproducts.app.data.preferences.LanguagePreferences  preferencesDataStore <com.healthyproducts.app.data.preferences.LanguagePreferences  provideDelegate <com.healthyproducts.app.data.preferences.LanguagePreferences  stringPreferencesKey <com.healthyproducts.app.data.preferences.LanguagePreferences  Context Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  	DataStore Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  Flow Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  
LANGUAGE_CODE Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  LocaleHelper Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  Preferences Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  String Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  	dataStore Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getMAP Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getMap Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getPREFERENCESDataStore Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getPROVIDEDelegate Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getPreferencesDataStore Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getProvideDelegate Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getSTRINGPreferencesKey Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  getStringPreferencesKey Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  invoke Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  map Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  preferencesDataStore Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  provideDelegate Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  stringPreferencesKey Fcom.healthyproducts.app.data.preferences.LanguagePreferences.Companion  AdditiveRepository 'com.healthyproducts.app.data.repository  AllergenRepository 'com.healthyproducts.app.data.repository  Boolean 'com.healthyproducts.app.data.repository  	ByteArray 'com.healthyproducts.app.data.repository  DeepSeekRepository 'com.healthyproducts.app.data.repository  Double 'com.healthyproducts.app.data.repository  
FatRepository 'com.healthyproducts.app.data.repository  FavoritesRepository 'com.healthyproducts.app.data.repository  FirebaseAuthRepository 'com.healthyproducts.app.data.repository  FirebaseFirestore 'com.healthyproducts.app.data.repository  FirebaseStorageRepository 'com.healthyproducts.app.data.repository  FirestoreRepository 'com.healthyproducts.app.data.repository  FoodAnalysisRepository 'com.healthyproducts.app.data.repository  FoodCertificateRepository 'com.healthyproducts.app.data.repository  ImageUploadRepository 'com.healthyproducts.app.data.repository  Int 'com.healthyproducts.app.data.repository  IntoleranceRepository 'com.healthyproducts.app.data.repository  List 'com.healthyproducts.app.data.repository  MutableList 'com.healthyproducts.app.data.repository  MutableStateFlow 'com.healthyproducts.app.data.repository  OcrCorrectionRepository 'com.healthyproducts.app.data.repository  PreservativeRepository 'com.healthyproducts.app.data.repository  ProductRepository 'com.healthyproducts.app.data.repository  Result 'com.healthyproducts.app.data.repository  ScanHistoryRepository 'com.healthyproducts.app.data.repository  String 'com.healthyproducts.app.data.repository  SugarRepository 'com.healthyproducts.app.data.repository  SupportedLanguage 'com.healthyproducts.app.data.repository  Unit 'com.healthyproducts.app.data.repository  UserFoodPreferenceRepository 'com.healthyproducts.app.data.repository  UserPreferences 'com.healthyproducts.app.data.repository  UserRepository 'com.healthyproducts.app.data.repository  asStateFlow 'com.healthyproducts.app.data.repository  	emptyList 'com.healthyproducts.app.data.repository  mutableMapOf 'com.healthyproducts.app.data.repository  Additive :com.healthyproducts.app.data.repository.AdditiveRepository  Boolean :com.healthyproducts.app.data.repository.AdditiveRepository  Context :com.healthyproducts.app.data.repository.AdditiveRepository  FirebaseFirestore :com.healthyproducts.app.data.repository.AdditiveRepository  Inject :com.healthyproducts.app.data.repository.AdditiveRepository  Int :com.healthyproducts.app.data.repository.AdditiveRepository  List :com.healthyproducts.app.data.repository.AdditiveRepository  MutableStateFlow :com.healthyproducts.app.data.repository.AdditiveRepository  Result :com.healthyproducts.app.data.repository.AdditiveRepository  	StateFlow :com.healthyproducts.app.data.repository.AdditiveRepository  String :com.healthyproducts.app.data.repository.AdditiveRepository  Unit :com.healthyproducts.app.data.repository.AdditiveRepository  
_additives :com.healthyproducts.app.data.repository.AdditiveRepository  _error :com.healthyproducts.app.data.repository.AdditiveRepository  
_isLoading :com.healthyproducts.app.data.repository.AdditiveRepository  asStateFlow :com.healthyproducts.app.data.repository.AdditiveRepository  	emptyList :com.healthyproducts.app.data.repository.AdditiveRepository  getASStateFlow :com.healthyproducts.app.data.repository.AdditiveRepository  getAsStateFlow :com.healthyproducts.app.data.repository.AdditiveRepository  getEMPTYList :com.healthyproducts.app.data.repository.AdditiveRepository  getEmptyList :com.healthyproducts.app.data.repository.AdditiveRepository  Allergen :com.healthyproducts.app.data.repository.AllergenRepository  Boolean :com.healthyproducts.app.data.repository.AllergenRepository  Context :com.healthyproducts.app.data.repository.AllergenRepository  FirebaseFirestore :com.healthyproducts.app.data.repository.AllergenRepository  Inject :com.healthyproducts.app.data.repository.AllergenRepository  Int :com.healthyproducts.app.data.repository.AllergenRepository  	JSONArray :com.healthyproducts.app.data.repository.AllergenRepository  List :com.healthyproducts.app.data.repository.AllergenRepository  MutableStateFlow :com.healthyproducts.app.data.repository.AllergenRepository  Result :com.healthyproducts.app.data.repository.AllergenRepository  	StateFlow :com.healthyproducts.app.data.repository.AllergenRepository  String :com.healthyproducts.app.data.repository.AllergenRepository  Unit :com.healthyproducts.app.data.repository.AllergenRepository  
_allergens :com.healthyproducts.app.data.repository.AllergenRepository  _error :com.healthyproducts.app.data.repository.AllergenRepository  
_isLoading :com.healthyproducts.app.data.repository.AllergenRepository  asStateFlow :com.healthyproducts.app.data.repository.AllergenRepository  	emptyList :com.healthyproducts.app.data.repository.AllergenRepository  getASStateFlow :com.healthyproducts.app.data.repository.AllergenRepository  getAsStateFlow :com.healthyproducts.app.data.repository.AllergenRepository  getEMPTYList :com.healthyproducts.app.data.repository.AllergenRepository  getEmptyList :com.healthyproducts.app.data.repository.AllergenRepository  Allergen Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  Boolean Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  Context Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  FirebaseFirestore Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  Inject Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  Int Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  	JSONArray Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  List Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  MutableStateFlow Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  Result Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  	StateFlow Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  String Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  Unit Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  asStateFlow Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  	emptyList Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  getASStateFlow Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  getAsStateFlow Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  getEMPTYList Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  getEmptyList Dcom.healthyproducts.app.data.repository.AllergenRepository.Companion  DeepSeekApi :com.healthyproducts.app.data.repository.DeepSeekRepository  DeepSeekResponse :com.healthyproducts.app.data.repository.DeepSeekRepository  FirestoreRepository :com.healthyproducts.app.data.repository.DeepSeekRepository  Inject :com.healthyproducts.app.data.repository.DeepSeekRepository  String :com.healthyproducts.app.data.repository.DeepSeekRepository  Boolean 5com.healthyproducts.app.data.repository.FatRepository  Context 5com.healthyproducts.app.data.repository.FatRepository  Fat 5com.healthyproducts.app.data.repository.FatRepository  FirebaseFirestore 5com.healthyproducts.app.data.repository.FatRepository  Inject 5com.healthyproducts.app.data.repository.FatRepository  Int 5com.healthyproducts.app.data.repository.FatRepository  	JSONArray 5com.healthyproducts.app.data.repository.FatRepository  List 5com.healthyproducts.app.data.repository.FatRepository  MutableStateFlow 5com.healthyproducts.app.data.repository.FatRepository  Result 5com.healthyproducts.app.data.repository.FatRepository  	StateFlow 5com.healthyproducts.app.data.repository.FatRepository  String 5com.healthyproducts.app.data.repository.FatRepository  Unit 5com.healthyproducts.app.data.repository.FatRepository  _error 5com.healthyproducts.app.data.repository.FatRepository  _fats 5com.healthyproducts.app.data.repository.FatRepository  
_isLoading 5com.healthyproducts.app.data.repository.FatRepository  asStateFlow 5com.healthyproducts.app.data.repository.FatRepository  	emptyList 5com.healthyproducts.app.data.repository.FatRepository  getASStateFlow 5com.healthyproducts.app.data.repository.FatRepository  getAsStateFlow 5com.healthyproducts.app.data.repository.FatRepository  getEMPTYList 5com.healthyproducts.app.data.repository.FatRepository  getEmptyList 5com.healthyproducts.app.data.repository.FatRepository  Boolean ?com.healthyproducts.app.data.repository.FatRepository.Companion  Context ?com.healthyproducts.app.data.repository.FatRepository.Companion  Fat ?com.healthyproducts.app.data.repository.FatRepository.Companion  FirebaseFirestore ?com.healthyproducts.app.data.repository.FatRepository.Companion  Inject ?com.healthyproducts.app.data.repository.FatRepository.Companion  Int ?com.healthyproducts.app.data.repository.FatRepository.Companion  	JSONArray ?com.healthyproducts.app.data.repository.FatRepository.Companion  List ?com.healthyproducts.app.data.repository.FatRepository.Companion  MutableStateFlow ?com.healthyproducts.app.data.repository.FatRepository.Companion  Result ?com.healthyproducts.app.data.repository.FatRepository.Companion  	StateFlow ?com.healthyproducts.app.data.repository.FatRepository.Companion  String ?com.healthyproducts.app.data.repository.FatRepository.Companion  Unit ?com.healthyproducts.app.data.repository.FatRepository.Companion  asStateFlow ?com.healthyproducts.app.data.repository.FatRepository.Companion  	emptyList ?com.healthyproducts.app.data.repository.FatRepository.Companion  getASStateFlow ?com.healthyproducts.app.data.repository.FatRepository.Companion  getAsStateFlow ?com.healthyproducts.app.data.repository.FatRepository.Companion  getEMPTYList ?com.healthyproducts.app.data.repository.FatRepository.Companion  getEmptyList ?com.healthyproducts.app.data.repository.FatRepository.Companion  Boolean ;com.healthyproducts.app.data.repository.FavoritesRepository  FavoriteProduct ;com.healthyproducts.app.data.repository.FavoritesRepository  List ;com.healthyproducts.app.data.repository.FavoritesRepository  MutableList ;com.healthyproducts.app.data.repository.FavoritesRepository  Product ;com.healthyproducts.app.data.repository.FavoritesRepository  Result ;com.healthyproducts.app.data.repository.FavoritesRepository  String ;com.healthyproducts.app.data.repository.FavoritesRepository  Unit ;com.healthyproducts.app.data.repository.FavoritesRepository  getMUTABLEMapOf ;com.healthyproducts.app.data.repository.FavoritesRepository  getMutableMapOf ;com.healthyproducts.app.data.repository.FavoritesRepository  mutableMapOf ;com.healthyproducts.app.data.repository.FavoritesRepository  Boolean >com.healthyproducts.app.data.repository.FirebaseAuthRepository  FirebaseAuth >com.healthyproducts.app.data.repository.FirebaseAuthRepository  FirebaseUser >com.healthyproducts.app.data.repository.FirebaseAuthRepository  Inject >com.healthyproducts.app.data.repository.FirebaseAuthRepository  String >com.healthyproducts.app.data.repository.FirebaseAuthRepository  FirebaseAuthRepository Acom.healthyproducts.app.data.repository.FirebaseStorageRepository  FirebaseStorage Acom.healthyproducts.app.data.repository.FirebaseStorageRepository  Inject Acom.healthyproducts.app.data.repository.FirebaseStorageRepository  StorageReference Acom.healthyproducts.app.data.repository.FirebaseStorageRepository  String Acom.healthyproducts.app.data.repository.FirebaseStorageRepository  Uri Acom.healthyproducts.app.data.repository.FirebaseStorageRepository  FirebaseAuthRepository ;com.healthyproducts.app.data.repository.FirestoreRepository  FirebaseFirestore ;com.healthyproducts.app.data.repository.FirestoreRepository  Inject ;com.healthyproducts.app.data.repository.FirestoreRepository  List ;com.healthyproducts.app.data.repository.FirestoreRepository  MutableStateFlow ;com.healthyproducts.app.data.repository.FirestoreRepository  	StateFlow ;com.healthyproducts.app.data.repository.FirestoreRepository  String ;com.healthyproducts.app.data.repository.FirestoreRepository  SupportedLanguage ;com.healthyproducts.app.data.repository.FirestoreRepository  UserPreferences ;com.healthyproducts.app.data.repository.FirestoreRepository  UserSettings ;com.healthyproducts.app.data.repository.FirestoreRepository  
_userLanguage ;com.healthyproducts.app.data.repository.FirestoreRepository  _userPreferences ;com.healthyproducts.app.data.repository.FirestoreRepository  
_userSettings ;com.healthyproducts.app.data.repository.FirestoreRepository  asStateFlow ;com.healthyproducts.app.data.repository.FirestoreRepository  getASStateFlow ;com.healthyproducts.app.data.repository.FirestoreRepository  getAsStateFlow ;com.healthyproducts.app.data.repository.FirestoreRepository  userLanguage ;com.healthyproducts.app.data.repository.FirestoreRepository  Allergen >com.healthyproducts.app.data.repository.FoodAnalysisRepository  Fat >com.healthyproducts.app.data.repository.FoodAnalysisRepository  FirebaseFirestore >com.healthyproducts.app.data.repository.FoodAnalysisRepository  FoodCertificate >com.healthyproducts.app.data.repository.FoodAnalysisRepository  Inject >com.healthyproducts.app.data.repository.FoodAnalysisRepository  List >com.healthyproducts.app.data.repository.FoodAnalysisRepository  Result >com.healthyproducts.app.data.repository.FoodAnalysisRepository  String >com.healthyproducts.app.data.repository.FoodAnalysisRepository  Sugar >com.healthyproducts.app.data.repository.FoodAnalysisRepository  Boolean Acom.healthyproducts.app.data.repository.FoodCertificateRepository  Context Acom.healthyproducts.app.data.repository.FoodCertificateRepository  FirebaseFirestore Acom.healthyproducts.app.data.repository.FoodCertificateRepository  FoodCertificate Acom.healthyproducts.app.data.repository.FoodCertificateRepository  Inject Acom.healthyproducts.app.data.repository.FoodCertificateRepository  Int Acom.healthyproducts.app.data.repository.FoodCertificateRepository  List Acom.healthyproducts.app.data.repository.FoodCertificateRepository  MutableStateFlow Acom.healthyproducts.app.data.repository.FoodCertificateRepository  Result Acom.healthyproducts.app.data.repository.FoodCertificateRepository  	StateFlow Acom.healthyproducts.app.data.repository.FoodCertificateRepository  String Acom.healthyproducts.app.data.repository.FoodCertificateRepository  Unit Acom.healthyproducts.app.data.repository.FoodCertificateRepository  
_certificates Acom.healthyproducts.app.data.repository.FoodCertificateRepository  _error Acom.healthyproducts.app.data.repository.FoodCertificateRepository  
_isLoading Acom.healthyproducts.app.data.repository.FoodCertificateRepository  asStateFlow Acom.healthyproducts.app.data.repository.FoodCertificateRepository  	emptyList Acom.healthyproducts.app.data.repository.FoodCertificateRepository  getASStateFlow Acom.healthyproducts.app.data.repository.FoodCertificateRepository  getAsStateFlow Acom.healthyproducts.app.data.repository.FoodCertificateRepository  getEMPTYList Acom.healthyproducts.app.data.repository.FoodCertificateRepository  getEmptyList Acom.healthyproducts.app.data.repository.FoodCertificateRepository  Boolean Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  Context Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  FirebaseFirestore Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  FoodCertificate Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  Inject Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  Int Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  List Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  MutableStateFlow Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  Result Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  	StateFlow Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  String Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  Unit Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  asStateFlow Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  	emptyList Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  getASStateFlow Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  getAsStateFlow Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  getEMPTYList Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  getEmptyList Kcom.healthyproducts.app.data.repository.FoodCertificateRepository.Companion  Bitmap =com.healthyproducts.app.data.repository.ImageUploadRepository  	ByteArray =com.healthyproducts.app.data.repository.ImageUploadRepository  Context =com.healthyproducts.app.data.repository.ImageUploadRepository  FirebaseStorage =com.healthyproducts.app.data.repository.ImageUploadRepository  Inject =com.healthyproducts.app.data.repository.ImageUploadRepository  Int =com.healthyproducts.app.data.repository.ImageUploadRepository  Result =com.healthyproducts.app.data.repository.ImageUploadRepository  String =com.healthyproducts.app.data.repository.ImageUploadRepository  Unit =com.healthyproducts.app.data.repository.ImageUploadRepository  Uri =com.healthyproducts.app.data.repository.ImageUploadRepository  Bitmap Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  	ByteArray Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  Context Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  FirebaseStorage Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  Inject Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  Int Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  Result Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  String Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  Unit Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  Uri Gcom.healthyproducts.app.data.repository.ImageUploadRepository.Companion  Boolean =com.healthyproducts.app.data.repository.IntoleranceRepository  FirebaseFirestore =com.healthyproducts.app.data.repository.IntoleranceRepository  Inject =com.healthyproducts.app.data.repository.IntoleranceRepository  Intolerance =com.healthyproducts.app.data.repository.IntoleranceRepository  List =com.healthyproducts.app.data.repository.IntoleranceRepository  MutableStateFlow =com.healthyproducts.app.data.repository.IntoleranceRepository  Result =com.healthyproducts.app.data.repository.IntoleranceRepository  	StateFlow =com.healthyproducts.app.data.repository.IntoleranceRepository  String =com.healthyproducts.app.data.repository.IntoleranceRepository  Unit =com.healthyproducts.app.data.repository.IntoleranceRepository  _error =com.healthyproducts.app.data.repository.IntoleranceRepository  
_intolerances =com.healthyproducts.app.data.repository.IntoleranceRepository  
_isLoading =com.healthyproducts.app.data.repository.IntoleranceRepository  asStateFlow =com.healthyproducts.app.data.repository.IntoleranceRepository  	emptyList =com.healthyproducts.app.data.repository.IntoleranceRepository  getASStateFlow =com.healthyproducts.app.data.repository.IntoleranceRepository  getAsStateFlow =com.healthyproducts.app.data.repository.IntoleranceRepository  getEMPTYList =com.healthyproducts.app.data.repository.IntoleranceRepository  getEmptyList =com.healthyproducts.app.data.repository.IntoleranceRepository  Boolean Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  FirebaseFirestore Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  Inject Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  Intolerance Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  List Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  MutableStateFlow Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  Result Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  	StateFlow Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  String Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  Unit Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  asStateFlow Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  	emptyList Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  getASStateFlow Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  getAsStateFlow Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  getEMPTYList Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  getEmptyList Gcom.healthyproducts.app.data.repository.IntoleranceRepository.Companion  	AiService ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  ApplicationContext ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  Boolean ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  Context ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  FirestoreRepository ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  Inject ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  List ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  Result ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  String ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  SupportedLanguage ?com.healthyproducts.app.data.repository.OcrCorrectionRepository  Boolean >com.healthyproducts.app.data.repository.PreservativeRepository  Context >com.healthyproducts.app.data.repository.PreservativeRepository  FirebaseFirestore >com.healthyproducts.app.data.repository.PreservativeRepository  Inject >com.healthyproducts.app.data.repository.PreservativeRepository  Int >com.healthyproducts.app.data.repository.PreservativeRepository  	JSONArray >com.healthyproducts.app.data.repository.PreservativeRepository  List >com.healthyproducts.app.data.repository.PreservativeRepository  MutableStateFlow >com.healthyproducts.app.data.repository.PreservativeRepository  Preservative >com.healthyproducts.app.data.repository.PreservativeRepository  Result >com.healthyproducts.app.data.repository.PreservativeRepository  	StateFlow >com.healthyproducts.app.data.repository.PreservativeRepository  String >com.healthyproducts.app.data.repository.PreservativeRepository  Unit >com.healthyproducts.app.data.repository.PreservativeRepository  _error >com.healthyproducts.app.data.repository.PreservativeRepository  
_isLoading >com.healthyproducts.app.data.repository.PreservativeRepository  _preservatives >com.healthyproducts.app.data.repository.PreservativeRepository  asStateFlow >com.healthyproducts.app.data.repository.PreservativeRepository  	emptyList >com.healthyproducts.app.data.repository.PreservativeRepository  getASStateFlow >com.healthyproducts.app.data.repository.PreservativeRepository  getAsStateFlow >com.healthyproducts.app.data.repository.PreservativeRepository  getEMPTYList >com.healthyproducts.app.data.repository.PreservativeRepository  getEmptyList >com.healthyproducts.app.data.repository.PreservativeRepository  Boolean Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  Context Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  FirebaseFirestore Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  Inject Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  Int Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  	JSONArray Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  List Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  MutableStateFlow Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  Preservative Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  Result Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  	StateFlow Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  String Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  Unit Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  asStateFlow Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  	emptyList Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  getASStateFlow Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  getAsStateFlow Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  getEMPTYList Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  getEmptyList Hcom.healthyproducts.app.data.repository.PreservativeRepository.Companion  Double 9com.healthyproducts.app.data.repository.ProductRepository  FirebaseFirestore 9com.healthyproducts.app.data.repository.ProductRepository  Inject 9com.healthyproducts.app.data.repository.ProductRepository  Int 9com.healthyproducts.app.data.repository.ProductRepository  List 9com.healthyproducts.app.data.repository.ProductRepository  Product 9com.healthyproducts.app.data.repository.ProductRepository  ProductSearchResult 9com.healthyproducts.app.data.repository.ProductRepository  Result 9com.healthyproducts.app.data.repository.ProductRepository  SaveProductRequest 9com.healthyproducts.app.data.repository.ProductRepository  String 9com.healthyproducts.app.data.repository.ProductRepository  Unit 9com.healthyproducts.app.data.repository.ProductRepository  Double Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  FirebaseFirestore Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  Inject Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  Int Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  List Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  Product Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  ProductSearchResult Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  Result Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  SaveProductRequest Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  String Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  Unit Ccom.healthyproducts.app.data.repository.ProductRepository.Companion  Inject =com.healthyproducts.app.data.repository.ScanHistoryRepository  Int =com.healthyproducts.app.data.repository.ScanHistoryRepository  List =com.healthyproducts.app.data.repository.ScanHistoryRepository  MutableList =com.healthyproducts.app.data.repository.ScanHistoryRepository  Product =com.healthyproducts.app.data.repository.ScanHistoryRepository  Result =com.healthyproducts.app.data.repository.ScanHistoryRepository  ScanHistory =com.healthyproducts.app.data.repository.ScanHistoryRepository  ScanHistoryWithProduct =com.healthyproducts.app.data.repository.ScanHistoryRepository  ScanType =com.healthyproducts.app.data.repository.ScanHistoryRepository  String =com.healthyproducts.app.data.repository.ScanHistoryRepository  Unit =com.healthyproducts.app.data.repository.ScanHistoryRepository  getMUTABLEMapOf =com.healthyproducts.app.data.repository.ScanHistoryRepository  getMutableMapOf =com.healthyproducts.app.data.repository.ScanHistoryRepository  mutableMapOf =com.healthyproducts.app.data.repository.ScanHistoryRepository  Product Tcom.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct  ScanHistory Tcom.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct  Boolean 7com.healthyproducts.app.data.repository.SugarRepository  Context 7com.healthyproducts.app.data.repository.SugarRepository  FirebaseFirestore 7com.healthyproducts.app.data.repository.SugarRepository  Inject 7com.healthyproducts.app.data.repository.SugarRepository  Int 7com.healthyproducts.app.data.repository.SugarRepository  	JSONArray 7com.healthyproducts.app.data.repository.SugarRepository  List 7com.healthyproducts.app.data.repository.SugarRepository  MutableStateFlow 7com.healthyproducts.app.data.repository.SugarRepository  Result 7com.healthyproducts.app.data.repository.SugarRepository  	StateFlow 7com.healthyproducts.app.data.repository.SugarRepository  String 7com.healthyproducts.app.data.repository.SugarRepository  Sugar 7com.healthyproducts.app.data.repository.SugarRepository  Unit 7com.healthyproducts.app.data.repository.SugarRepository  _error 7com.healthyproducts.app.data.repository.SugarRepository  
_isLoading 7com.healthyproducts.app.data.repository.SugarRepository  _sugars 7com.healthyproducts.app.data.repository.SugarRepository  asStateFlow 7com.healthyproducts.app.data.repository.SugarRepository  	emptyList 7com.healthyproducts.app.data.repository.SugarRepository  getASStateFlow 7com.healthyproducts.app.data.repository.SugarRepository  getAsStateFlow 7com.healthyproducts.app.data.repository.SugarRepository  getEMPTYList 7com.healthyproducts.app.data.repository.SugarRepository  getEmptyList 7com.healthyproducts.app.data.repository.SugarRepository  Boolean Acom.healthyproducts.app.data.repository.SugarRepository.Companion  Context Acom.healthyproducts.app.data.repository.SugarRepository.Companion  FirebaseFirestore Acom.healthyproducts.app.data.repository.SugarRepository.Companion  Inject Acom.healthyproducts.app.data.repository.SugarRepository.Companion  Int Acom.healthyproducts.app.data.repository.SugarRepository.Companion  	JSONArray Acom.healthyproducts.app.data.repository.SugarRepository.Companion  List Acom.healthyproducts.app.data.repository.SugarRepository.Companion  MutableStateFlow Acom.healthyproducts.app.data.repository.SugarRepository.Companion  Result Acom.healthyproducts.app.data.repository.SugarRepository.Companion  	StateFlow Acom.healthyproducts.app.data.repository.SugarRepository.Companion  String Acom.healthyproducts.app.data.repository.SugarRepository.Companion  Sugar Acom.healthyproducts.app.data.repository.SugarRepository.Companion  Unit Acom.healthyproducts.app.data.repository.SugarRepository.Companion  asStateFlow Acom.healthyproducts.app.data.repository.SugarRepository.Companion  	emptyList Acom.healthyproducts.app.data.repository.SugarRepository.Companion  getASStateFlow Acom.healthyproducts.app.data.repository.SugarRepository.Companion  getAsStateFlow Acom.healthyproducts.app.data.repository.SugarRepository.Companion  getEMPTYList Acom.healthyproducts.app.data.repository.SugarRepository.Companion  getEmptyList Acom.healthyproducts.app.data.repository.SugarRepository.Companion  FirebaseFirestore Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  Flow Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  FoodPreferenceType Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  Inject Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  List Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  MutableStateFlow Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  Result Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  String Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  Unit Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  UserFoodPreference Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  _userFoodPreferences Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  asStateFlow Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  	emptyList Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  getASStateFlow Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  getAsStateFlow Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  getEMPTYList Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  getEmptyList Dcom.healthyproducts.app.data.repository.UserFoodPreferenceRepository  FirebaseFirestore Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  Flow Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  FoodPreferenceType Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  Inject Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  List Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  MutableStateFlow Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  Result Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  String Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  Unit Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  UserFoodPreference Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  asStateFlow Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  	emptyList Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  getASStateFlow Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  getAsStateFlow Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  getEMPTYList Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  getEmptyList Ncom.healthyproducts.app.data.repository.UserFoodPreferenceRepository.Companion  FirebaseAuthRepository 6com.healthyproducts.app.data.repository.UserRepository  FirebaseFirestore 6com.healthyproducts.app.data.repository.UserRepository  FirebaseUser 6com.healthyproducts.app.data.repository.UserRepository  Inject 6com.healthyproducts.app.data.repository.UserRepository  Result 6com.healthyproducts.app.data.repository.UserRepository  String 6com.healthyproducts.app.data.repository.UserRepository  Unit 6com.healthyproducts.app.data.repository.UserRepository  User 6com.healthyproducts.app.data.repository.UserRepository  UserPreferences 6com.healthyproducts.app.data.repository.UserRepository  AdditiveWarning $com.healthyproducts.app.data.service  AllergenWarning $com.healthyproducts.app.data.service  Boolean $com.healthyproducts.app.data.service  CertificateVerification $com.healthyproducts.app.data.service  
FatWarning $com.healthyproducts.app.data.service  FoodAnalysisResult $com.healthyproducts.app.data.service  FoodAnalysisService $com.healthyproducts.app.data.service  Int $com.healthyproducts.app.data.service  IntoleranceWarning $com.healthyproducts.app.data.service  List $com.healthyproducts.app.data.service  PreservativeWarning $com.healthyproducts.app.data.service  String $com.healthyproducts.app.data.service  SugarWarning $com.healthyproducts.app.data.service  Additive 4com.healthyproducts.app.data.service.AdditiveWarning  Int 4com.healthyproducts.app.data.service.AdditiveWarning  String 4com.healthyproducts.app.data.service.AdditiveWarning  Allergen 4com.healthyproducts.app.data.service.AllergenWarning  Int 4com.healthyproducts.app.data.service.AllergenWarning  String 4com.healthyproducts.app.data.service.AllergenWarning  Boolean <com.healthyproducts.app.data.service.CertificateVerification  FoodCertificate <com.healthyproducts.app.data.service.CertificateVerification  String <com.healthyproducts.app.data.service.CertificateVerification  Fat /com.healthyproducts.app.data.service.FatWarning  Int /com.healthyproducts.app.data.service.FatWarning  String /com.healthyproducts.app.data.service.FatWarning  AdditiveWarning 7com.healthyproducts.app.data.service.FoodAnalysisResult  AllergenWarning 7com.healthyproducts.app.data.service.FoodAnalysisResult  CertificateVerification 7com.healthyproducts.app.data.service.FoodAnalysisResult  
FatWarning 7com.healthyproducts.app.data.service.FoodAnalysisResult  Int 7com.healthyproducts.app.data.service.FoodAnalysisResult  IntoleranceWarning 7com.healthyproducts.app.data.service.FoodAnalysisResult  List 7com.healthyproducts.app.data.service.FoodAnalysisResult  PreservativeWarning 7com.healthyproducts.app.data.service.FoodAnalysisResult  SugarWarning 7com.healthyproducts.app.data.service.FoodAnalysisResult  AdditiveRepository 8com.healthyproducts.app.data.service.FoodAnalysisService  AdditiveWarning 8com.healthyproducts.app.data.service.FoodAnalysisService  AllergenRepository 8com.healthyproducts.app.data.service.FoodAnalysisService  AllergenWarning 8com.healthyproducts.app.data.service.FoodAnalysisService  
FatRepository 8com.healthyproducts.app.data.service.FoodAnalysisService  
FatWarning 8com.healthyproducts.app.data.service.FoodAnalysisService  FoodAnalysisResult 8com.healthyproducts.app.data.service.FoodAnalysisService  FoodCertificateRepository 8com.healthyproducts.app.data.service.FoodAnalysisService  Inject 8com.healthyproducts.app.data.service.FoodAnalysisService  Int 8com.healthyproducts.app.data.service.FoodAnalysisService  IntoleranceRepository 8com.healthyproducts.app.data.service.FoodAnalysisService  IntoleranceWarning 8com.healthyproducts.app.data.service.FoodAnalysisService  List 8com.healthyproducts.app.data.service.FoodAnalysisService  PreservativeRepository 8com.healthyproducts.app.data.service.FoodAnalysisService  PreservativeWarning 8com.healthyproducts.app.data.service.FoodAnalysisService  String 8com.healthyproducts.app.data.service.FoodAnalysisService  SugarRepository 8com.healthyproducts.app.data.service.FoodAnalysisService  SugarWarning 8com.healthyproducts.app.data.service.FoodAnalysisService  SupportedLanguage 8com.healthyproducts.app.data.service.FoodAnalysisService  AdditiveRepository Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  AdditiveWarning Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  AllergenRepository Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  AllergenWarning Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  
FatRepository Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  
FatWarning Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  FoodAnalysisResult Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  FoodCertificateRepository Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  Inject Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  Int Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  IntoleranceRepository Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  IntoleranceWarning Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  List Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  PreservativeRepository Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  PreservativeWarning Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  String Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  SugarRepository Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  SugarWarning Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  SupportedLanguage Bcom.healthyproducts.app.data.service.FoodAnalysisService.Companion  Int 7com.healthyproducts.app.data.service.IntoleranceWarning  Intolerance 7com.healthyproducts.app.data.service.IntoleranceWarning  String 7com.healthyproducts.app.data.service.IntoleranceWarning  Int 8com.healthyproducts.app.data.service.PreservativeWarning  Preservative 8com.healthyproducts.app.data.service.PreservativeWarning  String 8com.healthyproducts.app.data.service.PreservativeWarning  Int 1com.healthyproducts.app.data.service.SugarWarning  String 1com.healthyproducts.app.data.service.SugarWarning  Sugar 1com.healthyproducts.app.data.service.SugarWarning  	AppModule com.healthyproducts.app.di  FirebaseModule com.healthyproducts.app.di  
NetworkModule com.healthyproducts.app.di  RepositoryModule com.healthyproducts.app.di  SingletonComponent com.healthyproducts.app.di  String com.healthyproducts.app.di  android com.healthyproducts.app.di  ApplicationContext $com.healthyproducts.app.di.AppModule  Context $com.healthyproducts.app.di.AppModule  Provides $com.healthyproducts.app.di.AppModule  	Singleton $com.healthyproducts.app.di.AppModule  FirebaseAuth )com.healthyproducts.app.di.FirebaseModule  FirebaseFirestore )com.healthyproducts.app.di.FirebaseModule  FirebaseStorage )com.healthyproducts.app.di.FirebaseModule  Provides )com.healthyproducts.app.di.FirebaseModule  	Singleton )com.healthyproducts.app.di.FirebaseModule  	AiService (com.healthyproducts.app.di.NetworkModule  DeepSeekApi (com.healthyproducts.app.di.NetworkModule  DeepSeekApiService (com.healthyproducts.app.di.NetworkModule  GeminiApiService (com.healthyproducts.app.di.NetworkModule  HttpLoggingInterceptor (com.healthyproducts.app.di.NetworkModule  Interceptor (com.healthyproducts.app.di.NetworkModule  OkHttpClient (com.healthyproducts.app.di.NetworkModule  Provides (com.healthyproducts.app.di.NetworkModule  	Singleton (com.healthyproducts.app.di.NetworkModule  String (com.healthyproducts.app.di.NetworkModule  AdditiveRepository +com.healthyproducts.app.di.RepositoryModule  AllergenRepository +com.healthyproducts.app.di.RepositoryModule  
FatRepository +com.healthyproducts.app.di.RepositoryModule  FirebaseAuth +com.healthyproducts.app.di.RepositoryModule  FirebaseAuthRepository +com.healthyproducts.app.di.RepositoryModule  FirebaseFirestore +com.healthyproducts.app.di.RepositoryModule  FirebaseStorage +com.healthyproducts.app.di.RepositoryModule  FirebaseStorageRepository +com.healthyproducts.app.di.RepositoryModule  FirestoreRepository +com.healthyproducts.app.di.RepositoryModule  FoodAnalysisService +com.healthyproducts.app.di.RepositoryModule  FoodCertificateRepository +com.healthyproducts.app.di.RepositoryModule  ImageUploadRepository +com.healthyproducts.app.di.RepositoryModule  IntoleranceRepository +com.healthyproducts.app.di.RepositoryModule  PreservativeRepository +com.healthyproducts.app.di.RepositoryModule  ProductRepository +com.healthyproducts.app.di.RepositoryModule  Provides +com.healthyproducts.app.di.RepositoryModule  	Singleton +com.healthyproducts.app.di.RepositoryModule  SugarRepository +com.healthyproducts.app.di.RepositoryModule  android +com.healthyproducts.app.di.RepositoryModule  Boolean com.healthyproducts.app.model  Certificate com.healthyproducts.app.model  CertificateType com.healthyproducts.app.model  FavoriteProduct com.healthyproducts.app.model  
Ingredient com.healthyproducts.app.model  IngredientStatus com.healthyproducts.app.model  List com.healthyproducts.app.model  Product com.healthyproducts.app.model  ScanHistory com.healthyproducts.app.model  ScanType com.healthyproducts.app.model  String com.healthyproducts.app.model  User com.healthyproducts.app.model  UserPreferences com.healthyproducts.app.model  CertificateType )com.healthyproducts.app.model.Certificate  Date )com.healthyproducts.app.model.Certificate  String )com.healthyproducts.app.model.Certificate  Date -com.healthyproducts.app.model.FavoriteProduct  String -com.healthyproducts.app.model.FavoriteProduct  IngredientStatus (com.healthyproducts.app.model.Ingredient  List (com.healthyproducts.app.model.Ingredient  String (com.healthyproducts.app.model.Ingredient  Boolean %com.healthyproducts.app.model.Product  Certificate %com.healthyproducts.app.model.Product  Date %com.healthyproducts.app.model.Product  
Ingredient %com.healthyproducts.app.model.Product  List %com.healthyproducts.app.model.Product  String %com.healthyproducts.app.model.Product  Date )com.healthyproducts.app.model.ScanHistory  ScanType )com.healthyproducts.app.model.ScanHistory  String )com.healthyproducts.app.model.ScanHistory  Date "com.healthyproducts.app.model.User  
DocumentId "com.healthyproducts.app.model.User  ServerTimestamp "com.healthyproducts.app.model.User  String "com.healthyproducts.app.model.User  Boolean -com.healthyproducts.app.model.UserPreferences  Date -com.healthyproducts.app.model.UserPreferences  PropertyName -com.healthyproducts.app.model.UserPreferences  ServerTimestamp -com.healthyproducts.app.model.UserPreferences  String -com.healthyproducts.app.model.UserPreferences  
AppNavigation "com.healthyproducts.app.navigation  Screen "com.healthyproducts.app.navigation  String "com.healthyproducts.app.navigation  	Favorites )com.healthyproducts.app.navigation.Screen  Home )com.healthyproducts.app.navigation.Screen  Profile )com.healthyproducts.app.navigation.Screen  Scan )com.healthyproducts.app.navigation.Screen  Screen )com.healthyproducts.app.navigation.Screen  String )com.healthyproducts.app.navigation.Screen  String 8com.healthyproducts.app.navigation.Screen.AdditiveDetail  String 6com.healthyproducts.app.navigation.Screen.AdditiveEdit  String 8com.healthyproducts.app.navigation.Screen.AllergenDetail  String 3com.healthyproducts.app.navigation.Screen.FatDetail  String 6com.healthyproducts.app.navigation.Screen.FoodAnalysis  String ?com.healthyproducts.app.navigation.Screen.FoodCertificateDetail  String <com.healthyproducts.app.navigation.Screen.PreservativeDetail  String 7com.healthyproducts.app.navigation.Screen.ProductDetail  String 5com.healthyproducts.app.navigation.Screen.SugarDetail  Boolean com.healthyproducts.app.ui.auth  ExperimentalMaterial3Api com.healthyproducts.app.ui.auth  LoginScreen com.healthyproducts.app.ui.auth  LoginViewModel com.healthyproducts.app.ui.auth  MutableStateFlow com.healthyproducts.app.ui.auth  OptIn com.healthyproducts.app.ui.auth  RegisterScreen com.healthyproducts.app.ui.auth  RegisterViewModel com.healthyproducts.app.ui.auth  String com.healthyproducts.app.ui.auth  Unit com.healthyproducts.app.ui.auth  asStateFlow com.healthyproducts.app.ui.auth  Boolean .com.healthyproducts.app.ui.auth.LoginViewModel  FirebaseAuthRepository .com.healthyproducts.app.ui.auth.LoginViewModel  Inject .com.healthyproducts.app.ui.auth.LoginViewModel  MutableStateFlow .com.healthyproducts.app.ui.auth.LoginViewModel  	StateFlow .com.healthyproducts.app.ui.auth.LoginViewModel  String .com.healthyproducts.app.ui.auth.LoginViewModel  _email .com.healthyproducts.app.ui.auth.LoginViewModel  
_isLoading .com.healthyproducts.app.ui.auth.LoginViewModel  _isLoggedIn .com.healthyproducts.app.ui.auth.LoginViewModel  _loginError .com.healthyproducts.app.ui.auth.LoginViewModel  	_password .com.healthyproducts.app.ui.auth.LoginViewModel  asStateFlow .com.healthyproducts.app.ui.auth.LoginViewModel  getASStateFlow .com.healthyproducts.app.ui.auth.LoginViewModel  getAsStateFlow .com.healthyproducts.app.ui.auth.LoginViewModel  Boolean 1com.healthyproducts.app.ui.auth.RegisterViewModel  FirebaseAuthRepository 1com.healthyproducts.app.ui.auth.RegisterViewModel  Inject 1com.healthyproducts.app.ui.auth.RegisterViewModel  MutableStateFlow 1com.healthyproducts.app.ui.auth.RegisterViewModel  	StateFlow 1com.healthyproducts.app.ui.auth.RegisterViewModel  String 1com.healthyproducts.app.ui.auth.RegisterViewModel  _confirmPassword 1com.healthyproducts.app.ui.auth.RegisterViewModel  _email 1com.healthyproducts.app.ui.auth.RegisterViewModel  
_isLoading 1com.healthyproducts.app.ui.auth.RegisterViewModel  
_isRegistered 1com.healthyproducts.app.ui.auth.RegisterViewModel  	_password 1com.healthyproducts.app.ui.auth.RegisterViewModel  _registerError 1com.healthyproducts.app.ui.auth.RegisterViewModel  asStateFlow 1com.healthyproducts.app.ui.auth.RegisterViewModel  getASStateFlow 1com.healthyproducts.app.ui.auth.RegisterViewModel  getAsStateFlow 1com.healthyproducts.app.ui.auth.RegisterViewModel  AddCustomItemDialog %com.healthyproducts.app.ui.components  
BackButton %com.healthyproducts.app.ui.components  BarcodeCameraPreview %com.healthyproducts.app.ui.components  Boolean %com.healthyproducts.app.ui.components  BottomNavBar %com.healthyproducts.app.ui.components  
BottomNavItem %com.healthyproducts.app.ui.components  
CameraPreview %com.healthyproducts.app.ui.components  	ChipGroup %com.healthyproducts.app.ui.components  
Composable %com.healthyproducts.app.ui.components  EditableIngredientsList %com.healthyproducts.app.ui.components  ExperimentalLayoutApi %com.healthyproducts.app.ui.components  ExperimentalMaterial3Api %com.healthyproducts.app.ui.components  Float %com.healthyproducts.app.ui.components  	FrameSize %com.healthyproducts.app.ui.components  ImagePickerComponent %com.healthyproducts.app.ui.components  
ImageProxy %com.healthyproducts.app.ui.components  IngredientItem %com.healthyproducts.app.ui.components  Int %com.healthyproducts.app.ui.components  List %com.healthyproducts.app.ui.components  OcrCameraPreview %com.healthyproducts.app.ui.components  OptIn %com.healthyproducts.app.ui.components  R %com.healthyproducts.app.ui.components  RiskLevelIndicator %com.healthyproducts.app.ui.components  SaveProductDialog %com.healthyproducts.app.ui.components  ScannerOverlay %com.healthyproducts.app.ui.components  Screen %com.healthyproducts.app.ui.components  String %com.healthyproducts.app.ui.components  Unit %com.healthyproducts.app.ui.components  androidx %com.healthyproducts.app.ui.components  bottomNavItems %com.healthyproducts.app.ui.components  captureImage %com.healthyproducts.app.ui.components  	clickable %com.healthyproducts.app.ui.components  com %com.healthyproducts.app.ui.components  
cropBitmap %com.healthyproducts.app.ui.components  extractIngredientsSection %com.healthyproducts.app.ui.components  listOf %com.healthyproducts.app.ui.components  processImageProxy %com.healthyproducts.app.ui.components  rotateBitmap %com.healthyproducts.app.ui.components  toBitmap %com.healthyproducts.app.ui.components  uriToBitmap %com.healthyproducts.app.ui.components  Int 3com.healthyproducts.app.ui.components.BottomNavItem  Screen 3com.healthyproducts.app.ui.components.BottomNavItem  androidx 3com.healthyproducts.app.ui.components.BottomNavItem  MEDIUM /com.healthyproducts.app.ui.components.FrameSize  AdditiveDetail ,com.healthyproducts.app.ui.screens.additives  AdditiveDetailScreen ,com.healthyproducts.app.ui.screens.additives  AdditiveEditScreen ,com.healthyproducts.app.ui.screens.additives  AdditiveItem ,com.healthyproducts.app.ui.screens.additives  
AdditivesList ,com.healthyproducts.app.ui.screens.additives  AdditivesScreen ,com.healthyproducts.app.ui.screens.additives  CategoryDropdown ,com.healthyproducts.app.ui.screens.additives  
DetailItem ,com.healthyproducts.app.ui.screens.additives  ExperimentalMaterial3Api ,com.healthyproducts.app.ui.screens.additives  HalalStatusDropdown ,com.healthyproducts.app.ui.screens.additives  ImportDialog ,com.healthyproducts.app.ui.screens.additives  KosherStatusDropdown ,com.healthyproducts.app.ui.screens.additives  List ,com.healthyproducts.app.ui.screens.additives  OptIn ,com.healthyproducts.app.ui.screens.additives  SearchAndFilterBar ,com.healthyproducts.app.ui.screens.additives  String ,com.healthyproducts.app.ui.screens.additives  Unit ,com.healthyproducts.app.ui.screens.additives  VeganStatusDropdown ,com.healthyproducts.app.ui.screens.additives  DataUploadScreen (com.healthyproducts.app.ui.screens.admin  ExperimentalMaterial3Api (com.healthyproducts.app.ui.screens.admin  OptIn (com.healthyproducts.app.ui.screens.admin  String (com.healthyproducts.app.ui.screens.admin  UploadStatus (com.healthyproducts.app.ui.screens.admin  String 5com.healthyproducts.app.ui.screens.admin.UploadStatus  UploadStatus 5com.healthyproducts.app.ui.screens.admin.UploadStatus  String ;com.healthyproducts.app.ui.screens.admin.UploadStatus.Error  ExperimentalMaterial3Api 'com.healthyproducts.app.ui.screens.auth  LoginScreen 'com.healthyproducts.app.ui.screens.auth  OptIn 'com.healthyproducts.app.ui.screens.auth  RegisterScreen 'com.healthyproducts.app.ui.screens.auth  SocialLoginButtons 'com.healthyproducts.app.ui.screens.auth  Unit 'com.healthyproducts.app.ui.screens.auth  BarcodeScanScreen *com.healthyproducts.app.ui.screens.barcode  Boolean *com.healthyproducts.app.ui.screens.barcode  
Composable *com.healthyproducts.app.ui.screens.barcode  ExperimentalMaterial3Api *com.healthyproducts.app.ui.screens.barcode  OptIn *com.healthyproducts.app.ui.screens.barcode  String *com.healthyproducts.app.ui.screens.barcode  Unit *com.healthyproducts.app.ui.screens.barcode  android *com.healthyproducts.app.ui.screens.barcode  saveProduct *com.healthyproducts.app.ui.screens.barcode  EmptyFavoritesContent ,com.healthyproducts.app.ui.screens.favorites  ExperimentalMaterial3Api ,com.healthyproducts.app.ui.screens.favorites  FavoritesContent ,com.healthyproducts.app.ui.screens.favorites  FavoritesScreen ,com.healthyproducts.app.ui.screens.favorites  List ,com.healthyproducts.app.ui.screens.favorites  LoadingContent ,com.healthyproducts.app.ui.screens.favorites  NotLoggedInContent ,com.healthyproducts.app.ui.screens.favorites  OptIn ,com.healthyproducts.app.ui.screens.favorites  Unit ,com.healthyproducts.app.ui.screens.favorites  AllergenDetailScreen /com.healthyproducts.app.ui.screens.foodanalysis  AllergenItem /com.healthyproducts.app.ui.screens.foodanalysis  AllergensScreen /com.healthyproducts.app.ui.screens.foodanalysis  AnalysisResultView /com.healthyproducts.app.ui.screens.foodanalysis  
ApprovalBadge /com.healthyproducts.app.ui.screens.foodanalysis  BadgeSection /com.healthyproducts.app.ui.screens.foodanalysis  Boolean /com.healthyproducts.app.ui.screens.foodanalysis  CertificateTypeIndicator /com.healthyproducts.app.ui.screens.foodanalysis  ExperimentalMaterial3Api /com.healthyproducts.app.ui.screens.foodanalysis  FatDetailScreen /com.healthyproducts.app.ui.screens.foodanalysis  FatItem /com.healthyproducts.app.ui.screens.foodanalysis  
FatsScreen /com.healthyproducts.app.ui.screens.foodanalysis  Float /com.healthyproducts.app.ui.screens.foodanalysis  FoodAnalysisOptionCard /com.healthyproducts.app.ui.screens.foodanalysis  FoodAnalysisOptions /com.healthyproducts.app.ui.screens.foodanalysis  FoodAnalysisScreen /com.healthyproducts.app.ui.screens.foodanalysis  FoodCertificateDetailScreen /com.healthyproducts.app.ui.screens.foodanalysis  FoodCertificateItem /com.healthyproducts.app.ui.screens.foodanalysis  FoodCertificatesScreen /com.healthyproducts.app.ui.screens.foodanalysis  GlycemicIndexIndicator /com.healthyproducts.app.ui.screens.foodanalysis  HealthLevelIndicator /com.healthyproducts.app.ui.screens.foodanalysis  Int /com.healthyproducts.app.ui.screens.foodanalysis  List /com.healthyproducts.app.ui.screens.foodanalysis  Map /com.healthyproducts.app.ui.screens.foodanalysis  OptIn /com.healthyproducts.app.ui.screens.foodanalysis  PreservativeDetailContent /com.healthyproducts.app.ui.screens.foodanalysis  PreservativeDetailScreen /com.healthyproducts.app.ui.screens.foodanalysis  PreservativeItem /com.healthyproducts.app.ui.screens.foodanalysis  PreservativesList /com.healthyproducts.app.ui.screens.foodanalysis  PreservativesScreen /com.healthyproducts.app.ui.screens.foodanalysis  RiskLevelIndicator /com.healthyproducts.app.ui.screens.foodanalysis  
ScoreBadge /com.healthyproducts.app.ui.screens.foodanalysis  String /com.healthyproducts.app.ui.screens.foodanalysis  SugarDetailScreen /com.healthyproducts.app.ui.screens.foodanalysis  	SugarItem /com.healthyproducts.app.ui.screens.foodanalysis  SugarsScreen /com.healthyproducts.app.ui.screens.foodanalysis  Unit /com.healthyproducts.app.ui.screens.foodanalysis  WarningBadge /com.healthyproducts.app.ui.screens.foodanalysis  androidx /com.healthyproducts.app.ui.screens.foodanalysis  parseAnalysisResult /com.healthyproducts.app.ui.screens.foodanalysis  ExperimentalMaterial3Api 'com.healthyproducts.app.ui.screens.home  FoodAnalysisOptions 'com.healthyproducts.app.ui.screens.home  HomeContent 'com.healthyproducts.app.ui.screens.home  
HomeScreen 'com.healthyproducts.app.ui.screens.home  List 'com.healthyproducts.app.ui.screens.home  OptIn 'com.healthyproducts.app.ui.screens.home  RecentScanItem 'com.healthyproducts.app.ui.screens.home  ScanOptionCard 'com.healthyproducts.app.ui.screens.home  ScanOptions 'com.healthyproducts.app.ui.screens.home  String 'com.healthyproducts.app.ui.screens.home  Unit 'com.healthyproducts.app.ui.screens.home  androidx 'com.healthyproducts.app.ui.screens.home  Boolean *com.healthyproducts.app.ui.screens.product  ExperimentalMaterial3Api *com.healthyproducts.app.ui.screens.product  IngredientItem *com.healthyproducts.app.ui.screens.product  OptIn *com.healthyproducts.app.ui.screens.product  ProductDetailScreen *com.healthyproducts.app.ui.screens.product  
StatusCard *com.healthyproducts.app.ui.screens.product  String *com.healthyproducts.app.ui.screens.product  ExperimentalMaterial3Api *com.healthyproducts.app.ui.screens.profile  LoadingContent *com.healthyproducts.app.ui.screens.profile  NotLoggedInContent *com.healthyproducts.app.ui.screens.profile  OptIn *com.healthyproducts.app.ui.screens.profile  ProfileContent *com.healthyproducts.app.ui.screens.profile  
ProfileScreen *com.healthyproducts.app.ui.screens.profile  Unit *com.healthyproducts.app.ui.screens.profile  BarcodeScanContent 'com.healthyproducts.app.ui.screens.scan  Boolean 'com.healthyproducts.app.ui.screens.scan  ExperimentalMaterial3Api 'com.healthyproducts.app.ui.screens.scan  FrameSizeOption 'com.healthyproducts.app.ui.screens.scan  OcrScanContent 'com.healthyproducts.app.ui.screens.scan  OptIn 'com.healthyproducts.app.ui.screens.scan  
ScanScreen 'com.healthyproducts.app.ui.screens.scan  String 'com.healthyproducts.app.ui.screens.scan  Unit 'com.healthyproducts.app.ui.screens.scan  
AiModelOption +com.healthyproducts.app.ui.screens.settings  AiModelSelectionContent +com.healthyproducts.app.ui.screens.settings  AiModelSelectionScreen +com.healthyproducts.app.ui.screens.settings  Boolean +com.healthyproducts.app.ui.screens.settings  ExperimentalMaterial3Api +com.healthyproducts.app.ui.screens.settings  LanguageItem +com.healthyproducts.app.ui.screens.settings  LanguageScreen +com.healthyproducts.app.ui.screens.settings  OptIn +com.healthyproducts.app.ui.screens.settings  SettingsCategory +com.healthyproducts.app.ui.screens.settings  SettingsContent +com.healthyproducts.app.ui.screens.settings  SettingsItem +com.healthyproducts.app.ui.screens.settings  SettingsScreen +com.healthyproducts.app.ui.screens.settings  SettingsSwitch +com.healthyproducts.app.ui.screens.settings  String +com.healthyproducts.app.ui.screens.settings  Unit +com.healthyproducts.app.ui.screens.settings  UserFoodPreferenceItem +com.healthyproducts.app.ui.screens.settings  UserFoodPreferencesScreen +com.healthyproducts.app.ui.screens.settings  updatePreferences +com.healthyproducts.app.ui.screens.settings  
Background  com.healthyproducts.app.ui.theme  BackgroundDark  com.healthyproducts.app.ui.theme  Boolean  com.healthyproducts.app.ui.theme  DarkColorScheme  com.healthyproducts.app.ui.theme  Error  com.healthyproducts.app.ui.theme  	ErrorDark  com.healthyproducts.app.ui.theme  Halal  com.healthyproducts.app.ui.theme  Haram  com.healthyproducts.app.ui.theme  Harmful  com.healthyproducts.app.ui.theme  HealthyProductsTheme  com.healthyproducts.app.ui.theme  LightColorScheme  com.healthyproducts.app.ui.theme  OnBackground  com.healthyproducts.app.ui.theme  OnBackgroundDark  com.healthyproducts.app.ui.theme  OnError  com.healthyproducts.app.ui.theme  OnErrorDark  com.healthyproducts.app.ui.theme  	OnPrimary  com.healthyproducts.app.ui.theme  
OnPrimaryDark  com.healthyproducts.app.ui.theme  OnSecondary  com.healthyproducts.app.ui.theme  OnSecondaryDark  com.healthyproducts.app.ui.theme  	OnSurface  com.healthyproducts.app.ui.theme  
OnSurfaceDark  com.healthyproducts.app.ui.theme  Primary  com.healthyproducts.app.ui.theme  PrimaryDark  com.healthyproducts.app.ui.theme  PrimaryVariant  com.healthyproducts.app.ui.theme  PrimaryVariantDark  com.healthyproducts.app.ui.theme  	Secondary  com.healthyproducts.app.ui.theme  
SecondaryDark  com.healthyproducts.app.ui.theme  SecondaryVariant  com.healthyproducts.app.ui.theme  SecondaryVariantDark  com.healthyproducts.app.ui.theme  Surface  com.healthyproducts.app.ui.theme  SurfaceDark  com.healthyproducts.app.ui.theme  
Suspicious  com.healthyproducts.app.ui.theme  
Typography  com.healthyproducts.app.ui.theme  	Unhealthy  com.healthyproducts.app.ui.theme  Unit  com.healthyproducts.app.ui.theme  Vegan  com.healthyproducts.app.ui.theme  AdditiveViewModel $com.healthyproducts.app.ui.viewmodel  AllergensState $com.healthyproducts.app.ui.viewmodel  
AnalysisState $com.healthyproducts.app.ui.viewmodel  Boolean $com.healthyproducts.app.ui.viewmodel  CertificatesState $com.healthyproducts.app.ui.viewmodel  CorrectionState $com.healthyproducts.app.ui.viewmodel  	FatsState $com.healthyproducts.app.ui.viewmodel  FavoritesState $com.healthyproducts.app.ui.viewmodel  FavoritesViewModel $com.healthyproducts.app.ui.viewmodel  FoodAnalysisViewModel $com.healthyproducts.app.ui.viewmodel  	FrameSize $com.healthyproducts.app.ui.viewmodel  ImportStatus $com.healthyproducts.app.ui.viewmodel  Int $com.healthyproducts.app.ui.viewmodel  LanguagePreferences $com.healthyproducts.app.ui.viewmodel  LanguageViewModel $com.healthyproducts.app.ui.viewmodel  List $com.healthyproducts.app.ui.viewmodel  LocaleHelper $com.healthyproducts.app.ui.viewmodel  Map $com.healthyproducts.app.ui.viewmodel  MutableStateFlow $com.healthyproducts.app.ui.viewmodel  OcrScanState $com.healthyproducts.app.ui.viewmodel  PreservativeState $com.healthyproducts.app.ui.viewmodel  PreservativesState $com.healthyproducts.app.ui.viewmodel  ScanHistoryState $com.healthyproducts.app.ui.viewmodel  ScanHistoryViewModel $com.healthyproducts.app.ui.viewmodel  	ScanState $com.healthyproducts.app.ui.viewmodel  
ScanViewModel $com.healthyproducts.app.ui.viewmodel  SharingStarted $com.healthyproducts.app.ui.viewmodel  String $com.healthyproducts.app.ui.viewmodel  SugarsState $com.healthyproducts.app.ui.viewmodel  Unit $com.healthyproducts.app.ui.viewmodel  UserFoodPreferencesState $com.healthyproducts.app.ui.viewmodel  UserPreferences $com.healthyproducts.app.ui.viewmodel  	UserState $com.healthyproducts.app.ui.viewmodel  
UserViewModel $com.healthyproducts.app.ui.viewmodel  asStateFlow $com.healthyproducts.app.ui.viewmodel  com $com.healthyproducts.app.ui.viewmodel  	emptyList $com.healthyproducts.app.ui.viewmodel  emptyMap $com.healthyproducts.app.ui.viewmodel  map $com.healthyproducts.app.ui.viewmodel  stateIn $com.healthyproducts.app.ui.viewmodel  toList $com.healthyproducts.app.ui.viewmodel  viewModelScope $com.healthyproducts.app.ui.viewmodel  Additive 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  AdditiveRepository 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  ApplicationContext 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  Boolean 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  Context 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  ImportStatus 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  Inject 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  Int 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  List 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  MutableStateFlow 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  	StateFlow 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  String 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  
_additives 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  _error 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  _filterCategory 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  
_importStatus 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  
_isLoading 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  _searchQuery 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  _selectedAdditive 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  asStateFlow 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  	emptyList 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  getASStateFlow 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  getAsStateFlow 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  getEMPTYList 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  getEmptyList 6com.healthyproducts.app.ui.viewmodel.AdditiveViewModel  Idle Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus  ImportStatus Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus  Int Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus  String Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus  String Icom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.Error  Int Kcom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.Success  Allergen 3com.healthyproducts.app.ui.viewmodel.AllergensState  AllergensState 3com.healthyproducts.app.ui.viewmodel.AllergensState  List 3com.healthyproducts.app.ui.viewmodel.AllergensState  Loading 3com.healthyproducts.app.ui.viewmodel.AllergensState  String 3com.healthyproducts.app.ui.viewmodel.AllergensState  String 9com.healthyproducts.app.ui.viewmodel.AllergensState.Error  Allergen ;com.healthyproducts.app.ui.viewmodel.AllergensState.Success  List ;com.healthyproducts.app.ui.viewmodel.AllergensState.Success  
AnalysisState 2com.healthyproducts.app.ui.viewmodel.AnalysisState  Initial 2com.healthyproducts.app.ui.viewmodel.AnalysisState  String 2com.healthyproducts.app.ui.viewmodel.AnalysisState  String 8com.healthyproducts.app.ui.viewmodel.AnalysisState.Error  String :com.healthyproducts.app.ui.viewmodel.AnalysisState.Success  CertificatesState 6com.healthyproducts.app.ui.viewmodel.CertificatesState  FoodCertificate 6com.healthyproducts.app.ui.viewmodel.CertificatesState  List 6com.healthyproducts.app.ui.viewmodel.CertificatesState  Loading 6com.healthyproducts.app.ui.viewmodel.CertificatesState  String 6com.healthyproducts.app.ui.viewmodel.CertificatesState  String <com.healthyproducts.app.ui.viewmodel.CertificatesState.Error  FoodCertificate >com.healthyproducts.app.ui.viewmodel.CertificatesState.Success  List >com.healthyproducts.app.ui.viewmodel.CertificatesState.Success  Fat .com.healthyproducts.app.ui.viewmodel.FatsState  	FatsState .com.healthyproducts.app.ui.viewmodel.FatsState  List .com.healthyproducts.app.ui.viewmodel.FatsState  Loading .com.healthyproducts.app.ui.viewmodel.FatsState  String .com.healthyproducts.app.ui.viewmodel.FatsState  String 4com.healthyproducts.app.ui.viewmodel.FatsState.Error  Fat 6com.healthyproducts.app.ui.viewmodel.FatsState.Success  List 6com.healthyproducts.app.ui.viewmodel.FatsState.Success  Boolean 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  FavoritesRepository 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  FavoritesState 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  List 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  MutableStateFlow 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  Product 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  	StateFlow 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  String 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  Unit 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  _favoritesState 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  asStateFlow 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  getASStateFlow 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  getAsStateFlow 7com.healthyproducts.app.ui.viewmodel.FavoritesViewModel  FavoritesState Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState  List Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState  Loading Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState  Product Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState  String Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState  String Lcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Error  List Ncom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Success  Product Ncom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Success  	AiService :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  Allergen :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  AllergenRepository :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  AllergensState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  
AnalysisState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  Boolean :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  CertificatesState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  Context :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  Fat :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  
FatRepository :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  	FatsState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  FoodAnalysisRepository :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  FoodAnalysisService :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  FoodCertificate :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  FoodCertificateRepository :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  FoodPreferenceType :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  ImportStatus :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  Inject :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  List :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  Map :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  MutableStateFlow :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  PreservativeRepository :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  PreservativeState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  PreservativesState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  	StateFlow :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  String :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  Sugar :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  SugarRepository :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  SugarsState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  SupportedLanguage :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  UserFoodPreference :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  UserFoodPreferenceRepository :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  UserFoodPreferencesState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _allergensState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _analysisResult :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _certificatesState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  
_fatsState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  
_importStatus :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _preservativeState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _preservativesState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _selectedAllergen :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _selectedCertificate :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _selectedFat :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _selectedFoodPreferences :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _selectedSugar :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _sugarsState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  _userFoodPreferencesState :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  emptyMap :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  getEMPTYMap :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  getEmptyMap :com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel  ImportStatus 1com.healthyproducts.app.ui.viewmodel.ImportStatus  Initial 1com.healthyproducts.app.ui.viewmodel.ImportStatus  Int 1com.healthyproducts.app.ui.viewmodel.ImportStatus  String 1com.healthyproducts.app.ui.viewmodel.ImportStatus  String 7com.healthyproducts.app.ui.viewmodel.ImportStatus.Error  Int 9com.healthyproducts.app.ui.viewmodel.ImportStatus.Success  Activity 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  Application 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  FirestoreRepository 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  Inject 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  LanguagePreferences 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  LocaleHelper 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  SharingStarted 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  	StateFlow 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  getMAP 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  getMap 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  
getSTATEIn 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  
getStateIn 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  	getTOList 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  	getToList 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  getVIEWModelScope 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  getViewModelScope 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  invoke 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  languagePreferences 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  map 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  stateIn 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  toList 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  viewModelScope 6com.healthyproducts.app.ui.viewmodel.LanguageViewModel  Initial 6com.healthyproducts.app.ui.viewmodel.PreservativeState  Preservative 6com.healthyproducts.app.ui.viewmodel.PreservativeState  PreservativeState 6com.healthyproducts.app.ui.viewmodel.PreservativeState  String 6com.healthyproducts.app.ui.viewmodel.PreservativeState  String <com.healthyproducts.app.ui.viewmodel.PreservativeState.Error  Preservative >com.healthyproducts.app.ui.viewmodel.PreservativeState.Success  List 7com.healthyproducts.app.ui.viewmodel.PreservativesState  Loading 7com.healthyproducts.app.ui.viewmodel.PreservativesState  Preservative 7com.healthyproducts.app.ui.viewmodel.PreservativesState  PreservativesState 7com.healthyproducts.app.ui.viewmodel.PreservativesState  String 7com.healthyproducts.app.ui.viewmodel.PreservativesState  String =com.healthyproducts.app.ui.viewmodel.PreservativesState.Error  List ?com.healthyproducts.app.ui.viewmodel.PreservativesState.Success  Preservative ?com.healthyproducts.app.ui.viewmodel.PreservativesState.Success  Inject 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  Int 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  List 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  MutableStateFlow 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  ScanHistoryRepository 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  ScanHistoryState 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  ScanType 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  	StateFlow 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  String 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  _scanHistoryState 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  asStateFlow 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  getASStateFlow 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  getAsStateFlow 9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel  List Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState  Loading Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState  ScanHistoryRepository Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState  ScanHistoryState Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState  String Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState  String Pcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Error  List Rcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Success  ScanHistoryRepository Rcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Success  Application 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  Barcode 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  Bitmap 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  Boolean 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  CameraPermissionState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  Context 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  CorrectionState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  FirestoreRepository 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  	FrameSize 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  Inject 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  List 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  MutableStateFlow 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  OcrCorrectionRepository 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  OcrScanState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  ScanHistoryRepository 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  	ScanState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  	StateFlow 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  String 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  SupportedLanguage 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  Unit 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  _cameraPermissionState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  _correctionMode 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  _correctionState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  
_frameSize 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  _ingredientsForAnalysis 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  
_ocrScanState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  _rawOcrText 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  _recognizedIngredients 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  
_scanState 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  asStateFlow 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  com 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  	emptyList 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  firestoreRepository 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  getASStateFlow 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  getAsStateFlow 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  getEMPTYList 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  getEmptyList 2com.healthyproducts.app.ui.viewmodel.ScanViewModel  CorrectionState Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState  Idle Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState  String Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState  String Hcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.Error  Idle ?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState  OcrScanState ?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState  String ?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState  String Ecom.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.Error  String Gcom.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.Success  Idle <com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState  	ScanState <com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState  String <com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState  String Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.Error  String Dcom.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.Success  List 0com.healthyproducts.app.ui.viewmodel.SugarsState  Loading 0com.healthyproducts.app.ui.viewmodel.SugarsState  String 0com.healthyproducts.app.ui.viewmodel.SugarsState  Sugar 0com.healthyproducts.app.ui.viewmodel.SugarsState  SugarsState 0com.healthyproducts.app.ui.viewmodel.SugarsState  String 6com.healthyproducts.app.ui.viewmodel.SugarsState.Error  List 8com.healthyproducts.app.ui.viewmodel.SugarsState.Success  Sugar 8com.healthyproducts.app.ui.viewmodel.SugarsState.Success  Initial =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState  List =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState  String =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState  UserFoodPreference =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState  UserFoodPreferencesState =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState  String Ccom.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState.Error  List Ecom.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState.Success  UserFoodPreference Ecom.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState.Success  Inject 2com.healthyproducts.app.ui.viewmodel.UserViewModel  MutableStateFlow 2com.healthyproducts.app.ui.viewmodel.UserViewModel  	StateFlow 2com.healthyproducts.app.ui.viewmodel.UserViewModel  String 2com.healthyproducts.app.ui.viewmodel.UserViewModel  User 2com.healthyproducts.app.ui.viewmodel.UserViewModel  UserPreferences 2com.healthyproducts.app.ui.viewmodel.UserViewModel  UserRepository 2com.healthyproducts.app.ui.viewmodel.UserViewModel  	UserState 2com.healthyproducts.app.ui.viewmodel.UserViewModel  _userPreferences 2com.healthyproducts.app.ui.viewmodel.UserViewModel  
_userState 2com.healthyproducts.app.ui.viewmodel.UserViewModel  asStateFlow 2com.healthyproducts.app.ui.viewmodel.UserViewModel  getASStateFlow 2com.healthyproducts.app.ui.viewmodel.UserViewModel  getAsStateFlow 2com.healthyproducts.app.ui.viewmodel.UserViewModel  Loading <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState  String <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState  User <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState  	UserState <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState  String Bcom.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.Error  User Ecom.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.LoggedIn  BarcodeAnalyzer com.healthyproducts.app.util  BarcodeScanning com.healthyproducts.app.util  CameraPermissionHelper com.healthyproducts.app.util  CameraPermissionState com.healthyproducts.app.util  DataUploader com.healthyproducts.app.util  FirebaseFirestore com.healthyproducts.app.util  Gson com.healthyproducts.app.util  List com.healthyproducts.app.util  LocaleHelper com.healthyproducts.app.util  Result com.healthyproducts.app.util  String com.healthyproducts.app.util  TextRecognition com.healthyproducts.app.util  TextRecognitionAnalyzer com.healthyproducts.app.util  TextRecognizerOptions com.healthyproducts.app.util  Unit com.healthyproducts.app.util  rememberCameraPermissionState com.healthyproducts.app.util  Barcode ,com.healthyproducts.app.util.BarcodeAnalyzer  BarcodeScanning ,com.healthyproducts.app.util.BarcodeAnalyzer  
ImageProxy ,com.healthyproducts.app.util.BarcodeAnalyzer  List ,com.healthyproducts.app.util.BarcodeAnalyzer  SuppressLint ,com.healthyproducts.app.util.BarcodeAnalyzer  Unit ,com.healthyproducts.app.util.BarcodeAnalyzer  CameraPermissionState 3com.healthyproducts.app.util.CameraPermissionHelper  Context 3com.healthyproducts.app.util.CameraPermissionHelper  Context )com.healthyproducts.app.util.DataUploader  FirebaseFirestore )com.healthyproducts.app.util.DataUploader  Gson )com.healthyproducts.app.util.DataUploader  Result )com.healthyproducts.app.util.DataUploader  String )com.healthyproducts.app.util.DataUploader  Unit )com.healthyproducts.app.util.DataUploader  
Configuration )com.healthyproducts.app.util.LocaleHelper  Context )com.healthyproducts.app.util.LocaleHelper  Language )com.healthyproducts.app.util.LocaleHelper  String )com.healthyproducts.app.util.LocaleHelper  SupportedLanguage )com.healthyproducts.app.util.LocaleHelper  getCurrentLocale )com.healthyproducts.app.util.LocaleHelper  getSystemLocale )com.healthyproducts.app.util.LocaleHelper  Language 2com.healthyproducts.app.util.LocaleHelper.Language  String 2com.healthyproducts.app.util.LocaleHelper.Language  code 2com.healthyproducts.app.util.LocaleHelper.Language  fromCode 2com.healthyproducts.app.util.LocaleHelper.Language  values 2com.healthyproducts.app.util.LocaleHelper.Language  Language <com.healthyproducts.app.util.LocaleHelper.Language.Companion  String <com.healthyproducts.app.util.LocaleHelper.Language.Companion  fromCode <com.healthyproducts.app.util.LocaleHelper.Language.Companion  values <com.healthyproducts.app.util.LocaleHelper.Language.Companion  
ImageProxy 4com.healthyproducts.app.util.TextRecognitionAnalyzer  SuppressLint 4com.healthyproducts.app.util.TextRecognitionAnalyzer  Text 4com.healthyproducts.app.util.TextRecognitionAnalyzer  TextRecognition 4com.healthyproducts.app.util.TextRecognitionAnalyzer  TextRecognizerOptions 4com.healthyproducts.app.util.TextRecognitionAnalyzer  Unit 4com.healthyproducts.app.util.TextRecognitionAnalyzer  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  ByteArrayOutputStream java.io  File java.io  IOException java.io  AllergensState 	java.lang  
AnalysisState 	java.lang  BarcodeScanning 	java.lang  CertificatesState 	java.lang  CoroutineScope 	java.lang  CorrectionState 	java.lang  Dispatchers 	java.lang  ExperimentalLayoutApi 	java.lang  ExperimentalMaterial3Api 	java.lang  	FatsState 	java.lang  FavoritesState 	java.lang  FirebaseFirestore 	java.lang  	FrameSize 	java.lang  Gson 	java.lang  ImportStatus 	java.lang  
LANGUAGE_CODE 	java.lang  LanguagePreferences 	java.lang  LocaleHelper 	java.lang  MutableStateFlow 	java.lang  OcrScanState 	java.lang  PreservativeState 	java.lang  PreservativesState 	java.lang  R 	java.lang  ScanHistoryState 	java.lang  	ScanState 	java.lang  Screen 	java.lang  SharingStarted 	java.lang  SingletonComponent 	java.lang  SugarsState 	java.lang  
SupervisorJob 	java.lang  SupportedLanguage 	java.lang  TextRecognition 	java.lang  TextRecognizerOptions 	java.lang  UserFoodPreferencesState 	java.lang  UserPreferences 	java.lang  	UserState 	java.lang  android 	java.lang  androidx 	java.lang  any 	java.lang  asStateFlow 	java.lang  com 	java.lang  contains 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  filter 	java.lang  listOf 	java.lang  map 	java.lang  mutableMapOf 	java.lang  preferencesDataStore 	java.lang  provideDelegate 	java.lang  stateIn 	java.lang  stringPreferencesKey 	java.lang  toList 	java.lang  Date 	java.util  Locale 	java.util  UUID 	java.util  Executor java.util.concurrent  ExecutorService java.util.concurrent  	Executors java.util.concurrent  TimeUnit java.util.concurrent  Inject javax.inject  	Singleton javax.inject  AllergensState kotlin  
AnalysisState kotlin  BarcodeScanning kotlin  Boolean kotlin  	ByteArray kotlin  CertificatesState kotlin  CoroutineScope kotlin  CorrectionState kotlin  Dispatchers kotlin  Double kotlin  ExperimentalLayoutApi kotlin  ExperimentalMaterial3Api kotlin  	FatsState kotlin  FavoritesState kotlin  FirebaseFirestore kotlin  Float kotlin  	FrameSize kotlin  	Function0 kotlin  	Function1 kotlin  Gson kotlin  ImportStatus kotlin  Int kotlin  
LANGUAGE_CODE kotlin  LanguagePreferences kotlin  LocaleHelper kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OcrScanState kotlin  OptIn kotlin  PreservativeState kotlin  PreservativesState kotlin  R kotlin  Result kotlin  ScanHistoryState kotlin  	ScanState kotlin  Screen kotlin  SharingStarted kotlin  SingletonComponent kotlin  String kotlin  SugarsState kotlin  
SupervisorJob kotlin  SupportedLanguage kotlin  TextRecognition kotlin  TextRecognizerOptions kotlin  Unit kotlin  UserFoodPreferencesState kotlin  UserPreferences kotlin  	UserState kotlin  android kotlin  androidx kotlin  any kotlin  asStateFlow kotlin  com kotlin  contains kotlin  	emptyList kotlin  emptyMap kotlin  filter kotlin  listOf kotlin  map kotlin  mutableMapOf kotlin  preferencesDataStore kotlin  provideDelegate kotlin  stateIn kotlin  stringPreferencesKey kotlin  toList kotlin  	getTOList kotlin.Array  	getToList kotlin.Array  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  getCONTAINS 
kotlin.String  getContains 
kotlin.String  AllergensState kotlin.annotation  
AnalysisState kotlin.annotation  BarcodeScanning kotlin.annotation  CertificatesState kotlin.annotation  CoroutineScope kotlin.annotation  CorrectionState kotlin.annotation  Dispatchers kotlin.annotation  ExperimentalLayoutApi kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  	FatsState kotlin.annotation  FavoritesState kotlin.annotation  FirebaseFirestore kotlin.annotation  	FrameSize kotlin.annotation  Gson kotlin.annotation  ImportStatus kotlin.annotation  
LANGUAGE_CODE kotlin.annotation  LanguagePreferences kotlin.annotation  LocaleHelper kotlin.annotation  MutableStateFlow kotlin.annotation  OcrScanState kotlin.annotation  PreservativeState kotlin.annotation  PreservativesState kotlin.annotation  R kotlin.annotation  Result kotlin.annotation  ScanHistoryState kotlin.annotation  	ScanState kotlin.annotation  Screen kotlin.annotation  SharingStarted kotlin.annotation  SingletonComponent kotlin.annotation  SugarsState kotlin.annotation  
SupervisorJob kotlin.annotation  SupportedLanguage kotlin.annotation  TextRecognition kotlin.annotation  TextRecognizerOptions kotlin.annotation  UserFoodPreferencesState kotlin.annotation  UserPreferences kotlin.annotation  	UserState kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  any kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  filter kotlin.annotation  listOf kotlin.annotation  map kotlin.annotation  mutableMapOf kotlin.annotation  preferencesDataStore kotlin.annotation  provideDelegate kotlin.annotation  stateIn kotlin.annotation  stringPreferencesKey kotlin.annotation  toList kotlin.annotation  AllergensState kotlin.collections  
AnalysisState kotlin.collections  BarcodeScanning kotlin.collections  CertificatesState kotlin.collections  CoroutineScope kotlin.collections  CorrectionState kotlin.collections  Dispatchers kotlin.collections  ExperimentalLayoutApi kotlin.collections  ExperimentalMaterial3Api kotlin.collections  	FatsState kotlin.collections  FavoritesState kotlin.collections  FirebaseFirestore kotlin.collections  	FrameSize kotlin.collections  Gson kotlin.collections  ImportStatus kotlin.collections  
LANGUAGE_CODE kotlin.collections  LanguagePreferences kotlin.collections  List kotlin.collections  LocaleHelper kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  OcrScanState kotlin.collections  PreservativeState kotlin.collections  PreservativesState kotlin.collections  R kotlin.collections  Result kotlin.collections  ScanHistoryState kotlin.collections  	ScanState kotlin.collections  Screen kotlin.collections  SharingStarted kotlin.collections  SingletonComponent kotlin.collections  SugarsState kotlin.collections  
SupervisorJob kotlin.collections  SupportedLanguage kotlin.collections  TextRecognition kotlin.collections  TextRecognizerOptions kotlin.collections  UserFoodPreferencesState kotlin.collections  UserPreferences kotlin.collections  	UserState kotlin.collections  android kotlin.collections  androidx kotlin.collections  any kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  listOf kotlin.collections  map kotlin.collections  mutableMapOf kotlin.collections  preferencesDataStore kotlin.collections  provideDelegate kotlin.collections  stateIn kotlin.collections  stringPreferencesKey kotlin.collections  toList kotlin.collections  getANY kotlin.collections.List  getAny kotlin.collections.List  	getFILTER kotlin.collections.List  	getFilter kotlin.collections.List  AllergensState kotlin.comparisons  
AnalysisState kotlin.comparisons  BarcodeScanning kotlin.comparisons  CertificatesState kotlin.comparisons  CoroutineScope kotlin.comparisons  CorrectionState kotlin.comparisons  Dispatchers kotlin.comparisons  ExperimentalLayoutApi kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  	FatsState kotlin.comparisons  FavoritesState kotlin.comparisons  FirebaseFirestore kotlin.comparisons  	FrameSize kotlin.comparisons  Gson kotlin.comparisons  ImportStatus kotlin.comparisons  
LANGUAGE_CODE kotlin.comparisons  LanguagePreferences kotlin.comparisons  LocaleHelper kotlin.comparisons  MutableStateFlow kotlin.comparisons  OcrScanState kotlin.comparisons  PreservativeState kotlin.comparisons  PreservativesState kotlin.comparisons  R kotlin.comparisons  Result kotlin.comparisons  ScanHistoryState kotlin.comparisons  	ScanState kotlin.comparisons  Screen kotlin.comparisons  SharingStarted kotlin.comparisons  SingletonComponent kotlin.comparisons  SugarsState kotlin.comparisons  
SupervisorJob kotlin.comparisons  SupportedLanguage kotlin.comparisons  TextRecognition kotlin.comparisons  TextRecognizerOptions kotlin.comparisons  UserFoodPreferencesState kotlin.comparisons  UserPreferences kotlin.comparisons  	UserState kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  any kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  filter kotlin.comparisons  listOf kotlin.comparisons  map kotlin.comparisons  mutableMapOf kotlin.comparisons  preferencesDataStore kotlin.comparisons  provideDelegate kotlin.comparisons  stateIn kotlin.comparisons  stringPreferencesKey kotlin.comparisons  toList kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  resume kotlin.coroutines  suspendCoroutine kotlin.coroutines  AllergensState 	kotlin.io  
AnalysisState 	kotlin.io  BarcodeScanning 	kotlin.io  CertificatesState 	kotlin.io  CoroutineScope 	kotlin.io  CorrectionState 	kotlin.io  Dispatchers 	kotlin.io  ExperimentalLayoutApi 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  	FatsState 	kotlin.io  FavoritesState 	kotlin.io  FirebaseFirestore 	kotlin.io  	FrameSize 	kotlin.io  Gson 	kotlin.io  ImportStatus 	kotlin.io  
LANGUAGE_CODE 	kotlin.io  LanguagePreferences 	kotlin.io  LocaleHelper 	kotlin.io  MutableStateFlow 	kotlin.io  OcrScanState 	kotlin.io  PreservativeState 	kotlin.io  PreservativesState 	kotlin.io  R 	kotlin.io  Result 	kotlin.io  ScanHistoryState 	kotlin.io  	ScanState 	kotlin.io  Screen 	kotlin.io  SharingStarted 	kotlin.io  SingletonComponent 	kotlin.io  SugarsState 	kotlin.io  
SupervisorJob 	kotlin.io  SupportedLanguage 	kotlin.io  TextRecognition 	kotlin.io  TextRecognizerOptions 	kotlin.io  UserFoodPreferencesState 	kotlin.io  UserPreferences 	kotlin.io  	UserState 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  any 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  filter 	kotlin.io  listOf 	kotlin.io  map 	kotlin.io  mutableMapOf 	kotlin.io  preferencesDataStore 	kotlin.io  provideDelegate 	kotlin.io  stateIn 	kotlin.io  stringPreferencesKey 	kotlin.io  toList 	kotlin.io  AllergensState 
kotlin.jvm  
AnalysisState 
kotlin.jvm  BarcodeScanning 
kotlin.jvm  CertificatesState 
kotlin.jvm  CoroutineScope 
kotlin.jvm  CorrectionState 
kotlin.jvm  Dispatchers 
kotlin.jvm  ExperimentalLayoutApi 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  	FatsState 
kotlin.jvm  FavoritesState 
kotlin.jvm  FirebaseFirestore 
kotlin.jvm  	FrameSize 
kotlin.jvm  Gson 
kotlin.jvm  ImportStatus 
kotlin.jvm  
LANGUAGE_CODE 
kotlin.jvm  LanguagePreferences 
kotlin.jvm  LocaleHelper 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OcrScanState 
kotlin.jvm  PreservativeState 
kotlin.jvm  PreservativesState 
kotlin.jvm  R 
kotlin.jvm  Result 
kotlin.jvm  ScanHistoryState 
kotlin.jvm  	ScanState 
kotlin.jvm  Screen 
kotlin.jvm  SharingStarted 
kotlin.jvm  SingletonComponent 
kotlin.jvm  SugarsState 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  SupportedLanguage 
kotlin.jvm  TextRecognition 
kotlin.jvm  TextRecognizerOptions 
kotlin.jvm  UserFoodPreferencesState 
kotlin.jvm  UserPreferences 
kotlin.jvm  	UserState 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  any 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  filter 
kotlin.jvm  listOf 
kotlin.jvm  map 
kotlin.jvm  mutableMapOf 
kotlin.jvm  preferencesDataStore 
kotlin.jvm  provideDelegate 
kotlin.jvm  stateIn 
kotlin.jvm  stringPreferencesKey 
kotlin.jvm  toList 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getPROVIDEDelegate "kotlin.properties.ReadOnlyProperty  getProvideDelegate "kotlin.properties.ReadOnlyProperty  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  AllergensState 
kotlin.ranges  
AnalysisState 
kotlin.ranges  BarcodeScanning 
kotlin.ranges  CertificatesState 
kotlin.ranges  CoroutineScope 
kotlin.ranges  CorrectionState 
kotlin.ranges  Dispatchers 
kotlin.ranges  ExperimentalLayoutApi 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  	FatsState 
kotlin.ranges  FavoritesState 
kotlin.ranges  FirebaseFirestore 
kotlin.ranges  	FrameSize 
kotlin.ranges  Gson 
kotlin.ranges  ImportStatus 
kotlin.ranges  
LANGUAGE_CODE 
kotlin.ranges  LanguagePreferences 
kotlin.ranges  LocaleHelper 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OcrScanState 
kotlin.ranges  PreservativeState 
kotlin.ranges  PreservativesState 
kotlin.ranges  R 
kotlin.ranges  Result 
kotlin.ranges  ScanHistoryState 
kotlin.ranges  	ScanState 
kotlin.ranges  Screen 
kotlin.ranges  SharingStarted 
kotlin.ranges  SingletonComponent 
kotlin.ranges  SugarsState 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  SupportedLanguage 
kotlin.ranges  TextRecognition 
kotlin.ranges  TextRecognizerOptions 
kotlin.ranges  UserFoodPreferencesState 
kotlin.ranges  UserPreferences 
kotlin.ranges  	UserState 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  any 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  filter 
kotlin.ranges  listOf 
kotlin.ranges  map 
kotlin.ranges  mutableMapOf 
kotlin.ranges  preferencesDataStore 
kotlin.ranges  provideDelegate 
kotlin.ranges  stateIn 
kotlin.ranges  stringPreferencesKey 
kotlin.ranges  toList 
kotlin.ranges  KClass kotlin.reflect  AllergensState kotlin.sequences  
AnalysisState kotlin.sequences  BarcodeScanning kotlin.sequences  CertificatesState kotlin.sequences  CoroutineScope kotlin.sequences  CorrectionState kotlin.sequences  Dispatchers kotlin.sequences  ExperimentalLayoutApi kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  	FatsState kotlin.sequences  FavoritesState kotlin.sequences  FirebaseFirestore kotlin.sequences  	FrameSize kotlin.sequences  Gson kotlin.sequences  ImportStatus kotlin.sequences  
LANGUAGE_CODE kotlin.sequences  LanguagePreferences kotlin.sequences  LocaleHelper kotlin.sequences  MutableStateFlow kotlin.sequences  OcrScanState kotlin.sequences  PreservativeState kotlin.sequences  PreservativesState kotlin.sequences  R kotlin.sequences  Result kotlin.sequences  ScanHistoryState kotlin.sequences  	ScanState kotlin.sequences  Screen kotlin.sequences  SharingStarted kotlin.sequences  SingletonComponent kotlin.sequences  SugarsState kotlin.sequences  
SupervisorJob kotlin.sequences  SupportedLanguage kotlin.sequences  TextRecognition kotlin.sequences  TextRecognizerOptions kotlin.sequences  UserFoodPreferencesState kotlin.sequences  UserPreferences kotlin.sequences  	UserState kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  any kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  filter kotlin.sequences  listOf kotlin.sequences  map kotlin.sequences  mutableMapOf kotlin.sequences  preferencesDataStore kotlin.sequences  provideDelegate kotlin.sequences  stateIn kotlin.sequences  stringPreferencesKey kotlin.sequences  toList kotlin.sequences  AllergensState kotlin.text  
AnalysisState kotlin.text  BarcodeScanning kotlin.text  CertificatesState kotlin.text  CoroutineScope kotlin.text  CorrectionState kotlin.text  Dispatchers kotlin.text  ExperimentalLayoutApi kotlin.text  ExperimentalMaterial3Api kotlin.text  	FatsState kotlin.text  FavoritesState kotlin.text  FirebaseFirestore kotlin.text  	FrameSize kotlin.text  Gson kotlin.text  ImportStatus kotlin.text  
LANGUAGE_CODE kotlin.text  LanguagePreferences kotlin.text  LocaleHelper kotlin.text  MutableStateFlow kotlin.text  OcrScanState kotlin.text  PreservativeState kotlin.text  PreservativesState kotlin.text  R kotlin.text  Result kotlin.text  ScanHistoryState kotlin.text  	ScanState kotlin.text  Screen kotlin.text  SharingStarted kotlin.text  SingletonComponent kotlin.text  SugarsState kotlin.text  
SupervisorJob kotlin.text  SupportedLanguage kotlin.text  TextRecognition kotlin.text  TextRecognizerOptions kotlin.text  UserFoodPreferencesState kotlin.text  UserPreferences kotlin.text  	UserState kotlin.text  android kotlin.text  androidx kotlin.text  any kotlin.text  asStateFlow kotlin.text  com kotlin.text  contains kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  filter kotlin.text  listOf kotlin.text  map kotlin.text  mutableMapOf kotlin.text  preferencesDataStore kotlin.text  provideDelegate kotlin.text  stateIn kotlin.text  stringPreferencesKey kotlin.text  toList kotlin.text  CompletableJob kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  GlobalScope kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  	MainScope kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  plus !kotlinx.coroutines.CompletableJob  Main kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  first kotlinx.coroutines.flow  map kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  getMAP kotlinx.coroutines.flow.Flow  getMap kotlinx.coroutines.flow.Flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  await kotlinx.coroutines.tasks  
SerialName kotlinx.serialization  Serializable kotlinx.serialization  Interceptor okhttp3  OkHttpClient okhttp3  HttpLoggingInterceptor okhttp3.logging  	JSONArray org.json  
JSONObject org.json  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  Body retrofit2.http  Header retrofit2.http  POST retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              