package com.healthyproducts.app.data.repository

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.GsonBuilder
import com.healthyproducts.app.data.model.Preservative
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import org.json.JSONArray
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Koruyucu ve antibakteriyel kalıntılar işlemlerini yöneten repository sınıfı
 */
@Singleton
class PreservativeRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val context: Context
) {
    companion object {
        private const val TAG = "PreservativeRepository"
        private const val COLLECTION_NAME = "preservatives"
        private const val JSON_FILE = "preservativeList.json"
    }

    private val _preservatives = MutableStateFlow<List<Preservative>>(emptyList())
    val preservatives: StateFlow<List<Preservative>> = _preservatives.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        // Uygulama başladığında koruyucuları yükle
        GlobalScope.launch(Dispatchers.IO) {
            try {
                getAllPreservatives()
                Log.d(TAG, "Initial preservatives loaded: ${preservatives.value.size}")
            } catch (e: Exception) {
                Log.e(TAG, "Error loading initial preservatives", e)
            }
        }
    }

    /**
     * Tüm koruyucuları getirir
     */
    suspend fun getAllPreservatives(): Result<List<Preservative>> = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null

            Log.d(TAG, "Getting all preservatives")

            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val preservativesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Preservative::class.java)
            }

            Log.d(TAG, "Retrieved ${preservativesList.size} preservatives")

            _preservatives.value = preservativesList
            _isLoading.value = false

            Result.success(preservativesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting preservatives", e)
            _error.value = e.message
            _isLoading.value = false
            Result.failure(e)
        }
    }

    /**
     * Koruyucu ekler
     */
    suspend fun addPreservative(preservative: Preservative): Result<Preservative> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding preservative: ${preservative.preservativeId} - ${preservative.nameTr}")

            // Koruyucuyu Firestore'a ekle
            val documentRef = firestore.collection(COLLECTION_NAME).document(preservative.preservativeId)
            documentRef.set(preservative).await()

            // Koruyucuyu yerel listeye ekle
            val currentList = _preservatives.value.toMutableList()
            currentList.add(preservative)
            _preservatives.value = currentList

            Log.d(TAG, "Preservative added successfully")

            Result.success(preservative)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding preservative", e)
            Result.failure(e)
        }
    }

    /**
     * Koruyucuyu günceller
     */
    suspend fun updatePreservative(preservative: Preservative): Result<Preservative> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating preservative: ${preservative.preservativeId} - ${preservative.nameTr}")

            // Koruyucuyu Firestore'da güncelle
            val documentRef = firestore.collection(COLLECTION_NAME).document(preservative.preservativeId)
            documentRef.set(preservative).await()

            // Koruyucuyu yerel listede güncelle
            val currentList = _preservatives.value.toMutableList()
            val index = currentList.indexOfFirst { it.preservativeId == preservative.preservativeId }
            if (index != -1) {
                currentList[index] = preservative
                _preservatives.value = currentList
            }

            Log.d(TAG, "Preservative updated successfully")

            Result.success(preservative)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating preservative", e)
            Result.failure(e)
        }
    }

    /**
     * Koruyucuyu siler
     */
    suspend fun deletePreservative(preservativeId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting preservative: $preservativeId")

            // Koruyucuyu Firestore'dan sil
            val documentRef = firestore.collection(COLLECTION_NAME).document(preservativeId)
            documentRef.delete().await()

            // Koruyucuyu yerel listeden sil
            val currentList = _preservatives.value.toMutableList()
            val index = currentList.indexOfFirst { it.preservativeId == preservativeId }
            if (index != -1) {
                currentList.removeAt(index)
                _preservatives.value = currentList
            }

            Log.d(TAG, "Preservative deleted successfully")

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting preservative", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dosyasından koruyucuları içe aktarır
     */
    suspend fun importPreservativesFromJson(): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Importing preservatives from JSON")

            // JSON dosyasını oku
            val jsonString = context.assets.open(JSON_FILE).bufferedReader().use { it.readText() }

            val gson = GsonBuilder()
                .setLenient()
                .create()

            val jsonArray = JSONArray(jsonString)
            val preservatives = mutableListOf<Preservative>()

            try {
                // JSON içeriğini logla
                Log.d(TAG, "JSON array length: ${jsonArray.length()}")

                // İlk birkaç JSON nesnesini logla
                for (i in 0 until minOf(3, jsonArray.length())) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    Log.d(TAG, "JSON object $i: ${jsonObject.toString().take(200)}...")
                }

                // Tüm JSON nesnelerini işle
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)

                    // Dizileri işle
                    val functionalType = parseJsonArray(jsonObject.optJSONArray("functional_type"))
                    val commonProductsTr = parseJsonArray(jsonObject.optJSONArray("common_products_tr"))
                    val commonProductsEn = parseJsonArray(jsonObject.optJSONArray("common_products_en"))

                    // Preservative nesnesini oluştur
                    val preservative = Preservative(
                        id = jsonObject.optString("id", ""),
                        preservativeId = jsonObject.optString("id", ""),
                        nameTr = jsonObject.optString("name_tr", ""),
                        nameEn = jsonObject.optString("name_en", ""),
                        symbol = jsonObject.optString("symbol", ""),
                        descriptionTr = jsonObject.optString("description_tr", ""),
                        descriptionEn = jsonObject.optString("description_en", ""),
                        riskLevel = jsonObject.optInt("risk_level", 0),
                        functionalType = functionalType,
                        mainRiskTr = jsonObject.optString("main_risk_tr", ""),
                        mainRiskEn = jsonObject.optString("main_risk_en", ""),
                        commonProductsTr = commonProductsTr,
                        commonProductsEn = commonProductsEn,
                        notesTr = jsonObject.optString("notes_tr", ""),
                        notesEn = jsonObject.optString("notes_en", "")
                    )

                    preservatives.add(preservative)
                }

                // İlk birkaç koruyucunun içeriğini detaylı logla
                preservatives.take(3).forEach { preservative ->
                    Log.d(TAG, "Parsed preservative: ${preservative.id}")
                    Log.d(TAG, "  preservativeId: '${preservative.preservativeId}'")
                    Log.d(TAG, "  nameTr: '${preservative.nameTr}'")
                    Log.d(TAG, "  nameEn: '${preservative.nameEn}'")
                    Log.d(TAG, "  descriptionTr: '${preservative.descriptionTr}'")
                    Log.d(TAG, "  symbol: '${preservative.symbol}'")
                    Log.d(TAG, "  functionalType: ${preservative.functionalType}")
                    Log.d(TAG, "  riskLevel: ${preservative.riskLevel}")
                }

                Log.d(TAG, "Parsed ${preservatives.size} preservatives from JSON")

                // Firestore'a kaydet
                var successCount = 0
                preservatives.forEach { preservative ->
                    try {
                        val documentRef = firestore.collection(COLLECTION_NAME).document(preservative.preservativeId)

                        // Preservative nesnesini doğrudan kaydet
                        Log.d(TAG, "Saving preservative to Firestore: ${preservative.preservativeId}")
                        documentRef.set(preservative).await()

                        // Kaydedilen veriyi kontrol et
                        val savedDoc = documentRef.get().await()
                        Log.d(TAG, "  Saved document: ${savedDoc.data?.entries?.joinToString { "${it.key}='${it.value}'" }}")

                        successCount++
                    } catch (e: Exception) {
                        Log.e(TAG, "Error importing preservative ${preservative.preservativeId}", e)
                    }
                }

                Log.d(TAG, "Successfully imported $successCount out of ${preservatives.size} preservatives")

                // Yerel listeyi güncelle
                if (successCount > 0) {
                    getAllPreservatives()
                }

                Result.success(successCount)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing JSON", e)
                return@withContext Result.failure(e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error importing preservatives from JSON", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dizisini String listesine dönüştürür
     */
    private fun parseJsonArray(jsonArray: JSONArray?): List<String> {
        if (jsonArray == null) return emptyList()

        val result = mutableListOf<String>()
        for (i in 0 until jsonArray.length()) {
            result.add(jsonArray.optString(i, ""))
        }
        return result
    }

    /**
     * Koruyucuyu ID ile getirir
     */
    suspend fun getPreservativeById(preservativeId: String): Result<Preservative> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting preservative by ID: $preservativeId")

            // Önce yerel listede ara
            val localPreservative = _preservatives.value.find { it.preservativeId == preservativeId }
            if (localPreservative != null) {
                Log.d(TAG, "Preservative found in local cache")
                return@withContext Result.success(localPreservative)
            }

            // Yerel listede yoksa Firestore'dan getir
            val documentRef = firestore.collection(COLLECTION_NAME).document(preservativeId)
            val document = documentRef.get().await()

            val preservative = document.toObject(Preservative::class.java)

            if (preservative != null) {
                Log.d(TAG, "Preservative found in Firestore")
                return@withContext Result.success(preservative)
            } else {
                Log.d(TAG, "Preservative not found")
                return@withContext Result.failure(NoSuchElementException("Preservative not found with ID: $preservativeId"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting preservative by ID", e)
            return@withContext Result.failure(e)
        }
    }

    /**
     * Koruyucuları adına göre arar
     */
    suspend fun searchPreservativesByName(query: String): Result<List<Preservative>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching preservatives by name: $query")

            // Firestore'da tam metin araması yapılamadığı için tüm koruyucuları getirip filtreliyoruz
            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val preservativesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Preservative::class.java)
            }.filter { preservative ->
                preservative.nameTr.contains(query, ignoreCase = true) ||
                preservative.nameEn.contains(query, ignoreCase = true) ||
                preservative.preservativeId.contains(query, ignoreCase = true)
            }

            Log.d(TAG, "Found ${preservativesList.size} preservatives matching query")

            Result.success(preservativesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching preservatives", e)
            Result.failure(e)
        }
    }
}
