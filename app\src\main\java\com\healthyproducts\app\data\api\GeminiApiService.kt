package com.healthyproducts.app.data.api

import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.GenerateContentResponse
import com.healthyproducts.app.BuildConfig
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gemini API servisi
 */
@Singleton
class GeminiApiService @Inject constructor() {

    /**
     * Gemini API'sine istek gönderir ve metin üretir
     */
    suspend fun generateText(prompt: String, modelName: String = "gemini-1.5-flash"): GenerateContentResponse {
        android.util.Log.d("GeminiApiService", "Creating GenerativeModel with model: $modelName and API key: ${BuildConfig.GEMINI_API_KEY.take(5)}...")

        val model = GenerativeModel(
            modelName = modelName,
            apiKey = BuildConfig.GEMINI_API_KEY
        )

        android.util.Log.d("GeminiApiService", "Sending request to Gemini API")
        try {
            val response = model.generateContent(prompt)
            android.util.Log.d("GeminiApiService", "Received response from Gemini API: ${response.text}")
            return response
        } catch (e: Exception) {
            android.util.Log.e("GeminiApiService", "Error calling Gemini API", e)
            throw e
        }
    }
}
