{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,26350", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,26425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "363,364", "startColumns": "4,4", "startOffsets": "28859,28943", "endColumns": "83,86", "endOffsets": "28938,29025"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values-tr\\strings.xml", "from": {"startLines": "68,106,245,210,214,211,215,213,212,46,111,108,110,105,112,115,114,109,117,116,113,104,238,66,67,140,136,239,235,234,101,233,191,1,204,99,252,251,21,229,23,91,188,143,41,242,167,168,169,166,160,85,100,196,82,231,227,223,216,60,127,131,126,130,92,145,150,93,107,29,80,189,96,141,137,6,18,201,200,139,171,170,32,195,144,164,163,165,24,42,44,162,237,148,4,174,206,179,175,177,178,176,180,192,40,31,36,232,30,28,153,59,123,120,122,121,95,77,218,86,79,194,15,83,230,222,197,17,159,158,27,22,98,146,81,193,220,219,185,184,183,241,69,248,39,253,247,254,250,249,7,244,226,11,78,87,151,47,190,97,205,149,90,217,246,5,14,12,84,13,26,25,94,207,228,8,74,72,73,61,64,63,65,62,35,34,33,50,51,55,156,157,54,155,52,56,53,154,142,138,240,243,45,186,70,187,133,161,132,128,129,147,221,236,43,152,71,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3405,5074,12879,10653,10908,10719,10979,10837,10776,2304,5377,5198,5319,5009,5432,5622,5558,5259,5768,5693,5495,4955,12469,3295,3342,6893,6695,12531,12274,12211,4885,12132,9692,16,10375,4807,13337,13236,834,11804,927,4475,9534,7056,2007,12702,8426,8498,8565,8360,7997,4220,4845,9964,4066,12015,11685,11533,11037,2886,6160,6483,6094,6408,4516,7184,7429,4555,5134,1349,3981,9584,4685,6952,6744,183,753,10191,10129,6828,8702,8638,1592,9904,7120,8246,8186,8305,1002,2061,2169,8124,12394,7317,102,8787,10496,9122,8854,8974,9046,8912,9193,9743,1957,1520,1782,12075,1445,1280,7615,2845,6006,5841,5948,5896,4635,3846,11158,4278,3936,9857,561,4126,11950,11472,10023,692,7941,7900,1189,881,4770,7233,4023,9810,11323,11246,9396,9332,9278,12640,3448,13035,1898,13409,12985,13478,13158,13090,231,12813,11628,345,3890,4345,7490,2365,9648,4724,10437,7376,4435,11095,12931,145,510,400,4164,453,1106,1056,4596,10551,11744,274,3754,3616,3689,2937,3145,3075,3218,3006,1737,1692,1647,2464,2511,2716,7794,7842,2659,7727,2558,2767,2612,7670,7002,6784,12588,12764,2235,9446,3511,9493,6610,8058,6549,6281,6343,7274,11405,12332,2115,7551,3574,615", "endColumns": "42,59,51,65,70,56,57,70,60,60,54,60,57,64,62,70,63,59,49,74,62,53,61,46,62,58,48,56,57,62,45,78,50,53,61,37,71,100,46,145,74,40,49,63,53,61,71,66,72,65,60,57,39,58,59,59,58,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,74,58,42,66,54,70,57,71,75,61,56,66,49,71,86,56,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,37,64,60,70,60,55,40,90,45,36,40,42,46,81,76,49,63,53,61,62,54,58,68,49,84,77,67,42,65,56,54,45,68,60,68,43,45,58,52,39,62,53,37,50,52,55,56,82,49,38,65,59,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,51,48,68,46,62,40,56,65,60,61,64,42,66,61,53,63,41,76", "endOffsets": "3443,5129,12926,10714,10974,10771,11032,10903,10832,2360,5427,5254,5372,5069,5490,5688,5617,5314,5813,5763,5553,5004,12526,3337,3400,6947,6739,12583,12327,12269,4926,12206,9738,65,10432,4840,13404,13332,876,11945,997,4511,9579,7115,2056,12759,8493,8560,8633,8421,8053,4273,4880,10018,4121,12070,11739,11577,11090,2932,6276,6544,6155,6478,4550,7228,7485,4591,5193,1440,4018,9643,4719,6997,6779,226,803,10338,10186,6888,8761,8697,1642,9959,7179,8300,8241,8355,1051,2110,2230,8181,12464,7371,140,8849,10546,9188,8907,9041,9117,8969,9245,9805,2002,1587,1864,12127,1515,1344,7665,2881,6063,5891,6001,5943,4680,3885,11241,4340,3976,9899,610,4159,12010,11528,10089,748,7992,7936,1275,922,4802,7269,4061,9852,11400,11318,9441,9391,9327,12697,3506,13085,1952,13473,13030,13558,13231,13153,269,12874,11680,395,3931,4409,7546,2429,9687,4765,10491,7424,4470,11153,12980,178,556,448,4215,505,1184,1101,4630,10612,11799,314,3819,3684,3749,3001,3213,3140,3290,3070,1777,1732,1687,2506,2553,2762,7837,7895,2711,7789,2607,2817,2654,7722,7051,6823,12635,12808,2299,9488,3569,9529,6662,8119,6605,6338,6403,7312,11467,12389,2164,7610,3611,687"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,75,76,77,78,79,80,81,82,83,84,85,86,87,88,130,133,134,136,137,138,139,140,141,142,143,146,147,148,150,151,152,153,154,155,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,266,267,268,269,271,272,273,274,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,325,326,327,328,329,330,331,332,333,334,335,336,337,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,359,362,365,366,367,368,369,370,371,372,373,374,375,376,377", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,2840,2900,2952,3018,3089,3146,3204,3275,3336,3397,3452,3513,3571,3636,3699,3770,3834,3894,3944,4019,4082,4136,4198,4245,4308,4367,4416,4473,4531,4594,4640,4719,4770,4824,4886,4924,4996,5097,5857,6003,6078,6119,6169,6233,6287,6349,6421,6488,6561,6627,6688,6746,11595,11798,11858,12018,12077,12126,12184,12235,12356,12422,12488,12758,12797,12846,12991,13032,13096,13192,13234,13298,13656,13706,13746,13794,13849,14001,14063,14128,14192,14256,14311,14371,14435,14494,14554,14609,14663,14717,14783,14845,14920,14979,15022,15089,15144,15215,15273,15345,15421,15483,15723,15790,15840,15912,15999,16056,16131,16200,16255,16296,16358,16413,16471,16523,16573,16617,16705,16772,22977,23024,23078,23116,23269,23330,23401,23462,23602,23643,23734,23780,23817,23858,23901,23948,24030,24107,24157,24221,24275,24337,24400,24455,24514,24583,24633,24718,24796,24864,25075,25141,25198,25253,25299,25368,25429,25498,25542,25588,25647,25700,25740,25803,25857,25895,25946,25999,26055,26112,26195,26245,26284,26501,26561,26606,26676,26749,26814,26883,26956,27026,27103,27172,27217,27262,27629,27676,27723,27774,27822,27880,27937,28004,28058,28113,28160,28217,28271,28315,28367,28416,28563,28796,29030,29071,29128,29194,29255,29317,29382,29425,29492,29554,29608,29672,29714", "endColumns": "42,59,51,65,70,56,57,70,60,60,54,60,57,64,62,70,63,59,49,74,62,53,61,46,62,58,48,56,57,62,45,78,50,53,61,37,71,100,46,145,74,40,49,63,53,61,71,66,72,65,60,57,39,58,59,59,58,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,74,58,42,66,54,70,57,71,75,61,56,66,49,71,86,56,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,37,64,60,70,60,55,40,90,45,36,40,42,46,81,76,49,63,53,61,62,54,58,68,49,84,77,67,42,65,56,54,45,68,60,68,43,45,58,52,39,62,53,37,50,52,55,56,82,49,38,65,59,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,51,48,68,46,62,40,56,65,60,61,64,42,66,61,53,63,41,76", "endOffsets": "2835,2895,2947,3013,3084,3141,3199,3270,3331,3392,3447,3508,3566,3631,3694,3765,3829,3889,3939,4014,4077,4131,4193,4240,4303,4362,4411,4468,4526,4589,4635,4714,4765,4819,4881,4919,4991,5092,5139,5998,6073,6114,6164,6228,6282,6344,6416,6483,6556,6622,6683,6741,6781,11649,11853,11913,12072,12121,12179,12230,12351,12417,12483,12558,12792,12841,12902,13027,13091,13187,13229,13293,13332,13701,13741,13789,13844,13996,14058,14123,14187,14251,14306,14366,14430,14489,14549,14604,14658,14712,14778,14840,14915,14974,15017,15084,15139,15210,15268,15340,15416,15478,15535,15785,15835,15907,15994,16051,16126,16195,16250,16291,16353,16408,16466,16518,16568,16612,16700,16767,16812,23019,23073,23111,23176,23325,23396,23457,23513,23638,23729,23775,23812,23853,23896,23943,24025,24102,24152,24216,24270,24332,24395,24450,24509,24578,24628,24713,24791,24859,24902,25136,25193,25248,25294,25363,25424,25493,25537,25583,25642,25695,25735,25798,25852,25890,25941,25994,26050,26107,26190,26240,26279,26345,26556,26601,26671,26744,26809,26878,26951,27021,27098,27167,27212,27257,27302,27671,27718,27769,27817,27875,27932,27999,28053,28108,28155,28212,28266,28310,28362,28411,28480,28605,28854,29066,29123,29189,29250,29312,29377,29420,29487,29549,29603,29667,29709,29786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "135,156,157,158", "startColumns": "4,4,4,4", "startOffsets": "11918,13337,13443,13550", "endColumns": "99,105,106,105", "endOffsets": "12013,13438,13545,13651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "68,69,70,71,72,73,74,341", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5144,5241,5343,5441,5538,5640,5746,27528", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "5236,5338,5436,5533,5635,5741,5852,27624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,996,1084,1155,1229,1304,1376,1454,1522", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,991,1079,1150,1224,1299,1371,1449,1517,1635"}, "to": {"startLines": "89,90,144,145,149,189,190,270,275,298,299,324,338,339,340,358,360,361", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6786,6879,12563,12658,12907,15540,15623,23181,23518,24907,24987,26430,27307,27381,27456,28485,28610,28678", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "6874,6958,12653,12753,12986,15618,15718,23264,23597,24982,25070,26496,27376,27451,27523,28558,28673,28791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "114,115,116,117,118,119,120,121,123,124,125,126,127,128,129,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9488,9598,9753,9889,9994,10141,10271,10398,10651,10823,10930,11087,11221,11366,11533,11654,11718", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "9593,9748,9884,9989,10136,10266,10393,10499,10818,10925,11082,11216,11361,11528,11590,11713,11793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16817,16932,17045,17163,17278,17374,17470,17583,17716,17838,17978,18063,18161,18250,18347,18462,18583,18686,18823,18959,19081,19252,19370,19486,19604,19719,19809,19907,20031,20160,20261,20363,20469,20605,20745,20857,20959,21035,21132,21230,21340,21426,21511,21628,21708,21792,21892,21992,22088,22183,22271,22377,22477,22576,22697,22777,22884", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "16927,17040,17158,17273,17369,17465,17578,17711,17833,17973,18058,18156,18245,18342,18457,18578,18681,18818,18954,19076,19247,19365,19481,19599,19714,19804,19902,20026,20155,20256,20358,20464,20600,20740,20852,20954,21030,21127,21225,21335,21421,21506,21623,21703,21787,21887,21987,22083,22178,22266,22372,22472,22571,22692,22772,22879,22972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,361,497,610,695,781,859,947,1039,1152,1262,1355,1448,1555,1675,1757,1840,2017,2113,2222,2338,2441", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "219,356,492,605,690,776,854,942,1034,1147,1257,1350,1443,1550,1670,1752,1835,2012,2108,2217,2333,2436,2575"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6963,7132,7269,7405,7518,7603,7689,7767,7855,7947,8060,8170,8263,8356,8463,8583,8665,8748,8925,9021,9130,9246,9349", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "7127,7264,7400,7513,7598,7684,7762,7850,7942,8055,8165,8258,8351,8458,8578,8660,8743,8920,9016,9125,9241,9344,9483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "10504", "endColumns": "146", "endOffsets": "10646"}}]}]}