package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName

/**
 * <PERSON><PERSON>er türünü temsil eden veri sınıfı
 */
data class Sugar(
    @get:PropertyName("id")
    @set:PropertyName("id")
    var id: String = "",

    // Benzersiz tanımlayıcı (örn. "glucose_syrup", "fructose")
    @get:PropertyName("sugarId")
    @set:PropertyName("sugarId")
    var sugarId: String = "",

    // Zararlılık seviyesi (0-5)
    @get:PropertyName("harmful_level")
    @set:PropertyName("harmful_level")
    var harmfulLevel: Int = 0,

    // Sağlıksızlık seviyesi (0-5)
    @get:PropertyName("unhealthy_level")
    @set:PropertyName("unhealthy_level")
    var unhealthyLevel: Int = 0,

    @get:PropertyName("name_tr")
    @set:PropertyName("name_tr")
    var nameTr: String = "", // Şekerin Türkçe adı

    @get:PropertyName("name_en")
    @set:PropertyName("name_en")
    var nameEn: String = "", // Şekerin İngilizce adı

    @get:PropertyName("symbol")
    @set:PropertyName("symbol")
    var symbol: String = "", // Emoji veya ikon referansı

    @get:PropertyName("description_tr")
    @set:PropertyName("description_tr")
    var descriptionTr: String = "", // Şekerin Türkçe açıklaması

    @get:PropertyName("description_en")
    @set:PropertyName("description_en")
    var descriptionEn: String = "", // Şekerin İngilizce açıklaması

    @get:PropertyName("source_tr")
    @set:PropertyName("source_tr")
    var sourceTr: String = "", // Şekerin Türkçe kaynağı

    @get:PropertyName("source_en")
    @set:PropertyName("source_en")
    var sourceEn: String = "", // Şekerin İngilizce kaynağı

    @get:PropertyName("glycemic_index")
    @set:PropertyName("glycemic_index")
    var glycemicIndex: Int = 0, // Glisemik indeks değeri

    @get:PropertyName("risk_level")
    @set:PropertyName("risk_level")
    var riskLevel: Int = 0, // Risk seviyesi (1-3)

    @get:PropertyName("labels_tr")
    @set:PropertyName("labels_tr")
    var labelsTr: List<String> = listOf(), // Türkçe alternatif etiketler

    @get:PropertyName("labels_en")
    @set:PropertyName("labels_en")
    var labelsEn: List<String> = listOf(), // İngilizce alternatif etiketler

    @get:PropertyName("notes_tr")
    @set:PropertyName("notes_tr")
    var notesTr: String = "", // Türkçe ek notlar

    @get:PropertyName("notes_en")
    @set:PropertyName("notes_en")
    var notesEn: String = "", // İngilizce ek notlar

    @get:PropertyName("functional_type")
    @set:PropertyName("functional_type")
    var functionalType: List<String> = listOf() // Fonksiyonel tür (örn. "natural", "artificial", "high_glycemic")
) {
    // Firestore için boş constructor
    constructor() : this(
        id = "",
        sugarId = "",
        harmfulLevel = 0,
        unhealthyLevel = 0,
        nameTr = "",
        nameEn = "",
        symbol = "",
        descriptionTr = "",
        descriptionEn = "",
        sourceTr = "",
        sourceEn = "",
        glycemicIndex = 0,
        riskLevel = 0,
        labelsTr = listOf(),
        labelsEn = listOf(),
        notesTr = "",
        notesEn = "",
        functionalType = listOf()
    )

    // Kullanıcının dil tercihine göre adı döndürür
    fun getName(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> nameTr.ifEmpty { nameEn }
            SupportedLanguage.ENGLISH -> nameEn.ifEmpty { nameTr }
        }
    }

    // Kullanıcının dil tercihine göre açıklamayı döndürür
    fun getDescription(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> descriptionTr.ifEmpty { descriptionEn }
            SupportedLanguage.ENGLISH -> descriptionEn.ifEmpty { descriptionTr }
        }
    }

    // Kullanıcının dil tercihine göre kaynağı döndürür
    fun getSource(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> sourceTr.ifEmpty { sourceEn }
            SupportedLanguage.ENGLISH -> sourceEn.ifEmpty { sourceTr }
        }
    }

    // Kullanıcının dil tercihine göre etiketleri döndürür
    fun getLabels(language: SupportedLanguage): List<String> {
        return when (language) {
            SupportedLanguage.TURKISH -> labelsTr.ifEmpty { labelsEn }
            SupportedLanguage.ENGLISH -> labelsEn.ifEmpty { labelsTr }
        }
    }

    // Kullanıcının dil tercihine göre notları döndürür
    fun getNotes(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> notesTr.ifEmpty { notesEn }
            SupportedLanguage.ENGLISH -> notesEn.ifEmpty { notesTr }
        }
    }

    // Kullanıcının dil tercihine göre kökeni döndürür
    fun getOrigin(language: SupportedLanguage): String {
        return getSource(language)
    }

    // Kullanıcının dil tercihine göre kimyasal yapıyı döndürür
    fun getChemicalStructure(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> "Kimyasal yapı bilgisi mevcut değil"
            SupportedLanguage.ENGLISH -> "Chemical structure information not available"
        }
    }

    // Kullanıcının dil tercihine göre kullanımı döndürür
    fun getUsage(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> "Kullanım bilgisi mevcut değil"
            SupportedLanguage.ENGLISH -> "Usage information not available"
        }
    }

    // Kullanıcının dil tercihine göre sağlık etkilerini döndürür
    fun getHealthEffects(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> "Sağlık etkileri bilgisi mevcut değil"
            SupportedLanguage.ENGLISH -> "Health effects information not available"
        }
    }

    // Vegan durumlarını döndürür
    val veganStatuses: List<String>
        get() = functionalType.filter { it == "vegan" || it == "vegetarian" }

    // Dini durumları döndürür
    val religiousStatuses: List<String>
        get() = functionalType.filter { it == "halal" || it == "kosher" || it == "haram" }

    // Glisemik yükü döndürür
    val glycemicLoad: Int
        get() = glycemicIndex / 2 // Basit bir hesaplama
}
