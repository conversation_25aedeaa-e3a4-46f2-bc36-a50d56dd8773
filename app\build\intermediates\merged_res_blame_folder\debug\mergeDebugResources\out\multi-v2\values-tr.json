{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,21960", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,22035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "270,271", "startColumns": "4,4", "startOffsets": "23126,23210", "endColumns": "83,86", "endOffsets": "23205,23292"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values-tr\\strings.xml", "from": {"startLines": "61,126,121,101,68,74,69,116,102,43,98,97,88,96,2,86,133,132,25,92,22,78,105,87,37,63,94,90,42,70,79,44,80,35,50,19,83,117,9,123,122,119,41,38,55,71,72,73,100,5,95,26,53,58,113,110,112,111,82,32,46,45,40,39,34,49,15,93,59,85,36,51,54,52,23,24,120,104,60,67,129,134,128,135,131,130,7,64,107,89,12,33,48,47,84,77,127,6,27,13,18,14,17,81,91,8,65,118,103,106,29,66,28,99,62,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3041,6385,6087,5147,3419,3721,3466,5835,5209,2050,4952,4889,4260,4810,55,4182,6843,6742,1153,4482,923,3850,5380,4220,1688,3126,4693,4363,1993,3529,3891,2123,3930,1603,2496,820,4060,5884,303,6203,6141,5968,1933,1748,2807,3575,3617,3671,5072,134,4753,1212,2686,2901,5745,5580,5687,5635,4010,1468,2247,2187,1862,1795,1558,2435,549,4628,2942,4145,1645,2555,2745,2615,982,1061,6033,5318,2996,3356,6541,6915,6491,6984,6664,6596,215,3177,5491,4306,384,1512,2371,2304,4099,3810,6437,177,1278,439,749,492,680,3971,4422,258,3237,5924,5266,5442,1379,3293,1329,5010,3084,603", "endColumns": "42,51,53,61,46,67,62,48,56,72,57,62,45,78,53,37,71,100,58,145,58,40,61,39,59,50,59,58,56,45,38,63,40,41,58,76,38,39,54,151,61,64,59,46,63,41,53,49,74,42,56,65,58,40,61,54,57,51,49,43,56,59,70,66,44,60,53,64,53,36,42,59,61,70,78,91,53,61,44,62,54,68,49,84,77,67,42,59,65,56,54,45,63,66,45,39,53,37,50,52,70,56,68,38,59,44,55,43,51,48,59,62,49,61,41,76", "endOffsets": "3079,6432,6136,5204,3461,3784,3524,5879,5261,2118,5005,4947,4301,4884,104,4215,6910,6838,1207,4623,977,3886,5437,4255,1743,3172,4748,4417,2045,3570,3925,2182,3966,1640,2550,892,4094,5919,353,6350,6198,6028,1988,1790,2866,3612,3666,3716,5142,172,4805,1273,2740,2937,5802,5630,5740,5682,4055,1507,2299,2242,1928,1857,1598,2491,598,4688,2991,4177,1683,2610,2802,2681,1056,1148,6082,5375,3036,3414,6591,6979,6536,7064,6737,6659,253,3232,5552,4358,434,1553,2430,2366,4140,3845,6486,210,1324,487,815,544,744,4005,4477,298,3288,5963,5313,5486,1434,3351,1374,5067,3121,675"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,55,56,57,58,59,103,104,105,107,108,109,112,113,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,211,212,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,255,256,257,262,263,264,265,269,272,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,2840,2892,2946,3008,3055,3123,3186,3235,3292,3365,3423,3486,3532,3611,3665,3703,3775,3876,4648,4794,4853,4894,4956,9949,10009,10060,10220,10279,10336,10577,10616,10764,10805,10847,10906,10983,11341,11381,11436,11588,11650,11715,11775,11822,11886,11928,11982,12032,12107,12333,12390,12456,12515,12556,12618,12673,12731,12783,12833,12877,12934,12994,13065,13132,13177,19398,19452,19689,19743,19780,19823,19883,19945,20016,20095,20187,20241,20303,20348,20411,20466,20535,20585,20670,20748,20816,21027,21087,21153,21210,21265,21311,21375,21442,21488,21528,21582,21620,21671,21724,21795,21852,21921,22111,22171,22216,22594,22638,22690,22739,23063,23297,23347,23409,23451", "endColumns": "42,51,53,61,46,67,62,48,56,72,57,62,45,78,53,37,71,100,58,145,58,40,61,39,59,50,59,58,56,45,38,63,40,41,58,76,38,39,54,151,61,64,59,46,63,41,53,49,74,42,56,65,58,40,61,54,57,51,49,43,56,59,70,66,44,60,53,64,53,36,42,59,61,70,78,91,53,61,44,62,54,68,49,84,77,67,42,59,65,56,54,45,63,66,45,39,53,37,50,52,70,56,68,38,59,44,55,43,51,48,59,62,49,61,41,76", "endOffsets": "2835,2887,2941,3003,3050,3118,3181,3230,3287,3360,3418,3481,3527,3606,3660,3698,3770,3871,3930,4789,4848,4889,4951,4991,10004,10055,10115,10274,10331,10377,10611,10675,10800,10842,10901,10978,11017,11376,11431,11583,11645,11710,11770,11817,11881,11923,11977,12027,12102,12145,12385,12451,12510,12551,12613,12668,12726,12778,12828,12872,12929,12989,13060,13127,13172,13233,19447,19512,19738,19775,19818,19878,19940,20011,20090,20182,20236,20298,20343,20406,20461,20530,20580,20665,20743,20811,20854,21082,21148,21205,21260,21306,21370,21437,21483,21523,21577,21615,21666,21719,21790,21847,21916,21955,22166,22211,22267,22633,22685,22734,22794,23121,23342,23404,23446,23523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "106,120,121,122", "startColumns": "4,4,4,4", "startOffsets": "10120,11022,11128,11235", "endColumns": "99,105,106,105", "endOffsets": "10215,11123,11230,11336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "48,49,50,51,52,53,54,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3935,4032,4134,4232,4329,4431,4537,22493", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "4027,4129,4227,4324,4426,4532,4643,22589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,996,1084,1155,1229,1304,1376,1454,1522", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,991,1079,1150,1224,1299,1371,1449,1517,1635"}, "to": {"startLines": "60,61,110,111,114,136,137,213,214,234,235,254,258,259,260,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4996,5089,10382,10477,10680,12150,12233,19517,19605,20859,20939,22040,22272,22346,22421,22799,22877,22945", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "5084,5168,10472,10572,10759,12228,12328,19600,19684,20934,21022,22106,22341,22416,22488,22872,22940,23058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "85,86,87,88,89,90,91,92,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7698,7808,7963,8099,8204,8351,8481,8608,8861,9033,9140,9297,9431,9576,9743,9805,9869", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "7803,7958,8094,8199,8346,8476,8603,8709,9028,9135,9292,9426,9571,9738,9800,9864,9944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13238,13353,13466,13584,13699,13795,13891,14004,14137,14259,14399,14484,14582,14671,14768,14883,15004,15107,15244,15380,15502,15673,15791,15907,16025,16140,16230,16328,16452,16581,16682,16784,16890,17026,17166,17278,17380,17456,17553,17651,17761,17847,17932,18049,18129,18213,18313,18413,18509,18604,18692,18798,18898,18997,19118,19198,19305", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "13348,13461,13579,13694,13790,13886,13999,14132,14254,14394,14479,14577,14666,14763,14878,14999,15102,15239,15375,15497,15668,15786,15902,16020,16135,16225,16323,16447,16576,16677,16779,16885,17021,17161,17273,17375,17451,17548,17646,17756,17842,17927,18044,18124,18208,18308,18408,18504,18599,18687,18793,18893,18992,19113,19193,19300,19393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,361,497,610,695,781,859,947,1039,1152,1262,1355,1448,1555,1675,1757,1840,2017,2113,2222,2338,2441", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "219,356,492,605,690,776,854,942,1034,1147,1257,1350,1443,1550,1670,1752,1835,2012,2108,2217,2333,2436,2575"}, "to": {"startLines": "62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5173,5342,5479,5615,5728,5813,5899,5977,6065,6157,6270,6380,6473,6566,6673,6793,6875,6958,7135,7231,7340,7456,7559", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "5337,5474,5610,5723,5808,5894,5972,6060,6152,6265,6375,6468,6561,6668,6788,6870,6953,7130,7226,7335,7451,7554,7693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "93", "startColumns": "4", "startOffsets": "8714", "endColumns": "146", "endOffsets": "8856"}}]}]}