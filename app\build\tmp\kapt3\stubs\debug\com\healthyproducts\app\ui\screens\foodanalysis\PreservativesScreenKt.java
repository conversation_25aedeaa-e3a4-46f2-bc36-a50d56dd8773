package com.healthyproducts.app.ui.screens.foodanalysis;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000Z\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001aF\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u0014\b\u0002\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000bH\u0007\u001ad\u0010\f\u001a\u00020\u00012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00030\u000e2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00070\u00122\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00132\u0018\u0010\u0017\u001a\u0014\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0018H\u0007\u001a$\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u001a\u001a\u00020\u001b2\b\b\u0002\u0010\u001c\u001a\u00020\u001dH\u0007\u00a8\u0006\u001e"}, d2 = {"PreservativeItem", "", "preservative", "Lcom/healthyproducts/app/data/model/Preservative;", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "isSelected", "", "onCheckedChange", "Lkotlin/Function1;", "onClick", "Lkotlin/Function0;", "PreservativesList", "preservatives", "", "navController", "Landroidx/navigation/NavController;", "selectedFoodPreferences", "", "", "userFoodPreferencesState", "Lcom/healthyproducts/app/ui/viewmodel/UserFoodPreferencesState;", "userId", "onSavePreference", "Lkotlin/Function2;", "PreservativesScreen", "foodAnalysisViewModel", "Lcom/healthyproducts/app/ui/viewmodel/FoodAnalysisViewModel;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "app_debug"})
public final class PreservativesScreenKt {
    
    /**
     * Koruyucular ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PreservativesScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel foodAnalysisViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * Koruyucular listesi
     */
    @androidx.compose.runtime.Composable()
    public static final void PreservativesList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.model.Preservative> preservatives, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.Boolean> selectedFoodPreferences, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState userFoodPreferencesState, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.healthyproducts.app.data.model.Preservative, ? super java.lang.Boolean, kotlin.Unit> onSavePreference) {
    }
    
    /**
     * Koruyucu öğesi
     */
    @androidx.compose.runtime.Composable()
    public static final void PreservativeItem(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.Preservative preservative, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onCheckedChange, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}