package com.healthyproducts.app.data.repository

import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.healthyproducts.app.data.model.Intolerance
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gıda intoleransları işlemlerini yöneten repository sınıfı
 */
@Singleton
class IntoleranceRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val TAG = "IntoleranceRepository"
        private const val COLLECTION_NAME = "intolerances"
    }

    private val _intolerances = MutableStateFlow<List<Intolerance>>(emptyList())
    val intolerances: StateFlow<List<Intolerance>> = _intolerances.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    /**
     * Tüm intoleransları getirir
     */
    suspend fun getAllIntolerances(): Result<List<Intolerance>> = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null

            Log.d(TAG, "Getting all intolerances")

            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val intolerancesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Intolerance::class.java)
            }

            Log.d(TAG, "Retrieved ${intolerancesList.size} intolerances")

            _intolerances.value = intolerancesList
            _isLoading.value = false

            Result.success(intolerancesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting intolerances", e)
            _error.value = e.message
            _isLoading.value = false
            Result.failure(e)
        }
    }

    /**
     * İntolerans ekler
     */
    suspend fun addIntolerance(intolerance: Intolerance): Result<Intolerance> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding intolerance: ${intolerance.intoleranceId} - ${intolerance.nameTr}")

            // İntoleransı Firestore'a ekle
            val documentRef = firestore.collection(COLLECTION_NAME).document(intolerance.intoleranceId)
            documentRef.set(intolerance).await()

            // İntoleransı yerel listeye ekle
            val currentList = _intolerances.value.toMutableList()
            currentList.add(intolerance)
            _intolerances.value = currentList

            Log.d(TAG, "Intolerance added successfully")

            Result.success(intolerance)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding intolerance", e)
            Result.failure(e)
        }
    }

    /**
     * İntoleransı günceller
     */
    suspend fun updateIntolerance(intolerance: Intolerance): Result<Intolerance> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating intolerance: ${intolerance.intoleranceId} - ${intolerance.nameTr}")

            // İntoleransı Firestore'da güncelle
            val documentRef = firestore.collection(COLLECTION_NAME).document(intolerance.intoleranceId)
            documentRef.set(intolerance).await()

            // İntoleransı yerel listede güncelle
            val currentList = _intolerances.value.toMutableList()
            val index = currentList.indexOfFirst { it.intoleranceId == intolerance.intoleranceId }
            if (index != -1) {
                currentList[index] = intolerance
                _intolerances.value = currentList
            }

            Log.d(TAG, "Intolerance updated successfully")

            Result.success(intolerance)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating intolerance", e)
            Result.failure(e)
        }
    }

    /**
     * İntoleransı siler
     */
    suspend fun deleteIntolerance(intoleranceId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting intolerance: $intoleranceId")

            // İntoleransı Firestore'dan sil
            val documentRef = firestore.collection(COLLECTION_NAME).document(intoleranceId)
            documentRef.delete().await()

            // İntoleransı yerel listeden sil
            val currentList = _intolerances.value.toMutableList()
            val index = currentList.indexOfFirst { it.intoleranceId == intoleranceId }
            if (index != -1) {
                currentList.removeAt(index)
                _intolerances.value = currentList
            }

            Log.d(TAG, "Intolerance deleted successfully")

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting intolerance", e)
            Result.failure(e)
        }
    }

    /**
     * İntoleransı ID ile getirir
     */
    suspend fun getIntoleranceById(intoleranceId: String): Result<Intolerance?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting intolerance by ID: $intoleranceId")

            // Önce yerel listede ara
            val localIntolerance = _intolerances.value.find { it.intoleranceId == intoleranceId }
            if (localIntolerance != null) {
                Log.d(TAG, "Intolerance found in local cache")
                return@withContext Result.success(localIntolerance)
            }

            // Yerel listede yoksa Firestore'dan getir
            val documentRef = firestore.collection(COLLECTION_NAME).document(intoleranceId)
            val document = documentRef.get().await()

            val intolerance = document.toObject(Intolerance::class.java)

            if (intolerance != null) {
                Log.d(TAG, "Intolerance found in Firestore")
            } else {
                Log.d(TAG, "Intolerance not found")
            }

            Result.success(intolerance)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting intolerance by ID", e)
            Result.failure(e)
        }
    }

    /**
     * İntoleransları adına göre arar
     */
    suspend fun searchIntolerancesByName(query: String): Result<List<Intolerance>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching intolerances by name: $query")

            // Firestore'da tam metin araması yapılamadığı için tüm intoleransları getirip filtreliyoruz
            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val intolerancesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Intolerance::class.java)
            }.filter { intolerance ->
                intolerance.nameTr.contains(query, ignoreCase = true) ||
                intolerance.nameEn.contains(query, ignoreCase = true) ||
                intolerance.intoleranceId.contains(query, ignoreCase = true)
            }

            Log.d(TAG, "Found ${intolerancesList.size} intolerances matching query")

            Result.success(intolerancesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching intolerances", e)
            Result.failure(e)
        }
    }
}
