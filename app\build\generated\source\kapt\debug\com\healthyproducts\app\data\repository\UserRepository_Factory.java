package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepository_Factory implements Factory<UserRepository> {
  private final Provider<FirebaseAuthRepository> firebaseAuthRepositoryProvider;

  private final Provider<FirebaseFirestore> firestoreProvider;

  public UserRepository_Factory(Provider<FirebaseAuthRepository> firebaseAuthRepositoryProvider,
      Provider<FirebaseFirestore> firestoreProvider) {
    this.firebaseAuthRepositoryProvider = firebaseAuthRepositoryProvider;
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public UserRepository get() {
    return newInstance(firebaseAuthRepositoryProvider.get(), firestoreProvider.get());
  }

  public static UserRepository_Factory create(
      Provider<FirebaseAuthRepository> firebaseAuthRepositoryProvider,
      Provider<FirebaseFirestore> firestoreProvider) {
    return new UserRepository_Factory(firebaseAuthRepositoryProvider, firestoreProvider);
  }

  public static UserRepository newInstance(FirebaseAuthRepository firebaseAuthRepository,
      FirebaseFirestore firestore) {
    return new UserRepository(firebaseAuthRepository, firestore);
  }
}
