package com.healthyproducts.app.ui.viewmodel;

import android.app.Application;
import com.healthyproducts.app.data.repository.FirestoreRepository;
import com.healthyproducts.app.data.repository.OcrCorrectionRepository;
import com.healthyproducts.app.data.repository.ScanHistoryRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ScanViewModel_Factory implements Factory<ScanViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<ScanHistoryRepository> scanHistoryRepositoryProvider;

  private final Provider<FirestoreRepository> firestoreRepositoryProvider;

  private final Provider<OcrCorrectionRepository> ocrCorrectionRepositoryProvider;

  public ScanViewModel_Factory(Provider<Application> applicationProvider,
      Provider<ScanHistoryRepository> scanHistoryRepositoryProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider,
      Provider<OcrCorrectionRepository> ocrCorrectionRepositoryProvider) {
    this.applicationProvider = applicationProvider;
    this.scanHistoryRepositoryProvider = scanHistoryRepositoryProvider;
    this.firestoreRepositoryProvider = firestoreRepositoryProvider;
    this.ocrCorrectionRepositoryProvider = ocrCorrectionRepositoryProvider;
  }

  @Override
  public ScanViewModel get() {
    return newInstance(applicationProvider.get(), scanHistoryRepositoryProvider.get(), firestoreRepositoryProvider.get(), ocrCorrectionRepositoryProvider.get());
  }

  public static ScanViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<ScanHistoryRepository> scanHistoryRepositoryProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider,
      Provider<OcrCorrectionRepository> ocrCorrectionRepositoryProvider) {
    return new ScanViewModel_Factory(applicationProvider, scanHistoryRepositoryProvider, firestoreRepositoryProvider, ocrCorrectionRepositoryProvider);
  }

  public static ScanViewModel newInstance(Application application,
      ScanHistoryRepository scanHistoryRepository, FirestoreRepository firestoreRepository,
      OcrCorrectionRepository ocrCorrectionRepository) {
    return new ScanViewModel(application, scanHistoryRepository, firestoreRepository, ocrCorrectionRepository);
  }
}
