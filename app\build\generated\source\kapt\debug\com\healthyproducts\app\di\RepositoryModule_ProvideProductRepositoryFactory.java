package com.healthyproducts.app.di;

import com.google.firebase.firestore.FirebaseFirestore;
import com.healthyproducts.app.data.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideProductRepositoryFactory implements Factory<ProductRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public RepositoryModule_ProvideProductRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public ProductRepository get() {
    return provideProductRepository(firestoreProvider.get());
  }

  public static RepositoryModule_ProvideProductRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new RepositoryModule_ProvideProductRepositoryFactory(firestoreProvider);
  }

  public static ProductRepository provideProductRepository(FirebaseFirestore firestore) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideProductRepository(firestore));
  }
}
