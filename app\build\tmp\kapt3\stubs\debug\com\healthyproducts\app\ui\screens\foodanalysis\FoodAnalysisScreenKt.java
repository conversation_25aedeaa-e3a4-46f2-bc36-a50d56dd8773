package com.healthyproducts.app.ui.screens.foodanalysis;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000X\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u001a\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a.\u0010\n\u001a\u00020\u00012\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\f2\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a0\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\b\u001a\u00020\t2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001aN\u0010\u0013\u001a\u00020\u00012\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001a8\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u001b2\b\b\u0002\u0010\u001c\u001a\u00020\u00032\b\b\u0002\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\u001f\u001a\u00020 2\b\b\u0002\u0010!\u001a\u00020\"H\u0007\u001a\"\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a\u001a\u0010&\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a\u001c\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\f2\u0006\u0010\u0002\u001a\u00020\u0003H\u0002\u00a8\u0006("}, d2 = {"AnalysisResultView", "", "analysisResult", "", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "ApprovalBadge", "text", "modifier", "Landroidx/compose/ui/Modifier;", "BadgeSection", "sections", "", "FoodAnalysisOptionCard", "title", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "onClick", "Lkotlin/Function0;", "FoodAnalysisOptions", "onAllergensClick", "onFatsClick", "onSugarsClick", "onCertificatesClick", "onPreservativesClick", "FoodAnalysisScreen", "navController", "Landroidx/navigation/NavController;", "ingredients", "foodAnalysisViewModel", "Lcom/healthyproducts/app/ui/viewmodel/FoodAnalysisViewModel;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "scanViewModel", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel;", "ScoreBadge", "score", "", "WarningBadge", "parseAnalysisResult", "app_debug"})
public final class FoodAnalysisScreenKt {
    
    /**
     * Gıda analizi ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void FoodAnalysisScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String ingredients, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel foodAnalysisViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.ScanViewModel scanViewModel) {
    }
    
    /**
     * Gıda analizi seçenekleri bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void FoodAnalysisOptions(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAllergensClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onFatsClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSugarsClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCertificatesClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPreservativesClick) {
    }
    
    /**
     * Gıda analizi seçenek kartı
     */
    @androidx.compose.runtime.Composable()
    public static final void FoodAnalysisOptionCard(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * Analiz sonuçlarını görselleştiren bileşen
     */
    @androidx.compose.runtime.Composable()
    public static final void AnalysisResultView(@org.jetbrains.annotations.NotNull()
    java.lang.String analysisResult, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
    }
    
    /**
     * Analiz sonucunu parçalara ayırır
     */
    private static final java.util.Map<java.lang.String, java.lang.String> parseAnalysisResult(java.lang.String analysisResult) {
        return null;
    }
    
    /**
     * Skor Badge'i - Sayfanın en üstünde gösterilen büyük puan göstergesi
     */
    @androidx.compose.runtime.Composable()
    public static final void ScoreBadge(float score, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Badge Section - Uyarı ve onay badge'lerini gösteren bölüm
     */
    @androidx.compose.runtime.Composable()
    public static final void BadgeSection(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> sections, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Uyarı Badge'i - Kırmızı renkli uyarı göstergesi
     */
    @androidx.compose.runtime.Composable()
    public static final void WarningBadge(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Onay Badge'i - Yeşil renkli onay göstergesi
     */
    @androidx.compose.runtime.Composable()
    public static final void ApprovalBadge(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}