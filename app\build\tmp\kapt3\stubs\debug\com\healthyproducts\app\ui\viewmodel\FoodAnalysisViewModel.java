package com.healthyproducts.app.ui.viewmodel;

/**
 * Gıda analizi için ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00d8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0019\b\u0007\u0018\u00002\u00020\u0001BO\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u0012\u0006\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\u0002\u0010\u0014J\u001e\u0010S\u001a\u00020T2\u0006\u0010U\u001a\u00020,2\u0006\u0010V\u001a\u00020W2\u0006\u0010X\u001a\u00020,J\u001c\u0010Y\u001a\u00020T2\f\u0010Z\u001a\b\u0012\u0004\u0012\u00020,0[2\u0006\u0010\\\u001a\u00020]Jd\u0010^\u001a\u00020,2\f\u0010Z\u001a\b\u0012\u0004\u0012\u00020,0[2\u0006\u0010\\\u001a\u00020]2\f\u0010_\u001a\b\u0012\u0004\u0012\u00020`0[2\f\u0010a\u001a\b\u0012\u0004\u0012\u00020`0[2\f\u0010b\u001a\b\u0012\u0004\u0012\u00020`0[2\f\u0010c\u001a\b\u0012\u0004\u0012\u00020`0[2\f\u0010d\u001a\b\u0012\u0004\u0012\u00020`0[H\u0002J\u0006\u0010e\u001a\u00020TJ\u0006\u0010f\u001a\u00020TJ\u0006\u0010g\u001a\u00020TJ\u0006\u0010h\u001a\u00020TJ\u0006\u0010i\u001a\u00020TJ\u001e\u0010j\u001a\u00020T2\u0006\u0010k\u001a\u00020,2\u0006\u0010U\u001a\u00020,2\u0006\u0010V\u001a\u00020WJ\u000e\u0010l\u001a\u00020T2\u0006\u0010m\u001a\u00020nJ\u000e\u0010o\u001a\u00020T2\u0006\u0010m\u001a\u00020nJ\u000e\u0010p\u001a\u00020T2\u0006\u0010m\u001a\u00020nJ\u0006\u0010q\u001a\u00020TJ\u000e\u0010r\u001a\u00020T2\u0006\u0010m\u001a\u00020nJ\u000e\u0010s\u001a\u00020T2\u0006\u0010U\u001a\u00020,J\u000e\u0010t\u001a\u00020T2\u0006\u0010u\u001a\u00020,J\u0006\u0010v\u001a\u00020TJ\u000e\u0010w\u001a\u00020T2\u0006\u0010u\u001a\u00020,J\u0006\u0010x\u001a\u00020TJ\u000e\u0010y\u001a\u00020T2\u0006\u0010u\u001a\u00020,J\u0006\u0010z\u001a\u00020TJ\u000e\u0010{\u001a\u00020T2\u0006\u0010|\u001a\u00020,J\u0006\u0010}\u001a\u00020TJ\u000e\u0010~\u001a\u00020T2\u0006\u0010u\u001a\u00020,J\u0006\u0010\u007f\u001a\u00020TJ\u000f\u0010\u0080\u0001\u001a\u00020T2\u0006\u0010U\u001a\u00020,J\u0017\u0010\u0081\u0001\u001a\u00020T2\u0006\u0010U\u001a\u00020,2\u0006\u0010V\u001a\u00020WJ\u0007\u0010\u0082\u0001\u001a\u00020TJ(\u0010\u0083\u0001\u001a\u00020T2\u0006\u0010U\u001a\u00020,2\u0006\u0010V\u001a\u00020W2\u0007\u0010\u0084\u0001\u001a\u00020,2\u0006\u0010X\u001a\u00020,J\u0019\u0010\u0085\u0001\u001a\u00020T2\u0007\u0010\u0084\u0001\u001a\u00020,2\u0007\u0010\u0086\u0001\u001a\u00020-R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001f0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\"\u001a\b\u0012\u0004\u0012\u00020#0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010$\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010%0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010&\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\'0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010(\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010)0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010*\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020,\u0012\u0004\u0012\u00020-0+0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010.\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010/0\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u00100\u001a\b\u0012\u0004\u0012\u0002010\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u00102\u001a\b\u0012\u0004\u0012\u0002030\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u00104\u001a\b\u0012\u0004\u0012\u00020\u001705\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00107R\u0017\u00108\u001a\b\u0012\u0004\u0012\u00020\u001905\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u00107R\u0017\u0010:\u001a\b\u0012\u0004\u0012\u00020\u001b05\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00107R\u000e\u0010<\u001a\u00020,X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010=\u001a\b\u0012\u0004\u0012\u00020\u001d05\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u00107R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010?\u001a\b\u0012\u0004\u0012\u00020\u001f05\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u00107R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010A\u001a\b\u0012\u0004\u0012\u00020!05\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u00107R\u0017\u0010C\u001a\b\u0012\u0004\u0012\u00020#05\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u00107R\u0019\u0010E\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010%05\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u00107R\u0019\u0010G\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\'05\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u00107R\u0019\u0010I\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010)05\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u00107R#\u0010K\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020,\u0012\u0004\u0012\u00020-0+05\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u00107R\u0019\u0010M\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010/05\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u00107R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010O\u001a\b\u0012\u0004\u0012\u00020105\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u00107R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010Q\u001a\b\u0012\u0004\u0012\u00020305\u00a2\u0006\b\n\u0000\u001a\u0004\bR\u00107\u00a8\u0006\u0087\u0001"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/FoodAnalysisViewModel;", "Landroidx/lifecycle/ViewModel;", "foodAnalysisRepository", "Lcom/healthyproducts/app/data/repository/FoodAnalysisRepository;", "allergenRepository", "Lcom/healthyproducts/app/data/repository/AllergenRepository;", "fatRepository", "Lcom/healthyproducts/app/data/repository/FatRepository;", "sugarRepository", "Lcom/healthyproducts/app/data/repository/SugarRepository;", "preservativeRepository", "Lcom/healthyproducts/app/data/repository/PreservativeRepository;", "foodCertificateRepository", "Lcom/healthyproducts/app/data/repository/FoodCertificateRepository;", "userFoodPreferenceRepository", "Lcom/healthyproducts/app/data/repository/UserFoodPreferenceRepository;", "foodAnalysisService", "Lcom/healthyproducts/app/data/service/FoodAnalysisService;", "aiService", "Lcom/healthyproducts/app/data/api/AiService;", "(Lcom/healthyproducts/app/data/repository/FoodAnalysisRepository;Lcom/healthyproducts/app/data/repository/AllergenRepository;Lcom/healthyproducts/app/data/repository/FatRepository;Lcom/healthyproducts/app/data/repository/SugarRepository;Lcom/healthyproducts/app/data/repository/PreservativeRepository;Lcom/healthyproducts/app/data/repository/FoodCertificateRepository;Lcom/healthyproducts/app/data/repository/UserFoodPreferenceRepository;Lcom/healthyproducts/app/data/service/FoodAnalysisService;Lcom/healthyproducts/app/data/api/AiService;)V", "_allergensState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/healthyproducts/app/ui/viewmodel/AllergensState;", "_analysisResult", "Lcom/healthyproducts/app/ui/viewmodel/AnalysisState;", "_certificatesState", "Lcom/healthyproducts/app/ui/viewmodel/CertificatesState;", "_fatsState", "Lcom/healthyproducts/app/ui/viewmodel/FatsState;", "_importStatus", "Lcom/healthyproducts/app/ui/viewmodel/ImportStatus;", "_preservativeState", "Lcom/healthyproducts/app/ui/viewmodel/PreservativeState;", "_preservativesState", "Lcom/healthyproducts/app/ui/viewmodel/PreservativesState;", "_selectedAllergen", "Lcom/healthyproducts/app/data/model/Allergen;", "_selectedCertificate", "Lcom/healthyproducts/app/data/model/FoodCertificate;", "_selectedFat", "Lcom/healthyproducts/app/data/model/Fat;", "_selectedFoodPreferences", "", "", "", "_selectedSugar", "Lcom/healthyproducts/app/data/model/Sugar;", "_sugarsState", "Lcom/healthyproducts/app/ui/viewmodel/SugarsState;", "_userFoodPreferencesState", "Lcom/healthyproducts/app/ui/viewmodel/UserFoodPreferencesState;", "allergensState", "Lkotlinx/coroutines/flow/StateFlow;", "getAllergensState", "()Lkotlinx/coroutines/flow/StateFlow;", "analysisResult", "getAnalysisResult", "certificatesState", "getCertificatesState", "currentUserId", "fatsState", "getFatsState", "importStatus", "getImportStatus", "preservativeState", "getPreservativeState", "preservativesState", "getPreservativesState", "selectedAllergen", "getSelectedAllergen", "selectedCertificate", "getSelectedCertificate", "selectedFat", "getSelectedFat", "selectedFoodPreferences", "getSelectedFoodPreferences", "selectedSugar", "getSelectedSugar", "sugarsState", "getSugarsState", "userFoodPreferencesState", "getUserFoodPreferencesState", "addCustomFoodPreference", "", "userId", "type", "Lcom/healthyproducts/app/data/model/FoodPreferenceType;", "itemName", "analyzeIngredients", "ingredients", "", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "buildAnalysisPrompt", "userAllergens", "Lcom/healthyproducts/app/data/model/UserFoodPreference;", "userFats", "userSugars", "userCertificates", "userPreservatives", "clearPreservativeState", "clearSelectedAllergen", "clearSelectedCertificate", "clearSelectedFat", "clearSelectedSugar", "deleteUserFoodPreference", "preferenceId", "importAllergensFromJson", "context", "Landroid/content/Context;", "importCertificatesFromJson", "importFatsFromJson", "importPreservativesFromJson", "importSugarsFromJson", "initUserPreferences", "loadAllergenByCode", "code", "loadAllergens", "loadFatByCode", "loadFats", "loadFoodCertificateByCode", "loadFoodCertificates", "loadPreservative", "preservativeId", "loadPreservatives", "loadSugarByCode", "loadSugars", "loadUserFoodPreferences", "loadUserFoodPreferencesByType", "resetImportStatus", "saveUserFoodPreference", "itemId", "updateSelectedFoodPreference", "isSelected", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class FoodAnalysisViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FoodAnalysisRepository foodAnalysisRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.AllergenRepository allergenRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FatRepository fatRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.SugarRepository sugarRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.PreservativeRepository preservativeRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FoodCertificateRepository foodCertificateRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.UserFoodPreferenceRepository userFoodPreferenceRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.service.FoodAnalysisService foodAnalysisService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.api.AiService aiService = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.AllergensState> _allergensState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.AllergensState> allergensState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.FatsState> _fatsState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.FatsState> fatsState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.SugarsState> _sugarsState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.SugarsState> sugarsState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.CertificatesState> _certificatesState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.CertificatesState> certificatesState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.data.model.Allergen> _selectedAllergen = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Allergen> selectedAllergen = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.data.model.Fat> _selectedFat = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Fat> selectedFat = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.data.model.Sugar> _selectedSugar = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Sugar> selectedSugar = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.data.model.FoodCertificate> _selectedCertificate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.FoodCertificate> selectedCertificate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.PreservativesState> _preservativesState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.PreservativesState> preservativesState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.PreservativeState> _preservativeState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.PreservativeState> preservativeState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState> _userFoodPreferencesState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState> userFoodPreferencesState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, java.lang.Boolean>> _selectedFoodPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.lang.Boolean>> selectedFoodPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentUserId = "";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.AnalysisState> _analysisResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.AnalysisState> analysisResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.ImportStatus> _importStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ImportStatus> importStatus = null;
    
    @javax.inject.Inject()
    public FoodAnalysisViewModel(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FoodAnalysisRepository foodAnalysisRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.AllergenRepository allergenRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FatRepository fatRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.SugarRepository sugarRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.PreservativeRepository preservativeRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FoodCertificateRepository foodCertificateRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.UserFoodPreferenceRepository userFoodPreferenceRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.service.FoodAnalysisService foodAnalysisService, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.api.AiService aiService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.AllergensState> getAllergensState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.FatsState> getFatsState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.SugarsState> getSugarsState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.CertificatesState> getCertificatesState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Allergen> getSelectedAllergen() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Fat> getSelectedFat() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Sugar> getSelectedSugar() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.FoodCertificate> getSelectedCertificate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.PreservativesState> getPreservativesState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.PreservativeState> getPreservativeState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState> getUserFoodPreferencesState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.lang.Boolean>> getSelectedFoodPreferences() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.AnalysisState> getAnalysisResult() {
        return null;
    }
    
    public final void initUserPreferences(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
    
    /**
     * İçerikleri analiz eder
     */
    public final void analyzeIngredients(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> ingredients, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
    }
    
    /**
     * Analiz için prompt oluşturur
     */
    private final java.lang.String buildAnalysisPrompt(java.util.List<java.lang.String> ingredients, com.healthyproducts.app.data.model.SupportedLanguage language, java.util.List<com.healthyproducts.app.data.model.UserFoodPreference> userAllergens, java.util.List<com.healthyproducts.app.data.model.UserFoodPreference> userFats, java.util.List<com.healthyproducts.app.data.model.UserFoodPreference> userSugars, java.util.List<com.healthyproducts.app.data.model.UserFoodPreference> userCertificates, java.util.List<com.healthyproducts.app.data.model.UserFoodPreference> userPreservatives) {
        return null;
    }
    
    /**
     * Tüm alerjenleri yükler
     */
    public final void loadAllergens() {
    }
    
    /**
     * Belirli bir alerjeni yükler
     */
    public final void loadAllergenByCode(@org.jetbrains.annotations.NotNull()
    java.lang.String code) {
    }
    
    /**
     * Tüm yağları yükler
     */
    public final void loadFats() {
    }
    
    /**
     * Belirli bir yağı yükler
     */
    public final void loadFatByCode(@org.jetbrains.annotations.NotNull()
    java.lang.String code) {
    }
    
    /**
     * Tüm şekerleri yükler
     */
    public final void loadSugars() {
    }
    
    /**
     * Belirli bir şekeri yükler
     */
    public final void loadSugarByCode(@org.jetbrains.annotations.NotNull()
    java.lang.String code) {
    }
    
    /**
     * Tüm gıda sertifikalarını yükler
     */
    public final void loadFoodCertificates() {
    }
    
    /**
     * Belirli bir gıda sertifikasını yükler
     */
    public final void loadFoodCertificateByCode(@org.jetbrains.annotations.NotNull()
    java.lang.String code) {
    }
    
    /**
     * Seçilen alerjeni temizler
     */
    public final void clearSelectedAllergen() {
    }
    
    /**
     * Seçilen yağı temizler
     */
    public final void clearSelectedFat() {
    }
    
    /**
     * Seçilen şekeri temizler
     */
    public final void clearSelectedSugar() {
    }
    
    /**
     * Seçilen sertifikayı temizler
     */
    public final void clearSelectedCertificate() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ImportStatus> getImportStatus() {
        return null;
    }
    
    /**
     * JSON dosyasından alerjenleri içeri aktarır
     */
    public final void importAllergensFromJson(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * İçeri aktarma durumunu sıfırlar
     */
    public final void resetImportStatus() {
    }
    
    /**
     * JSON dosyasından yağları içe aktarır
     */
    public final void importFatsFromJson(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * JSON dosyasından şekerleri içe aktarır
     */
    public final void importSugarsFromJson(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Tüm koruyucuları yükler
     */
    public final void loadPreservatives() {
    }
    
    /**
     * Belirli bir koruyucuyu yükler
     */
    public final void loadPreservative(@org.jetbrains.annotations.NotNull()
    java.lang.String preservativeId) {
    }
    
    /**
     * Seçilen koruyucuyu temizler
     */
    public final void clearPreservativeState() {
    }
    
    /**
     * JSON dosyasından koruyucuları içe aktarır
     */
    public final void importPreservativesFromJson() {
    }
    
    /**
     * JSON dosyasından gıda sertifikalarını içe aktarır
     */
    public final void importCertificatesFromJson(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Kullanıcının gıda tercihlerini yükler
     */
    public final void loadUserFoodPreferences(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
    
    /**
     * Kullanıcının belirli türdeki gıda tercihlerini yükler
     */
    public final void loadUserFoodPreferencesByType(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.FoodPreferenceType type) {
    }
    
    /**
     * Kullanıcının gıda tercihini kaydeder
     */
    public final void saveUserFoodPreference(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.FoodPreferenceType type, @org.jetbrains.annotations.NotNull()
    java.lang.String itemId, @org.jetbrains.annotations.NotNull()
    java.lang.String itemName) {
    }
    
    /**
     * Kullanıcının gıda tercihini siler
     */
    public final void deleteUserFoodPreference(@org.jetbrains.annotations.NotNull()
    java.lang.String preferenceId, @org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.FoodPreferenceType type) {
    }
    
    /**
     * Kullanıcının özel gıda tercihini ekler
     */
    public final void addCustomFoodPreference(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.FoodPreferenceType type, @org.jetbrains.annotations.NotNull()
    java.lang.String itemName) {
    }
    
    /**
     * Seçilen gıda tercihlerini günceller
     */
    public final void updateSelectedFoodPreference(@org.jetbrains.annotations.NotNull()
    java.lang.String itemId, boolean isSelected) {
    }
}