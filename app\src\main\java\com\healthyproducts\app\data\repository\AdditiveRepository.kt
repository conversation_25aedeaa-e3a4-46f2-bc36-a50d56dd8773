package com.healthyproducts.app.data.repository

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.healthyproducts.app.data.model.Additive
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Katkı maddeleri işlemlerini yöneten repository sınıfı
 */
@Singleton
class AdditiveRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    private val TAG = "AdditiveRepository"

    // Katkı maddeleri listesi
    private val _additives = MutableStateFlow<List<Additive>>(emptyList())
    val additives: StateFlow<List<Additive>> = _additives.asStateFlow()

    // Katkı maddeleri yükleniyor mu?
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // Katkı maddeleri yüklenirken hata oluştu mu?
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    /**
     * Tüm katkı maddelerini getirir
     */
    suspend fun getAllAdditives(): Result<List<Additive>> = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null

            Log.d(TAG, "Getting all additives")

            val snapshot = firestore.collection("additives")
                .get()
                .await()

            val additivesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Additive::class.java)
            }

            Log.d(TAG, "Retrieved ${additivesList.size} additives")

            _additives.value = additivesList
            _isLoading.value = false

            Result.success(additivesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting additives", e)
            _error.value = e.message
            _isLoading.value = false
            Result.failure(e)
        }
    }

    /**
     * Katkı maddesi ekler
     */
    suspend fun addAdditive(additive: Additive): Result<Additive> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding additive: ${additive.code} - ${additive.nameTr}")

            // Katkı maddesini Firestore'a ekle
            val documentRef = firestore.collection("additives").document(additive.code)
            documentRef.set(additive).await()

            // Katkı maddesini yerel listeye ekle
            val currentList = _additives.value.toMutableList()
            currentList.add(additive)
            _additives.value = currentList

            Log.d(TAG, "Additive added successfully")

            Result.success(additive)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding additive", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddesini günceller
     */
    suspend fun updateAdditive(additive: Additive): Result<Additive> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating additive: ${additive.code} - ${additive.nameTr}")

            // Katkı maddesini Firestore'da güncelle
            val documentRef = firestore.collection("additives").document(additive.code)
            documentRef.set(additive).await()

            // Katkı maddesini yerel listede güncelle
            val currentList = _additives.value.toMutableList()
            val index = currentList.indexOfFirst { it.code == additive.code }
            if (index != -1) {
                currentList[index] = additive
                _additives.value = currentList
            }

            Log.d(TAG, "Additive updated successfully")

            Result.success(additive)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating additive", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddesini siler
     */
    suspend fun deleteAdditive(code: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting additive: $code")

            // Katkı maddesini Firestore'dan sil
            val documentRef = firestore.collection("additives").document(code)
            documentRef.delete().await()

            // Katkı maddesini yerel listeden sil
            val currentList = _additives.value.toMutableList()
            val index = currentList.indexOfFirst { it.code == code }
            if (index != -1) {
                currentList.removeAt(index)
                _additives.value = currentList
            }

            Log.d(TAG, "Additive deleted successfully")

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting additive", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddesini kodu ile getirir
     */
    suspend fun getAdditiveByCode(code: String): Result<Additive?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting additive by code: $code")

            // Önce yerel listede ara
            val localAdditive = _additives.value.find { it.code == code }
            if (localAdditive != null) {
                Log.d(TAG, "Additive found in local cache")
                return@withContext Result.success(localAdditive)
            }

            // Yerel listede yoksa Firestore'dan getir
            val documentRef = firestore.collection("additives").document(code)
            val document = documentRef.get().await()

            val additive = document.toObject(Additive::class.java)

            if (additive != null) {
                Log.d(TAG, "Additive found in Firestore")
            } else {
                Log.d(TAG, "Additive not found")
            }

            Result.success(additive)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting additive by code", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddelerini kategoriye göre filtreler
     */
    suspend fun getAdditivesByCategory(category: String): Result<List<Additive>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting additives by category: $category")

            val snapshot = firestore.collection("additives")
                .whereEqualTo("category", category)
                .get()
                .await()

            val additivesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Additive::class.java)
            }

            Log.d(TAG, "Retrieved ${additivesList.size} additives for category: $category")

            Result.success(additivesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting additives by category", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddelerini helal durumuna göre filtreler
     */
    suspend fun getAdditivesByHalalStatus(halalStatus: String): Result<List<Additive>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting additives by halal status: $halalStatus")

            val snapshot = firestore.collection("additives")
                .whereEqualTo("halal_status", halalStatus)
                .get()
                .await()

            val additivesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Additive::class.java)
            }

            Log.d(TAG, "Retrieved ${additivesList.size} additives for halal status: $halalStatus")

            Result.success(additivesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting additives by halal status", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddelerini vegan durumuna göre filtreler
     */
    suspend fun getAdditivesByVeganStatus(veganStatus: String): Result<List<Additive>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting additives by vegan status: $veganStatus")

            val snapshot = firestore.collection("additives")
                .whereEqualTo("vegan_status", veganStatus)
                .get()
                .await()

            val additivesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Additive::class.java)
            }

            Log.d(TAG, "Retrieved ${additivesList.size} additives for vegan status: $veganStatus")

            Result.success(additivesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting additives by vegan status", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddelerini zararlılık seviyesine göre filtreler
     */
    suspend fun getAdditivesByHarmfulLevel(harmfulLevel: Int): Result<List<Additive>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting additives by harmful level: $harmfulLevel")

            val snapshot = firestore.collection("additives")
                .whereEqualTo("harmful_level", harmfulLevel)
                .get()
                .await()

            val additivesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Additive::class.java)
            }

            Log.d(TAG, "Retrieved ${additivesList.size} additives for harmful level: $harmfulLevel")

            Result.success(additivesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting additives by harmful level", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddelerini adına göre arar
     */
    suspend fun searchAdditivesByName(query: String): Result<List<Additive>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching additives by name: $query")

            // Firestore'da tam metin araması yapılamadığı için tüm katkı maddelerini getirip filtreliyoruz
            val snapshot = firestore.collection("additives")
                .get()
                .await()

            val additivesList = snapshot.documents.mapNotNull { document ->
                document.toObject(Additive::class.java)
            }.filter { additive ->
                additive.nameTr.contains(query, ignoreCase = true) ||
                additive.nameEn.contains(query, ignoreCase = true) ||
                additive.code.contains(query, ignoreCase = true)
            }

            Log.d(TAG, "Found ${additivesList.size} additives matching query: $query")

            Result.success(additivesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching additives by name", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dosyasından katkı maddelerini yükler ve Firestore'a kaydeder
     */
    suspend fun importAdditivesFromJson(context: Context): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Importing additives from JSON file")

            // JSON dosyasını oku
            val jsonString = try {
                val inputStream = context.assets.open("additivesList.json")
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                inputStream.close()
                String(buffer, Charsets.UTF_8)
            } catch (e: IOException) {
                Log.e(TAG, "Error reading additives JSON file", e)
                return@withContext Result.failure(e)
            }

            // JSON içeriğinin bir kısmını logla
            Log.d(TAG, "JSON content length: ${jsonString.length}")
            Log.d(TAG, "JSON content first 100 chars: ${jsonString.take(100)}")
            Log.d(TAG, "JSON content last 100 chars: ${jsonString.takeLast(100)}")

            // JSON'ı parse et
            val gson = GsonBuilder()
                .setLenient()
                .create()

            val jsonArray = org.json.JSONArray(jsonString)
            val additives = mutableListOf<Additive>()

            try {
                // JSON içeriğini logla
                Log.d(TAG, "JSON array length: ${jsonArray.length()}")

                // İlk birkaç JSON nesnesini logla
                for (i in 0 until minOf(3, jsonArray.length())) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    Log.d(TAG, "JSON object $i: ${jsonObject.toString().take(200)}...")
                }

                // JSON'ı manuel olarak parse et
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)

                    val additive = Additive(
                        id = jsonObject.optString("code", ""),
                        code = jsonObject.optString("code", ""),
                        nameTr = jsonObject.optString("name_tr", ""),
                        nameEn = jsonObject.optString("name_en", ""),
                        descriptionTr = jsonObject.optString("description_tr", ""),
                        descriptionEn = jsonObject.optString("description_en", ""),
                        category = jsonObject.optString("category", ""),
                        categoryTr = jsonObject.optString("category_tr", ""),
                        categoryEn = jsonObject.optString("category_en", ""),
                        originTr = jsonObject.optString("origin_tr", ""),
                        originEn = jsonObject.optString("origin_en", ""),
                        usageTr = jsonObject.optString("usage_tr", ""),
                        usageEn = jsonObject.optString("usage_en", ""),
                        healthEffectTr = jsonObject.optString("health_effect_tr", ""),
                        healthEffectEn = jsonObject.optString("health_effect_en", ""),
                        halalStatus = jsonObject.optString("halal_status", ""),
                        veganStatus = jsonObject.optString("vegan_status", ""),
                        vegetarianStatus = jsonObject.optString("vegetarian_status", ""),
                        kosherStatus = jsonObject.optString("kosher_status", ""),
                        harmfulLevel = jsonObject.optInt("harmful_level", 0),
                        unhealthyLevel = jsonObject.optInt("unhealthy_level", 0),
                        risky = jsonObject.optBoolean("risky", false),
                        notesTr = jsonObject.optString("notes_tr", ""),
                        notesEn = jsonObject.optString("notes_en", "")
                    )

                    additives.add(additive)
                }

                // İlk birkaç katkı maddesinin içeriğini detaylı logla
                additives.take(3).forEach { additive ->
                    Log.d(TAG, "Parsed additive: ${additive.code}")
                    Log.d(TAG, "  nameTr: '${additive.nameTr}'")
                    Log.d(TAG, "  nameEn: '${additive.nameEn}'")
                    Log.d(TAG, "  descriptionTr: '${additive.descriptionTr}'")
                    Log.d(TAG, "  categoryTr: '${additive.categoryTr}'")
                    Log.d(TAG, "  categoryEn: '${additive.categoryEn}'")
                    Log.d(TAG, "  halalStatus: '${additive.halalStatus}'")
                    Log.d(TAG, "  veganStatus: '${additive.veganStatus}'")
                }

                Log.d(TAG, "Parsed ${additives.size} additives from JSON")

                // Firestore'a kaydet
                var successCount = 0
                additives.forEach { additive ->
                    try {
                        // Additive nesnesini Map'e dönüştür
                        val additiveMap = mapOf(
                            "code" to additive.code,
                            "name_tr" to additive.nameTr,
                            "name_en" to additive.nameEn,
                            "description_tr" to additive.descriptionTr,
                            "description_en" to additive.descriptionEn,
                            "category" to additive.category,
                            "category_tr" to additive.categoryTr,
                            "category_en" to additive.categoryEn,
                            "origin_tr" to additive.originTr,
                            "origin_en" to additive.originEn,
                            "usage_tr" to additive.usageTr,
                            "usage_en" to additive.usageEn,
                            "health_effect_tr" to additive.healthEffectTr,
                            "health_effect_en" to additive.healthEffectEn,
                            "halal_status" to additive.halalStatus,
                            "vegan_status" to additive.veganStatus,
                            "vegetarian_status" to additive.vegetarianStatus,
                            "kosher_status" to additive.kosherStatus,
                            "harmful_level" to additive.harmfulLevel,
                            "unhealthy_level" to additive.unhealthyLevel,
                            "risky" to additive.risky,
                            "notes_tr" to additive.notesTr,
                            "notes_en" to additive.notesEn
                        )

                        val documentRef = firestore.collection("additives").document(additive.code)

                        // Map içeriğini logla
                        Log.d(TAG, "Saving additive to Firestore: ${additive.code}")
                        Log.d(TAG, "  Map content: ${additiveMap.entries.joinToString { "${it.key}='${it.value}'" }}")

                        documentRef.set(additiveMap).await()

                        // Kaydedilen veriyi kontrol et
                        val savedDoc = documentRef.get().await()
                        Log.d(TAG, "  Saved document: ${savedDoc.data?.entries?.joinToString { "${it.key}='${it.value}'" }}")

                        successCount++
                    } catch (e: Exception) {
                        Log.e(TAG, "Error importing additive ${additive.code}", e)
                    }
                }

                Log.d(TAG, "Successfully imported $successCount out of ${additives.size} additives")

                Result.success(successCount)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing JSON", e)
                return@withContext Result.failure(e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error importing additives from JSON", e)
            Result.failure(e)
        }
    }
}
