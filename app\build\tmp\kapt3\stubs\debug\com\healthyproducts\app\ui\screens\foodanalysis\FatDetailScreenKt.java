package com.healthyproducts.app.ui.screens.foodanalysis;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\n"}, d2 = {"FatDetailScreen", "", "navController", "Landroidx/navigation/NavController;", "fatCode", "", "foodAnalysisViewModel", "Lcom/healthyproducts/app/ui/viewmodel/FoodAnalysisViewModel;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "app_debug"})
public final class FatDetailScreenKt {
    
    /**
     * Yağ detay ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void FatDetailScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String fatCode, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel foodAnalysisViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
}