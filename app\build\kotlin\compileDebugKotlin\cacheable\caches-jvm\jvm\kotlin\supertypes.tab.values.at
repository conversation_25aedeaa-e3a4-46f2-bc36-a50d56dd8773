/ Header Record For PersistentHashMapValueStorage android.app.Application$ #androidx.activity.ComponentActivity+ *com.healthyproducts.app.data.api.AiService kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen* )com.healthyproducts.app.navigation.Screen androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum6 5com.healthyproducts.app.ui.screens.admin.UploadStatus6 5com.healthyproducts.app.ui.screens.admin.UploadStatus6 5com.healthyproducts.app.ui.screens.admin.UploadStatus androidx.lifecycle.ViewModelD Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatusD Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatusD Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatusD Ccom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus androidx.lifecycle.ViewModelG Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesStateG Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesStateG Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesStateG Fcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState androidx.lifecycle.ViewModel4 3com.healthyproducts.app.ui.viewmodel.AllergensState4 3com.healthyproducts.app.ui.viewmodel.AllergensState4 3com.healthyproducts.app.ui.viewmodel.AllergensState/ .com.healthyproducts.app.ui.viewmodel.FatsState/ .com.healthyproducts.app.ui.viewmodel.FatsState/ .com.healthyproducts.app.ui.viewmodel.FatsState1 0com.healthyproducts.app.ui.viewmodel.SugarsState1 0com.healthyproducts.app.ui.viewmodel.SugarsState1 0com.healthyproducts.app.ui.viewmodel.SugarsState7 6com.healthyproducts.app.ui.viewmodel.CertificatesState7 6com.healthyproducts.app.ui.viewmodel.CertificatesState7 6com.healthyproducts.app.ui.viewmodel.CertificatesState2 1com.healthyproducts.app.ui.viewmodel.ImportStatus2 1com.healthyproducts.app.ui.viewmodel.ImportStatus2 1com.healthyproducts.app.ui.viewmodel.ImportStatus2 1com.healthyproducts.app.ui.viewmodel.ImportStatus3 2com.healthyproducts.app.ui.viewmodel.AnalysisState3 2com.healthyproducts.app.ui.viewmodel.AnalysisState3 2com.healthyproducts.app.ui.viewmodel.AnalysisState3 2com.healthyproducts.app.ui.viewmodel.AnalysisState8 7com.healthyproducts.app.ui.viewmodel.PreservativesState8 7com.healthyproducts.app.ui.viewmodel.PreservativesState8 7com.healthyproducts.app.ui.viewmodel.PreservativesState7 6com.healthyproducts.app.ui.viewmodel.PreservativeState7 6com.healthyproducts.app.ui.viewmodel.PreservativeState7 6com.healthyproducts.app.ui.viewmodel.PreservativeState7 6com.healthyproducts.app.ui.viewmodel.PreservativeState> =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState> =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState> =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState> =com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModelK Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryStateK Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryStateK Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryStateK Jcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState$ #androidx.lifecycle.AndroidViewModel= <com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState= <com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState= <com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState= <com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState@ ?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState@ ?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState@ ?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState@ ?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanStateC Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionStateC Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionStateC Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionStateC Bcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState androidx.lifecycle.ViewModel= <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState= <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState= <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState= <com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState, +androidx.camera.core.ImageAnalysis.Analyzer kotlin.Enum kotlin.Enum, +androidx.camera.core.ImageAnalysis.Analyzer$ #androidx.activity.ComponentActivity kotlin.Enum6 5com.healthyproducts.app.ui.screens.admin.UploadStatus6 5com.healthyproducts.app.ui.screens.admin.UploadStatus6 5com.healthyproducts.app.ui.screens.admin.UploadStatus