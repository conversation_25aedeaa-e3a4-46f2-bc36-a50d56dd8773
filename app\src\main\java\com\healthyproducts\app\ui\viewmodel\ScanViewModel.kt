package com.healthyproducts.app.ui.viewmodel

import android.app.Application
import android.content.Context
import androidx.camera.core.ImageCapture
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.google.mlkit.vision.barcode.common.Barcode
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.repository.FirestoreRepository
import com.healthyproducts.app.data.repository.OcrCorrectionRepository
import com.healthyproducts.app.data.repository.ScanHistoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import com.healthyproducts.app.model.ScanType
import com.healthyproducts.app.ui.components.FrameSize
import com.healthyproducts.app.ui.components.captureImage
import com.healthyproducts.app.util.CameraPermissionHelper
import com.healthyproducts.app.util.CameraPermissionState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Tarama ekranı için ViewModel
 */
@HiltViewModel
class ScanViewModel @Inject constructor(
    application: Application,
    private val scanHistoryRepository: ScanHistoryRepository,
    private val firestoreRepository: FirestoreRepository,
    private val ocrCorrectionRepository: OcrCorrectionRepository
) : AndroidViewModel(application) {

    // Kamera izni durumu
    private val _cameraPermissionState = MutableStateFlow<CameraPermissionState?>(null)
    val cameraPermissionState: StateFlow<CameraPermissionState?> = _cameraPermissionState.asStateFlow()

    // Tarama durumu
    private val _scanState = MutableStateFlow<ScanState>(ScanState.Idle)
    val scanState: StateFlow<ScanState> = _scanState.asStateFlow()

    // OCR tarama durumu
    private val _ocrScanState = MutableStateFlow<OcrScanState>(OcrScanState.Idle)
    val ocrScanState: StateFlow<OcrScanState> = _ocrScanState.asStateFlow()

    // Tanınan içerikler
    private val _recognizedIngredients = MutableStateFlow<List<String>>(emptyList())
    val recognizedIngredients: StateFlow<List<String>> = _recognizedIngredients.asStateFlow()

    // Ham OCR sonucu (düzeltilmemiş)
    private val _rawOcrText = MutableStateFlow<String>("")
    val rawOcrText: StateFlow<String> = _rawOcrText.asStateFlow()

    // Düzeltme durumu
    private val _correctionState = MutableStateFlow<CorrectionState>(CorrectionState.Idle)
    val correctionState: StateFlow<CorrectionState> = _correctionState.asStateFlow()

    // İçerik düzeltme modu
    private val _correctionMode = MutableStateFlow(false)
    val correctionMode: StateFlow<Boolean> = _correctionMode.asStateFlow()

    // Çerçeve boyutu
    private val _frameSize = MutableStateFlow(FrameSize.MEDIUM)
    val frameSize: StateFlow<FrameSize> = _frameSize.asStateFlow()

    // Kullanıcı dili
    val userLanguage: StateFlow<SupportedLanguage> = firestoreRepository.userLanguage

    /**
     * Kamera izni durumunu günceller
     */
    fun updateCameraPermissionState(state: CameraPermissionState) {
        _cameraPermissionState.value = state
    }

    /**
     * Kamera izni durumunu kontrol eder
     */
    fun checkCameraPermission(context: Context) {
        _cameraPermissionState.value = CameraPermissionHelper.checkCameraPermission(context)
    }

    /**
     * Barkod algılandığında çağrılır
     */
    fun onBarcodeDetected(barcodes: List<Barcode>) {
        if (barcodes.isEmpty() || _scanState.value is ScanState.Success) {
            return
        }

        val barcode = barcodes.firstOrNull()
        val barcodeValue = barcode?.rawValue

        if (barcodeValue != null) {
            _scanState.value = ScanState.Success(barcodeValue)

            // Tarama geçmişine ekle (kullanıcı giriş yapmışsa)
            val userId = getCurrentUserId()
            if (userId != null) {
                viewModelScope.launch {
                    scanHistoryRepository.addToScanHistory(
                        userId = userId,
                        barcode = barcodeValue,
                        scanType = ScanType.BARCODE
                    )
                }
            }
        }
    }

    /**
     * Metin algılandığında çağrılır
     */
    fun onTextDetected(text: com.google.mlkit.vision.text.Text) {
        if (_ocrScanState.value is OcrScanState.Success) {
            return
        }

        val detectedText = text.text

        if (detectedText.isNotEmpty()) {
            // Ham OCR metnini kaydet
            _rawOcrText.value = detectedText

            // Metin içindeki içerikleri ayır
            val ingredients = parseIngredients(detectedText)

            if (ingredients.isNotEmpty()) {
                // Ham içerikleri kaydet
                _recognizedIngredients.value = ingredients
                _ocrScanState.value = OcrScanState.Success(detectedText)

                // Düzeltme modu aktifse DeepSeek API ile içerikleri düzelt
                if (_correctionMode.value) {
                    correctIngredients(ingredients)
                }

                // Tarama geçmişine ekle (kullanıcı giriş yapmışsa)
                val userId = getCurrentUserId()
                if (userId != null) {
                    viewModelScope.launch {
                        scanHistoryRepository.addToScanHistory(
                            userId = userId,
                            imageUrl = null, // OCR taraması için resim URL'si yok
                            scanType = ScanType.OCR
                        )
                    }
                }
            } else {
                _ocrScanState.value = OcrScanState.Error("İçerik listesi bulunamadı")
            }
        }
    }

    /**
     * İçerikleri DeepSeek API ile düzeltir
     */
    private fun correctIngredients(ingredients: List<String>) {
        _correctionState.value = CorrectionState.Correcting

        viewModelScope.launch {
            // Kullanıcının dil tercihini al
            val language = userLanguage.value

            ocrCorrectionRepository.correctOcrIngredients(ingredients, language)
                .onSuccess { correctedIngredients ->
                    _recognizedIngredients.value = correctedIngredients
                    _correctionState.value = CorrectionState.Success
                }
                .onFailure { error ->
                    // Düzeltme başarısız oldu, orijinal içerikleri kullan
                    _correctionState.value = CorrectionState.Error(error.message ?: "Düzeltme hatası")
                }
        }
    }

    /**
     * Metin içindeki içerikleri ayırır
     */
    private fun parseIngredients(text: String): List<String> {
        // Basit bir içerik ayrıştırma algoritması
        // Gerçek uygulamada daha gelişmiş bir algoritma kullanılmalı

        // İçerik listesi genellikle "İçindekiler:" veya "Ingredients:" ile başlar
        val ingredientsText = text.lowercase()
            .replace("\\s+".toRegex(), " ") // Fazla boşlukları temizle

        // İçerikleri virgül veya nokta ile ayır
        val ingredients = ingredientsText.split("[,.]".toRegex())
            .map { it.trim() }
            .filter { it.isNotEmpty() && it.length > 2 } // Çok kısa olanları filtrele

        return ingredients
    }

    /**
     * Tarama durumunu sıfırlar
     */
    fun resetScanState() {
        _scanState.value = ScanState.Idle
    }

    /**
     * OCR tarama durumunu sıfırlar
     */
    fun resetOcrScanState() {
        _ocrScanState.value = OcrScanState.Idle
        _recognizedIngredients.value = emptyList()
        _rawOcrText.value = ""
        _correctionMode.value = false
        _correctionState.value = CorrectionState.Idle
    }

    /**
     * İçerik düzeltme modunu ayarlar
     */
    fun setCorrectionMode(enabled: Boolean) {
        _correctionMode.value = enabled

        // Düzeltme modu aktifleştirildiğinde ve içerikler varsa düzeltme işlemini başlat
        if (enabled && _recognizedIngredients.value.isNotEmpty()) {
            correctIngredients(_recognizedIngredients.value)
        }
    }

    /**
     * Ham OCR sonucunu içerik listesine dönüştürür
     */
    fun parseRawOcrToIngredients() {
        val rawText = _rawOcrText.value
        if (rawText.isNotEmpty()) {
            val ingredients = parseIngredients(rawText)
            if (ingredients.isNotEmpty()) {
                _recognizedIngredients.value = ingredients
            }
        }
    }

    /**
     * Tanınan içerikleri günceller
     */
    fun updateRecognizedIngredients(ingredients: List<String>) {
        _recognizedIngredients.value = ingredients

        // Düzeltilen içerikleri önbelleğe kaydet
        viewModelScope.launch {
            val context = getApplication<Application>().applicationContext
            val sharedPrefs = context.getSharedPreferences("OCR_CORRECTIONS", Context.MODE_PRIVATE)
            val editor = sharedPrefs.edit()

            ingredients.forEach { ingredient ->
                // İçeriği bir anahtar olarak kaydet (ileride öneri olarak kullanılabilir)
                editor.putString(ingredient.lowercase(), ingredient)
            }

            editor.apply()
        }
    }

    /**
     * Kamera görüntüsünü yakala ve analiz et
     */
    fun captureAndAnalyzeImage() {
        _ocrScanState.value = OcrScanState.Scanning

        // SharedPreferences aracılığıyla OcrCameraPreview'a görüntü yakalama sinyali gönder
        val context = getApplication<android.app.Application>().applicationContext
        context.getSharedPreferences("OCR_PREFS", Context.MODE_PRIVATE)
            .edit()
            .putBoolean("CAPTURE_IMAGE", true)
            .apply()
    }

    /**
     * Çerçeve boyutunu günceller
     */
    fun updateFrameSize(size: FrameSize) {
        _frameSize.value = size
    }

    /**
     * Mevcut kullanıcı ID'sini alır
     */
    private fun getCurrentUserId(): String? {
        return firestoreRepository.authRepository.getCurrentUser()?.uid
    }

    /**
     * Tarama durumunu temsil eden sealed class
     */
    sealed class ScanState {
        object Idle : ScanState()
        object Scanning : ScanState()
        data class Success(val barcode: String) : ScanState()
        data class Error(val message: String) : ScanState()
    }

    /**
     * OCR tarama durumunu temsil eden sealed class
     */
    sealed class OcrScanState {
        object Idle : OcrScanState()
        object Scanning : OcrScanState()
        data class Success(val text: String) : OcrScanState()
        data class Error(val message: String) : OcrScanState()
    }

    /**
     * Düzeltme durumunu temsil eden sealed class
     */
    sealed class CorrectionState {
        object Idle : CorrectionState()
        object Correcting : CorrectionState()
        object Success : CorrectionState()
        data class Error(val message: String) : CorrectionState()
    }
}
