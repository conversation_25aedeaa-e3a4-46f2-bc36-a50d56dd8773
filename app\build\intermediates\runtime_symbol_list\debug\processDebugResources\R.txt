int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim fragment_fast_out_extra_slow_in 0x7f010018
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int attr action 0x7f030000
int attr actionBarDivider 0x7f030001
int attr actionBarItemBackground 0x7f030002
int attr actionBarPopupTheme 0x7f030003
int attr actionBarSize 0x7f030004
int attr actionBarSplitStyle 0x7f030005
int attr actionBarStyle 0x7f030006
int attr actionBarTabBarStyle 0x7f030007
int attr actionBarTabStyle 0x7f030008
int attr actionBarTabTextStyle 0x7f030009
int attr actionBarTheme 0x7f03000a
int attr actionBarWidgetTheme 0x7f03000b
int attr actionButtonStyle 0x7f03000c
int attr actionDropDownStyle 0x7f03000d
int attr actionLayout 0x7f03000e
int attr actionMenuTextAppearance 0x7f03000f
int attr actionMenuTextColor 0x7f030010
int attr actionModeBackground 0x7f030011
int attr actionModeCloseButtonStyle 0x7f030012
int attr actionModeCloseContentDescription 0x7f030013
int attr actionModeCloseDrawable 0x7f030014
int attr actionModeCopyDrawable 0x7f030015
int attr actionModeCutDrawable 0x7f030016
int attr actionModeFindDrawable 0x7f030017
int attr actionModePasteDrawable 0x7f030018
int attr actionModePopupWindowStyle 0x7f030019
int attr actionModeSelectAllDrawable 0x7f03001a
int attr actionModeShareDrawable 0x7f03001b
int attr actionModeSplitBackground 0x7f03001c
int attr actionModeStyle 0x7f03001d
int attr actionModeTheme 0x7f03001e
int attr actionModeWebSearchDrawable 0x7f03001f
int attr actionOverflowButtonStyle 0x7f030020
int attr actionOverflowMenuStyle 0x7f030021
int attr actionProviderClass 0x7f030022
int attr actionViewClass 0x7f030023
int attr activityChooserViewStyle 0x7f030024
int attr alertDialogButtonGroupStyle 0x7f030025
int attr alertDialogCenterButtons 0x7f030026
int attr alertDialogStyle 0x7f030027
int attr alertDialogTheme 0x7f030028
int attr allowStacking 0x7f030029
int attr alpha 0x7f03002a
int attr alphabeticModifiers 0x7f03002b
int attr argType 0x7f03002c
int attr arrowHeadLength 0x7f03002d
int attr arrowShaftLength 0x7f03002e
int attr autoCompleteTextViewStyle 0x7f03002f
int attr autoSizeMaxTextSize 0x7f030030
int attr autoSizeMinTextSize 0x7f030031
int attr autoSizePresetSizes 0x7f030032
int attr autoSizeStepGranularity 0x7f030033
int attr autoSizeTextType 0x7f030034
int attr background 0x7f030035
int attr backgroundSplit 0x7f030036
int attr backgroundStacked 0x7f030037
int attr backgroundTint 0x7f030038
int attr backgroundTintMode 0x7f030039
int attr barLength 0x7f03003a
int attr borderlessButtonStyle 0x7f03003b
int attr buttonBarButtonStyle 0x7f03003c
int attr buttonBarNegativeButtonStyle 0x7f03003d
int attr buttonBarNeutralButtonStyle 0x7f03003e
int attr buttonBarPositiveButtonStyle 0x7f03003f
int attr buttonBarStyle 0x7f030040
int attr buttonCompat 0x7f030041
int attr buttonGravity 0x7f030042
int attr buttonIconDimen 0x7f030043
int attr buttonPanelSideLayout 0x7f030044
int attr buttonSize 0x7f030045
int attr buttonStyle 0x7f030046
int attr buttonStyleSmall 0x7f030047
int attr buttonTint 0x7f030048
int attr buttonTintMode 0x7f030049
int attr cardBackgroundColor 0x7f03004a
int attr cardCornerRadius 0x7f03004b
int attr cardElevation 0x7f03004c
int attr cardMaxElevation 0x7f03004d
int attr cardPreventCornerOverlap 0x7f03004e
int attr cardUseCompatPadding 0x7f03004f
int attr cardViewStyle 0x7f030050
int attr checkMarkCompat 0x7f030051
int attr checkMarkTint 0x7f030052
int attr checkMarkTintMode 0x7f030053
int attr checkboxStyle 0x7f030054
int attr checkedTextViewStyle 0x7f030055
int attr circleCrop 0x7f030056
int attr closeIcon 0x7f030057
int attr closeItemLayout 0x7f030058
int attr collapseContentDescription 0x7f030059
int attr collapseIcon 0x7f03005a
int attr color 0x7f03005b
int attr colorAccent 0x7f03005c
int attr colorBackgroundFloating 0x7f03005d
int attr colorButtonNormal 0x7f03005e
int attr colorControlActivated 0x7f03005f
int attr colorControlHighlight 0x7f030060
int attr colorControlNormal 0x7f030061
int attr colorError 0x7f030062
int attr colorPrimary 0x7f030063
int attr colorPrimaryDark 0x7f030064
int attr colorScheme 0x7f030065
int attr colorSwitchThumbNormal 0x7f030066
int attr com_facebook_auxiliary_view_position 0x7f030067
int attr com_facebook_confirm_logout 0x7f030068
int attr com_facebook_foreground_color 0x7f030069
int attr com_facebook_horizontal_alignment 0x7f03006a
int attr com_facebook_is_cropped 0x7f03006b
int attr com_facebook_login_button_radius 0x7f03006c
int attr com_facebook_login_button_transparency 0x7f03006d
int attr com_facebook_login_text 0x7f03006e
int attr com_facebook_logout_text 0x7f03006f
int attr com_facebook_object_id 0x7f030070
int attr com_facebook_object_type 0x7f030071
int attr com_facebook_preset_size 0x7f030072
int attr com_facebook_style 0x7f030073
int attr com_facebook_tooltip_mode 0x7f030074
int attr commitIcon 0x7f030075
int attr contentDescription 0x7f030076
int attr contentInsetEnd 0x7f030077
int attr contentInsetEndWithActions 0x7f030078
int attr contentInsetLeft 0x7f030079
int attr contentInsetRight 0x7f03007a
int attr contentInsetStart 0x7f03007b
int attr contentInsetStartWithNavigation 0x7f03007c
int attr contentPadding 0x7f03007d
int attr contentPaddingBottom 0x7f03007e
int attr contentPaddingLeft 0x7f03007f
int attr contentPaddingRight 0x7f030080
int attr contentPaddingTop 0x7f030081
int attr controlBackground 0x7f030082
int attr coordinatorLayoutStyle 0x7f030083
int attr customNavigationLayout 0x7f030084
int attr data 0x7f030085
int attr dataPattern 0x7f030086
int attr defaultQueryHint 0x7f030087
int attr destination 0x7f030088
int attr dialogCornerRadius 0x7f030089
int attr dialogPreferredPadding 0x7f03008a
int attr dialogTheme 0x7f03008b
int attr displayOptions 0x7f03008c
int attr divider 0x7f03008d
int attr dividerHorizontal 0x7f03008e
int attr dividerPadding 0x7f03008f
int attr dividerVertical 0x7f030090
int attr drawableBottomCompat 0x7f030091
int attr drawableEndCompat 0x7f030092
int attr drawableLeftCompat 0x7f030093
int attr drawableRightCompat 0x7f030094
int attr drawableSize 0x7f030095
int attr drawableStartCompat 0x7f030096
int attr drawableTint 0x7f030097
int attr drawableTintMode 0x7f030098
int attr drawableTopCompat 0x7f030099
int attr drawerArrowStyle 0x7f03009a
int attr dropDownListViewStyle 0x7f03009b
int attr dropdownListPreferredItemHeight 0x7f03009c
int attr editTextBackground 0x7f03009d
int attr editTextColor 0x7f03009e
int attr editTextStyle 0x7f03009f
int attr elevation 0x7f0300a0
int attr emojiCompatEnabled 0x7f0300a1
int attr enterAnim 0x7f0300a2
int attr exitAnim 0x7f0300a3
int attr expandActivityOverflowButtonDrawable 0x7f0300a4
int attr firstBaselineToTopHeight 0x7f0300a5
int attr font 0x7f0300a6
int attr fontFamily 0x7f0300a7
int attr fontProviderAuthority 0x7f0300a8
int attr fontProviderCerts 0x7f0300a9
int attr fontProviderFetchStrategy 0x7f0300aa
int attr fontProviderFetchTimeout 0x7f0300ab
int attr fontProviderPackage 0x7f0300ac
int attr fontProviderQuery 0x7f0300ad
int attr fontProviderSystemFontFamily 0x7f0300ae
int attr fontStyle 0x7f0300af
int attr fontVariationSettings 0x7f0300b0
int attr fontWeight 0x7f0300b1
int attr gapBetweenBars 0x7f0300b2
int attr goIcon 0x7f0300b3
int attr graph 0x7f0300b4
int attr height 0x7f0300b5
int attr hideOnContentScroll 0x7f0300b6
int attr homeAsUpIndicator 0x7f0300b7
int attr homeLayout 0x7f0300b8
int attr icon 0x7f0300b9
int attr iconTint 0x7f0300ba
int attr iconTintMode 0x7f0300bb
int attr iconifiedByDefault 0x7f0300bc
int attr imageAspectRatio 0x7f0300bd
int attr imageAspectRatioAdjust 0x7f0300be
int attr imageButtonStyle 0x7f0300bf
int attr implementationMode 0x7f0300c0
int attr indeterminateProgressStyle 0x7f0300c1
int attr initialActivityCount 0x7f0300c2
int attr isLightTheme 0x7f0300c3
int attr itemPadding 0x7f0300c4
int attr keylines 0x7f0300c5
int attr lStar 0x7f0300c6
int attr lastBaselineToBottomHeight 0x7f0300c7
int attr launchSingleTop 0x7f0300c8
int attr layout 0x7f0300c9
int attr layout_anchor 0x7f0300ca
int attr layout_anchorGravity 0x7f0300cb
int attr layout_behavior 0x7f0300cc
int attr layout_dodgeInsetEdges 0x7f0300cd
int attr layout_insetEdge 0x7f0300ce
int attr layout_keyline 0x7f0300cf
int attr lineHeight 0x7f0300d0
int attr listChoiceBackgroundIndicator 0x7f0300d1
int attr listChoiceIndicatorMultipleAnimated 0x7f0300d2
int attr listChoiceIndicatorSingleAnimated 0x7f0300d3
int attr listDividerAlertDialog 0x7f0300d4
int attr listItemLayout 0x7f0300d5
int attr listLayout 0x7f0300d6
int attr listMenuViewStyle 0x7f0300d7
int attr listPopupWindowStyle 0x7f0300d8
int attr listPreferredItemHeight 0x7f0300d9
int attr listPreferredItemHeightLarge 0x7f0300da
int attr listPreferredItemHeightSmall 0x7f0300db
int attr listPreferredItemPaddingEnd 0x7f0300dc
int attr listPreferredItemPaddingLeft 0x7f0300dd
int attr listPreferredItemPaddingRight 0x7f0300de
int attr listPreferredItemPaddingStart 0x7f0300df
int attr logo 0x7f0300e0
int attr logoDescription 0x7f0300e1
int attr maxButtonHeight 0x7f0300e2
int attr measureWithLargestChild 0x7f0300e3
int attr menu 0x7f0300e4
int attr mimeType 0x7f0300e5
int attr multiChoiceItemLayout 0x7f0300e6
int attr navGraph 0x7f0300e7
int attr navigationContentDescription 0x7f0300e8
int attr navigationIcon 0x7f0300e9
int attr navigationMode 0x7f0300ea
int attr nestedScrollViewStyle 0x7f0300eb
int attr nullable 0x7f0300ec
int attr numericModifiers 0x7f0300ed
int attr overlapAnchor 0x7f0300ee
int attr paddingBottomNoButtons 0x7f0300ef
int attr paddingEnd 0x7f0300f0
int attr paddingStart 0x7f0300f1
int attr paddingTopNoTitle 0x7f0300f2
int attr panelBackground 0x7f0300f3
int attr panelMenuListTheme 0x7f0300f4
int attr panelMenuListWidth 0x7f0300f5
int attr popEnterAnim 0x7f0300f6
int attr popExitAnim 0x7f0300f7
int attr popUpTo 0x7f0300f8
int attr popUpToInclusive 0x7f0300f9
int attr popUpToSaveState 0x7f0300fa
int attr popupMenuStyle 0x7f0300fb
int attr popupTheme 0x7f0300fc
int attr popupWindowStyle 0x7f0300fd
int attr preserveIconSpacing 0x7f0300fe
int attr progressBarPadding 0x7f0300ff
int attr progressBarStyle 0x7f030100
int attr queryBackground 0x7f030101
int attr queryHint 0x7f030102
int attr queryPatterns 0x7f030103
int attr radioButtonStyle 0x7f030104
int attr ratingBarStyle 0x7f030105
int attr ratingBarStyleIndicator 0x7f030106
int attr ratingBarStyleSmall 0x7f030107
int attr restoreState 0x7f030108
int attr route 0x7f030109
int attr scaleType 0x7f03010a
int attr scopeUris 0x7f03010b
int attr searchHintIcon 0x7f03010c
int attr searchIcon 0x7f03010d
int attr searchViewStyle 0x7f03010e
int attr seekBarStyle 0x7f03010f
int attr selectableItemBackground 0x7f030110
int attr selectableItemBackgroundBorderless 0x7f030111
int attr shortcutMatchRequired 0x7f030112
int attr showAsAction 0x7f030113
int attr showDividers 0x7f030114
int attr showText 0x7f030115
int attr showTitle 0x7f030116
int attr singleChoiceItemLayout 0x7f030117
int attr spinBars 0x7f030118
int attr spinnerDropDownItemStyle 0x7f030119
int attr spinnerStyle 0x7f03011a
int attr splitTrack 0x7f03011b
int attr srcCompat 0x7f03011c
int attr startDestination 0x7f03011d
int attr state_above_anchor 0x7f03011e
int attr statusBarBackground 0x7f03011f
int attr subMenuArrow 0x7f030120
int attr submitBackground 0x7f030121
int attr subtitle 0x7f030122
int attr subtitleTextAppearance 0x7f030123
int attr subtitleTextColor 0x7f030124
int attr subtitleTextStyle 0x7f030125
int attr suggestionRowLayout 0x7f030126
int attr switchMinWidth 0x7f030127
int attr switchPadding 0x7f030128
int attr switchStyle 0x7f030129
int attr switchTextAppearance 0x7f03012a
int attr targetPackage 0x7f03012b
int attr textAllCaps 0x7f03012c
int attr textAppearanceLargePopupMenu 0x7f03012d
int attr textAppearanceListItem 0x7f03012e
int attr textAppearanceListItemSecondary 0x7f03012f
int attr textAppearanceListItemSmall 0x7f030130
int attr textAppearancePopupMenuHeader 0x7f030131
int attr textAppearanceSearchResultSubtitle 0x7f030132
int attr textAppearanceSearchResultTitle 0x7f030133
int attr textAppearanceSmallPopupMenu 0x7f030134
int attr textColorAlertDialogListItem 0x7f030135
int attr textColorSearchUrl 0x7f030136
int attr textLocale 0x7f030137
int attr theme 0x7f030138
int attr thickness 0x7f030139
int attr thumbTextPadding 0x7f03013a
int attr thumbTint 0x7f03013b
int attr thumbTintMode 0x7f03013c
int attr tickMark 0x7f03013d
int attr tickMarkTint 0x7f03013e
int attr tickMarkTintMode 0x7f03013f
int attr tint 0x7f030140
int attr tintMode 0x7f030141
int attr title 0x7f030142
int attr titleMargin 0x7f030143
int attr titleMarginBottom 0x7f030144
int attr titleMarginEnd 0x7f030145
int attr titleMarginStart 0x7f030146
int attr titleMarginTop 0x7f030147
int attr titleMargins 0x7f030148
int attr titleTextAppearance 0x7f030149
int attr titleTextColor 0x7f03014a
int attr titleTextStyle 0x7f03014b
int attr toolbarNavigationButtonStyle 0x7f03014c
int attr toolbarStyle 0x7f03014d
int attr tooltipForegroundColor 0x7f03014e
int attr tooltipFrameBackground 0x7f03014f
int attr tooltipText 0x7f030150
int attr track 0x7f030151
int attr trackTint 0x7f030152
int attr trackTintMode 0x7f030153
int attr ttcIndex 0x7f030154
int attr uri 0x7f030155
int attr viewInflaterClass 0x7f030156
int attr voiceIcon 0x7f030157
int attr windowActionBar 0x7f030158
int attr windowActionBarOverlay 0x7f030159
int attr windowActionModeOverlay 0x7f03015a
int attr windowFixedHeightMajor 0x7f03015b
int attr windowFixedHeightMinor 0x7f03015c
int attr windowFixedWidthMajor 0x7f03015d
int attr windowFixedWidthMinor 0x7f03015e
int attr windowMinWidthMajor 0x7f03015f
int attr windowMinWidthMinor 0x7f030160
int attr windowNoTitle 0x7f030161
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_config_actionMenuItemAllCaps 0x7f040001
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color androidx_core_ripple_material_light 0x7f05001b
int color androidx_core_secondary_text_default_material_light 0x7f05001c
int color background_floating_material_dark 0x7f05001d
int color background_floating_material_light 0x7f05001e
int color background_material_dark 0x7f05001f
int color background_material_light 0x7f050020
int color black 0x7f050021
int color bright_foreground_disabled_material_dark 0x7f050022
int color bright_foreground_disabled_material_light 0x7f050023
int color bright_foreground_inverse_material_dark 0x7f050024
int color bright_foreground_inverse_material_light 0x7f050025
int color bright_foreground_material_dark 0x7f050026
int color bright_foreground_material_light 0x7f050027
int color browser_actions_bg_grey 0x7f050028
int color browser_actions_divider_color 0x7f050029
int color browser_actions_text_color 0x7f05002a
int color browser_actions_title_color 0x7f05002b
int color button_material_dark 0x7f05002c
int color button_material_light 0x7f05002d
int color call_notification_answer_color 0x7f05002e
int color call_notification_decline_color 0x7f05002f
int color cardview_dark_background 0x7f050030
int color cardview_light_background 0x7f050031
int color cardview_shadow_end_color 0x7f050032
int color cardview_shadow_start_color 0x7f050033
int color com_facebook_blue 0x7f050034
int color com_facebook_button_background_color 0x7f050035
int color com_facebook_button_background_color_disabled 0x7f050036
int color com_facebook_button_background_color_pressed 0x7f050037
int color com_facebook_button_text_color 0x7f050038
int color com_facebook_device_auth_text 0x7f050039
int color com_facebook_likeboxcountview_border_color 0x7f05003a
int color com_facebook_likeboxcountview_text_color 0x7f05003b
int color com_facebook_likeview_text_color 0x7f05003c
int color com_facebook_primary_button_disabled_text_color 0x7f05003d
int color com_facebook_primary_button_pressed_text_color 0x7f05003e
int color com_facebook_primary_button_text_color 0x7f05003f
int color com_smart_login_code 0x7f050040
int color common_google_signin_btn_text_dark 0x7f050041
int color common_google_signin_btn_text_dark_default 0x7f050042
int color common_google_signin_btn_text_dark_disabled 0x7f050043
int color common_google_signin_btn_text_dark_focused 0x7f050044
int color common_google_signin_btn_text_dark_pressed 0x7f050045
int color common_google_signin_btn_text_light 0x7f050046
int color common_google_signin_btn_text_light_default 0x7f050047
int color common_google_signin_btn_text_light_disabled 0x7f050048
int color common_google_signin_btn_text_light_focused 0x7f050049
int color common_google_signin_btn_text_light_pressed 0x7f05004a
int color common_google_signin_btn_tint 0x7f05004b
int color dim_foreground_disabled_material_dark 0x7f05004c
int color dim_foreground_disabled_material_light 0x7f05004d
int color dim_foreground_material_dark 0x7f05004e
int color dim_foreground_material_light 0x7f05004f
int color error_color_material_dark 0x7f050050
int color error_color_material_light 0x7f050051
int color foreground_material_dark 0x7f050052
int color foreground_material_light 0x7f050053
int color highlighted_text_material_dark 0x7f050054
int color highlighted_text_material_light 0x7f050055
int color material_blue_grey_800 0x7f050056
int color material_blue_grey_900 0x7f050057
int color material_blue_grey_950 0x7f050058
int color material_deep_teal_200 0x7f050059
int color material_deep_teal_500 0x7f05005a
int color material_grey_100 0x7f05005b
int color material_grey_300 0x7f05005c
int color material_grey_50 0x7f05005d
int color material_grey_600 0x7f05005e
int color material_grey_800 0x7f05005f
int color material_grey_850 0x7f050060
int color material_grey_900 0x7f050061
int color notification_action_color_filter 0x7f050062
int color notification_icon_bg_color 0x7f050063
int color notification_material_background_media_default_color 0x7f050064
int color primary_dark_material_dark 0x7f050065
int color primary_dark_material_light 0x7f050066
int color primary_material_dark 0x7f050067
int color primary_material_light 0x7f050068
int color primary_text_default_material_dark 0x7f050069
int color primary_text_default_material_light 0x7f05006a
int color primary_text_disabled_material_dark 0x7f05006b
int color primary_text_disabled_material_light 0x7f05006c
int color purple_200 0x7f05006d
int color purple_500 0x7f05006e
int color purple_700 0x7f05006f
int color ripple_material_dark 0x7f050070
int color ripple_material_light 0x7f050071
int color secondary_text_default_material_dark 0x7f050072
int color secondary_text_default_material_light 0x7f050073
int color secondary_text_disabled_material_dark 0x7f050074
int color secondary_text_disabled_material_light 0x7f050075
int color switch_thumb_disabled_material_dark 0x7f050076
int color switch_thumb_disabled_material_light 0x7f050077
int color switch_thumb_material_dark 0x7f050078
int color switch_thumb_material_light 0x7f050079
int color switch_thumb_normal_material_dark 0x7f05007a
int color switch_thumb_normal_material_light 0x7f05007b
int color teal_200 0x7f05007c
int color teal_700 0x7f05007d
int color tooltip_background_dark 0x7f05007e
int color tooltip_background_light 0x7f05007f
int color vector_tint_color 0x7f050080
int color vector_tint_theme_color 0x7f050081
int color white 0x7f050082
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_star_big 0x7f06003b
int dimen abc_star_medium 0x7f06003c
int dimen abc_star_small 0x7f06003d
int dimen abc_switch_padding 0x7f06003e
int dimen abc_text_size_body_1_material 0x7f06003f
int dimen abc_text_size_body_2_material 0x7f060040
int dimen abc_text_size_button_material 0x7f060041
int dimen abc_text_size_caption_material 0x7f060042
int dimen abc_text_size_display_1_material 0x7f060043
int dimen abc_text_size_display_2_material 0x7f060044
int dimen abc_text_size_display_3_material 0x7f060045
int dimen abc_text_size_display_4_material 0x7f060046
int dimen abc_text_size_headline_material 0x7f060047
int dimen abc_text_size_large_material 0x7f060048
int dimen abc_text_size_medium_material 0x7f060049
int dimen abc_text_size_menu_header_material 0x7f06004a
int dimen abc_text_size_menu_material 0x7f06004b
int dimen abc_text_size_small_material 0x7f06004c
int dimen abc_text_size_subhead_material 0x7f06004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004e
int dimen abc_text_size_title_material 0x7f06004f
int dimen abc_text_size_title_material_toolbar 0x7f060050
int dimen browser_actions_context_menu_max_width 0x7f060051
int dimen browser_actions_context_menu_min_padding 0x7f060052
int dimen cardview_compat_inset_shadow 0x7f060053
int dimen cardview_default_elevation 0x7f060054
int dimen cardview_default_radius 0x7f060055
int dimen com_facebook_auth_dialog_corner_radius 0x7f060056
int dimen com_facebook_auth_dialog_corner_radius_oversized 0x7f060057
int dimen com_facebook_button_corner_radius 0x7f060058
int dimen com_facebook_button_login_corner_radius 0x7f060059
int dimen com_facebook_likeboxcountview_border_radius 0x7f06005a
int dimen com_facebook_likeboxcountview_border_width 0x7f06005b
int dimen com_facebook_likeboxcountview_caret_height 0x7f06005c
int dimen com_facebook_likeboxcountview_caret_width 0x7f06005d
int dimen com_facebook_likeboxcountview_text_padding 0x7f06005e
int dimen com_facebook_likeboxcountview_text_size 0x7f06005f
int dimen com_facebook_likeview_edge_padding 0x7f060060
int dimen com_facebook_likeview_internal_padding 0x7f060061
int dimen com_facebook_likeview_text_size 0x7f060062
int dimen com_facebook_profilepictureview_preset_size_large 0x7f060063
int dimen com_facebook_profilepictureview_preset_size_normal 0x7f060064
int dimen com_facebook_profilepictureview_preset_size_small 0x7f060065
int dimen compat_button_inset_horizontal_material 0x7f060066
int dimen compat_button_inset_vertical_material 0x7f060067
int dimen compat_button_padding_horizontal_material 0x7f060068
int dimen compat_button_padding_vertical_material 0x7f060069
int dimen compat_control_corner_material 0x7f06006a
int dimen compat_notification_large_icon_max_height 0x7f06006b
int dimen compat_notification_large_icon_max_width 0x7f06006c
int dimen disabled_alpha_material_dark 0x7f06006d
int dimen disabled_alpha_material_light 0x7f06006e
int dimen highlight_alpha_material_colored 0x7f06006f
int dimen highlight_alpha_material_dark 0x7f060070
int dimen highlight_alpha_material_light 0x7f060071
int dimen hint_alpha_material_dark 0x7f060072
int dimen hint_alpha_material_light 0x7f060073
int dimen hint_pressed_alpha_material_dark 0x7f060074
int dimen hint_pressed_alpha_material_light 0x7f060075
int dimen notification_action_icon_size 0x7f060076
int dimen notification_action_text_size 0x7f060077
int dimen notification_big_circle_margin 0x7f060078
int dimen notification_content_margin_start 0x7f060079
int dimen notification_large_icon_height 0x7f06007a
int dimen notification_large_icon_width 0x7f06007b
int dimen notification_main_column_padding_top 0x7f06007c
int dimen notification_media_narrow_margin 0x7f06007d
int dimen notification_right_icon_size 0x7f06007e
int dimen notification_right_side_padding_top 0x7f06007f
int dimen notification_small_icon_background_padding 0x7f060080
int dimen notification_small_icon_size_as_large 0x7f060081
int dimen notification_subtext_size 0x7f060082
int dimen notification_top_pad 0x7f060083
int dimen notification_top_pad_large_text 0x7f060084
int dimen subtitle_corner_radius 0x7f060085
int dimen subtitle_outline_width 0x7f060086
int dimen subtitle_shadow_offset 0x7f060087
int dimen subtitle_shadow_radius 0x7f060088
int dimen tooltip_corner_radius 0x7f060089
int dimen tooltip_horizontal_padding 0x7f06008a
int dimen tooltip_margin 0x7f06008b
int dimen tooltip_precise_anchor_extra_offset 0x7f06008c
int dimen tooltip_precise_anchor_threshold 0x7f06008d
int dimen tooltip_vertical_padding 0x7f06008e
int dimen tooltip_y_offset_non_touch 0x7f06008f
int dimen tooltip_y_offset_touch 0x7f060090
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070001
int drawable abc_action_bar_item_background_material 0x7f070002
int drawable abc_btn_borderless_material 0x7f070003
int drawable abc_btn_check_material 0x7f070004
int drawable abc_btn_check_material_anim 0x7f070005
int drawable abc_btn_check_to_on_mtrl_000 0x7f070006
int drawable abc_btn_check_to_on_mtrl_015 0x7f070007
int drawable abc_btn_colored_material 0x7f070008
int drawable abc_btn_default_mtrl_shape 0x7f070009
int drawable abc_btn_radio_material 0x7f07000a
int drawable abc_btn_radio_material_anim 0x7f07000b
int drawable abc_btn_radio_to_on_mtrl_000 0x7f07000c
int drawable abc_btn_radio_to_on_mtrl_015 0x7f07000d
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f07000e
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f07000f
int drawable abc_cab_background_internal_bg 0x7f070010
int drawable abc_cab_background_top_material 0x7f070011
int drawable abc_cab_background_top_mtrl_alpha 0x7f070012
int drawable abc_control_background_material 0x7f070013
int drawable abc_dialog_material_background 0x7f070014
int drawable abc_edit_text_material 0x7f070015
int drawable abc_ic_ab_back_material 0x7f070016
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f070017
int drawable abc_ic_clear_material 0x7f070018
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070019
int drawable abc_ic_go_search_api_material 0x7f07001a
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f07001b
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f07001c
int drawable abc_ic_menu_overflow_material 0x7f07001d
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f07001e
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f07001f
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070020
int drawable abc_ic_search_api_material 0x7f070021
int drawable abc_ic_voice_search_api_material 0x7f070022
int drawable abc_item_background_holo_dark 0x7f070023
int drawable abc_item_background_holo_light 0x7f070024
int drawable abc_list_divider_material 0x7f070025
int drawable abc_list_divider_mtrl_alpha 0x7f070026
int drawable abc_list_focused_holo 0x7f070027
int drawable abc_list_longpressed_holo 0x7f070028
int drawable abc_list_pressed_holo_dark 0x7f070029
int drawable abc_list_pressed_holo_light 0x7f07002a
int drawable abc_list_selector_background_transition_holo_dark 0x7f07002b
int drawable abc_list_selector_background_transition_holo_light 0x7f07002c
int drawable abc_list_selector_disabled_holo_dark 0x7f07002d
int drawable abc_list_selector_disabled_holo_light 0x7f07002e
int drawable abc_list_selector_holo_dark 0x7f07002f
int drawable abc_list_selector_holo_light 0x7f070030
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070031
int drawable abc_popup_background_mtrl_mult 0x7f070032
int drawable abc_ratingbar_indicator_material 0x7f070033
int drawable abc_ratingbar_material 0x7f070034
int drawable abc_ratingbar_small_material 0x7f070035
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f070036
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f070037
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f070038
int drawable abc_scrubber_primary_mtrl_alpha 0x7f070039
int drawable abc_scrubber_track_mtrl_alpha 0x7f07003a
int drawable abc_seekbar_thumb_material 0x7f07003b
int drawable abc_seekbar_tick_mark_material 0x7f07003c
int drawable abc_seekbar_track_material 0x7f07003d
int drawable abc_spinner_mtrl_am_alpha 0x7f07003e
int drawable abc_spinner_textfield_background_material 0x7f07003f
int drawable abc_star_black_48dp 0x7f070040
int drawable abc_star_half_black_48dp 0x7f070041
int drawable abc_switch_thumb_material 0x7f070042
int drawable abc_switch_track_mtrl_alpha 0x7f070043
int drawable abc_tab_indicator_material 0x7f070044
int drawable abc_tab_indicator_mtrl_alpha 0x7f070045
int drawable abc_text_cursor_material 0x7f070046
int drawable abc_text_select_handle_left_mtrl 0x7f070047
int drawable abc_text_select_handle_middle_mtrl 0x7f070048
int drawable abc_text_select_handle_right_mtrl 0x7f070049
int drawable abc_textfield_activated_mtrl_alpha 0x7f07004a
int drawable abc_textfield_default_mtrl_alpha 0x7f07004b
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f07004c
int drawable abc_textfield_search_default_mtrl_alpha 0x7f07004d
int drawable abc_textfield_search_material 0x7f07004e
int drawable abc_vector_test 0x7f07004f
int drawable btn_checkbox_checked_mtrl 0x7f070050
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f070051
int drawable btn_checkbox_unchecked_mtrl 0x7f070052
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f070053
int drawable btn_radio_off_mtrl 0x7f070054
int drawable btn_radio_off_to_on_mtrl_animation 0x7f070055
int drawable btn_radio_on_mtrl 0x7f070056
int drawable btn_radio_on_to_off_mtrl_animation 0x7f070057
int drawable com_facebook_auth_dialog_background 0x7f070058
int drawable com_facebook_auth_dialog_cancel_background 0x7f070059
int drawable com_facebook_auth_dialog_header_background 0x7f07005a
int drawable com_facebook_button_background 0x7f07005b
int drawable com_facebook_button_icon 0x7f07005c
int drawable com_facebook_button_like_background 0x7f07005d
int drawable com_facebook_button_like_icon_selected 0x7f07005e
int drawable com_facebook_close 0x7f07005f
int drawable com_facebook_favicon_blue 0x7f070060
int drawable com_facebook_profile_picture_blank_portrait 0x7f070061
int drawable com_facebook_profile_picture_blank_square 0x7f070062
int drawable com_facebook_tooltip_black_background 0x7f070063
int drawable com_facebook_tooltip_black_bottomnub 0x7f070064
int drawable com_facebook_tooltip_black_topnub 0x7f070065
int drawable com_facebook_tooltip_black_xout 0x7f070066
int drawable com_facebook_tooltip_blue_background 0x7f070067
int drawable com_facebook_tooltip_blue_bottomnub 0x7f070068
int drawable com_facebook_tooltip_blue_topnub 0x7f070069
int drawable com_facebook_tooltip_blue_xout 0x7f07006a
int drawable common_full_open_on_phone 0x7f07006b
int drawable common_google_signin_btn_icon_dark 0x7f07006c
int drawable common_google_signin_btn_icon_dark_focused 0x7f07006d
int drawable common_google_signin_btn_icon_dark_normal 0x7f07006e
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f07006f
int drawable common_google_signin_btn_icon_disabled 0x7f070070
int drawable common_google_signin_btn_icon_light 0x7f070071
int drawable common_google_signin_btn_icon_light_focused 0x7f070072
int drawable common_google_signin_btn_icon_light_normal 0x7f070073
int drawable common_google_signin_btn_icon_light_normal_background 0x7f070074
int drawable common_google_signin_btn_text_dark 0x7f070075
int drawable common_google_signin_btn_text_dark_focused 0x7f070076
int drawable common_google_signin_btn_text_dark_normal 0x7f070077
int drawable common_google_signin_btn_text_dark_normal_background 0x7f070078
int drawable common_google_signin_btn_text_disabled 0x7f070079
int drawable common_google_signin_btn_text_light 0x7f07007a
int drawable common_google_signin_btn_text_light_focused 0x7f07007b
int drawable common_google_signin_btn_text_light_normal 0x7f07007c
int drawable common_google_signin_btn_text_light_normal_background 0x7f07007d
int drawable googleg_disabled_color_18 0x7f07007e
int drawable googleg_standard_color_18 0x7f07007f
int drawable ic_call_answer 0x7f070080
int drawable ic_call_answer_low 0x7f070081
int drawable ic_call_answer_video 0x7f070082
int drawable ic_call_answer_video_low 0x7f070083
int drawable ic_call_decline 0x7f070084
int drawable ic_call_decline_low 0x7f070085
int drawable ic_facebook 0x7f070086
int drawable ic_google 0x7f070087
int drawable ic_launcher_background 0x7f070088
int drawable ic_launcher_foreground 0x7f070089
int drawable notification_action_background 0x7f07008a
int drawable notification_bg 0x7f07008b
int drawable notification_bg_low 0x7f07008c
int drawable notification_bg_low_normal 0x7f07008d
int drawable notification_bg_low_pressed 0x7f07008e
int drawable notification_bg_normal 0x7f07008f
int drawable notification_bg_normal_pressed 0x7f070090
int drawable notification_icon_background 0x7f070091
int drawable notification_oversize_large_icon_bg 0x7f070092
int drawable notification_template_icon_bg 0x7f070093
int drawable notification_template_icon_low_bg 0x7f070094
int drawable notification_tile_bg 0x7f070095
int drawable notify_panel_notification_icon_bg 0x7f070096
int drawable test_level_drawable 0x7f070097
int drawable tooltip_frame_dark 0x7f070098
int drawable tooltip_frame_light 0x7f070099
int id ALT 0x7f080000
int id CTRL 0x7f080001
int id FUNCTION 0x7f080002
int id META 0x7f080003
int id SHIFT 0x7f080004
int id SYM 0x7f080005
int id accessibility_action_clickable_span 0x7f080006
int id accessibility_custom_action_0 0x7f080007
int id accessibility_custom_action_1 0x7f080008
int id accessibility_custom_action_10 0x7f080009
int id accessibility_custom_action_11 0x7f08000a
int id accessibility_custom_action_12 0x7f08000b
int id accessibility_custom_action_13 0x7f08000c
int id accessibility_custom_action_14 0x7f08000d
int id accessibility_custom_action_15 0x7f08000e
int id accessibility_custom_action_16 0x7f08000f
int id accessibility_custom_action_17 0x7f080010
int id accessibility_custom_action_18 0x7f080011
int id accessibility_custom_action_19 0x7f080012
int id accessibility_custom_action_2 0x7f080013
int id accessibility_custom_action_20 0x7f080014
int id accessibility_custom_action_21 0x7f080015
int id accessibility_custom_action_22 0x7f080016
int id accessibility_custom_action_23 0x7f080017
int id accessibility_custom_action_24 0x7f080018
int id accessibility_custom_action_25 0x7f080019
int id accessibility_custom_action_26 0x7f08001a
int id accessibility_custom_action_27 0x7f08001b
int id accessibility_custom_action_28 0x7f08001c
int id accessibility_custom_action_29 0x7f08001d
int id accessibility_custom_action_3 0x7f08001e
int id accessibility_custom_action_30 0x7f08001f
int id accessibility_custom_action_31 0x7f080020
int id accessibility_custom_action_4 0x7f080021
int id accessibility_custom_action_5 0x7f080022
int id accessibility_custom_action_6 0x7f080023
int id accessibility_custom_action_7 0x7f080024
int id accessibility_custom_action_8 0x7f080025
int id accessibility_custom_action_9 0x7f080026
int id action0 0x7f080027
int id action_bar 0x7f080028
int id action_bar_activity_content 0x7f080029
int id action_bar_container 0x7f08002a
int id action_bar_root 0x7f08002b
int id action_bar_spinner 0x7f08002c
int id action_bar_subtitle 0x7f08002d
int id action_bar_title 0x7f08002e
int id action_container 0x7f08002f
int id action_context_bar 0x7f080030
int id action_divider 0x7f080031
int id action_image 0x7f080032
int id action_menu_divider 0x7f080033
int id action_menu_presenter 0x7f080034
int id action_mode_bar 0x7f080035
int id action_mode_bar_stub 0x7f080036
int id action_mode_close_button 0x7f080037
int id action_text 0x7f080038
int id actions 0x7f080039
int id activity_chooser_view_content 0x7f08003a
int id add 0x7f08003b
int id adjust_height 0x7f08003c
int id adjust_width 0x7f08003d
int id alertTitle 0x7f08003e
int id all 0x7f08003f
int id always 0x7f080040
int id androidx_compose_ui_view_composition_context 0x7f080041
int id async 0x7f080042
int id auto 0x7f080043
int id automatic 0x7f080044
int id beginning 0x7f080045
int id blocking 0x7f080046
int id bottom 0x7f080047
int id box_count 0x7f080048
int id browser_actions_header_text 0x7f080049
int id browser_actions_menu_item_icon 0x7f08004a
int id browser_actions_menu_item_text 0x7f08004b
int id browser_actions_menu_items 0x7f08004c
int id browser_actions_menu_view 0x7f08004d
int id button 0x7f08004e
int id buttonPanel 0x7f08004f
int id cancel_action 0x7f080050
int id cancel_button 0x7f080051
int id center 0x7f080052
int id center_horizontal 0x7f080053
int id center_vertical 0x7f080054
int id checkbox 0x7f080055
int id checked 0x7f080056
int id chronometer 0x7f080057
int id clip_horizontal 0x7f080058
int id clip_vertical 0x7f080059
int id coil_request_manager 0x7f08005a
int id collapseActionView 0x7f08005b
int id com_facebook_body_frame 0x7f08005c
int id com_facebook_button_xout 0x7f08005d
int id com_facebook_device_auth_instructions 0x7f08005e
int id com_facebook_fragment_container 0x7f08005f
int id com_facebook_login_fragment_progress_bar 0x7f080060
int id com_facebook_smart_instructions_0 0x7f080061
int id com_facebook_smart_instructions_or 0x7f080062
int id com_facebook_tooltip_bubble_view_bottom_pointer 0x7f080063
int id com_facebook_tooltip_bubble_view_text_body 0x7f080064
int id com_facebook_tooltip_bubble_view_top_pointer 0x7f080065
int id compatible 0x7f080066
int id compose_view_saveable_id_tag 0x7f080067
int id confirmation_code 0x7f080068
int id consume_window_insets_tag 0x7f080069
int id content 0x7f08006a
int id contentPanel 0x7f08006b
int id custom 0x7f08006c
int id customPanel 0x7f08006d
int id dark 0x7f08006e
int id decor_content_parent 0x7f08006f
int id default_activity_button 0x7f080070
int id dialog_button 0x7f080071
int id disableHome 0x7f080072
int id display_always 0x7f080073
int id edit_query 0x7f080074
int id edit_text_id 0x7f080075
int id end 0x7f080076
int id end_padder 0x7f080077
int id expand_activities_button 0x7f080078
int id expanded_menu 0x7f080079
int id fill 0x7f08007a
int id fillCenter 0x7f08007b
int id fillEnd 0x7f08007c
int id fillStart 0x7f08007d
int id fill_horizontal 0x7f08007e
int id fill_vertical 0x7f08007f
int id fitCenter 0x7f080080
int id fitEnd 0x7f080081
int id fitStart 0x7f080082
int id forever 0x7f080083
int id fragment_container_view_tag 0x7f080084
int id group_divider 0x7f080085
int id hide_graphics_layer_in_inspector_tag 0x7f080086
int id hide_ime_id 0x7f080087
int id hide_in_inspector_tag 0x7f080088
int id home 0x7f080089
int id homeAsUp 0x7f08008a
int id icon 0x7f08008b
int id icon_group 0x7f08008c
int id icon_only 0x7f08008d
int id ifRoom 0x7f08008e
int id image 0x7f08008f
int id info 0x7f080090
int id inline 0x7f080091
int id inspection_slot_table_set 0x7f080092
int id is_pooling_container_tag 0x7f080093
int id italic 0x7f080094
int id large 0x7f080095
int id left 0x7f080096
int id light 0x7f080097
int id line1 0x7f080098
int id line3 0x7f080099
int id listMode 0x7f08009a
int id list_item 0x7f08009b
int id media_actions 0x7f08009c
int id message 0x7f08009d
int id middle 0x7f08009e
int id multiply 0x7f08009f
int id nav_controller_view_tag 0x7f0800a0
int id never 0x7f0800a1
int id never_display 0x7f0800a2
int id none 0x7f0800a3
int id normal 0x7f0800a4
int id notification_background 0x7f0800a5
int id notification_main_column 0x7f0800a6
int id notification_main_column_container 0x7f0800a7
int id off 0x7f0800a8
int id on 0x7f0800a9
int id open_graph 0x7f0800aa
int id page 0x7f0800ab
int id parentPanel 0x7f0800ac
int id performance 0x7f0800ad
int id pooling_container_listener_holder_tag 0x7f0800ae
int id progress_bar 0x7f0800af
int id progress_circular 0x7f0800b0
int id progress_horizontal 0x7f0800b1
int id radio 0x7f0800b2
int id report_drawn 0x7f0800b3
int id right 0x7f0800b4
int id right_icon 0x7f0800b5
int id right_side 0x7f0800b6
int id screen 0x7f0800b7
int id scrollIndicatorDown 0x7f0800b8
int id scrollIndicatorUp 0x7f0800b9
int id scrollView 0x7f0800ba
int id search_badge 0x7f0800bb
int id search_bar 0x7f0800bc
int id search_button 0x7f0800bd
int id search_close_btn 0x7f0800be
int id search_edit_frame 0x7f0800bf
int id search_go_btn 0x7f0800c0
int id search_mag_icon 0x7f0800c1
int id search_plate 0x7f0800c2
int id search_src_text 0x7f0800c3
int id search_voice_btn 0x7f0800c4
int id select_dialog_listview 0x7f0800c5
int id shortcut 0x7f0800c6
int id showCustom 0x7f0800c7
int id showHome 0x7f0800c8
int id showTitle 0x7f0800c9
int id small 0x7f0800ca
int id spacer 0x7f0800cb
int id special_effects_controller_view_tag 0x7f0800cc
int id split_action_bar 0x7f0800cd
int id src_atop 0x7f0800ce
int id src_in 0x7f0800cf
int id src_over 0x7f0800d0
int id standard 0x7f0800d1
int id start 0x7f0800d2
int id status_bar_latest_event_content 0x7f0800d3
int id submenuarrow 0x7f0800d4
int id submit_area 0x7f0800d5
int id tabMode 0x7f0800d6
int id tag_accessibility_actions 0x7f0800d7
int id tag_accessibility_clickable_spans 0x7f0800d8
int id tag_accessibility_heading 0x7f0800d9
int id tag_accessibility_pane_title 0x7f0800da
int id tag_on_apply_window_listener 0x7f0800db
int id tag_on_receive_content_listener 0x7f0800dc
int id tag_on_receive_content_mime_types 0x7f0800dd
int id tag_screen_reader_focusable 0x7f0800de
int id tag_state_description 0x7f0800df
int id tag_transition_group 0x7f0800e0
int id tag_unhandled_key_event_manager 0x7f0800e1
int id tag_unhandled_key_listeners 0x7f0800e2
int id tag_window_insets_animation_callback 0x7f0800e3
int id text 0x7f0800e4
int id text2 0x7f0800e5
int id textSpacerNoButtons 0x7f0800e6
int id textSpacerNoTitle 0x7f0800e7
int id time 0x7f0800e8
int id title 0x7f0800e9
int id titleDividerNoCustom 0x7f0800ea
int id title_template 0x7f0800eb
int id top 0x7f0800ec
int id topPanel 0x7f0800ed
int id unchecked 0x7f0800ee
int id uniform 0x7f0800ef
int id unknown 0x7f0800f0
int id up 0x7f0800f1
int id useLogo 0x7f0800f2
int id view_tree_lifecycle_owner 0x7f0800f3
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0800f4
int id view_tree_saved_state_registry_owner 0x7f0800f5
int id view_tree_view_model_store_owner 0x7f0800f6
int id visible_removing_fragment_view_tag 0x7f0800f7
int id wide 0x7f0800f8
int id withText 0x7f0800f9
int id wrap_content 0x7f0800fa
int id wrapped_composition_tag 0x7f0800fb
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer cancel_button_image_alpha 0x7f090002
int integer config_tooltipAnimTime 0x7f090003
int integer google_play_services_version 0x7f090004
int integer m3c_window_layout_in_display_cutout_mode 0x7f090005
int integer status_bar_notification_info_maxnum 0x7f090006
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0a0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0a0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0a0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0a0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0a0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0a0005
int interpolator fast_out_slow_in 0x7f0a0006
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout browser_actions_context_menu_page 0x7f0b001c
int layout browser_actions_context_menu_row 0x7f0b001d
int layout com_facebook_activity_layout 0x7f0b001e
int layout com_facebook_device_auth_dialog_fragment 0x7f0b001f
int layout com_facebook_login_fragment 0x7f0b0020
int layout com_facebook_smart_device_dialog_fragment 0x7f0b0021
int layout com_facebook_tooltip_bubble 0x7f0b0022
int layout custom_dialog 0x7f0b0023
int layout ime_base_split_test_activity 0x7f0b0024
int layout ime_secondary_split_test_activity 0x7f0b0025
int layout notification_action 0x7f0b0026
int layout notification_action_tombstone 0x7f0b0027
int layout notification_media_action 0x7f0b0028
int layout notification_media_cancel_action 0x7f0b0029
int layout notification_template_big_media 0x7f0b002a
int layout notification_template_big_media_custom 0x7f0b002b
int layout notification_template_big_media_narrow 0x7f0b002c
int layout notification_template_big_media_narrow_custom 0x7f0b002d
int layout notification_template_custom_big 0x7f0b002e
int layout notification_template_icon_group 0x7f0b002f
int layout notification_template_lines_media 0x7f0b0030
int layout notification_template_media 0x7f0b0031
int layout notification_template_media_custom 0x7f0b0032
int layout notification_template_part_chronometer 0x7f0b0033
int layout notification_template_part_time 0x7f0b0034
int layout select_dialog_item_material 0x7f0b0035
int layout select_dialog_multichoice_material 0x7f0b0036
int layout select_dialog_singlechoice_material 0x7f0b0037
int layout support_simple_spinner_dropdown_item 0x7f0b0038
int mipmap ic_launcher 0x7f0c0000
int mipmap ic_launcher_round 0x7f0c0001
int raw firebase_common_keep 0x7f0d0000
int string abc_action_bar_home_description 0x7f0e0000
int string abc_action_bar_up_description 0x7f0e0001
int string abc_action_menu_overflow_description 0x7f0e0002
int string abc_action_mode_done 0x7f0e0003
int string abc_activity_chooser_view_see_all 0x7f0e0004
int string abc_activitychooserview_choose_application 0x7f0e0005
int string abc_capital_off 0x7f0e0006
int string abc_capital_on 0x7f0e0007
int string abc_menu_alt_shortcut_label 0x7f0e0008
int string abc_menu_ctrl_shortcut_label 0x7f0e0009
int string abc_menu_delete_shortcut_label 0x7f0e000a
int string abc_menu_enter_shortcut_label 0x7f0e000b
int string abc_menu_function_shortcut_label 0x7f0e000c
int string abc_menu_meta_shortcut_label 0x7f0e000d
int string abc_menu_shift_shortcut_label 0x7f0e000e
int string abc_menu_space_shortcut_label 0x7f0e000f
int string abc_menu_sym_shortcut_label 0x7f0e0010
int string abc_prepend_shortcut_label 0x7f0e0011
int string abc_search_hint 0x7f0e0012
int string abc_searchview_description_clear 0x7f0e0013
int string abc_searchview_description_query 0x7f0e0014
int string abc_searchview_description_search 0x7f0e0015
int string abc_searchview_description_submit 0x7f0e0016
int string abc_searchview_description_voice 0x7f0e0017
int string abc_shareactionprovider_share_with 0x7f0e0018
int string abc_shareactionprovider_share_with_application 0x7f0e0019
int string abc_toolbar_collapse_description 0x7f0e001a
int string about 0x7f0e001b
int string add_additive 0x7f0e001c
int string add_custom_allergen 0x7f0e001d
int string add_custom_certificate 0x7f0e001e
int string add_custom_fat 0x7f0e001f
int string add_custom_item 0x7f0e0020
int string add_custom_preservative 0x7f0e0021
int string add_custom_sugar 0x7f0e0022
int string add_to_favorites 0x7f0e0023
int string additive_category 0x7f0e0024
int string additive_code 0x7f0e0025
int string additive_description 0x7f0e0026
int string additive_detail 0x7f0e0027
int string additive_halal_status 0x7f0e0028
int string additive_harmful_level 0x7f0e0029
int string additive_kosher_status 0x7f0e002a
int string additive_name 0x7f0e002b
int string additive_notes 0x7f0e002c
int string additive_unhealthy_level 0x7f0e002d
int string additive_vegan_status 0x7f0e002e
int string additives 0x7f0e002f
int string ai_model 0x7f0e0030
int string ai_model_selection 0x7f0e0031
int string allergen_detail 0x7f0e0032
int string allergens 0x7f0e0033
int string analysis_error 0x7f0e0034
int string analysis_results 0x7f0e0035
int string analyze 0x7f0e0036
int string analyzing_ingredients 0x7f0e0037
int string androidx_startup 0x7f0e0038
int string antibiotic 0x7f0e0039
int string app_name 0x7f0e003a
int string applies_to 0x7f0e003b
int string back 0x7f0e003c
int string barcode_tab 0x7f0e003d
int string call_notification_answer_action 0x7f0e003e
int string call_notification_answer_video_action 0x7f0e003f
int string call_notification_decline_action 0x7f0e0040
int string call_notification_hang_up_action 0x7f0e0041
int string call_notification_incoming_text 0x7f0e0042
int string call_notification_ongoing_text 0x7f0e0043
int string call_notification_screening_text 0x7f0e0044
int string camera_permission_denied 0x7f0e0045
int string camera_permission_required 0x7f0e0046
int string cancel 0x7f0e0047
int string carcinogen 0x7f0e0048
int string certificate_detail 0x7f0e0049
int string certificates 0x7f0e004a
int string certification_criteria 0x7f0e004b
int string certification_process 0x7f0e004c
int string certification_validity 0x7f0e004d
int string certifying_body 0x7f0e004e
int string chemical_structure 0x7f0e004f
int string clear_history 0x7f0e0050
int string close 0x7f0e0051
int string close_drawer 0x7f0e0052
int string close_sheet 0x7f0e0053
int string com_facebook_device_auth_instructions 0x7f0e0054
int string com_facebook_image_download_unknown_error 0x7f0e0055
int string com_facebook_internet_permission_error_message 0x7f0e0056
int string com_facebook_internet_permission_error_title 0x7f0e0057
int string com_facebook_like_button_liked 0x7f0e0058
int string com_facebook_like_button_not_liked 0x7f0e0059
int string com_facebook_loading 0x7f0e005a
int string com_facebook_loginview_cancel_action 0x7f0e005b
int string com_facebook_loginview_log_in_button 0x7f0e005c
int string com_facebook_loginview_log_in_button_continue 0x7f0e005d
int string com_facebook_loginview_log_in_button_long 0x7f0e005e
int string com_facebook_loginview_log_out_action 0x7f0e005f
int string com_facebook_loginview_log_out_button 0x7f0e0060
int string com_facebook_loginview_logged_in_as 0x7f0e0061
int string com_facebook_loginview_logged_in_using_facebook 0x7f0e0062
int string com_facebook_send_button_text 0x7f0e0063
int string com_facebook_share_button_text 0x7f0e0064
int string com_facebook_smart_device_instructions 0x7f0e0065
int string com_facebook_smart_device_instructions_or 0x7f0e0066
int string com_facebook_smart_login_confirmation_cancel 0x7f0e0067
int string com_facebook_smart_login_confirmation_continue_as 0x7f0e0068
int string com_facebook_smart_login_confirmation_title 0x7f0e0069
int string com_facebook_tooltip_default 0x7f0e006a
int string common_google_play_services_enable_button 0x7f0e006b
int string common_google_play_services_enable_text 0x7f0e006c
int string common_google_play_services_enable_title 0x7f0e006d
int string common_google_play_services_install_button 0x7f0e006e
int string common_google_play_services_install_text 0x7f0e006f
int string common_google_play_services_install_title 0x7f0e0070
int string common_google_play_services_notification_channel_name 0x7f0e0071
int string common_google_play_services_notification_ticker 0x7f0e0072
int string common_google_play_services_unknown_issue 0x7f0e0073
int string common_google_play_services_unsupported_text 0x7f0e0074
int string common_google_play_services_update_button 0x7f0e0075
int string common_google_play_services_update_text 0x7f0e0076
int string common_google_play_services_update_title 0x7f0e0077
int string common_google_play_services_updating_text 0x7f0e0078
int string common_google_play_services_wear_update_text 0x7f0e0079
int string common_open_on_phone 0x7f0e007a
int string common_products 0x7f0e007b
int string common_signin_button_text 0x7f0e007c
int string common_signin_button_text_long 0x7f0e007d
int string confirm_password 0x7f0e007e
int string content_analysis 0x7f0e007f
int string copy_toast_msg 0x7f0e0080
int string correct_content 0x7f0e0081
int string custom_item 0x7f0e0082
int string custom_item_name 0x7f0e0083
int string dark_mode 0x7f0e0084
int string data_upload_description 0x7f0e0085
int string data_upload_error 0x7f0e0086
int string data_upload_screen_title 0x7f0e0087
int string data_upload_success 0x7f0e0088
int string default_error_message 0x7f0e0089
int string default_popup_window_title 0x7f0e008a
int string default_web_client_id 0x7f0e008b
int string delete 0x7f0e008c
int string description 0x7f0e008d
int string dietary_statuses 0x7f0e008e
int string dropdown_menu 0x7f0e008f
int string edit 0x7f0e0090
int string edit_additive 0x7f0e0091
int string edit_recognized_ingredients 0x7f0e0092
int string email 0x7f0e0093
int string endocrine_disruptor 0x7f0e0094
int string error 0x7f0e0095
int string fallback_menu_item_copy_link 0x7f0e0096
int string fallback_menu_item_open_in_browser 0x7f0e0097
int string fallback_menu_item_share_link 0x7f0e0098
int string fat_detail 0x7f0e0099
int string fats 0x7f0e009a
int string favorites 0x7f0e009b
int string food_analysis 0x7f0e009c
int string food_analysis_description 0x7f0e009d
int string food_analysis_screen 0x7f0e009e
int string food_certificates 0x7f0e009f
int string food_halal_status 0x7f0e00a0
int string food_vegan_status 0x7f0e00a1
int string frame_size 0x7f0e00a2
int string functional_type 0x7f0e00a3
int string functional_types 0x7f0e00a4
int string gcm_defaultSenderId 0x7f0e00a5
int string glycemic_index 0x7f0e00a6
int string glycemic_info 0x7f0e00a7
int string glycemic_load 0x7f0e00a8
int string google_api_key 0x7f0e00a9
int string google_app_id 0x7f0e00aa
int string google_crash_reporting_api_key 0x7f0e00ab
int string google_storage_bucket 0x7f0e00ac
int string grant_permission 0x7f0e00ad
int string halal_status 0x7f0e00ae
int string harmful_ingredients 0x7f0e00af
int string harmful_level 0x7f0e00b0
int string health_effects 0x7f0e00b1
int string home 0x7f0e00b2
int string import_allergens 0x7f0e00b3
int string import_data 0x7f0e00b4
int string import_error 0x7f0e00b5
int string import_fats 0x7f0e00b6
int string import_preservatives 0x7f0e00b7
int string import_success 0x7f0e00b8
int string import_sugars 0x7f0e00b9
int string importing 0x7f0e00ba
int string in_progress 0x7f0e00bb
int string indeterminate 0x7f0e00bc
int string industrial_residue 0x7f0e00bd
int string ingredients 0x7f0e00be
int string ingredients_corrected 0x7f0e00bf
int string ingredients_guide_text 0x7f0e00c0
int string ingredients_label 0x7f0e00c1
int string ingredients_raw 0x7f0e00c2
int string ingredients_recognized 0x7f0e00c3
int string kosher_status 0x7f0e00c4
int string language 0x7f0e00c5
int string language_changed 0x7f0e00c6
int string language_english 0x7f0e00c7
int string language_selection 0x7f0e00c8
int string language_turkish 0x7f0e00c9
int string loading 0x7f0e00ca
int string login 0x7f0e00cb
int string login_required 0x7f0e00cc
int string login_with_google 0x7f0e00cd
int string logout 0x7f0e00ce
int string m3c_bottom_sheet_collapse_description 0x7f0e00cf
int string m3c_bottom_sheet_dismiss_description 0x7f0e00d0
int string m3c_bottom_sheet_drag_handle_description 0x7f0e00d1
int string m3c_bottom_sheet_expand_description 0x7f0e00d2
int string m3c_bottom_sheet_pane_title 0x7f0e00d3
int string m3c_date_input_headline 0x7f0e00d4
int string m3c_date_input_headline_description 0x7f0e00d5
int string m3c_date_input_invalid_for_pattern 0x7f0e00d6
int string m3c_date_input_invalid_not_allowed 0x7f0e00d7
int string m3c_date_input_invalid_year_range 0x7f0e00d8
int string m3c_date_input_label 0x7f0e00d9
int string m3c_date_input_no_input_description 0x7f0e00da
int string m3c_date_input_title 0x7f0e00db
int string m3c_date_picker_headline 0x7f0e00dc
int string m3c_date_picker_headline_description 0x7f0e00dd
int string m3c_date_picker_navigate_to_year_description 0x7f0e00de
int string m3c_date_picker_no_selection_description 0x7f0e00df
int string m3c_date_picker_scroll_to_earlier_years 0x7f0e00e0
int string m3c_date_picker_scroll_to_later_years 0x7f0e00e1
int string m3c_date_picker_switch_to_calendar_mode 0x7f0e00e2
int string m3c_date_picker_switch_to_day_selection 0x7f0e00e3
int string m3c_date_picker_switch_to_input_mode 0x7f0e00e4
int string m3c_date_picker_switch_to_next_month 0x7f0e00e5
int string m3c_date_picker_switch_to_previous_month 0x7f0e00e6
int string m3c_date_picker_switch_to_year_selection 0x7f0e00e7
int string m3c_date_picker_title 0x7f0e00e8
int string m3c_date_picker_today_description 0x7f0e00e9
int string m3c_date_picker_year_picker_pane_title 0x7f0e00ea
int string m3c_date_range_input_invalid_range_input 0x7f0e00eb
int string m3c_date_range_input_title 0x7f0e00ec
int string m3c_date_range_picker_day_in_range 0x7f0e00ed
int string m3c_date_range_picker_end_headline 0x7f0e00ee
int string m3c_date_range_picker_scroll_to_next_month 0x7f0e00ef
int string m3c_date_range_picker_scroll_to_previous_month 0x7f0e00f0
int string m3c_date_range_picker_start_headline 0x7f0e00f1
int string m3c_date_range_picker_title 0x7f0e00f2
int string m3c_dialog 0x7f0e00f3
int string m3c_dropdown_menu_collapsed 0x7f0e00f4
int string m3c_dropdown_menu_expanded 0x7f0e00f5
int string m3c_dropdown_menu_toggle 0x7f0e00f6
int string m3c_search_bar_search 0x7f0e00f7
int string m3c_snackbar_dismiss 0x7f0e00f8
int string m3c_suggestions_available 0x7f0e00f9
int string m3c_time_picker_am 0x7f0e00fa
int string m3c_time_picker_hour 0x7f0e00fb
int string m3c_time_picker_hour_24h_suffix 0x7f0e00fc
int string m3c_time_picker_hour_selection 0x7f0e00fd
int string m3c_time_picker_hour_suffix 0x7f0e00fe
int string m3c_time_picker_hour_text_field 0x7f0e00ff
int string m3c_time_picker_minute 0x7f0e0100
int string m3c_time_picker_minute_selection 0x7f0e0101
int string m3c_time_picker_minute_suffix 0x7f0e0102
int string m3c_time_picker_minute_text_field 0x7f0e0103
int string m3c_time_picker_period_toggle_description 0x7f0e0104
int string m3c_time_picker_pm 0x7f0e0105
int string m3c_tooltip_long_press_label 0x7f0e0106
int string m3c_tooltip_pane_description 0x7f0e0107
int string main_risk 0x7f0e0108
int string manual_entry 0x7f0e0109
int string name 0x7f0e010a
int string name_cannot_be_empty 0x7f0e010b
int string navigation_menu 0x7f0e010c
int string no_preferences 0x7f0e010d
int string no_preservatives_found 0x7f0e010e
int string no_recent_scans 0x7f0e010f
int string no_results 0x7f0e0110
int string not_selected 0x7f0e0111
int string notes 0x7f0e0112
int string ocr_instructions 0x7f0e0113
int string ocr_tab 0x7f0e0114
int string ok 0x7f0e0115
int string origin 0x7f0e0116
int string password 0x7f0e0117
int string pesticide 0x7f0e0118
int string preferences_error 0x7f0e0119
int string preferences_saved 0x7f0e011a
int string preservative 0x7f0e011b
int string preservative_detail 0x7f0e011c
int string preservatives 0x7f0e011d
int string privacy_policy 0x7f0e011e
int string product_details 0x7f0e011f
int string profile 0x7f0e0120
int string project_id 0x7f0e0121
int string range_end 0x7f0e0122
int string range_start 0x7f0e0123
int string re_analyze 0x7f0e0124
int string recent_scans 0x7f0e0125
int string register 0x7f0e0126
int string register_with_google 0x7f0e0127
int string religious_statuses 0x7f0e0128
int string remove_from_favorites 0x7f0e0129
int string residue 0x7f0e012a
int string retry 0x7f0e012b
int string risk_if_missing 0x7f0e012c
int string risk_level 0x7f0e012d
int string save 0x7f0e012e
int string save_preferences 0x7f0e012f
int string scan 0x7f0e0130
int string scan_barcode 0x7f0e0131
int string scan_history 0x7f0e0132
int string scan_ingredients 0x7f0e0133
int string scan_instructions 0x7f0e0134
int string scanning 0x7f0e0135
int string search 0x7f0e0136
int string search_certificates 0x7f0e0137
int string search_menu_title 0x7f0e0138
int string selected 0x7f0e0139
int string send_to_analysis 0x7f0e013a
int string settings 0x7f0e013b
int string settings_category_about 0x7f0e013c
int string settings_category_analysis 0x7f0e013d
int string settings_category_appearance 0x7f0e013e
int string show_halal_analysis 0x7f0e013f
int string show_harmful_analysis 0x7f0e0140
int string show_kosher_analysis 0x7f0e0141
int string show_unhealthy_analysis 0x7f0e0142
int string show_vegan_analysis 0x7f0e0143
int string size_large 0x7f0e0144
int string size_medium 0x7f0e0145
int string size_small 0x7f0e0146
int string state_empty 0x7f0e0147
int string state_off 0x7f0e0148
int string state_on 0x7f0e0149
int string status_bar_notification_info_overflow 0x7f0e014a
int string status_halal 0x7f0e014b
int string status_haram 0x7f0e014c
int string status_harmful 0x7f0e014d
int string status_kosher 0x7f0e014e
int string status_not_kosher 0x7f0e014f
int string status_not_vegan 0x7f0e0150
int string status_not_vegetarian 0x7f0e0151
int string status_suspicious 0x7f0e0152
int string status_unhealthy 0x7f0e0153
int string status_vegan 0x7f0e0154
int string status_vegetarian 0x7f0e0155
int string sugar_detail 0x7f0e0156
int string sugars 0x7f0e0157
int string suspicious_ingredients 0x7f0e0158
int string switch_role 0x7f0e0159
int string synthetic 0x7f0e015a
int string tab 0x7f0e015b
int string template_percent 0x7f0e015c
int string terms_of_service 0x7f0e015d
int string tooltip_description 0x7f0e015e
int string tooltip_label 0x7f0e015f
int string toxic 0x7f0e0160
int string try_again_button 0x7f0e0161
int string unhealthy_level 0x7f0e0162
int string upload_again_button 0x7f0e0163
int string upload_data_button 0x7f0e0164
int string uploading_data 0x7f0e0165
int string usage 0x7f0e0166
int string user_food_preferences 0x7f0e0167
int string vegan_status 0x7f0e0168
int string vegetarian_status 0x7f0e0169
int string version 0x7f0e016a
int string welcome_message 0x7f0e016b
int style AlertDialog_AppCompat 0x7f0f0000
int style AlertDialog_AppCompat_Light 0x7f0f0001
int style Animation_AppCompat_Dialog 0x7f0f0002
int style Animation_AppCompat_DropDownUp 0x7f0f0003
int style Animation_AppCompat_Tooltip 0x7f0f0004
int style Base_AlertDialog_AppCompat 0x7f0f0005
int style Base_AlertDialog_AppCompat_Light 0x7f0f0006
int style Base_Animation_AppCompat_Dialog 0x7f0f0007
int style Base_Animation_AppCompat_DropDownUp 0x7f0f0008
int style Base_Animation_AppCompat_Tooltip 0x7f0f0009
int style Base_CardView 0x7f0f000a
int style Base_DialogWindowTitle_AppCompat 0x7f0f000b
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0f000c
int style Base_TextAppearance_AppCompat 0x7f0f000d
int style Base_TextAppearance_AppCompat_Body1 0x7f0f000e
int style Base_TextAppearance_AppCompat_Body2 0x7f0f000f
int style Base_TextAppearance_AppCompat_Button 0x7f0f0010
int style Base_TextAppearance_AppCompat_Caption 0x7f0f0011
int style Base_TextAppearance_AppCompat_Display1 0x7f0f0012
int style Base_TextAppearance_AppCompat_Display2 0x7f0f0013
int style Base_TextAppearance_AppCompat_Display3 0x7f0f0014
int style Base_TextAppearance_AppCompat_Display4 0x7f0f0015
int style Base_TextAppearance_AppCompat_Headline 0x7f0f0016
int style Base_TextAppearance_AppCompat_Inverse 0x7f0f0017
int style Base_TextAppearance_AppCompat_Large 0x7f0f0018
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0f0019
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f001a
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f001b
int style Base_TextAppearance_AppCompat_Medium 0x7f0f001c
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0f001d
int style Base_TextAppearance_AppCompat_Menu 0x7f0f001e
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0f001f
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f0020
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0f0021
int style Base_TextAppearance_AppCompat_Small 0x7f0f0022
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0f0023
int style Base_TextAppearance_AppCompat_Subhead 0x7f0f0024
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0f0025
int style Base_TextAppearance_AppCompat_Title 0x7f0f0026
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0f0027
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0f0028
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f0029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f002c
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f002d
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f002e
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f002f
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0f0030
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f0031
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f0032
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f0033
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f0034
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f0035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f0036
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f0037
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0f0038
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f0039
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f003a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f003b
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f003c
int style Base_Theme_AppCompat 0x7f0f003d
int style Base_Theme_AppCompat_CompactMenu 0x7f0f003e
int style Base_Theme_AppCompat_Dialog 0x7f0f003f
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0f0040
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0f0041
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0f0042
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0f0043
int style Base_Theme_AppCompat_Light 0x7f0f0044
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0f0045
int style Base_Theme_AppCompat_Light_Dialog 0x7f0f0046
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0f0047
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0f0048
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0049
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0f004a
int style Base_ThemeOverlay_AppCompat 0x7f0f004b
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0f004c
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0f004d
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f004e
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0f004f
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0050
int style Base_ThemeOverlay_AppCompat_Light 0x7f0f0051
int style Base_V21_Theme_AppCompat 0x7f0f0052
int style Base_V21_Theme_AppCompat_Dialog 0x7f0f0053
int style Base_V21_Theme_AppCompat_Light 0x7f0f0054
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0f0055
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0f0056
int style Base_V22_Theme_AppCompat 0x7f0f0057
int style Base_V22_Theme_AppCompat_Light 0x7f0f0058
int style Base_V23_Theme_AppCompat 0x7f0f0059
int style Base_V23_Theme_AppCompat_Light 0x7f0f005a
int style Base_V26_Theme_AppCompat 0x7f0f005b
int style Base_V26_Theme_AppCompat_Light 0x7f0f005c
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0f005d
int style Base_V28_Theme_AppCompat 0x7f0f005e
int style Base_V28_Theme_AppCompat_Light 0x7f0f005f
int style Base_V7_Theme_AppCompat 0x7f0f0060
int style Base_V7_Theme_AppCompat_Dialog 0x7f0f0061
int style Base_V7_Theme_AppCompat_Light 0x7f0f0062
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0f0063
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0f0064
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0f0065
int style Base_V7_Widget_AppCompat_EditText 0x7f0f0066
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0f0067
int style Base_Widget_AppCompat_ActionBar 0x7f0f0068
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0f0069
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0f006a
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0f006b
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0f006c
int style Base_Widget_AppCompat_ActionButton 0x7f0f006d
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0f006e
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0f006f
int style Base_Widget_AppCompat_ActionMode 0x7f0f0070
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0f0071
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0f0072
int style Base_Widget_AppCompat_Button 0x7f0f0073
int style Base_Widget_AppCompat_Button_Borderless 0x7f0f0074
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0f0075
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0076
int style Base_Widget_AppCompat_Button_Colored 0x7f0f0077
int style Base_Widget_AppCompat_Button_Small 0x7f0f0078
int style Base_Widget_AppCompat_ButtonBar 0x7f0f0079
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f007a
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0f007b
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0f007c
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0f007d
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0f007e
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0f007f
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0f0080
int style Base_Widget_AppCompat_EditText 0x7f0f0081
int style Base_Widget_AppCompat_ImageButton 0x7f0f0082
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0f0083
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0084
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0085
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0086
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0087
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0088
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0f0089
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f008a
int style Base_Widget_AppCompat_ListMenuView 0x7f0f008b
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0f008c
int style Base_Widget_AppCompat_ListView 0x7f0f008d
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0f008e
int style Base_Widget_AppCompat_ListView_Menu 0x7f0f008f
int style Base_Widget_AppCompat_PopupMenu 0x7f0f0090
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0f0091
int style Base_Widget_AppCompat_PopupWindow 0x7f0f0092
int style Base_Widget_AppCompat_ProgressBar 0x7f0f0093
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0f0094
int style Base_Widget_AppCompat_RatingBar 0x7f0f0095
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0f0096
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0f0097
int style Base_Widget_AppCompat_SearchView 0x7f0f0098
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0f0099
int style Base_Widget_AppCompat_SeekBar 0x7f0f009a
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0f009b
int style Base_Widget_AppCompat_Spinner 0x7f0f009c
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0f009d
int style Base_Widget_AppCompat_TextView 0x7f0f009e
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0f009f
int style Base_Widget_AppCompat_Toolbar 0x7f0f00a0
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f00a1
int style CardView 0x7f0f00a2
int style CardView_Dark 0x7f0f00a3
int style CardView_Light 0x7f0f00a4
int style DialogWindowTheme 0x7f0f00a5
int style EdgeToEdgeFloatingDialogTheme 0x7f0f00a6
int style EdgeToEdgeFloatingDialogWindowTheme 0x7f0f00a7
int style FloatingDialogTheme 0x7f0f00a8
int style FloatingDialogWindowTheme 0x7f0f00a9
int style Platform_AppCompat 0x7f0f00aa
int style Platform_AppCompat_Light 0x7f0f00ab
int style Platform_ThemeOverlay_AppCompat 0x7f0f00ac
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0f00ad
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0f00ae
int style Platform_V21_AppCompat 0x7f0f00af
int style Platform_V21_AppCompat_Light 0x7f0f00b0
int style Platform_V25_AppCompat 0x7f0f00b1
int style Platform_V25_AppCompat_Light 0x7f0f00b2
int style Platform_Widget_AppCompat_Spinner 0x7f0f00b3
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0f00b4
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0f00b5
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0f00b6
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0f00b7
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0f00b8
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0f00b9
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0f00ba
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0f00bb
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0f00bc
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0f00bd
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0f00be
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0f00bf
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0f00c0
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0f00c1
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0f00c2
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0f00c3
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0f00c4
int style TextAppearance_AppCompat 0x7f0f00c5
int style TextAppearance_AppCompat_Body1 0x7f0f00c6
int style TextAppearance_AppCompat_Body2 0x7f0f00c7
int style TextAppearance_AppCompat_Button 0x7f0f00c8
int style TextAppearance_AppCompat_Caption 0x7f0f00c9
int style TextAppearance_AppCompat_Display1 0x7f0f00ca
int style TextAppearance_AppCompat_Display2 0x7f0f00cb
int style TextAppearance_AppCompat_Display3 0x7f0f00cc
int style TextAppearance_AppCompat_Display4 0x7f0f00cd
int style TextAppearance_AppCompat_Headline 0x7f0f00ce
int style TextAppearance_AppCompat_Inverse 0x7f0f00cf
int style TextAppearance_AppCompat_Large 0x7f0f00d0
int style TextAppearance_AppCompat_Large_Inverse 0x7f0f00d1
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0f00d2
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0f00d3
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f00d4
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f00d5
int style TextAppearance_AppCompat_Medium 0x7f0f00d6
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0f00d7
int style TextAppearance_AppCompat_Menu 0x7f0f00d8
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f00d9
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0f00da
int style TextAppearance_AppCompat_Small 0x7f0f00db
int style TextAppearance_AppCompat_Small_Inverse 0x7f0f00dc
int style TextAppearance_AppCompat_Subhead 0x7f0f00dd
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0f00de
int style TextAppearance_AppCompat_Title 0x7f0f00df
int style TextAppearance_AppCompat_Title_Inverse 0x7f0f00e0
int style TextAppearance_AppCompat_Tooltip 0x7f0f00e1
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f00e2
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f00e3
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f00e4
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f00e5
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f00e6
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f00e7
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0f00e8
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f00e9
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0f00ea
int style TextAppearance_AppCompat_Widget_Button 0x7f0f00eb
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f00ec
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f00ed
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f00ee
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f00ef
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f00f0
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f00f1
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f00f2
int style TextAppearance_AppCompat_Widget_Switch 0x7f0f00f3
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f00f4
int style TextAppearance_Compat_Notification 0x7f0f00f5
int style TextAppearance_Compat_Notification_Info 0x7f0f00f6
int style TextAppearance_Compat_Notification_Info_Media 0x7f0f00f7
int style TextAppearance_Compat_Notification_Line2 0x7f0f00f8
int style TextAppearance_Compat_Notification_Line2_Media 0x7f0f00f9
int style TextAppearance_Compat_Notification_Media 0x7f0f00fa
int style TextAppearance_Compat_Notification_Time 0x7f0f00fb
int style TextAppearance_Compat_Notification_Time_Media 0x7f0f00fc
int style TextAppearance_Compat_Notification_Title 0x7f0f00fd
int style TextAppearance_Compat_Notification_Title_Media 0x7f0f00fe
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f00ff
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f0100
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f0101
int style Theme_AppCompat 0x7f0f0102
int style Theme_AppCompat_CompactMenu 0x7f0f0103
int style Theme_AppCompat_DayNight 0x7f0f0104
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0f0105
int style Theme_AppCompat_DayNight_Dialog 0x7f0f0106
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0f0107
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0f0108
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0f0109
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0f010a
int style Theme_AppCompat_Dialog 0x7f0f010b
int style Theme_AppCompat_Dialog_Alert 0x7f0f010c
int style Theme_AppCompat_Dialog_MinWidth 0x7f0f010d
int style Theme_AppCompat_DialogWhenLarge 0x7f0f010e
int style Theme_AppCompat_Empty 0x7f0f010f
int style Theme_AppCompat_Light 0x7f0f0110
int style Theme_AppCompat_Light_DarkActionBar 0x7f0f0111
int style Theme_AppCompat_Light_Dialog 0x7f0f0112
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0f0113
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0114
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0f0115
int style Theme_AppCompat_Light_NoActionBar 0x7f0f0116
int style Theme_AppCompat_NoActionBar 0x7f0f0117
int style Theme_HealthyProducts 0x7f0f0118
int style ThemeOverlay_AppCompat 0x7f0f0119
int style ThemeOverlay_AppCompat_ActionBar 0x7f0f011a
int style ThemeOverlay_AppCompat_Dark 0x7f0f011b
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f011c
int style ThemeOverlay_AppCompat_DayNight 0x7f0f011d
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f0f011e
int style ThemeOverlay_AppCompat_Dialog 0x7f0f011f
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0120
int style ThemeOverlay_AppCompat_Light 0x7f0f0121
int style Widget_AppCompat_ActionBar 0x7f0f0122
int style Widget_AppCompat_ActionBar_Solid 0x7f0f0123
int style Widget_AppCompat_ActionBar_TabBar 0x7f0f0124
int style Widget_AppCompat_ActionBar_TabText 0x7f0f0125
int style Widget_AppCompat_ActionBar_TabView 0x7f0f0126
int style Widget_AppCompat_ActionButton 0x7f0f0127
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0f0128
int style Widget_AppCompat_ActionButton_Overflow 0x7f0f0129
int style Widget_AppCompat_ActionMode 0x7f0f012a
int style Widget_AppCompat_ActivityChooserView 0x7f0f012b
int style Widget_AppCompat_AutoCompleteTextView 0x7f0f012c
int style Widget_AppCompat_Button 0x7f0f012d
int style Widget_AppCompat_Button_Borderless 0x7f0f012e
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0f012f
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0130
int style Widget_AppCompat_Button_Colored 0x7f0f0131
int style Widget_AppCompat_Button_Small 0x7f0f0132
int style Widget_AppCompat_ButtonBar 0x7f0f0133
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f0134
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0f0135
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0f0136
int style Widget_AppCompat_CompoundButton_Switch 0x7f0f0137
int style Widget_AppCompat_DrawerArrowToggle 0x7f0f0138
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0f0139
int style Widget_AppCompat_EditText 0x7f0f013a
int style Widget_AppCompat_ImageButton 0x7f0f013b
int style Widget_AppCompat_Light_ActionBar 0x7f0f013c
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0f013d
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0f013e
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f013f
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0f0140
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0141
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0142
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0143
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0f0144
int style Widget_AppCompat_Light_ActionButton 0x7f0f0145
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0f0146
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0f0147
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0f0148
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0f0149
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0f014a
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0f014b
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0f014c
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0f014d
int style Widget_AppCompat_Light_PopupMenu 0x7f0f014e
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f014f
int style Widget_AppCompat_Light_SearchView 0x7f0f0150
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0f0151
int style Widget_AppCompat_ListMenuView 0x7f0f0152
int style Widget_AppCompat_ListPopupWindow 0x7f0f0153
int style Widget_AppCompat_ListView 0x7f0f0154
int style Widget_AppCompat_ListView_DropDown 0x7f0f0155
int style Widget_AppCompat_ListView_Menu 0x7f0f0156
int style Widget_AppCompat_PopupMenu 0x7f0f0157
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0f0158
int style Widget_AppCompat_PopupWindow 0x7f0f0159
int style Widget_AppCompat_ProgressBar 0x7f0f015a
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0f015b
int style Widget_AppCompat_RatingBar 0x7f0f015c
int style Widget_AppCompat_RatingBar_Indicator 0x7f0f015d
int style Widget_AppCompat_RatingBar_Small 0x7f0f015e
int style Widget_AppCompat_SearchView 0x7f0f015f
int style Widget_AppCompat_SearchView_ActionBar 0x7f0f0160
int style Widget_AppCompat_SeekBar 0x7f0f0161
int style Widget_AppCompat_SeekBar_Discrete 0x7f0f0162
int style Widget_AppCompat_Spinner 0x7f0f0163
int style Widget_AppCompat_Spinner_DropDown 0x7f0f0164
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0f0165
int style Widget_AppCompat_Spinner_Underlined 0x7f0f0166
int style Widget_AppCompat_TextView 0x7f0f0167
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0f0168
int style Widget_AppCompat_Toolbar 0x7f0f0169
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f016a
int style Widget_Compat_NotificationActionContainer 0x7f0f016b
int style Widget_Compat_NotificationActionText 0x7f0f016c
int style Widget_Support_CoordinatorLayout 0x7f0f016d
int style com_facebook_activity_theme 0x7f0f016e
int style com_facebook_auth_dialog 0x7f0f016f
int style com_facebook_auth_dialog_instructions_textview 0x7f0f0170
int style com_facebook_button 0x7f0f0171
int style com_facebook_button_like 0x7f0f0172
int style com_facebook_loginview_default_style 0x7f0f0173
int style tooltip_bubble_text 0x7f0f0174
int[] styleable ActionBar { 0x7f030035, 0x7f030036, 0x7f030037, 0x7f030077, 0x7f030078, 0x7f030079, 0x7f03007a, 0x7f03007b, 0x7f03007c, 0x7f030084, 0x7f03008c, 0x7f03008d, 0x7f0300a0, 0x7f0300b5, 0x7f0300b6, 0x7f0300b7, 0x7f0300b8, 0x7f0300b9, 0x7f0300c1, 0x7f0300c4, 0x7f0300e0, 0x7f0300ea, 0x7f0300fc, 0x7f0300ff, 0x7f030100, 0x7f030122, 0x7f030125, 0x7f030142, 0x7f03014b }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f030035, 0x7f030036, 0x7f030058, 0x7f0300b5, 0x7f030125, 0x7f03014b }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0300a4, 0x7f0300c2 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f030000, 0x7f030085, 0x7f030086, 0x7f03012b }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AlertDialog { 0x010100f2, 0x7f030043, 0x7f030044, 0x7f0300d5, 0x7f0300d6, 0x7f0300e6, 0x7f030116, 0x7f030117 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f03011c, 0x7f030140, 0x7f030141 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f03013d, 0x7f03013e, 0x7f03013f }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f030030, 0x7f030031, 0x7f030032, 0x7f030033, 0x7f030034, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030096, 0x7f030097, 0x7f030098, 0x7f030099, 0x7f0300a1, 0x7f0300a5, 0x7f0300a7, 0x7f0300b0, 0x7f0300c7, 0x7f0300d0, 0x7f03012c, 0x7f030137 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030024, 0x7f030025, 0x7f030026, 0x7f030027, 0x7f030028, 0x7f03002f, 0x7f03003b, 0x7f03003c, 0x7f03003d, 0x7f03003e, 0x7f03003f, 0x7f030040, 0x7f030046, 0x7f030047, 0x7f030054, 0x7f030055, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f030066, 0x7f030082, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f03008e, 0x7f030090, 0x7f03009b, 0x7f03009c, 0x7f03009d, 0x7f03009e, 0x7f03009f, 0x7f0300b7, 0x7f0300bf, 0x7f0300d1, 0x7f0300d2, 0x7f0300d3, 0x7f0300d4, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0300f3, 0x7f0300f4, 0x7f0300f5, 0x7f0300fb, 0x7f0300fd, 0x7f030104, 0x7f030105, 0x7f030106, 0x7f030107, 0x7f03010e, 0x7f03010f, 0x7f030110, 0x7f030111, 0x7f030119, 0x7f03011a, 0x7f030129, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130, 0x7f030131, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f03014c, 0x7f03014d, 0x7f03014e, 0x7f03014f, 0x7f030156, 0x7f030158, 0x7f030159, 0x7f03015a, 0x7f03015b, 0x7f03015c, 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030160, 0x7f030161 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable ButtonBarLayout { 0x7f030029 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f030103, 0x7f030112 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f03004a, 0x7f03004b, 0x7f03004c, 0x7f03004d, 0x7f03004e, 0x7f03004f, 0x7f03007d, 0x7f03007e, 0x7f03007f, 0x7f030080, 0x7f030081 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable CheckedTextView { 0x01010108, 0x7f030051, 0x7f030052, 0x7f030053 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f03002a, 0x7f0300c6 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f030041, 0x7f030048, 0x7f030049 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable CoordinatorLayout { 0x7f0300c5, 0x7f03011f }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DrawerArrowToggle { 0x7f03002d, 0x7f03002e, 0x7f03003a, 0x7f03005b, 0x7f030095, 0x7f0300b2, 0x7f030118, 0x7f030139 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f0300a8, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0300a6, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f030154 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f03008d, 0x7f03008f, 0x7f0300e3, 0x7f030114 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable LoadingImageView { 0x7f030056, 0x7f0300bd, 0x7f0300be }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000e, 0x7f030022, 0x7f030023, 0x7f03002b, 0x7f030076, 0x7f0300ba, 0x7f0300bb, 0x7f0300ed, 0x7f030113, 0x7f030150 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0300fe, 0x7f030120 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable NavAction { 0x010100d0, 0x7f030088, 0x7f0300a2, 0x7f0300a3, 0x7f0300c8, 0x7f0300f6, 0x7f0300f7, 0x7f0300f8, 0x7f0300f9, 0x7f0300fa, 0x7f030108 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f03002c, 0x7f0300ec }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030000, 0x7f0300e5, 0x7f030155 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f03011d }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f0300e7 }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f0300b4 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f030109 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f0300ee }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f03011e }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PreviewView { 0x7f0300c0, 0x7f03010a }
int styleable PreviewView_implementationMode 0
int styleable PreviewView_scaleType 1
int[] styleable RecycleListView { 0x7f0300ef, 0x7f0300f2 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f030057, 0x7f030075, 0x7f030087, 0x7f0300b3, 0x7f0300bc, 0x7f0300c9, 0x7f030101, 0x7f030102, 0x7f03010c, 0x7f03010d, 0x7f030121, 0x7f030126, 0x7f030157 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable SignInButton { 0x7f030045, 0x7f030065, 0x7f03010b }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0300fc }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f030115, 0x7f03011b, 0x7f030127, 0x7f030128, 0x7f03012a, 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f030151, 0x7f030152, 0x7f030153 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f0300a7, 0x7f0300b0, 0x7f03012c, 0x7f030137 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f030042, 0x7f030059, 0x7f03005a, 0x7f030077, 0x7f030078, 0x7f030079, 0x7f03007a, 0x7f03007b, 0x7f03007c, 0x7f0300e0, 0x7f0300e1, 0x7f0300e2, 0x7f0300e4, 0x7f0300e8, 0x7f0300e9, 0x7f0300fc, 0x7f030122, 0x7f030123, 0x7f030124, 0x7f030142, 0x7f030143, 0x7f030144, 0x7f030145, 0x7f030146, 0x7f030147, 0x7f030148, 0x7f030149, 0x7f03014a }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable View { 0x01010000, 0x010100da, 0x7f0300f0, 0x7f0300f1, 0x7f030138 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f030038, 0x7f030039 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable com_facebook_like_view { 0x7f030067, 0x7f030069, 0x7f03006a, 0x7f030070, 0x7f030071, 0x7f030073 }
int styleable com_facebook_like_view_com_facebook_auxiliary_view_position 0
int styleable com_facebook_like_view_com_facebook_foreground_color 1
int styleable com_facebook_like_view_com_facebook_horizontal_alignment 2
int styleable com_facebook_like_view_com_facebook_object_id 3
int styleable com_facebook_like_view_com_facebook_object_type 4
int styleable com_facebook_like_view_com_facebook_style 5
int[] styleable com_facebook_login_view { 0x7f030068, 0x7f03006c, 0x7f03006d, 0x7f03006e, 0x7f03006f, 0x7f030074 }
int styleable com_facebook_login_view_com_facebook_confirm_logout 0
int styleable com_facebook_login_view_com_facebook_login_button_radius 1
int styleable com_facebook_login_view_com_facebook_login_button_transparency 2
int styleable com_facebook_login_view_com_facebook_login_text 3
int styleable com_facebook_login_view_com_facebook_logout_text 4
int styleable com_facebook_login_view_com_facebook_tooltip_mode 5
int[] styleable com_facebook_profile_picture_view { 0x7f03006b, 0x7f030072 }
int styleable com_facebook_profile_picture_view_com_facebook_is_cropped 0
int styleable com_facebook_profile_picture_view_com_facebook_preset_size 1
int xml backup_rules 0x7f110000
int xml data_extraction_rules 0x7f110001
int xml ga_ad_services_config 0x7f110002
int xml image_share_filepaths 0x7f110003
