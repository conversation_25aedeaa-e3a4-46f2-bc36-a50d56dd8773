package com.healthyproducts.app.di

import com.healthyproducts.app.BuildConfig
import com.healthyproducts.app.data.api.AiService
import com.healthyproducts.app.data.api.AiServiceImpl
import com.healthyproducts.app.data.api.DeepSeekApi
import com.healthyproducts.app.data.api.DeepSeekApiService
import com.healthyproducts.app.data.api.GeminiApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

/**
 * Ağ istekleri için gerekli servisleri sağlayan Hilt modülü
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    /**
     * DeepSeek API için temel URL
     */
    private const val DEEPSEEK_BASE_URL = "https://api.deepseek.com/"

    /**
     * OkHttpClient için bir interceptor sağlar
     */
    @Provides
    @Singleton
    fun provideAuthInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request().newBuilder()
                .addHeader("Authorization", "Bearer ${BuildConfig.DEEPSEEK_API_KEY}")
                .addHeader("Content-Type", "application/json")
                .build()
            chain.proceed(request)
        }
    }

    /**
     * Loglama interceptor'ı sağlar
     */
    @Provides
    @Singleton
    fun provideLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }
    }

    /**
     * OkHttpClient sağlar
     */
    @Provides
    @Singleton
    fun provideOkHttpClient(
        authInterceptor: Interceptor,
        loggingInterceptor: HttpLoggingInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }

    /**
     * DeepSeek API için Retrofit sağlar
     */
    @Provides
    @Singleton
    fun provideDeepSeekApi(okHttpClient: OkHttpClient): DeepSeekApi {
        return Retrofit.Builder()
            .baseUrl(DEEPSEEK_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(DeepSeekApi::class.java)
    }

    /**
     * DeepSeek API Service için Retrofit sağlar
     */
    @Provides
    @Singleton
    fun provideDeepSeekApiService(okHttpClient: OkHttpClient): DeepSeekApiService {
        return Retrofit.Builder()
            .baseUrl(DEEPSEEK_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(DeepSeekApiService::class.java)
    }

    /**
     * DeepSeek API Service için BASE_URL sağlar
     */
    @Provides
    @Singleton
    fun provideDeepSeekBaseUrl(): String {
        return DEEPSEEK_BASE_URL
    }

    /**
     * DeepSeek API için yetkilendirme başlığını sağlar
     */
    @Provides
    @Singleton
    fun provideDeepSeekAuthorizationHeader(): String {
        return "Bearer ${BuildConfig.DEEPSEEK_API_KEY}"
    }

    /**
     * Gemini API Service sağlar
     */
    @Provides
    @Singleton
    fun provideGeminiApiService(): GeminiApiService {
        return GeminiApiService()
    }

    /**
     * AI Service sağlar
     */
    @Provides
    @Singleton
    fun provideAiService(
        deepSeekApiService: DeepSeekApiService,
        geminiApiService: GeminiApiService
    ): AiService {
        return AiServiceImpl(deepSeekApiService, geminiApiService)
    }
}
