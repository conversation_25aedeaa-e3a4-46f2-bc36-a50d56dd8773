package com.healthyproducts.app.data.repository

import android.content.Context
import android.util.Log
import com.healthyproducts.app.data.api.AiService
import com.healthyproducts.app.data.model.SupportedLanguage
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * OCR düzeltme repository'si
 */
@Singleton
class OcrCorrectionRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val aiService: AiService,
    private val firestoreRepository: FirestoreRepository
) {

    // Kullanıcının dil tercihi
    private var userLanguage: SupportedLanguage = SupportedLanguage.TURKISH

    /**
     * OCR ile tanınan içerikleri düzeltir
     */
    suspend fun correctOcrIngredients(ingredients: List<String>, language: SupportedLanguage? = null): Result<List<String>> {
        return withContext(Dispatchers.IO) {
            try {
                // Kullanıcının dil tercihini güncelle
                if (language != null) {
                    userLanguage = language
                } else {
                    // Firestore'dan kullanıcının dil tercihini al
                    firestoreRepository.userLanguage.value.let {
                        userLanguage = it
                    }
                }

                // Önce önbellekte kontrol et
                val cachedCorrections = getCachedCorrections(ingredients)

                // Eğer tüm içerikler önbellekte varsa, önbellekten döndür
                if (cachedCorrections.size == ingredients.size) {
                    return@withContext Result.success(cachedCorrections)
                }

                // Önbellekte olmayan içerikleri API'ye gönder
                val uncachedIngredients = ingredients.filter { ingredient ->
                    !isCached(ingredient)
                }

                if (uncachedIngredients.isEmpty()) {
                    return@withContext Result.success(cachedCorrections)
                }

                // AI modeline istek gönder
                val prompt = buildPrompt(uncachedIngredients, userLanguage)

                // Kullanıcının tercih ettiği AI modelini ve dil tercihini al
                val userPreferences = firestoreRepository.userPreferences.value
                val userLanguageCode = userPreferences.language

                // Dil tercihini güncelle
                userLanguage = SupportedLanguage.fromCode(userLanguageCode)
                Log.d("OcrCorrectionRepo", "User language preference: $userLanguage")

                // Firestore'dan en güncel tercihleri al
                val currentUser = firestoreRepository.authRepository.getCurrentUser()
                if (currentUser != null) {
                    try {
                        // Tercihleri yeniden yükle
                        firestoreRepository.getUserPreferences(currentUser.uid)

                        // Yeniden yüklenen tercihleri kullan
                        val updatedPreferences = firestoreRepository.userPreferences.value
                        Log.d("OcrCorrectionRepo", "Refreshed user preferences: aiModel=${updatedPreferences.aiModel}, language=${updatedPreferences.language}")

                        // Dil tercihini güncelle
                        userLanguage = SupportedLanguage.fromCode(updatedPreferences.language)

                        // Güncellenmiş dil tercihine göre prompt oluştur
                        val updatedPrompt = buildPrompt(uncachedIngredients, userLanguage)

                        // AI modeline istek gönder
                        Log.d("OcrCorrectionRepo", "Sending request to AI service with model: ${updatedPreferences.aiModel}")
                        val correctedText = aiService.generateText(updatedPrompt, updatedPreferences.aiModel)
                        Log.d("OcrCorrectionRepo", "Received response from AI service")
                        return@withContext Result.success(parseResponse(correctedText))
                    } catch (e: Exception) {
                        Log.e("OcrCorrectionRepo", "Error refreshing user preferences", e)
                    }
                }

                // Eğer tercihleri yeniden yükleyemezsek, mevcut tercihleri kullan
                val userAiModel = userPreferences.aiModel
                Log.d("OcrCorrectionRepo", "Using existing AI model: $userAiModel")
                Log.d("OcrCorrectionRepo", "All user preferences: $userPreferences")

                // Güncellenmiş dil tercihine göre prompt oluştur
                val finalPrompt = buildPrompt(uncachedIngredients, userLanguage)

                // AI modeline istek gönder
                Log.d("OcrCorrectionRepo", "Sending request to AI service with model: $userAiModel")
                val correctedText = aiService.generateText(finalPrompt, userAiModel)
                Log.d("OcrCorrectionRepo", "Received response from AI service")
                val correctedIngredients = parseResponse(correctedText)

                // Düzeltmeleri önbelleğe al
                cacheCorrections(uncachedIngredients, correctedIngredients)

                // Önbellekteki düzeltmelerle birleştir
                val allCorrectedIngredients = cachedCorrections + correctedIngredients

                Result.success(allCorrectedIngredients)
            } catch (e: Exception) {
                Log.e("OcrCorrectionRepo", "Error correcting ingredients", e)
                Result.failure(e)
            }
        }
    }

    /**
     * AI modeli için prompt oluşturur
     */
    private fun buildPrompt(ingredients: List<String>, language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> {
                """
                Bu metin, bir gıda ürününün içerik listesinden OCR ile tanınmıştır ve bazı hatalar içerebilir.
                Lütfen bu içerikleri düzelt ve standart gıda maddesi isimlerine dönüştür:

                ${ingredients.joinToString(", ")}

                Düzeltilmiş içerikleri Türkçe olarak, virgülle ayrılmış liste olarak döndür.
                Sadece düzeltilmiş içerik listesini döndür, başka açıklama ekleme.
                """.trimIndent()
            }
            SupportedLanguage.ENGLISH -> {
                """
                This text has been recognized by OCR from a food product's ingredient list and may contain errors.
                Please correct these ingredients and convert them to standard food ingredient names:

                ${ingredients.joinToString(", ")}

                Return the corrected ingredients in English as a comma-separated list.
                Only return the corrected ingredient list, do not add any explanations.
                """.trimIndent()
            }
        }
    }

    /**
     * AI modeli yanıtını ayrıştırır
     */
    private fun parseResponse(response: String): List<String> {
        Log.d("OcrCorrectionRepo", "Parsing AI response: $response")

        // Yanıtı virgülle ayrılmış liste olarak ayrıştır
        val result = response
            .split(",")
            .map { it.trim() }
            .filter { it.isNotEmpty() }

        Log.d("OcrCorrectionRepo", "Parsed ingredients: ${result.joinToString(", ")}")
        return result
    }

    /**
     * İçeriğin önbellekte olup olmadığını kontrol eder
     */
    private fun isCached(ingredient: String): Boolean {
        val sharedPrefs = context.getSharedPreferences("OCR_CORRECTIONS", Context.MODE_PRIVATE)
        return sharedPrefs.contains(ingredient.lowercase())
    }

    /**
     * Önbellekteki düzeltmeleri getirir
     */
    private fun getCachedCorrections(ingredients: List<String>): List<String> {
        val sharedPrefs = context.getSharedPreferences("OCR_CORRECTIONS", Context.MODE_PRIVATE)
        return ingredients
            .filter { isCached(it) }
            .map { ingredient ->
                sharedPrefs.getString(ingredient.lowercase(), ingredient) ?: ingredient
            }
    }

    /**
     * Düzeltmeleri önbelleğe alır
     */
    private fun cacheCorrections(originalIngredients: List<String>, correctedIngredients: List<String>) {
        if (originalIngredients.size != correctedIngredients.size) {
            // Eşleşmeyen boyutlar, önbelleğe alma
            return
        }

        val sharedPrefs = context.getSharedPreferences("OCR_CORRECTIONS", Context.MODE_PRIVATE)
        val editor = sharedPrefs.edit()

        originalIngredients.forEachIndexed { index, original ->
            val corrected = correctedIngredients.getOrNull(index) ?: return@forEachIndexed
            editor.putString(original.lowercase(), corrected)
        }

        editor.apply()
    }
}
