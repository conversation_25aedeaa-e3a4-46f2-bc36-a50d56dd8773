package com.healthyproducts.app.di;

import com.healthyproducts.app.data.api.DeepSeekApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideDeepSeekApiFactory implements Factory<DeepSeekApi> {
  private final Provider<OkHttpClient> okHttpClientProvider;

  public NetworkModule_ProvideDeepSeekApiFactory(Provider<OkHttpClient> okHttpClientProvider) {
    this.okHttpClientProvider = okHttpClientProvider;
  }

  @Override
  public DeepSeekApi get() {
    return provideDeepSeekApi(okHttpClientProvider.get());
  }

  public static NetworkModule_ProvideDeepSeekApiFactory create(
      Provider<OkHttpClient> okHttpClientProvider) {
    return new NetworkModule_ProvideDeepSeekApiFactory(okHttpClientProvider);
  }

  public static DeepSeekApi provideDeepSeekApi(OkHttpClient okHttpClient) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideDeepSeekApi(okHttpClient));
  }
}
