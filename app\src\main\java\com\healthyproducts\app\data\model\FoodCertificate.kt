package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName
import com.google.gson.annotations.SerializedName

/**
 * Gıda sertifikasını temsil eden veri sınıfı
 */
data class FoodCertificate(
    @DocumentId
    val id: String = "",

    // Benzersiz tanımlayıcı (örn. "halal", "vegan")
    var certificateId: String = "",

    @SerializedName("name_tr")
    @get:PropertyName("name_tr")
    @set:PropertyName("name_tr")
    var nameTr: String = "", // Sertifikanın Türkçe adı

    @SerializedName("name_en")
    @get:PropertyName("name_en")
    @set:PropertyName("name_en")
    var nameEn: String = "", // Sertifikanın İngilizce adı

    @SerializedName("symbol")
    @get:PropertyName("symbol")
    @set:PropertyName("symbol")
    var symbol: String = "", // Emoji veya ikon referansı

    @SerializedName("description_tr")
    @get:PropertyName("description_tr")
    @set:PropertyName("description_tr")
    var descriptionTr: String = "", // Sertifikanın Türkçe açıklaması

    @SerializedName("description_en")
    @get:PropertyName("description_en")
    @set:PropertyName("description_en")
    var descriptionEn: String = "", // Sertifikanın İngilizce açıklaması

    @SerializedName("issuer_tr")
    @get:PropertyName("issuer_tr")
    @set:PropertyName("issuer_tr")
    var issuerTr: List<String> = listOf(), // Türkçe sertifika veren kurumlar

    @SerializedName("issuer_en")
    @get:PropertyName("issuer_en")
    @set:PropertyName("issuer_en")
    var issuerEn: List<String> = listOf(), // İngilizce sertifika veren kurumlar

    @SerializedName("valid_for_tr")
    @get:PropertyName("valid_for_tr")
    @set:PropertyName("valid_for_tr")
    var validForTr: String = "", // Türkçe geçerli olduğu ürünler

    @SerializedName("valid_for_en")
    @get:PropertyName("valid_for_en")
    @set:PropertyName("valid_for_en")
    var validForEn: String = "", // İngilizce geçerli olduğu ürünler

    @SerializedName("region")
    @get:PropertyName("region")
    @set:PropertyName("region")
    var region: List<String> = listOf(), // Geçerli olduğu bölgeler

    @SerializedName("risk_if_missing_tr")
    @get:PropertyName("risk_if_missing_tr")
    @set:PropertyName("risk_if_missing_tr")
    var riskIfMissingTr: String = "", // Türkçe eksiklik riski açıklaması

    @SerializedName("risk_if_missing_en")
    @get:PropertyName("risk_if_missing_en")
    @set:PropertyName("risk_if_missing_en")
    var riskIfMissingEn: String = "", // İngilizce eksiklik riski açıklaması

    @SerializedName("notes_tr")
    @get:PropertyName("notes_tr")
    @set:PropertyName("notes_tr")
    var notesTr: String = "", // Türkçe ek notlar

    @SerializedName("notes_en")
    @get:PropertyName("notes_en")
    @set:PropertyName("notes_en")
    var notesEn: String = "", // İngilizce ek notlar

    @SerializedName("functional_type")
    @get:PropertyName("functional_type")
    @set:PropertyName("functional_type")
    var functionalType: List<String> = listOf() // Fonksiyonel tür (örn. "vegan", "halal", "kosher", "organic")
) {
    // Firestore için boş constructor
    constructor() : this(
        id = "",
        certificateId = "",
        nameTr = "",
        nameEn = "",
        symbol = "",
        descriptionTr = "",
        descriptionEn = "",
        issuerTr = listOf(),
        issuerEn = listOf(),
        validForTr = "",
        validForEn = "",
        region = listOf(),
        riskIfMissingTr = "",
        riskIfMissingEn = "",
        notesTr = "",
        notesEn = "",
        functionalType = listOf()
    )

    // Kullanıcının dil tercihine göre adı döndürür
    fun getName(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> nameTr.ifEmpty { nameEn }
            SupportedLanguage.ENGLISH -> nameEn.ifEmpty { nameTr }
        }
    }

    // Kullanıcının dil tercihine göre açıklamayı döndürür
    fun getDescription(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> descriptionTr.ifEmpty { descriptionEn }
            SupportedLanguage.ENGLISH -> descriptionEn.ifEmpty { descriptionTr }
        }
    }

    // Kullanıcının dil tercihine göre standartları döndürür
    fun getStandards(language: SupportedLanguage): List<String> {
        return listOf() // Şu an için boş liste döndür
    }

    // Kullanıcının dil tercihine göre uygulanabilir ürünleri döndürür
    fun getAppliesTo(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> validForTr.ifEmpty { validForEn }
            SupportedLanguage.ENGLISH -> validForEn.ifEmpty { validForTr }
        }
    }

    // Kullanıcının dil tercihine göre eksiklik riski açıklamasını döndürür
    fun getRiskIfMissing(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> riskIfMissingTr.ifEmpty { riskIfMissingEn }
            SupportedLanguage.ENGLISH -> riskIfMissingEn.ifEmpty { riskIfMissingTr }
        }
    }

    // Kullanıcının dil tercihine göre notları döndürür
    fun getNotes(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> notesTr.ifEmpty { notesEn }
            SupportedLanguage.ENGLISH -> notesEn.ifEmpty { notesTr }
        }
    }

    // Kullanıcının dil tercihine göre sertifika veren kurumu döndürür
    fun getCertifyingBody(language: SupportedLanguage): String {
        val issuers = when (language) {
            SupportedLanguage.TURKISH -> issuerTr.ifEmpty { issuerEn }
            SupportedLanguage.ENGLISH -> issuerEn.ifEmpty { issuerTr }
        }

        return if (issuers.isNotEmpty()) {
            issuers.joinToString(", ")
        } else {
            when (language) {
                SupportedLanguage.TURKISH -> "Sertifika veren kurum bilgisi mevcut değil"
                SupportedLanguage.ENGLISH -> "Certifying body information not available"
            }
        }
    }

    // Kullanıcının dil tercihine göre sertifika kriterlerini döndürür
    fun getCertificationCriteria(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> "Sertifika kriterleri bilgisi mevcut değil"
            SupportedLanguage.ENGLISH -> "Certification criteria information not available"
        }
    }

    // Kullanıcının dil tercihine göre sertifika sürecini döndürür
    fun getCertificationProcess(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> "Sertifika süreci bilgisi mevcut değil"
            SupportedLanguage.ENGLISH -> "Certification process information not available"
        }
    }

    // Kullanıcının dil tercihine göre sertifika geçerliliğini döndürür
    fun getCertificationValidity(language: SupportedLanguage): String {
        return if (region.isNotEmpty()) {
            region.joinToString(", ")
        } else {
            when (language) {
                SupportedLanguage.TURKISH -> "Sertifika geçerliliği bilgisi mevcut değil"
                SupportedLanguage.ENGLISH -> "Certification validity information not available"
            }
        }
    }

    // Vegan durumlarını döndürür
    val veganStatuses: List<String>
        get() {
            val veganRelated = listOf("vegan", "vegetarian", "no_animal", "no_dairy", "no_egg", "no_honey", "ethical")
            return functionalType.filter { type ->
                veganRelated.any { it == type || type.contains(it, ignoreCase = true) }
            }
        }

    // Dini durumları döndürür
    val religiousStatuses: List<String>
        get() {
            val religiousRelated = listOf("religious", "halal", "kosher", "haram", "pork_free", "alcohol_free")
            return functionalType.filter { type ->
                religiousRelated.any { it == type || type.contains(it, ignoreCase = true) }
            }
        }
}
