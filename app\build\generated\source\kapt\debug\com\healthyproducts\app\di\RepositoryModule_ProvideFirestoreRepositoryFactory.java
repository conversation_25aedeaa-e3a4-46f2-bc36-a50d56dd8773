package com.healthyproducts.app.di;

import com.google.firebase.firestore.FirebaseFirestore;
import com.healthyproducts.app.data.repository.FirebaseAuthRepository;
import com.healthyproducts.app.data.repository.FirestoreRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideFirestoreRepositoryFactory implements Factory<FirestoreRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  private final Provider<FirebaseAuthRepository> authRepositoryProvider;

  public RepositoryModule_ProvideFirestoreRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    this.firestoreProvider = firestoreProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public FirestoreRepository get() {
    return provideFirestoreRepository(firestoreProvider.get(), authRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideFirestoreRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    return new RepositoryModule_ProvideFirestoreRepositoryFactory(firestoreProvider, authRepositoryProvider);
  }

  public static FirestoreRepository provideFirestoreRepository(FirebaseFirestore firestore,
      FirebaseAuthRepository authRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideFirestoreRepository(firestore, authRepository));
  }
}
