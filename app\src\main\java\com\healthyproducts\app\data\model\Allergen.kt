package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName

/**
 * Alerjeni temsil eden veri sınıfı
 */
data class Allergen(
    @get:PropertyName("id")
    @set:PropertyName("id")
    var id: String = "",

    // Benzersiz tanımlayıcı (örn. "milk", "peanut")
    @get:PropertyName("allergenId")
    @set:PropertyName("allergenId")
    var allergenId: String = "",

    // Zararlılık seviyesi (0-5)
    @get:PropertyName("harmful_level")
    @set:PropertyName("harmful_level")
    var harmfulLevel: Int = 0,

    // Sağlıksızlık seviyesi (0-5)
    @get:PropertyName("unhealthy_level")
    @set:PropertyName("unhealthy_level")
    var unhealthyLevel: Int = 0,

    @get:PropertyName("name_tr")
    @set:PropertyName("name_tr")
    var nameTr: String = "", // Alerjenin Türkçe adı

    @get:PropertyName("name_en")
    @set:PropertyName("name_en")
    var nameEn: String = "", // Alerjenin İngilizce adı

    @get:PropertyName("symbol")
    @set:PropertyName("symbol")
    var symbol: String = "", // Emoji veya ikon referansı

    @get:PropertyName("description_tr")
    @set:PropertyName("description_tr")
    var descriptionTr: String = "", // Alerjenin Türkçe açıklaması

    @get:PropertyName("description_en")
    @set:PropertyName("description_en")
    var descriptionEn: String = "", // Alerjenin İngilizce açıklaması

    @get:PropertyName("hidden_names_tr")
    @set:PropertyName("hidden_names_tr")
    var hiddenNamesTr: List<String> = listOf(), // Türkçe alternatif isimler

    @get:PropertyName("hidden_names_en")
    @set:PropertyName("hidden_names_en")
    var hiddenNamesEn: List<String> = listOf(), // İngilizce alternatif isimler

    @get:PropertyName("risk_level")
    @set:PropertyName("risk_level")
    var riskLevel: Int = 0, // Risk seviyesi (1-3)

    @get:PropertyName("common_products_tr")
    @set:PropertyName("common_products_tr")
    var commonProductsTr: List<String> = listOf(), // Türkçe yaygın ürünler

    @get:PropertyName("common_products_en")
    @set:PropertyName("common_products_en")
    var commonProductsEn: List<String> = listOf(), // İngilizce yaygın ürünler

    @get:PropertyName("notes_tr")
    @set:PropertyName("notes_tr")
    var notesTr: String = "", // Türkçe ek notlar

    @get:PropertyName("notes_en")
    @set:PropertyName("notes_en")
    var notesEn: String = "", // İngilizce ek notlar

    @get:PropertyName("functional_type")
    @set:PropertyName("functional_type")
    var functionalType: List<String> = listOf(), // Fonksiyonel tür (örn. "allergen", "intolerance")

    @get:PropertyName("source_tr")
    @set:PropertyName("source_tr")
    var sourceTr: String = "", // Türkçe köken

    @get:PropertyName("source_en")
    @set:PropertyName("source_en")
    var sourceEn: String = "", // İngilizce köken

    @get:PropertyName("vegan_statuses")
    @set:PropertyName("vegan_statuses")
    var veganStatuses: List<String> = listOf(), // Vegan durumları (örn. "vegan", "vegetarian")

    @get:PropertyName("religious_statuses")
    @set:PropertyName("religious_statuses")
    var religiousStatuses: List<String> = listOf() // Dini durumlar (örn. "halal", "kosher")
) {
    // Firestore için boş constructor
    constructor() : this(
        id = "",
        allergenId = "",
        harmfulLevel = 0,
        unhealthyLevel = 0,
        nameTr = "",
        nameEn = "",
        symbol = "",
        descriptionTr = "",
        descriptionEn = "",
        hiddenNamesTr = listOf(),
        hiddenNamesEn = listOf(),
        riskLevel = 0,
        commonProductsTr = listOf(),
        commonProductsEn = listOf(),
        notesTr = "",
        notesEn = "",
        functionalType = listOf(),
        sourceTr = "",
        sourceEn = "",
        veganStatuses = listOf(),
        religiousStatuses = listOf()
    )

    // Kullanıcının dil tercihine göre adı döndürür
    fun getName(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> nameTr.ifEmpty { nameEn }
            SupportedLanguage.ENGLISH -> nameEn.ifEmpty { nameTr }
        }
    }

    // Kullanıcının dil tercihine göre açıklamayı döndürür
    fun getDescription(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> descriptionTr.ifEmpty { descriptionEn }
            SupportedLanguage.ENGLISH -> descriptionEn.ifEmpty { descriptionTr }
        }
    }

    // Kullanıcının dil tercihine göre gizli isimleri döndürür
    fun getHiddenNames(language: SupportedLanguage): List<String> {
        return when (language) {
            SupportedLanguage.TURKISH -> hiddenNamesTr.ifEmpty { hiddenNamesEn }
            SupportedLanguage.ENGLISH -> hiddenNamesEn.ifEmpty { hiddenNamesTr }
        }
    }

    // Kullanıcının dil tercihine göre yaygın ürünleri döndürür
    fun getCommonProducts(language: SupportedLanguage): List<String> {
        return when (language) {
            SupportedLanguage.TURKISH -> commonProductsTr.ifEmpty { commonProductsEn }
            SupportedLanguage.ENGLISH -> commonProductsEn.ifEmpty { commonProductsTr }
        }
    }

    // Kullanıcının dil tercihine göre notları döndürür
    fun getNotes(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> notesTr.ifEmpty { notesEn }
            SupportedLanguage.ENGLISH -> notesEn.ifEmpty { notesTr }
        }
    }

    // Kullanıcının dil tercihine göre kökeni döndürür
    fun getOrigin(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> sourceTr.ifEmpty { sourceEn }
            SupportedLanguage.ENGLISH -> sourceEn.ifEmpty { sourceTr }
        }
    }

    // Vegan durumunu kontrol eder
    fun isVegan(): Boolean {
        return veganStatuses.contains("vegan")
    }

    // Vejetaryen durumunu kontrol eder
    fun isVegetarian(): Boolean {
        return veganStatuses.contains("vegetarian")
    }

    // Helal durumunu kontrol eder
    fun isHalal(): Boolean {
        return religiousStatuses.contains("halal")
    }

    // Koşer durumunu kontrol eder
    fun isKosher(): Boolean {
        return religiousStatuses.contains("kosher")
    }

    // Kullanım bilgisini döndürür (JSON'da olmadığı için açıklamadan türetiyoruz)
    fun getUsage(language: SupportedLanguage): String {
        return getDescription(language)
    }

    // Sağlık etkilerini döndürür (JSON'da olmadığı için açıklamadan türetiyoruz)
    fun getHealthEffects(language: SupportedLanguage): String {
        return getDescription(language)
    }
}
