package com.healthyproducts.app.data.api;

/**
 * Gemini API servisi
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J \u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\t"}, d2 = {"Lcom/healthyproducts/app/data/api/GeminiApiService;", "", "()V", "generateText", "Lcom/google/ai/client/generativeai/type/GenerateContentResponse;", "prompt", "", "modelName", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class GeminiApiService {
    
    @javax.inject.Inject()
    public GeminiApiService() {
        super();
    }
    
    /**
     * Gemini API'sine istek gönderir ve metin üretir
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object generateText(@org.jetbrains.annotations.NotNull()
    java.lang.String prompt, @org.jetbrains.annotations.NotNull()
    java.lang.String modelName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.google.ai.client.generativeai.type.GenerateContentResponse> $completion) {
        return null;
    }
}