package com.healthyproducts.app.data.repository;

import com.healthyproducts.app.data.api.DeepSeekApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DeepSeekRepository_Factory implements Factory<DeepSeekRepository> {
  private final Provider<DeepSeekApi> deepSeekApiProvider;

  private final Provider<FirestoreRepository> firestoreRepositoryProvider;

  public DeepSeekRepository_Factory(Provider<DeepSeekApi> deepSeekApiProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    this.deepSeekApiProvider = deepSeekApiProvider;
    this.firestoreRepositoryProvider = firestoreRepositoryProvider;
  }

  @Override
  public DeepSeekRepository get() {
    return newInstance(deepSeekApiProvider.get(), firestoreRepositoryProvider.get());
  }

  public static DeepSeekRepository_Factory create(Provider<DeepSeekApi> deepSeekApiProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    return new DeepSeekRepository_Factory(deepSeekApiProvider, firestoreRepositoryProvider);
  }

  public static DeepSeekRepository newInstance(DeepSeekApi deepSeekApi,
      FirestoreRepository firestoreRepository) {
    return new DeepSeekRepository(deepSeekApi, firestoreRepository);
  }
}
