"-Xallow-no-source-files" "-classpath" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6c5103162d840d72278d0e81f9f14fed\\transformed\\hilt-navigation-compose-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2478a2e3c96b0545660dc87d3c297b1c\\transformed\\hilt-navigation-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e91fd52239c35e7d45dda3b04898320\\transformed\\navigation-common-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3a9cd94335a8c8b6bbb8993997e591ec\\transformed\\navigation-runtime-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3a33c1bdcb2d81e07fabb95aa2142150\\transformed\\navigation-common-ktx-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01d8066b87ff206e1c25ac981c080743\\transformed\\navigation-runtime-ktx-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\234c5e817fd997bcf138ad28704f85cb\\transformed\\navigation-compose-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b3f8696ad8c9450cb092ce25edd84ad\\transformed\\material3-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8942a1799431ee8b2af5a529081bc069\\transformed\\coil-compose-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e045b92603fb7ae0a705d2ad6ade23e0\\transformed\\coil-compose-base-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\97cd7813c3f50664f53f9b16131fd829\\transformed\\foundation-layout-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0e3f08f1ad1b00296c21f8b0c4cc161a\\transformed\\material-ripple-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d14b1bcff7c55591c8e8c01dcbc33015\\transformed\\foundation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01401fc0bcc760f30af4a65134bb281d\\transformed\\animation-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\239f531f7d57d6e52c22003b2f0af539\\transformed\\animation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e4e99984e40e5a8dc5139c9b9aa44432\\transformed\\ui-util-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5bcad6b6712e872dd170276d33f596f7\\transformed\\ui-unit-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\358c22d8653ccc7d32c26fbf4ef7f493\\transformed\\ui-text-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2dbbccc080fb9f9c85c904965b588780\\transformed\\ui-geometry-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ced5285563aeef1f01b0c2321cddf7ea\\transformed\\ui-tooling-data-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a2a506f80c2749343ce9f788811fe8a1\\transformed\\ui-tooling-preview-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eba5e6b39a89b00f6d2360b12645ec19\\transformed\\ui-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4b3734fa2fa684282db66e12e0babee1\\transformed\\camera-video-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\99bf26abdaf629bcf14ba18316c8ca4c\\transformed\\camera-view-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\577ccd915b865e672440f55ff19a043a\\transformed\\camera-lifecycle-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\758cf0ae60f159ecec2937c2dfdf51a5\\transformed\\camera-camera2-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69d48dc2d458ffd0f8c2212d957de20a\\transformed\\camera-core-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\499b6f193482d037eb5be2afb7e23d94\\transformed\\hilt-android-2.48-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d2bd333cae7706d292acac34f4af50ef\\transformed\\facebook-login-16.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1ce5715aca621352e13f7b0f6d8698df\\transformed\\facebook-common-16.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\38f98ea0869b912b35c740827646d4fa\\transformed\\appcompat-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\110a49a1b72e9f40be2963cdadc94656\\transformed\\firebase-auth-22.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7d8bf8cfe100554aac71fb6b59174255\\transformed\\play-services-auth-20.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f35920f65b31947809deae9cd079c18\\transformed\\barcode-scanning-17.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a9a8986e8b4c73e414192de9c9440f4a\\transformed\\text-recognition-16.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25d6fba1c3d10736920ac282c4511968\\transformed\\firebase-firestore-24.10.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b937845a5838b45465e1b2a7a8647405\\transformed\\play-services-mlkit-barcode-scanning-18.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\20af76916118d4331497567c56c9f2e4\\transformed\\barcode-scanning-common-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9730f5863114f15e847ecb08a5d5cb5c\\transformed\\text-recognition-bundled-common-16.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1c12f85d1e9aaa70034ab3416cb91b9e\\transformed\\play-services-mlkit-text-recognition-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c82578f4dd6655f06505f750d458768b\\transformed\\play-services-mlkit-text-recognition-common-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6e56ce9b904024952c7b0accf715bfb5\\transformed\\vision-common-17.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\619f6f58b0c6cbf57b5485aede942029\\transformed\\common-18.9.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a61d9fa356f7eb863098a9b5de11ebb8\\transformed\\firebase-storage-20.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\59139c01413d18b2c5522b1cad74b7fe\\transformed\\play-services-auth-api-phone-18.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\263cd498f6143bd8fb3ea5fc2f1a792d\\transformed\\firebase-appcheck-17.1.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\742a532cc69b2a351655bb67a06ca958\\transformed\\firebase-appcheck-interop-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6c5dfe3a99e3ba3ca9bd14766c1a7fe0\\transformed\\firebase-database-collection-18.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d89e6fcec5ea829ec114148b27614d9c\\transformed\\play-services-auth-base-18.0.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0fc53b58fd5be9f314c5791c1d5440c9\\transformed\\play-services-fido-20.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5289c0c6a5a80a85817a7cf5a007b30f\\transformed\\play-services-base-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2cd079b1a860461f0fe4c3c986214333\\transformed\\firebase-analytics-21.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b0b22623554a05a10dd6a51a6ebeb3fe\\transformed\\play-services-measurement-21.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\89e3c2c1e4f07091c17bc8830a94579c\\transformed\\play-services-measurement-api-21.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9d6dc6719741e56efb400f77c9bd9d3e\\transformed\\play-services-measurement-sdk-21.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ef055a397224b1e10a703ada60b83e\\transformed\\firebase-installations-17.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0acbf668fd321baf5b134f9e0316e64b\\transformed\\firebase-common-ktx-20.4.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3ea1447364c121060c0727754cd8147\\transformed\\firebase-auth-interop-20.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\138d87f59a0f788648f4031497458318\\transformed\\firebase-common-20.4.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\97a89ae0041cbfe2baf9866e60fdc31c\\transformed\\recaptcha-18.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f51d69af14dd73a57ee0696b6b62500f\\transformed\\integrity-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72aac1e5486b970cd654c770ce1ccc10\\transformed\\vision-interfaces-16.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1a2ebbdc1c9acdeb31ce415e083f4231\\transformed\\firebase-installations-interop-17.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d9cc61bfbee37d252a5ca7c9c9dc7ca9\\transformed\\runtime-saveable-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69be315c0553cc45e32c91bde375ecd9\\transformed\\runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\67d80d22c40993ed52a68caef0851293\\transformed\\facebook-core-16.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5e3a98071f0b868c0e0a841b3389ae0b\\transformed\\play-services-measurement-impl-21.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6032b1154023be7bfe58b66172ac11da\\transformed\\play-services-stats-17.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0ea656ee124f21b7f0587ca9cbc51453\\transformed\\legacy-support-v4-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\188e95b032f7dc65879e9093744ba00d\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\22fa9df1141e688efad1eefed5c79511\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e714df97d941b3a83446a05dfe09907c\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ef52abb1613bd9dfe6de5933789f316\\transformed\\appcompat-resources-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\018f9e21a2c8b2c4a89b73fc407b4cf9\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\23e37cf3a03c2bad8724a93619660d4f\\transformed\\browser-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72c140166b32a3664f34aa33df622052\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e478b0ac16df1c5a6cc40a17dfc967f\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\892709384f3ce62c2c85f01833745b4c\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bf01ad8d4fd35e5bff1520cac272c0da\\transformed\\coordinatorlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\566acec71021d9b221f7f1e57065c94d\\transformed\\slidingpanelayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\890e6708c6a50defab5083a70bcc4181\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69864f54ff1568da8667734c89c3deb6\\transformed\\media-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7d0c9537503126aafb142ca557a3fec2\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\62023c6667c2340297528d18ab08a507\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\805d14da9b5b65a4998c7f2b2db0207b\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3f3bfaabce76c92b994ce1213c24a1ba\\transformed\\coil-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\378dc77344a6c0e15d3df512057d0c64\\transformed\\coil-base-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6e9beecde1484f6fdb5f6793a2ffb28\\transformed\\lifecycle-runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-jvm\\2.8.3\\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\\lifecycle-common-jvm-2.8.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\39bf16aaf5e15a5c59a797e9490efb9b\\transformed\\lifecycle-livedata-core-ktx-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\096788e0677d82eb149f8e24b160d16e\\transformed\\lifecycle-livedata-core-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\baef53540cb3a1b07dfae638a64199ff\\transformed\\lifecycle-viewmodel-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82bed7c6e4c30d06ff41067e9cbb4df9\\transformed\\lifecycle-viewmodel-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.3\\38d9cad3a0b03a10453b56577984bdeb48edeed5\\kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\564c438b460b3422f5e079f1486ba0ae\\transformed\\datastore-preferences-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3e883356ea27185f4889c73c39f14639\\transformed\\datastore-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.datastore\\datastore-preferences-core\\1.0.0\\403f64499b9a8994f5f7010329ddd1ee5c919ed5\\datastore-preferences-core-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.datastore\\datastore-core\\1.0.0\\91b04fb657294e2906d95dce6a9e5a851f6125c1\\datastore-core-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\272a46829f7d52407dc189323c8b875d\\transformed\\ads-adservices-java-1.0.0-beta05-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cb3e223c34b6ed90251856514ca55874\\transformed\\ads-adservices-1.0.0-beta05-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.3\\2b09627576f0989a436a00a4a54b55fa5026fb86\\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-play-services\\1.7.3\\7087d47913cfb0062c9909dacbfc78fe44c5ecff\\kotlinx-coroutines-play-services-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ca21f0c86855ce6129612593daa62038\\transformed\\play-services-tasks-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\817573fccbc1fd44b53b21ddb22db98a\\transformed\\play-services-ads-identifier-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6f1c2fba0947ce03fa9f41e4ed18c1a3\\transformed\\play-services-measurement-sdk-api-21.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\606c261c9fc7701d3a268ef0ba61d593\\transformed\\play-services-measurement-base-21.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d140a3a551632f4f9e83f59db6aac7c\\transformed\\firebase-measurement-connector-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\697740c38f67d68d784aab9cf6781fb2\\transformed\\play-services-basement-18.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\44ad02400b3665dd0c7c2e833b45f649\\transformed\\fragment-1.5.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cdf29cc4afba94f174a46711d04e7576\\transformed\\lifecycle-viewmodel-savedstate-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd08f26aeb487cbf6accd66b14bed1ef\\transformed\\lifecycle-runtime-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7f2556df1c4e666e57b241605e990ea4\\transformed\\lifecycle-livedata-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5323ee10b216d81e37982f97dd967789\\transformed\\lifecycle-runtime-ktx-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b2dc60bc3fb62ea376d340188d666c5e\\transformed\\lifecycle-viewmodel-ktx-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f6a255fdfc93ef2204ed333dfcc675c6\\transformed\\lifecycle-viewmodel-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4b8af95adf21d8a6a9b08c671592f92\\transformed\\material-icons-extended-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7e618ce640ffb3def870429983dda652\\transformed\\material-icons-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4fc0f00b1c4ac39c3bab364d59b9c35\\transformed\\ui-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5bfd7a2074fb5a07f83d9d18eb977059\\transformed\\ui-tooling-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6be8291b4f5948a9cd7ff1e2218c7096\\transformed\\ui-test-manifest-1.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\04045284dd90881dbcddde6d26b04775\\transformed\\activity-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ba3b071e8bd39fce6f74a81b5cbb578b\\transformed\\activity-compose-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c3eeb8eafa1a541ab373ee856febf535\\transformed\\activity-ktx-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f3bb1661e33adbe8ce716da2426a9d08\\transformed\\facebook-bolts-16.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3ce06577402f683bfa7afb8021b3b68b\\transformed\\core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6c9b20c8e880e9bb43a38e1ef774168a\\transformed\\savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07d3776973738a065e4d93d35ecfd561\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b187e9f0f1e5f87039b46685112f9890\\transformed\\accompanist-permissions-0.32.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.retrofit2\\converter-gson\\2.9.0\\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\\converter-gson-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.retrofit2\\retrofit\\2.9.0\\d8fdfbd5da952141a665a403348b74538efc05ff\\retrofit-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okhttp3\\logging-interceptor\\4.11.0\\87fa769912b1f738f3c2dd87e3bca4d1d7f0e666\\logging-interceptor-4.11.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okhttp3\\okhttp\\4.12.0\\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\\okhttp-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c34bd0b226bc321d192f1461ec3b889e\\transformed\\generativeai-0.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okio\\okio-jvm\\3.6.0\\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\\okio-jvm-3.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.9.10\\c7510d64a83411a649c76f2778304ddf71d7437b\\kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2e9e4fd463c1195de4b8f485a66ec4c\\transformed\\annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-serialization-core-jvm\\1.6.0\\94b35f721f1029b3b8d46fb277b4e53fb1a0a510\\kotlinx-serialization-core-jvm-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-serialization-json-jvm\\1.6.0\\59761a941bd8691b3ef2447f5b9376868a6b2d7e\\kotlinx-serialization-json-jvm-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aba067033a5bc380af5bd0851bc307ea\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d6d50eb6daf11855e639ff79610f4331\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b576c6589c69fa1658cb1a6ba181296\\transformed\\firebase-components-17.1.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\deabd6421d6e9cfe8c51fec288e2858f\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\987e08c9fb085a14d11e5880dfeb955e\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\607b13213a67203c1ed53140dfd50483\\transformed\\transport-backend-cct-2.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1cf9c6d9765dab6cc9c94ca82ccb0ea\\transformed\\firebase-encoders-json-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.firebase\\firebase-encoders\\17.0.0\\26f52dc549c42575b155f8c720e84059ee600a85\\firebase-encoders-17.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e006306c23ed5220acc9da1923656073\\transformed\\transport-runtime-2.2.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4629753ec47bf2e21774a66fc45d77f1\\transformed\\transport-api-2.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.concurrent\\concurrent-futures\\1.2.0-alpha02\\719a071a5b713cddbb81f3b2c94cb8246bd064ad\\concurrent-futures-1.2.0-alpha02.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-jvm\\1.4.0\\e209fb7bd1183032f55a0408121c6251a81acb49\\collection-jvm-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b87e85998ef49e1ad70f91a19608f91b\\transformed\\cardview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f81d76ae0a6892618d5e5b53745290e\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c28db3a863fbdd3d1318f9680f8e4864\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a353e4e3e0671800a5996318aa624d18\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.8.0\\b8a16fe526014b7941c1debaccaf9c5153692dbb\\annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.9.10\\bc5bfc2690338defd5195b05c57562f2194eeb10\\kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.0.21\\618b539767b4899b4660a83006e052b63f1db551\\kotlin-stdlib-2.0.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.grpc\\grpc-stub\\1.57.2\\df47b4eb69fb36940689649400c8e9cbf7c3dc2c\\grpc-stub-1.57.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\guava\\32.0.1-android\\d3fe54a75aeaca33fc562a2a9c1dc181e54cc5e9\\guava-32.0.1-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\b421526c5f297295adef1c886e5246c39d4ac629\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c16269284d4ca1a463fed86a4500035c\\transformed\\exifinterface-1.3.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.firebase\\firebase-annotations\\16.2.0\\ba0806703ca285d03fa9c888b5868f101134a501\\firebase-annotations-16.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.dagger\\hilt-core\\2.48\\b4568d616aefe08946cdeb1b7ad251d107a4a225\\hilt-core-2.48.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.dagger\\dagger\\2.48\\c4a5ecf0eb4df3a726179657f1b586290dc08d1b\\dagger-2.48.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\javax.inject\\javax.inject\\1\\6975da39a7040257bd51d21a231b76c915872d38\\javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9c62ee94c96cb866da0b6a92e75039ae\\transformed\\image-1.0.0-beta1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.grpc\\grpc-protobuf-lite\\1.57.2\\14d8e0cab40bf2f661e07b8d0dcc4192d4e9e85a\\grpc-protobuf-lite-1.57.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eba382dfd966312bbde879002de1b12f\\transformed\\grpc-android-1.57.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.grpc\\grpc-okhttp\\1.57.2\\6dbccffc5531af73b739205f8a24d7e77a0e51fc\\grpc-okhttp-1.57.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.grpc\\grpc-core\\1.57.2\\2e04aac0fe659243f4ed5dbe4c2839e88408dffc\\grpc-core-1.57.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.grpc\\grpc-api\\1.57.2\\c71a006b81ddae7bc4b7cb1d2da78c1b173761f4\\grpc-api-1.57.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.errorprone\\error_prone_annotations\\2.18.0\\89b684257096f548fa39a7df9fdaa409d4d4df91\\error_prone_annotations-2.18.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.gson\\gson\\2.10.1\\b3add478d4382b78ea20b1671390a858002feb6c\\gson-2.10.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\failureaccess\\1.0.1\\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\\failureaccess-1.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.findbugs\\jsr305\\3.0.2\\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\\jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.checkerframework\\checker-qual\\3.33.0\\de2b60b62da487644fc11f734e73c8b0b431238f\\checker-qual-3.33.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.j2objc\\j2objc-annotations\\2.8\\c85270e307e7b822f1086b93689124b89768e273\\j2objc-annotations-2.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\17a976cec817735b4bed6471f31203b5\\transformed\\protolite-well-known-types-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.protobuf\\protobuf-javalite\\3.22.3\\7df7200bce0ff77dc66dd063df79ff74c6114bfc\\protobuf-javalite-3.22.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21408b2c522b8a0346e901377d9f43ac\\transformed\\installreferrer-1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.zxing\\core\\3.3.3\\b640badcc97f18867c4dfd249ef8d20ec0204c07\\core-3.3.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4edd4351b01f189f853e8b3055e7b22b\\transformed\\dagger-lint-aar-2.48-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar;F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\tmp\\kapt3\\classes\\debug" "-d" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-compose-compiler-plugin-embeddable\\2.0.21\\e14f003d962fb25693b461de59490c91072a7979\\kotlin-compose-compiler-plugin-embeddable-2.0.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-serialization-compiler-plugin-embeddable\\2.0.21\\f921e430f2ca63ead1c5640ada25411d06618bfb\\kotlin-serialization-compiler-plugin-embeddable-2.0.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-annotation-processing-gradle\\2.0.21\\b3be9823176d79cb0fc710e77309cfe599be9abf\\kotlin-annotation-processing-gradle-2.0.21.jar" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:sourceInformation=true,plugin:androidx.compose.compiler.plugins.kotlin:generateFunctionKeyMetaClasses=false,plugin:androidx.compose.compiler.plugins.kotlin:traceMarkersEnabled=true" "-Xuse-inline-scopes-numbers" "-Xallow-unstable-dependencies" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\buildConfig\\debug\\com\\healthyproducts\\app\\BuildConfig.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\api\\AiServiceImpl_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\api\\GeminiApiService_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\AdditiveRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\AllergenRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\DeepSeekRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\FatRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\FirebaseAuthRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\FirebaseStorageRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\FirestoreRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\FoodAnalysisRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\FoodCertificateRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\IntoleranceRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\OcrCorrectionRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\PreservativeRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\ScanHistoryRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\SugarRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\UserFoodPreferenceRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\repository\\UserRepository_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\data\\service\\FoodAnalysisService_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\AppModule_ProvideContextFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\FirebaseModule_ProvideFirebaseAuthFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\FirebaseModule_ProvideFirebaseFirestoreFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\FirebaseModule_ProvideFirebaseStorageFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideAiServiceFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideAuthInterceptorFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideDeepSeekApiFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideDeepSeekApiServiceFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideDeepSeekAuthorizationHeaderFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideDeepSeekBaseUrlFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideGeminiApiServiceFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideLoggingInterceptorFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\NetworkModule_ProvideOkHttpClientFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideAdditiveRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideAllergenRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideFatRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideFirebaseAuthRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideFirebaseStorageRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideFirestoreRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideFoodAnalysisServiceFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideFoodCertificateRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideIntoleranceRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvidePreservativeRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\di\\RepositoryModule_ProvideSugarRepositoryFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\HealthyProductsApp_GeneratedInjector.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\HealthyProductsApp_MembersInjector.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\Hilt_MainActivity.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\MainActivity_GeneratedInjector.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\MainActivity_MembersInjector.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\auth\\LoginViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\auth\\LoginViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\auth\\LoginViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\auth\\RegisterViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\auth\\RegisterViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\auth\\RegisterViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\AdditiveViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\AdditiveViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\AdditiveViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\FoodAnalysisViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\FoodAnalysisViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\FoodAnalysisViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\LanguageViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\LanguageViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\LanguageViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanHistoryViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanHistoryViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanHistoryViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\UserViewModel_Factory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\UserViewModel_HiltModules.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\com\\healthyproducts\\app\\ui\\viewmodel\\UserViewModel_HiltModules_KeyModule_ProvideFactory.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_healthyproducts_app_HealthyProductsApp.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_di_AppModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_di_FirebaseModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_di_NetworkModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_di_RepositoryModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_HealthyProductsApp_GeneratedInjector.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_MainActivity_GeneratedInjector.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_auth_LoginViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_auth_LoginViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_auth_RegisterViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_auth_RegisterViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_AdditiveViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_AdditiveViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_FoodAnalysisViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_FoodAnalysisViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_LanguageViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_LanguageViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_ScanHistoryViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_ScanHistoryViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_ScanViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_ScanViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_UserViewModel_HiltModules_BindsModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\source\\kapt\\debug\\hilt_aggregated_deps\\_com_healthyproducts_app_ui_viewmodel_UserViewModel_HiltModules_KeyModule.java" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\api\\AiService.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\api\\DeepSeekApi.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\api\\DeepSeekApiService.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\api\\GeminiApiService.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\Additive.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\AiModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\Allergen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\DeepSeekModels.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\Fat.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\FoodCertificate.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\Intolerance.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\Preservative.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\Sugar.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\User.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\model\\UserFoodPreference.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\preferences\\LanguagePreferences.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\AdditiveRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\AllergenRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\DeepSeekRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\FatRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\FavoritesRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\FirebaseAuthRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\FirebaseStorageRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\FirestoreRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\FoodAnalysisRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\FoodCertificateRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\IntoleranceRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\OcrCorrectionRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\PreservativeRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\ScanHistoryRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\SugarRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\UserFoodPreferenceRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\repository\\UserRepository.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\data\\service\\FoodAnalysisService.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\di\\AppModule.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\di\\FirebaseModule.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\di\\NetworkModule.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\di\\RepositoryModule.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\HealthyProductsApp.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\MainActivity.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\model\\Product.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\model\\User.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\navigation\\AppNavigation.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\auth\\LoginScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\auth\\LoginViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\auth\\RegisterScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\auth\\RegisterViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\AddCustomItemDialog.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\BackButton.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\BottomNavBar.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\CameraPreview.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\ChipGroup.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\EditableIngredientsList.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\ModifierExtensions.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\OcrCameraPreview.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\RiskLevelIndicator.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\components\\ScannerOverlay.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\additives\\AdditiveDetailScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\additives\\AdditiveEditScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\additives\\AdditivesScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\admin\\DataUploadScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\auth\\LoginScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\auth\\RegisterScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\favorites\\FavoritesScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\AllergenDetailScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\AllergensScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\FatDetailScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\FatsScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\FoodAnalysisScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\FoodCertificateDetailScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\FoodCertificatesScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\PreservativeDetailScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\PreservativesScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\SugarDetailScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\foodanalysis\\SugarsScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\home\\HomeScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\product\\ProductDetailScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\profile\\ProfileScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\scan\\ScanScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\settings\\AiModelSelectionScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\settings\\LanguageScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\settings\\SettingsScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\screens\\settings\\UserFoodPreferencesScreen.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\theme\\Color.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\theme\\Theme.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\theme\\Type.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\viewmodel\\AdditiveViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\viewmodel\\FavoritesViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\viewmodel\\FoodAnalysisViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\viewmodel\\LanguageViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanHistoryViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\viewmodel\\ScanViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\ui\\viewmodel\\UserViewModel.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\util\\BarcodeAnalyzer.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\util\\CameraPermissionHelper.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\util\\DataUploader.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\util\\LocaleHelper.kt" "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\java\\com\\healthyproducts\\app\\util\\TextRecognitionAnalyzer.kt"