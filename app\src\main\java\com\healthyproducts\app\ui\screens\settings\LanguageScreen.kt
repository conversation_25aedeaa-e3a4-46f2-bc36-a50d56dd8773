package com.healthyproducts.app.ui.screens.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.healthyproducts.app.ui.components.clickable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import android.app.Activity
import com.healthyproducts.app.R
import com.healthyproducts.app.ui.viewmodel.LanguageViewModel
import com.healthyproducts.app.ui.viewmodel.UserViewModel
import com.healthyproducts.app.util.LocaleHelper

/**
 * Dil seçimi ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageScreen(
    navController: NavController,
    languageViewModel: LanguageViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val currentLanguage by languageViewModel.currentLanguage.collectAsState()
    val supportedLanguages = languageViewModel.supportedLanguages
    val snackbarHostState = remember { SnackbarHostState() }

    // Aktivite referansını ViewModel'e geçir
    val context = LocalContext.current
    LaunchedEffect(Unit) {
        if (context is Activity) {
            languageViewModel.setActivity(context)
        }
    }

    // Dil değişikliğini izle
    var showLanguageChangedMessage by remember { mutableStateOf(false) }

    // Dil değiştirildi mesajı
    val languageChangedMessage = stringResource(R.string.language_changed)

    LaunchedEffect(showLanguageChangedMessage) {
        if (showLanguageChangedMessage) {
            snackbarHostState.showSnackbar(
                message = languageChangedMessage
            )
            showLanguageChangedMessage = false
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.language_selection)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Geri"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxWidth()
            ) {
                items(supportedLanguages) { language ->
                    LanguageItem(
                        language = language,
                        isSelected = language == currentLanguage,
                        onClick = {
                            // Dil değişikliği yap
                            languageViewModel.setLanguage(language)

                            // UserViewModel'i de güncelle
                            val userPreferences = userViewModel.userPreferences.value
                            val updatedPreferences = userPreferences.copy(language = language.code)
                            userViewModel.updateUserPreferences(updatedPreferences)

                            showLanguageChangedMessage = true
                        }
                    )
                }
            }
        }
    }
}

/**
 * Dil seçimi öğesi
 */
@Composable
fun LanguageItem(
    language: LocaleHelper.Language,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    ListItem(
        headlineContent = {
            Text(
                text = when (language) {
                    LocaleHelper.Language.ENGLISH -> stringResource(R.string.language_english)
                    LocaleHelper.Language.TURKISH -> stringResource(R.string.language_turkish)
                }
            )
        },
        trailingContent = {
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        },
        modifier = Modifier.clickable(enabled = true, onClick = onClick)
    )
}
