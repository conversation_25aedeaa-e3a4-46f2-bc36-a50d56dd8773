package com.healthyproducts.app.ui.viewmodel;

import android.content.Context;
import com.healthyproducts.app.data.repository.AdditiveRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AdditiveViewModel_Factory implements Factory<AdditiveViewModel> {
  private final Provider<AdditiveRepository> additiveRepositoryProvider;

  private final Provider<Context> contextProvider;

  public AdditiveViewModel_Factory(Provider<AdditiveRepository> additiveRepositoryProvider,
      Provider<Context> contextProvider) {
    this.additiveRepositoryProvider = additiveRepositoryProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public AdditiveViewModel get() {
    return newInstance(additiveRepositoryProvider.get(), contextProvider.get());
  }

  public static AdditiveViewModel_Factory create(
      Provider<AdditiveRepository> additiveRepositoryProvider, Provider<Context> contextProvider) {
    return new AdditiveViewModel_Factory(additiveRepositoryProvider, contextProvider);
  }

  public static AdditiveViewModel newInstance(AdditiveRepository additiveRepository,
      Context context) {
    return new AdditiveViewModel(additiveRepository, context);
  }
}
