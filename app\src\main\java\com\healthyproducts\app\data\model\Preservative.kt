package com.healthyproducts.app.data.model

import com.google.firebase.firestore.PropertyName

/**
 * Koruyucu ve antibakteriyel kalıntıları temsil eden veri sınıfı
 */
data class Preservative(
    @get:PropertyName("id")
    @set:PropertyName("id")
    var id: String = "",

    // Benzersiz tanımlayıcı (örn. "sodium_benzoate", "nitrite")
    @get:PropertyName("preservativeId")
    @set:PropertyName("preservativeId")
    var preservativeId: String = "",

    @get:PropertyName("name_tr")
    @set:PropertyName("name_tr")
    var nameTr: String = "", // Koruyucunun Türkçe adı

    @get:PropertyName("name_en")
    @set:PropertyName("name_en")
    var nameEn: String = "", // Koruyucunun İngilizce adı

    @get:PropertyName("symbol")
    @set:PropertyName("symbol")
    var symbol: String = "", // Emoji veya ikon referansı

    @get:PropertyName("description_tr")
    @set:PropertyName("description_tr")
    var descriptionTr: String = "", // Koruyucunun Türkçe açıklaması

    @get:PropertyName("description_en")
    @set:PropertyName("description_en")
    var descriptionEn: String = "", // Koruyucunun İngilizce açıklaması

    @get:PropertyName("risk_level")
    @set:PropertyName("risk_level")
    var riskLevel: Int = 0, // Risk seviyesi (1-3)

    @get:PropertyName("functional_type")
    @set:PropertyName("functional_type")
    var functionalType: List<String> = listOf(), // Fonksiyonel tür (örn. "preservative", "synthetic")

    @get:PropertyName("main_risk_tr")
    @set:PropertyName("main_risk_tr")
    var mainRiskTr: String = "", // Ana risk Türkçe açıklaması

    @get:PropertyName("main_risk_en")
    @set:PropertyName("main_risk_en")
    var mainRiskEn: String = "", // Ana risk İngilizce açıklaması

    @get:PropertyName("common_products_tr")
    @set:PropertyName("common_products_tr")
    var commonProductsTr: List<String> = listOf(), // Türkçe yaygın ürünler

    @get:PropertyName("common_products_en")
    @set:PropertyName("common_products_en")
    var commonProductsEn: List<String> = listOf(), // İngilizce yaygın ürünler

    @get:PropertyName("notes_tr")
    @set:PropertyName("notes_tr")
    var notesTr: String = "", // Türkçe ek notlar

    @get:PropertyName("notes_en")
    @set:PropertyName("notes_en")
    var notesEn: String = "" // İngilizce ek notlar
) {
    // Firestore için boş constructor
    constructor() : this(
        id = "",
        preservativeId = "",
        nameTr = "",
        nameEn = "",
        symbol = "",
        descriptionTr = "",
        descriptionEn = "",
        riskLevel = 0,
        functionalType = listOf(),
        mainRiskTr = "",
        mainRiskEn = "",
        commonProductsTr = listOf(),
        commonProductsEn = listOf(),
        notesTr = "",
        notesEn = ""
    )

    // Kullanıcının dil tercihine göre adı döndürür
    fun getName(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> nameTr.ifEmpty { nameEn }
            SupportedLanguage.ENGLISH -> nameEn.ifEmpty { nameTr }
        }
    }

    // Kullanıcının dil tercihine göre açıklamayı döndürür
    fun getDescription(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> descriptionTr.ifEmpty { descriptionEn }
            SupportedLanguage.ENGLISH -> descriptionEn.ifEmpty { descriptionTr }
        }
    }

    // Kullanıcının dil tercihine göre ana riski döndürür
    fun getMainRisk(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> mainRiskTr.ifEmpty { mainRiskEn }
            SupportedLanguage.ENGLISH -> mainRiskEn.ifEmpty { mainRiskTr }
        }
    }

    // Kullanıcının dil tercihine göre yaygın ürünleri döndürür
    fun getCommonProducts(language: SupportedLanguage): List<String> {
        return when (language) {
            SupportedLanguage.TURKISH -> commonProductsTr.ifEmpty { commonProductsEn }
            SupportedLanguage.ENGLISH -> commonProductsEn.ifEmpty { commonProductsTr }
        }
    }

    // Kullanıcının dil tercihine göre notları döndürür
    fun getNotes(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> notesTr.ifEmpty { notesEn }
            SupportedLanguage.ENGLISH -> notesEn.ifEmpty { notesTr }
        }
    }

    // Kullanıcının dil tercihine göre sağlık etkilerini döndürür
    fun getHealthEffects(language: SupportedLanguage): String {
        return getMainRisk(language)
    }

    // Kullanıcının dil tercihine göre kullanımı döndürür
    fun getUsage(language: SupportedLanguage): String {
        return getDescription(language)
    }

    // Koruyucu türünü kontrol eder
    fun isPreservative(): Boolean {
        return functionalType.contains("preservative")
    }

    // Sentetik olup olmadığını kontrol eder
    fun isSynthetic(): Boolean {
        return functionalType.contains("synthetic")
    }

    // Toksik olup olmadığını kontrol eder
    fun isToxic(): Boolean {
        return functionalType.contains("toxic")
    }

    // Kanserojen olup olmadığını kontrol eder
    fun isCarcinogen(): Boolean {
        return functionalType.contains("carcinogen")
    }

    // Endokrin bozucu olup olmadığını kontrol eder
    fun isEndocrineDisruptor(): Boolean {
        return functionalType.contains("endocrine_disruptor")
    }
}
