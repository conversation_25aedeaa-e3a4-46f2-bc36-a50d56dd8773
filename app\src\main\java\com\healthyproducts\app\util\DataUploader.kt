package com.healthyproducts.app.util

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.healthyproducts.app.data.model.Additive
import com.healthyproducts.app.data.model.Allergen
import com.healthyproducts.app.data.model.Fat
import com.healthyproducts.app.data.model.FoodCertificate
import com.healthyproducts.app.data.model.Sugar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

/**
 * JSON dosyalarındaki verileri Firebase'e yüklemek için yardımcı sınıf
 */
class DataUploader(private val context: Context) {
    private val firestore = FirebaseFirestore.getInstance()
    private val gson = Gson()

    /**
     * Tüm verileri Firebase'e yükler
     */
    suspend fun uploadAllData(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Katkı maddelerini yükle
            uploadAdditives()

            // Alerjenleri yükle
            uploadAllergens()

            // Yağları yükle
            uploadFats()

            // Şekerleri yükle
            uploadSugars()

            // Gıda sertifikalarını yükle
            uploadFoodCertificates()

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("DataUploader", "Error uploading data", e)
            Result.failure(e)
        }
    }

    /**
     * Katkı maddelerini Firebase'e yükler
     */
    private suspend fun uploadAdditives() {
        try {
            val jsonString = readJsonFromAssets("additivesList.json")
            Log.d("DataUploader", "Additive JSON content: ${jsonString.take(100)}...")

            val additiveType = object : TypeToken<List<Additive>>() {}.type
            val additives: List<Additive> = gson.fromJson(jsonString, additiveType)

            Log.d("DataUploader", "Uploading ${additives.size} additives")

            // İlk birkaç katkı maddesinin içeriğini kontrol et
            additives.take(3).forEachIndexed { index, additive ->
                Log.d("DataUploader", "Additive $index: id=${additive.id}, code=${additive.code}, " +
                        "nameTr=${additive.nameTr}, nameEn=${additive.nameEn}")
            }

            // Batch işlemi için
            val batch = firestore.batch()

            additives.forEach { additive ->
                // code boş ise veya geçersizse, yeni bir ID oluştur
                val docId = if (additive.code.isBlank()) {
                    // id alanını kullan, o da boşsa yeni ID oluştur
                    if (additive.id.isNotBlank()) additive.id else firestore.collection("additives").document().id
                } else {
                    additive.code
                }

                val docRef = firestore.collection("additives").document(docId)
                batch.set(docRef, additive)
            }

            batch.commit().await()
            Log.d("DataUploader", "Additives uploaded successfully")
        } catch (e: Exception) {
            Log.e("DataUploader", "Error uploading additives", e)
            throw e
        }
    }

    /**
     * Alerjenleri Firebase'e yükler
     */
    private suspend fun uploadAllergens() {
        try {
            val jsonString = readJsonFromAssets("allergenList.json")
            Log.d("DataUploader", "Allergen JSON content: ${jsonString.take(100)}...")

            val allergenType = object : TypeToken<List<Allergen>>() {}.type
            val allergens: List<Allergen> = gson.fromJson(jsonString, allergenType)

            Log.d("DataUploader", "Uploading ${allergens.size} allergens")

            // İlk birkaç alerjenin içeriğini kontrol et
            allergens.take(3).forEachIndexed { index, allergen ->
                Log.d("DataUploader", "Allergen $index: id=${allergen.id}, allergenId=${allergen.allergenId}, " +
                        "nameTr=${allergen.nameTr}, nameEn=${allergen.nameEn}")
            }

            // Batch işlemi için
            val batch = firestore.batch()

            allergens.forEach { allergen ->
                // allergenId boş ise veya geçersizse, yeni bir ID oluştur
                val docId = if (allergen.allergenId.isBlank()) {
                    // id alanını kullan, o da boşsa yeni ID oluştur
                    if (allergen.id.isNotBlank()) allergen.id else firestore.collection("allergens").document().id
                } else {
                    allergen.allergenId
                }

                // allergenId'yi güncelle
                allergen.allergenId = docId

                val docRef = firestore.collection("allergens").document(docId)
                batch.set(docRef, allergen)
            }

            batch.commit().await()
            Log.d("DataUploader", "Allergens uploaded successfully")
        } catch (e: Exception) {
            Log.e("DataUploader", "Error uploading allergens", e)
            throw e
        }
    }

    /**
     * Yağları Firebase'e yükler
     */
    private suspend fun uploadFats() {
        try {
            val jsonString = readJsonFromAssets("fatList.json")
            Log.d("DataUploader", "Fat JSON content: ${jsonString.take(100)}...")

            val fatType = object : TypeToken<List<Fat>>() {}.type
            val fats: List<Fat> = gson.fromJson(jsonString, fatType)

            Log.d("DataUploader", "Uploading ${fats.size} fats")

            // İlk birkaç yağın içeriğini kontrol et
            fats.take(3).forEachIndexed { index, fat ->
                Log.d("DataUploader", "Fat $index: id=${fat.id}, fatId=${fat.fatId}, " +
                        "nameTr=${fat.nameTr}, nameEn=${fat.nameEn}")
            }

            // Batch işlemi için
            val batch = firestore.batch()

            fats.forEach { fat ->
                // fatId boş ise veya geçersizse, yeni bir ID oluştur
                val docId = if (fat.fatId.isBlank()) {
                    // id alanını kullan, o da boşsa yeni ID oluştur
                    if (fat.id.isNotBlank()) fat.id else firestore.collection("fats").document().id
                } else {
                    fat.fatId
                }

                // fatId'yi güncelle
                fat.fatId = docId

                val docRef = firestore.collection("fats").document(docId)
                batch.set(docRef, fat)
            }

            batch.commit().await()
            Log.d("DataUploader", "Fats uploaded successfully")
        } catch (e: Exception) {
            Log.e("DataUploader", "Error uploading fats", e)
            throw e
        }
    }

    /**
     * Şekerleri Firebase'e yükler
     */
    private suspend fun uploadSugars() {
        try {
            val jsonString = readJsonFromAssets("sugarList.json")
            Log.d("DataUploader", "Sugar JSON content: ${jsonString.take(100)}...")

            val sugarType = object : TypeToken<List<Sugar>>() {}.type
            val sugars: List<Sugar> = gson.fromJson(jsonString, sugarType)

            Log.d("DataUploader", "Uploading ${sugars.size} sugars")

            // İlk birkaç şekerin içeriğini kontrol et
            sugars.take(3).forEachIndexed { index, sugar ->
                Log.d("DataUploader", "Sugar $index: id=${sugar.id}, sugarId=${sugar.sugarId}, " +
                        "nameTr=${sugar.nameTr}, nameEn=${sugar.nameEn}")
            }

            // Batch işlemi için
            val batch = firestore.batch()

            sugars.forEach { sugar ->
                // sugarId boş ise veya geçersizse, yeni bir ID oluştur
                val docId = if (sugar.sugarId.isBlank()) {
                    // id alanını kullan, o da boşsa yeni ID oluştur
                    if (sugar.id.isNotBlank()) sugar.id else firestore.collection("sugars").document().id
                } else {
                    sugar.sugarId
                }

                // sugarId'yi güncelle
                sugar.sugarId = docId

                val docRef = firestore.collection("sugars").document(docId)
                batch.set(docRef, sugar)
            }

            batch.commit().await()
            Log.d("DataUploader", "Sugars uploaded successfully")
        } catch (e: Exception) {
            Log.e("DataUploader", "Error uploading sugars", e)
            throw e
        }
    }

    /**
     * Gıda sertifikalarını Firebase'e yükler
     */
    private suspend fun uploadFoodCertificates() {
        try {
            val jsonString = readJsonFromAssets("foodCertificateList.json")
            Log.d("DataUploader", "Food Certificate JSON content: ${jsonString.take(100)}...")

            val certificateType = object : TypeToken<List<FoodCertificate>>() {}.type
            val certificates: List<FoodCertificate> = gson.fromJson(jsonString, certificateType)

            Log.d("DataUploader", "Uploading ${certificates.size} food certificates")

            // İlk birkaç sertifikanın içeriğini kontrol et
            certificates.take(3).forEachIndexed { index, certificate ->
                Log.d("DataUploader", "Certificate $index: id=${certificate.id}, certificateId=${certificate.certificateId}, " +
                        "nameTr=${certificate.nameTr}, nameEn=${certificate.nameEn}")
            }

            // Batch işlemi için
            val batch = firestore.batch()

            certificates.forEach { certificate ->
                // certificateId boş ise veya geçersizse, yeni bir ID oluştur
                val docId = if (certificate.certificateId.isBlank()) {
                    // id alanını kullan, o da boşsa yeni ID oluştur
                    if (certificate.id.isNotBlank()) certificate.id else firestore.collection("food_certificates").document().id
                } else {
                    certificate.certificateId
                }

                // certificateId'yi güncelle
                certificate.certificateId = docId

                val docRef = firestore.collection("food_certificates").document(docId)
                batch.set(docRef, certificate)
            }

            batch.commit().await()
            Log.d("DataUploader", "Food certificates uploaded successfully")
        } catch (e: Exception) {
            Log.e("DataUploader", "Error uploading food certificates", e)
            throw e
        }
    }

    /**
     * JSON dosyasını okur
     */
    private fun readJsonFromAssets(fileName: String): String {
        try {
            Log.d("DataUploader", "Trying to read JSON file: $fileName")

            // Öncelikle json/ alt dizininden okumayı dene (tercih edilen yöntem)
            try {
                val inputStream = context.assets.open("json/$fileName")
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                inputStream.close()
                val jsonString = String(buffer, Charsets.UTF_8)
                Log.d("DataUploader", "Read JSON from assets/json: $fileName, length: ${jsonString.length}")
                return jsonString
            } catch (e: IOException) {
                Log.d("DataUploader", "Failed to read from assets/json, trying other methods", e)
            }

            // Doğrudan assets klasöründen okumayı dene
            try {
                val inputStream = context.assets.open(fileName)
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                inputStream.close()
                val jsonString = String(buffer, Charsets.UTF_8)
                Log.d("DataUploader", "Read JSON from assets: $fileName, length: ${jsonString.length}")
                return jsonString
            } catch (e: IOException) {
                Log.d("DataUploader", "Failed to read directly from assets", e)
            }

            // Doğrudan model klasöründen okumayı dene
            try {
                val modelPath = "app/src/main/java/com/healthyproducts/app/data/model/$fileName"
                val modelFile = File(modelPath)
                if (modelFile.exists()) {
                    val content = modelFile.readText(Charsets.UTF_8)
                    Log.d("DataUploader", "Read JSON from model directory: $fileName, length: ${content.length}")
                    return content
                } else {
                    Log.d("DataUploader", "Model file does not exist at: $modelPath")
                }
            } catch (e: Exception) {
                Log.e("DataUploader", "Error reading from model directory", e)
            }

            // ClassLoader ile okumayı dene
            try {
                val modelPath = "com/healthyproducts/app/data/model/$fileName"
                val inputStream = context.classLoader.getResourceAsStream(modelPath)
                if (inputStream != null) {
                    val size = inputStream.available()
                    val buffer = ByteArray(size)
                    inputStream.read(buffer)
                    inputStream.close()
                    val jsonString = String(buffer, Charsets.UTF_8)
                    Log.d("DataUploader", "Read JSON from classloader: $fileName, length: ${jsonString.length}")
                    return jsonString
                } else {
                    Log.d("DataUploader", "ClassLoader could not find resource: $modelPath")
                }
            } catch (e: Exception) {
                Log.e("DataUploader", "Error reading from classloader", e)
            }

            // Hiçbir yöntem başarılı olmazsa, hata fırlat
            throw IOException("Could not find JSON file: $fileName. Please make sure the file exists in assets/json directory.")
        } catch (e: Exception) {
            Log.e("DataUploader", "Error reading JSON file: $fileName", e)
            throw e
        }
    }
}
