package com.healthyproducts.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthyproducts.app.data.repository.FavoritesRepository
import com.healthyproducts.app.model.Product
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Favori ürünleri yöneten ViewModel
 */
class FavoritesViewModel(
    private val favoritesRepository: FavoritesRepository = FavoritesRepository()
) : ViewModel() {
    
    // Favori ürünler durumu
    private val _favoritesState = MutableStateFlow<FavoritesState>(FavoritesState.Loading)
    val favoritesState: StateFlow<FavoritesState> = _favoritesState.asStateFlow()
    
    /**
     * Kullanıcının favori ürünlerini getirme
     */
    fun getFavoriteProducts(userId: String) {
        viewModelScope.launch {
            _favoritesState.value = FavoritesState.Loading
            
            favoritesRepository.getFavoriteProducts(userId)
                .onSuccess { products ->
                    _favoritesState.value = if (products.isEmpty()) {
                        FavoritesState.Empty
                    } else {
                        FavoritesState.Success(products)
                    }
                }
                .onFailure { error ->
                    _favoritesState.value = FavoritesState.Error(error.message ?: "Favorileri getirme hatası")
                }
        }
    }
    
    /**
     * Ürünü favorilere ekleme
     */
    fun addToFavorites(userId: String, productId: String) {
        viewModelScope.launch {
            favoritesRepository.addToFavorites(userId, productId)
                .onSuccess {
                    // Favorileri yeniden yükle
                    getFavoriteProducts(userId)
                }
        }
    }
    
    /**
     * Ürünü favorilerden çıkarma
     */
    fun removeFromFavorites(userId: String, productId: String) {
        viewModelScope.launch {
            favoritesRepository.removeFromFavorites(userId, productId)
                .onSuccess {
                    // Favorileri yeniden yükle
                    getFavoriteProducts(userId)
                }
        }
    }
    
    /**
     * Ürünün favori olup olmadığını kontrol etme
     */
    fun checkIsFavorite(userId: String, productId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            favoritesRepository.isFavorite(userId, productId)
                .onSuccess { isFavorite ->
                    callback(isFavorite)
                }
                .onFailure {
                    callback(false)
                }
        }
    }
    
    /**
     * Favori ürünler durumunu temsil eden sealed class
     */
    sealed class FavoritesState {
        object Loading : FavoritesState()
        object Empty : FavoritesState()
        data class Success(val products: List<Product>) : FavoritesState()
        data class Error(val message: String) : FavoritesState()
    }
}
