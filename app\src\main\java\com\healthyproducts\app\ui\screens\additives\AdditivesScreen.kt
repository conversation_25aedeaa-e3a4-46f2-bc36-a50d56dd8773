package com.healthyproducts.app.ui.screens.additives

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.FileUpload
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.Additive
import com.healthyproducts.app.data.model.AdditiveCategory
import com.healthyproducts.app.ui.viewmodel.AdditiveViewModel

/**
 * Katkı maddeleri ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdditivesScreen(
    navController: NavController,
    viewModel: AdditiveViewModel = hiltViewModel()
) {
    val additives by viewModel.additives.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val filterCategory by viewModel.filterCategory.collectAsState()
    val importStatus by viewModel.importStatus.collectAsState()

    // İçe aktarma iletişim kutusu durumu
    val showImportDialog = remember { mutableStateOf(false) }

    // İçe aktarma iletişim kutusu
    if (showImportDialog.value) {
        ImportDialog(
            importStatus = importStatus,
            onDismiss = { showImportDialog.value = false },
            onConfirm = {
                viewModel.importAdditivesFromJson()
            }
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.additives)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Geri"
                        )
                    }
                },
                actions = {
                    // İçe aktarma butonu
                    IconButton(onClick = { showImportDialog.value = true }) {
                        Icon(
                            imageVector = Icons.Default.FileUpload,
                            contentDescription = "İçe Aktar"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // Arama ve filtreleme
            SearchAndFilterBar(
                searchQuery = searchQuery,
                onSearchQueryChange = viewModel::updateSearchQuery,
                filterCategory = filterCategory,
                onFilterCategoryChange = viewModel::updateFilterCategory
            )

            // İçerik
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                } else if (error != null) {
                    Text(
                        text = error ?: "Bir hata oluştu",
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.align(Alignment.Center)
                    )
                } else if (additives.isEmpty()) {
                    Text(
                        text = "Katkı maddesi bulunamadı",
                        modifier = Modifier.align(Alignment.Center)
                    )
                } else {
                    AdditivesList(
                        additives = additives,
                        onAdditiveClick = { additive ->
                            viewModel.selectAdditive(additive)
                            navController.navigate("additive_detail/${additive.code}")
                        }
                    )
                }
            }
        }
    }
}

/**
 * Arama ve filtreleme çubuğu
 */
@Composable
fun SearchAndFilterBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    filterCategory: String?,
    onFilterCategoryChange: (String?) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // Arama çubuğu
        OutlinedTextField(
            value = searchQuery,
            onValueChange = onSearchQueryChange,
            label = { Text("Katkı maddesi ara") },
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Ara"
                )
            },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Filtreleme
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Kategori:",
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.width(8.dp))

            var expanded by remember { mutableStateOf(false) }

            TextButton(
                onClick = { expanded = true }
            ) {
                Text(
                    text = filterCategory?.let {
                        AdditiveCategory.values().find { it.name == filterCategory }?.displayName
                    } ?: "Tümü"
                )
            }

            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                // Tümü seçeneği
                DropdownMenuItem(
                    text = { Text("Tümü") },
                    onClick = {
                        onFilterCategoryChange(null)
                        expanded = false
                    }
                )

                // Kategori seçenekleri
                AdditiveCategory.values().forEach { category ->
                    DropdownMenuItem(
                        text = { Text(category.displayName) },
                        onClick = {
                            onFilterCategoryChange(category.name)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * Katkı maddeleri listesi
 */
@Composable
fun AdditivesList(
    additives: List<Additive>,
    onAdditiveClick: (Additive) -> Unit
) {
    LazyColumn(
        contentPadding = PaddingValues(vertical = 8.dp)
    ) {
        items(additives) { additive ->
            AdditiveItem(
                additive = additive,
                onClick = { onAdditiveClick(additive) }
            )

            Divider()
        }
    }
}

/**
 * İçe aktarma iletişim kutusu
 */
@Composable
fun ImportDialog(
    importStatus: AdditiveViewModel.ImportStatus,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Katkı Maddelerini İçe Aktar") },
        text = {
            when (importStatus) {
                is AdditiveViewModel.ImportStatus.Idle -> {
                    Text("JSON dosyasından katkı maddelerini içe aktarmak istiyor musunuz?")
                }
                is AdditiveViewModel.ImportStatus.Loading -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        CircularProgressIndicator()
                        Spacer(modifier = Modifier.height(8.dp))
                        Text("İçe aktarılıyor...")
                    }
                }
                is AdditiveViewModel.ImportStatus.Success -> {
                    Text("${importStatus.count} katkı maddesi başarıyla içe aktarıldı.")
                }
                is AdditiveViewModel.ImportStatus.Error -> {
                    Text("Hata: ${importStatus.message}")
                }
            }
        },
        confirmButton = {
            if (importStatus is AdditiveViewModel.ImportStatus.Idle) {
                Button(onClick = onConfirm) {
                    Text("İçe Aktar")
                }
            } else {
                Button(onClick = onDismiss) {
                    Text("Tamam")
                }
            }
        },
        dismissButton = {
            if (importStatus is AdditiveViewModel.ImportStatus.Idle) {
                TextButton(onClick = onDismiss) {
                    Text("İptal")
                }
            }
        }
    )
}

/**
 * Katkı maddesi öğesi
 */
@Composable
fun AdditiveItem(
    additive: Additive,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable(onClick = onClick)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Kod ve isim
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = additive.code,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = additive.nameTr.ifEmpty { additive.nameEn },
                    style = MaterialTheme.typography.titleMedium
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // Kategori
            Text(
                text = "Kategori: ${additive.categoryTr.ifEmpty { additive.categoryEn }}",
                style = MaterialTheme.typography.bodyMedium
            )

            // Helal durumu
            Text(
                text = "Helal Durumu: ${additive.halalStatus}",
                style = MaterialTheme.typography.bodyMedium
            )

            // Vegan durumu
            Text(
                text = "Vegan Durumu: ${additive.veganStatus}",
                style = MaterialTheme.typography.bodyMedium
            )

            // Zararlılık seviyesi
            Text(
                text = "Zararlılık: ${additive.harmfulLevel}/5",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}
