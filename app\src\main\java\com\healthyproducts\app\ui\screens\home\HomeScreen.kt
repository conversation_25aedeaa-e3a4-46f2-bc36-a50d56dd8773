package com.healthyproducts.app.ui.screens.home

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CameraAlt
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material.icons.filled.Science
import androidx.compose.material.icons.filled.Sanitizer
import androidx.compose.material.icons.filled.Spa
import androidx.compose.material.icons.filled.Verified
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.model.Product
import com.healthyproducts.app.navigation.Screen
import com.healthyproducts.app.ui.components.BottomNavBar

/**
 * Ana sayfa ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(navController: NavController) {
    Scaffold(
        bottomBar = { BottomNavBar(navController = navController) }
    ) { innerPadding ->
        HomeContent(
            navController = navController,
            paddingValues = innerPadding
        )
    }
}

/**
 * Ana sayfa içeriği
 */
@Composable
fun HomeContent(
    navController: NavController,
    paddingValues: PaddingValues,
    recentScans: List<Product> = emptyList() // Şimdilik boş liste
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item {
            Text(
                text = stringResource(R.string.welcome_message),
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(16.dp),
                textAlign = TextAlign.Center
            )
        }

        item {
            ScanOptions(
                onBarcodeClick = { navController.navigate(Screen.Scan.route) },
                onIngredientsClick = { navController.navigate(Screen.Scan.route) },
                onManualEntryClick = { /* TODO: Manuel giriş ekranına yönlendir */ }
            )
        }

        item {
            ScanOptionCard(
                title = stringResource(R.string.food_analysis_screen),
                icon = Icons.Default.Science,
                modifier = Modifier.fillMaxWidth(0.8f),
                onClick = { navController.navigate(Screen.FoodAnalysis.route) }
            )
        }

        item {
            Text(
                text = stringResource(R.string.recent_scans),
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            )
        }

        if (recentScans.isEmpty()) {
            item {
                Text(
                    text = stringResource(R.string.no_recent_scans),
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    textAlign = TextAlign.Center
                )
            }
        } else {
            items(recentScans) { product ->
                RecentScanItem(
                    product = product,
                    onClick = { navController.navigate(Screen.ProductDetail.createRoute(product.id)) }
                )
            }
        }
    }
}

/**
 * Tarama seçenekleri bileşeni
 */
@Composable
fun ScanOptions(
    onBarcodeClick: () -> Unit,
    onIngredientsClick: () -> Unit,
    onManualEntryClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            ScanOptionCard(
                title = stringResource(R.string.scan_barcode),
                icon = Icons.Default.QrCodeScanner,
                modifier = Modifier.weight(1f),
                onClick = onBarcodeClick
            )

            ScanOptionCard(
                title = stringResource(R.string.scan_ingredients),
                icon = Icons.Default.CameraAlt,
                modifier = Modifier.weight(1f),
                onClick = onIngredientsClick
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        ScanOptionCard(
            title = stringResource(R.string.manual_entry),
            icon = Icons.Default.Edit,
            modifier = Modifier.fillMaxWidth(),
            onClick = onManualEntryClick
        )
    }
}

/**
 * Tarama seçeneği kartı bileşeni
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScanOptionCard(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    ElevatedCard(
        onClick = onClick,
        modifier = modifier.height(120.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Gıda analizi seçenekleri bileşeni
 */
@Composable
fun FoodAnalysisOptions(
    onAllergensClick: () -> Unit,
    onFatsClick: () -> Unit,
    onSugarsClick: () -> Unit,
    onCertificatesClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            ScanOptionCard(
                title = stringResource(R.string.allergens),
                icon = Icons.Default.Warning,
                modifier = Modifier.weight(1f),
                onClick = onAllergensClick
            )

            ScanOptionCard(
                title = stringResource(R.string.fats),
                icon = Icons.Default.Science,
                modifier = Modifier.weight(1f),
                onClick = onFatsClick
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            ScanOptionCard(
                title = stringResource(R.string.sugars),
                icon = Icons.Default.Spa,
                modifier = Modifier.weight(1f),
                onClick = onSugarsClick
            )

            ScanOptionCard(
                title = stringResource(R.string.food_certificates),
                icon = Icons.Default.Verified,
                modifier = Modifier.weight(1f),
                onClick = onCertificatesClick
            )
        }
    }
}

/**
 * Son tarama öğesi bileşeni
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecentScanItem(
    product: Product,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = product.name,
                    style = MaterialTheme.typography.titleMedium
                )

                if (product.brand != null) {
                    Text(
                        text = product.brand,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            // Ürün durumunu gösteren bir gösterge eklenebilir
            // Örneğin: Helal, Haram, Şüpheli vb.
        }
    }
}
