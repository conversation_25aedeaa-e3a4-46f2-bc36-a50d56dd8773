package com.healthyproducts.app.ui.screens.additives;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0004\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\b2\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u001a\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0007\u001a,\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00122\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001aD\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010\n2\b\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u0014\u0010\u001a\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0017\u0012\u0004\u0012\u00020\u00010\nH\u0007\u00a8\u0006\u001b"}, d2 = {"AdditiveItem", "", "additive", "Lcom/healthyproducts/app/data/model/Additive;", "onClick", "Lkotlin/Function0;", "AdditivesList", "additives", "", "onAdditiveClick", "Lkotlin/Function1;", "AdditivesScreen", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel;", "ImportDialog", "importStatus", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus;", "onDismiss", "onConfirm", "SearchAndFilterBar", "searchQuery", "", "onSearchQueryChange", "filterCategory", "onFilterCategoryChange", "app_debug"})
public final class AdditivesScreenKt {
    
    /**
     * Katkı maddeleri ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AdditivesScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.AdditiveViewModel viewModel) {
    }
    
    /**
     * Arama ve filtreleme çubuğu
     */
    @androidx.compose.runtime.Composable()
    public static final void SearchAndFilterBar(@org.jetbrains.annotations.NotNull()
    java.lang.String searchQuery, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSearchQueryChange, @org.jetbrains.annotations.Nullable()
    java.lang.String filterCategory, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onFilterCategoryChange) {
    }
    
    /**
     * Katkı maddeleri listesi
     */
    @androidx.compose.runtime.Composable()
    public static final void AdditivesList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.model.Additive> additives, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.healthyproducts.app.data.model.Additive, kotlin.Unit> onAdditiveClick) {
    }
    
    /**
     * İçe aktarma iletişim kutusu
     */
    @androidx.compose.runtime.Composable()
    public static final void ImportDialog(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus importStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm) {
    }
    
    /**
     * Katkı maddesi öğesi
     */
    @androidx.compose.runtime.Composable()
    public static final void AdditiveItem(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.Additive additive, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}