{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,333", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,26967", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,27042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "376,377", "startColumns": "4,4", "startOffsets": "29659,29743", "endColumns": "83,86", "endOffsets": "29738,29825"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values-tr\\strings.xml", "from": {"startLines": "69,107,246,211,215,212,216,214,213,259,258,47,112,109,111,106,113,116,115,110,118,117,114,105,239,67,68,141,137,240,236,235,102,270,234,192,1,205,100,253,252,21,230,23,92,189,144,42,243,168,169,170,167,260,271,161,263,86,101,197,83,232,228,224,217,61,128,132,127,131,93,146,151,94,108,30,81,190,97,142,138,6,18,202,201,140,172,171,33,196,145,165,164,166,24,43,45,163,238,149,4,267,266,265,175,207,180,176,178,179,177,181,193,41,32,37,233,31,29,154,60,124,121,123,122,96,78,219,87,80,195,15,84,231,223,198,17,160,159,28,22,99,147,82,194,221,220,186,185,184,242,70,249,40,254,248,255,251,250,7,245,227,11,79,88,152,48,191,98,206,150,91,218,247,268,5,14,12,85,13,27,26,95,208,269,261,229,8,75,73,74,62,65,64,66,63,36,35,34,51,52,56,157,158,55,156,53,57,54,155,143,139,241,244,46,187,262,71,188,134,162,133,129,130,264,148,25,222,237,44,153,72,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3455,5124,12929,10703,10958,10769,11029,10887,10826,13702,13640,2354,5427,5248,5369,5059,5482,5672,5608,5309,5818,5743,5545,5005,12519,3345,3392,6943,6745,12581,12324,12261,4935,14372,12182,9742,16,10425,4857,13387,13286,834,11854,927,4525,9584,7106,2057,12752,8476,8548,8615,8410,13751,14432,8047,13927,4270,4895,10014,4116,12065,11735,11583,11087,2936,6210,6533,6144,6458,4566,7234,7479,4605,5184,1399,4031,9634,4735,7002,6794,183,753,10241,10179,6878,8752,8688,1642,9954,7170,8296,8236,8355,1002,2111,2219,8174,12444,7367,102,14204,14127,14053,8837,10546,9172,8904,9024,9096,8962,9243,9793,2007,1570,1832,12125,1495,1330,7665,2895,6056,5891,5998,5946,4685,3896,11208,4328,3986,9907,561,4176,12000,11522,10073,692,7991,7950,1239,881,4820,7283,4073,9860,11373,11296,9446,9382,9328,12690,3498,13085,1948,13459,13035,13528,13208,13140,231,12863,11678,345,3940,4395,7540,2415,9698,4774,10487,7426,4485,11145,12981,14258,145,510,400,4214,453,1156,1106,4646,10601,14309,13807,11794,274,3804,3666,3739,2987,3195,3125,3268,3056,1787,1742,1697,2514,2561,2766,7844,7892,2709,7777,2608,2817,2662,7720,7052,6834,12638,12814,2285,9496,13875,3561,9543,6660,8108,6599,6331,6393,13989,7324,1056,11455,12382,2165,7601,3624,615", "endColumns": "42,59,51,65,70,56,57,70,60,48,61,60,54,60,57,64,62,70,63,59,49,74,62,53,61,46,62,58,48,56,57,62,45,59,78,50,53,61,37,71,100,46,145,74,40,49,63,53,61,71,66,72,65,55,71,60,61,57,39,58,59,59,58,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,74,58,42,53,76,73,66,54,70,57,71,75,61,56,66,49,71,86,56,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,37,64,60,70,60,55,40,90,45,36,40,42,46,81,76,49,63,53,61,62,54,58,68,49,84,77,67,42,65,56,54,45,68,60,68,43,45,58,52,39,62,53,50,37,50,52,55,56,82,49,38,65,62,67,59,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,51,48,68,46,51,62,40,56,65,60,61,64,63,42,49,66,61,53,63,41,76", "endOffsets": "3493,5179,12976,10764,11024,10821,11082,10953,10882,13746,13697,2410,5477,5304,5422,5119,5540,5738,5667,5364,5863,5813,5603,5054,12576,3387,3450,6997,6789,12633,12377,12319,4976,14427,12256,9788,65,10482,4890,13454,13382,876,11995,997,4561,9629,7165,2106,12809,8543,8610,8683,8471,13802,14499,8103,13984,4323,4930,10068,4171,12120,11789,11627,11140,2982,6326,6594,6205,6528,4600,7278,7535,4641,5243,1490,4068,9693,4769,7047,6829,226,803,10388,10236,6938,8811,8747,1692,10009,7229,8350,8291,8405,1051,2160,2280,8231,12514,7421,140,14253,14199,14122,8899,10596,9238,8957,9091,9167,9019,9295,9855,2052,1637,1914,12177,1565,1394,7715,2931,6113,5941,6051,5993,4730,3935,11291,4390,4026,9949,610,4209,12060,11578,10139,748,8042,7986,1325,922,4852,7319,4111,9902,11450,11368,9491,9441,9377,12747,3556,13135,2002,13523,13080,13608,13281,13203,269,12924,11730,395,3981,4459,7596,2479,9737,4815,10541,7474,4520,11203,13030,14304,178,556,448,4265,505,1234,1151,4680,10662,14367,13870,11849,314,3869,3734,3799,3051,3263,3190,3340,3120,1827,1782,1737,2556,2603,2812,7887,7945,2761,7839,2657,2867,2704,7772,7101,6873,12685,12858,2349,9538,13922,3619,9579,6712,8169,6655,6388,6453,14048,7362,1101,11517,12439,2214,7660,3661,687"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,136,139,140,142,143,144,145,146,147,148,149,152,153,154,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,275,276,277,278,280,281,282,283,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,337,338,339,340,341,342,343,344,345,346,347,348,349,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,371,373,375,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,2840,2900,2952,3018,3089,3146,3204,3275,3336,3385,3447,3508,3563,3624,3682,3747,3810,3881,3945,4005,4055,4130,4193,4247,4309,4356,4419,4478,4527,4584,4642,4705,4751,4811,4890,4941,4995,5057,5095,5167,5268,6028,6174,6249,6290,6340,6404,6458,6520,6592,6659,6732,6798,6854,6926,6987,7049,7107,11956,12159,12219,12379,12438,12487,12545,12596,12717,12783,12849,13119,13158,13207,13352,13393,13457,13553,13595,13659,14017,14067,14107,14155,14210,14362,14424,14489,14553,14617,14672,14732,14796,14855,14915,14970,15024,15078,15144,15206,15281,15340,15383,15437,15514,15588,15655,15710,15781,15839,15911,15987,16049,16289,16356,16406,16478,16565,16622,16697,16766,16821,16862,16924,16979,17037,17089,17139,17183,17271,17338,23543,23590,23644,23682,23835,23896,23967,24028,24168,24209,24300,24346,24383,24424,24467,24514,24596,24673,24723,24787,24841,24903,24966,25021,25080,25149,25199,25284,25362,25430,25641,25707,25764,25819,25865,25934,25995,26064,26108,26154,26213,26266,26306,26369,26423,26474,26512,26563,26616,26672,26729,26812,26862,26901,27047,27110,27249,27309,27354,27424,27497,27562,27631,27704,27774,27851,27920,27965,28010,28377,28424,28471,28522,28570,28628,28685,28752,28806,28861,28908,28965,29019,29063,29115,29164,29311,29426,29596,29830,29871,29928,29994,30055,30117,30182,30246,30289,30339,30406,30468,30522,30586,30628", "endColumns": "42,59,51,65,70,56,57,70,60,48,61,60,54,60,57,64,62,70,63,59,49,74,62,53,61,46,62,58,48,56,57,62,45,59,78,50,53,61,37,71,100,46,145,74,40,49,63,53,61,71,66,72,65,55,71,60,61,57,39,58,59,59,58,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,74,58,42,53,76,73,66,54,70,57,71,75,61,56,66,49,71,86,56,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,37,64,60,70,60,55,40,90,45,36,40,42,46,81,76,49,63,53,61,62,54,58,68,49,84,77,67,42,65,56,54,45,68,60,68,43,45,58,52,39,62,53,50,37,50,52,55,56,82,49,38,65,62,67,59,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,51,48,68,46,51,62,40,56,65,60,61,64,63,42,49,66,61,53,63,41,76", "endOffsets": "2835,2895,2947,3013,3084,3141,3199,3270,3331,3380,3442,3503,3558,3619,3677,3742,3805,3876,3940,4000,4050,4125,4188,4242,4304,4351,4414,4473,4522,4579,4637,4700,4746,4806,4885,4936,4990,5052,5090,5162,5263,5310,6169,6244,6285,6335,6399,6453,6515,6587,6654,6727,6793,6849,6921,6982,7044,7102,7142,12010,12214,12274,12433,12482,12540,12591,12712,12778,12844,12919,13153,13202,13263,13388,13452,13548,13590,13654,13693,14062,14102,14150,14205,14357,14419,14484,14548,14612,14667,14727,14791,14850,14910,14965,15019,15073,15139,15201,15276,15335,15378,15432,15509,15583,15650,15705,15776,15834,15906,15982,16044,16101,16351,16401,16473,16560,16617,16692,16761,16816,16857,16919,16974,17032,17084,17134,17178,17266,17333,17378,23585,23639,23677,23742,23891,23962,24023,24079,24204,24295,24341,24378,24419,24462,24509,24591,24668,24718,24782,24836,24898,24961,25016,25075,25144,25194,25279,25357,25425,25468,25702,25759,25814,25860,25929,25990,26059,26103,26149,26208,26261,26301,26364,26418,26469,26507,26558,26611,26667,26724,26807,26857,26896,26962,27105,27173,27304,27349,27419,27492,27557,27626,27699,27769,27846,27915,27960,28005,28050,28419,28466,28517,28565,28623,28680,28747,28801,28856,28903,28960,29014,29058,29110,29159,29228,29353,29473,29654,29866,29923,29989,30050,30112,30177,30241,30284,30334,30401,30463,30517,30581,30623,30700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "141,162,163,164", "startColumns": "4,4,4,4", "startOffsets": "12279,13698,13804,13911", "endColumns": "99,105,106,105", "endOffsets": "12374,13799,13906,14012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "71,72,73,74,75,76,77,353", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5315,5412,5514,5612,5709,5811,5917,28276", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "5407,5509,5607,5704,5806,5912,6023,28372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,996,1084,1155,1229,1304,1376,1454,1522", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,991,1079,1150,1224,1299,1371,1449,1517,1635"}, "to": {"startLines": "95,96,150,151,155,198,199,279,284,307,308,336,350,351,352,370,372,374", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7147,7240,12924,13019,13268,16106,16189,23747,24084,25473,25553,27178,28055,28129,28204,29233,29358,29478", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "7235,7319,13014,13114,13347,16184,16284,23830,24163,25548,25636,27244,28124,28199,28271,29306,29421,29591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "120,121,122,123,124,125,126,127,129,130,131,132,133,134,135,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9849,9959,10114,10250,10355,10502,10632,10759,11012,11184,11291,11448,11582,11727,11894,12015,12079", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "9954,10109,10245,10350,10497,10627,10754,10860,11179,11286,11443,11577,11722,11889,11951,12074,12154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17383,17498,17611,17729,17844,17940,18036,18149,18282,18404,18544,18629,18727,18816,18913,19028,19149,19252,19389,19525,19647,19818,19936,20052,20170,20285,20375,20473,20597,20726,20827,20929,21035,21171,21311,21423,21525,21601,21698,21796,21906,21992,22077,22194,22274,22358,22458,22558,22654,22749,22837,22943,23043,23142,23263,23343,23450", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "17493,17606,17724,17839,17935,18031,18144,18277,18399,18539,18624,18722,18811,18908,19023,19144,19247,19384,19520,19642,19813,19931,20047,20165,20280,20370,20468,20592,20721,20822,20924,21030,21166,21306,21418,21520,21596,21693,21791,21901,21987,22072,22189,22269,22353,22453,22553,22649,22744,22832,22938,23038,23137,23258,23338,23445,23538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,361,497,610,695,781,859,947,1039,1152,1262,1355,1448,1555,1675,1757,1840,2017,2113,2222,2338,2441", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "219,356,492,605,690,776,854,942,1034,1147,1257,1350,1443,1550,1670,1752,1835,2012,2108,2217,2333,2436,2575"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7324,7493,7630,7766,7879,7964,8050,8128,8216,8308,8421,8531,8624,8717,8824,8944,9026,9109,9286,9382,9491,9607,9710", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "7488,7625,7761,7874,7959,8045,8123,8211,8303,8416,8526,8619,8712,8819,8939,9021,9104,9281,9377,9486,9602,9705,9844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "128", "startColumns": "4", "startOffsets": "10865", "endColumns": "146", "endOffsets": "11007"}}]}]}