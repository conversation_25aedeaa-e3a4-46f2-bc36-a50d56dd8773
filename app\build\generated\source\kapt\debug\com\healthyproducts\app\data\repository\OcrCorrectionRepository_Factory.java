package com.healthyproducts.app.data.repository;

import android.content.Context;
import com.healthyproducts.app.data.api.AiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OcrCorrectionRepository_Factory implements Factory<OcrCorrectionRepository> {
  private final Provider<Context> contextProvider;

  private final Provider<AiService> aiServiceProvider;

  private final Provider<FirestoreRepository> firestoreRepositoryProvider;

  public OcrCorrectionRepository_Factory(Provider<Context> contextProvider,
      Provider<AiService> aiServiceProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.aiServiceProvider = aiServiceProvider;
    this.firestoreRepositoryProvider = firestoreRepositoryProvider;
  }

  @Override
  public OcrCorrectionRepository get() {
    return newInstance(contextProvider.get(), aiServiceProvider.get(), firestoreRepositoryProvider.get());
  }

  public static OcrCorrectionRepository_Factory create(Provider<Context> contextProvider,
      Provider<AiService> aiServiceProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    return new OcrCorrectionRepository_Factory(contextProvider, aiServiceProvider, firestoreRepositoryProvider);
  }

  public static OcrCorrectionRepository newInstance(Context context, AiService aiService,
      FirestoreRepository firestoreRepository) {
    return new OcrCorrectionRepository(context, aiService, firestoreRepository);
  }
}
