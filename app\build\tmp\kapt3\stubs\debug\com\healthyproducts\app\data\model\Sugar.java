package com.healthyproducts.app.data.model;

/**
 * <PERSON><PERSON><PERSON> türünü temsil eden veri sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u000b\n\u0002\u0010 \n\u0002\bI\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u00cb\u0001\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\u0004\u0012\b\b\u0002\u0010\n\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\f\u001a\u00020\u0004\u0012\b\b\u0002\u0010\r\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0007\u0012\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013\u0012\u000e\b\u0002\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013\u00a2\u0006\u0002\u0010\u0018J\t\u0010I\u001a\u00020\u0004H\u00c6\u0003J\t\u0010J\u001a\u00020\u0004H\u00c6\u0003J\t\u0010K\u001a\u00020\u0004H\u00c6\u0003J\t\u0010L\u001a\u00020\u0007H\u00c6\u0003J\t\u0010M\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013H\u00c6\u0003J\u000f\u0010O\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013H\u00c6\u0003J\t\u0010P\u001a\u00020\u0004H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0004H\u00c6\u0003J\u000f\u0010R\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013H\u00c6\u0003J\t\u0010S\u001a\u00020\u0004H\u00c6\u0003J\t\u0010T\u001a\u00020\u0007H\u00c6\u0003J\t\u0010U\u001a\u00020\u0007H\u00c6\u0003J\t\u0010V\u001a\u00020\u0004H\u00c6\u0003J\t\u0010W\u001a\u00020\u0004H\u00c6\u0003J\t\u0010X\u001a\u00020\u0004H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0004H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0004H\u00c6\u0003J\u00cf\u0001\u0010[\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00042\b\b\u0002\u0010\n\u001a\u00020\u00042\b\b\u0002\u0010\u000b\u001a\u00020\u00042\b\b\u0002\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\r\u001a\u00020\u00042\b\b\u0002\u0010\u000e\u001a\u00020\u00042\b\b\u0002\u0010\u000f\u001a\u00020\u00042\b\b\u0002\u0010\u0010\u001a\u00020\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u00072\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u00132\u000e\b\u0002\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u00132\b\b\u0002\u0010\u0015\u001a\u00020\u00042\b\b\u0002\u0010\u0016\u001a\u00020\u00042\u000e\b\u0002\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013H\u00c6\u0001J\u0013\u0010\\\u001a\u00020]2\b\u0010^\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010_\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\u000e\u0010b\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\u000e\u0010c\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\u0014\u0010d\u001a\b\u0012\u0004\u0012\u00020\u00040\u00132\u0006\u0010`\u001a\u00020aJ\u000e\u0010e\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\u000e\u0010f\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\u000e\u0010g\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\u000e\u0010h\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\u000e\u0010i\u001a\u00020\u00042\u0006\u0010`\u001a\u00020aJ\t\u0010j\u001a\u00020\u0007H\u00d6\u0001J\t\u0010k\u001a\u00020\u0004H\u00d6\u0001R\u001e\u0010\r\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u001a\"\u0004\b\u001b\u0010\u001cR\u001e\u0010\f\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u001a\"\u0004\b\u001e\u0010\u001cR$\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u00138\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\u001e\u0010\u0010\u001a\u00020\u00078\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010$\"\u0004\b%\u0010&R\u0011\u0010\'\u001a\u00020\u00078F\u00a2\u0006\u0006\u001a\u0004\b(\u0010$R\u001e\u0010\u0006\u001a\u00020\u00078\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b)\u0010$\"\u0004\b*\u0010&R\u001e\u0010\u0003\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b+\u0010\u001a\"\u0004\b,\u0010\u001cR$\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u00138\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b-\u0010 \"\u0004\b.\u0010\"R$\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u00138\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u0010 \"\u0004\b0\u0010\"R\u001e\u0010\n\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010\u001a\"\u0004\b2\u0010\u001cR\u001e\u0010\t\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010\u001a\"\u0004\b4\u0010\u001cR\u001e\u0010\u0016\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u0010\u001a\"\u0004\b6\u0010\u001cR\u001e\u0010\u0015\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b7\u0010\u001a\"\u0004\b8\u0010\u001cR\u0017\u00109\u001a\b\u0012\u0004\u0012\u00020\u00040\u00138F\u00a2\u0006\u0006\u001a\u0004\b:\u0010 R\u001e\u0010\u0011\u001a\u00020\u00078\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b;\u0010$\"\u0004\b<\u0010&R\u001e\u0010\u000f\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b=\u0010\u001a\"\u0004\b>\u0010\u001cR\u001e\u0010\u000e\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b?\u0010\u001a\"\u0004\b@\u0010\u001cR\u001e\u0010\u0005\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bA\u0010\u001a\"\u0004\bB\u0010\u001cR\u001e\u0010\u000b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bC\u0010\u001a\"\u0004\bD\u0010\u001cR\u001e\u0010\b\u001a\u00020\u00078\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bE\u0010$\"\u0004\bF\u0010&R\u0017\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00040\u00138F\u00a2\u0006\u0006\u001a\u0004\bH\u0010 \u00a8\u0006l"}, d2 = {"Lcom/healthyproducts/app/data/model/Sugar;", "", "()V", "id", "", "sugarId", "harmfulLevel", "", "unhealthyLevel", "nameTr", "nameEn", "symbol", "descriptionTr", "descriptionEn", "sourceTr", "sourceEn", "glycemicIndex", "riskLevel", "labelsTr", "", "labelsEn", "notesTr", "notesEn", "functionalType", "(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILjava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V", "getDescriptionEn", "()Ljava/lang/String;", "setDescriptionEn", "(Ljava/lang/String;)V", "getDescriptionTr", "setDescriptionTr", "getFunctionalType", "()Ljava/util/List;", "setFunctionalType", "(Ljava/util/List;)V", "getGlycemicIndex", "()I", "setGlycemicIndex", "(I)V", "glycemicLoad", "getGlycemicLoad", "getHarmfulLevel", "setHarmfulLevel", "getId", "setId", "getLabelsEn", "setLabelsEn", "getLabelsTr", "setLabelsTr", "getNameEn", "setNameEn", "getNameTr", "setNameTr", "getNotesEn", "setNotesEn", "getNotesTr", "setNotesTr", "religiousStatuses", "getReligiousStatuses", "getRiskLevel", "setRiskLevel", "getSourceEn", "setSourceEn", "getSourceTr", "setSourceTr", "getSugarId", "setSugarId", "getSymbol", "setSymbol", "getUnhealthyLevel", "setUnhealthyLevel", "veganStatuses", "getVeganStatuses", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "getChemicalStructure", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "getDescription", "getHealthEffects", "getLabels", "getName", "getNotes", "getOrigin", "getSource", "getUsage", "hashCode", "toString", "app_debug"})
public final class Sugar {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String id;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String sugarId;
    private int harmfulLevel;
    private int unhealthyLevel;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String symbol;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String sourceTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String sourceEn;
    private int glycemicIndex;
    private int riskLevel;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> labelsTr;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> labelsEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesEn;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> functionalType;
    
    public Sugar(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String sugarId, int harmfulLevel, int unhealthyLevel, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceTr, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceEn, int glycemicIndex, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType) {
        super();
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    public final void setId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "sugarId")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSugarId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "sugarId")
    public final void setSugarId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "harmful_level")
    public final int getHarmfulLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "harmful_level")
    public final void setHarmfulLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "unhealthy_level")
    public final int getUnhealthyLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "unhealthy_level")
    public final void setUnhealthyLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    public final void setNameTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    public final void setNameEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSymbol() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    public final void setSymbol(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    public final void setDescriptionTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    public final void setDescriptionEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "source_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSourceTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "source_tr")
    public final void setSourceTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "source_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSourceEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "source_en")
    public final void setSourceEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "glycemic_index")
    public final int getGlycemicIndex() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "glycemic_index")
    public final void setGlycemicIndex(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final int getRiskLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final void setRiskLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_tr")
    public final void setLabelsTr(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_en")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_en")
    public final void setLabelsEn(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    public final void setNotesTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    public final void setNotesEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getFunctionalType() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    public final void setFunctionalType(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    public Sugar() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSource(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabels(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotes(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrigin(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getChemicalStructure(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUsage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffects(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getVeganStatuses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getReligiousStatuses() {
        return null;
    }
    
    public final int getGlycemicLoad() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component15() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component18() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.model.Sugar copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String sugarId, int harmfulLevel, int unhealthyLevel, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceTr, @org.jetbrains.annotations.NotNull()
    java.lang.String sourceEn, int glycemicIndex, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}