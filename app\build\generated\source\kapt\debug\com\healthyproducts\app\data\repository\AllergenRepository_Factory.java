package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AllergenRepository_Factory implements Factory<AllergenRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public AllergenRepository_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public AllergenRepository get() {
    return newInstance(firestoreProvider.get());
  }

  public static AllergenRepository_Factory create(Provider<FirebaseFirestore> firestoreProvider) {
    return new AllergenRepository_Factory(firestoreProvider);
  }

  public static AllergenRepository newInstance(FirebaseFirestore firestore) {
    return new AllergenRepository(firestore);
  }
}
