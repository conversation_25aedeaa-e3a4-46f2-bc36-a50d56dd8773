package com.healthyproducts.app;

import com.healthyproducts.app.data.repository.FirestoreRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HealthyProductsApp_MembersInjector implements MembersInjector<HealthyProductsApp> {
  private final Provider<FirestoreRepository> firestoreRepositoryProvider;

  public HealthyProductsApp_MembersInjector(
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    this.firestoreRepositoryProvider = firestoreRepositoryProvider;
  }

  public static MembersInjector<HealthyProductsApp> create(
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    return new HealthyProductsApp_MembersInjector(firestoreRepositoryProvider);
  }

  @Override
  public void injectMembers(HealthyProductsApp instance) {
    injectFirestoreRepository(instance, firestoreRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.healthyproducts.app.HealthyProductsApp.firestoreRepository")
  public static void injectFirestoreRepository(HealthyProductsApp instance,
      FirestoreRepository firestoreRepository) {
    instance.firestoreRepository = firestoreRepository;
  }
}
