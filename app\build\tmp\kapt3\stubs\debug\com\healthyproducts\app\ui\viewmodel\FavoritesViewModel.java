package com.healthyproducts.app.ui.viewmodel;

/**
 * Favori ürünleri yöneten ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001:\u0001\u0017B\u000f\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fJ*\u0010\u0011\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\r0\u0013J\u000e\u0010\u0015\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\u0016\u0010\u0016\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0018"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel;", "Landroidx/lifecycle/ViewModel;", "favoritesRepository", "Lcom/healthyproducts/app/data/repository/FavoritesRepository;", "(Lcom/healthyproducts/app/data/repository/FavoritesRepository;)V", "_favoritesState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState;", "favoritesState", "Lkotlinx/coroutines/flow/StateFlow;", "getFavoritesState", "()Lkotlinx/coroutines/flow/StateFlow;", "addToFavorites", "", "userId", "", "productId", "checkIsFavorite", "callback", "Lkotlin/Function1;", "", "getFavoriteProducts", "removeFromFavorites", "FavoritesState", "app_debug"})
public final class FavoritesViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FavoritesRepository favoritesRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState> _favoritesState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState> favoritesState = null;
    
    public FavoritesViewModel(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FavoritesRepository favoritesRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState> getFavoritesState() {
        return null;
    }
    
    /**
     * Kullanıcının favori ürünlerini getirme
     */
    public final void getFavoriteProducts(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
    
    /**
     * Ürünü favorilere ekleme
     */
    public final void addToFavorites(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    java.lang.String productId) {
    }
    
    /**
     * Ürünü favorilerden çıkarma
     */
    public final void removeFromFavorites(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    java.lang.String productId) {
    }
    
    /**
     * Ürünün favori olup olmadığını kontrol etme
     */
    public final void checkIsFavorite(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    java.lang.String productId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    public FavoritesViewModel() {
        super();
    }
    
    /**
     * Favori ürünler durumunu temsil eden sealed class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState;", "", "()V", "Empty", "Error", "Loading", "Success", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Empty;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Success;", "app_debug"})
    public static abstract class FavoritesState {
        
        private FavoritesState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Empty;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState;", "()V", "app_debug"})
        public static final class Empty extends com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Empty INSTANCE = null;
            
            private Empty() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Error extends com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState;", "()V", "app_debug"})
        public static final class Loading extends com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Loading INSTANCE = null;
            
            private Loading() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u0013\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0002\u0010\u0005J\u000f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0019\u0010\t\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\rH\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState$Success;", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel$FavoritesState;", "products", "", "Lcom/healthyproducts/app/model/Product;", "(Ljava/util/List;)V", "getProducts", "()Ljava/util/List;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
        public static final class Success extends com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState {
            @org.jetbrains.annotations.NotNull()
            private final java.util.List<com.healthyproducts.app.model.Product> products = null;
            
            public Success(@org.jetbrains.annotations.NotNull()
            java.util.List<com.healthyproducts.app.model.Product> products) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.util.List<com.healthyproducts.app.model.Product> getProducts() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.util.List<com.healthyproducts.app.model.Product> component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Success copy(@org.jetbrains.annotations.NotNull()
            java.util.List<com.healthyproducts.app.model.Product> products) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
}