# Healthy Products - Ürün Gereksinimleri Dokümanı (PRD)

## 1. Ürün Vizyonu

Healthy Products, tüketicilerin gıda ürünlerinin içeriklerini analiz ederek, bu ü<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Şüpheli veya Vegan olup olmadığını belirlemelerine yardımcı olan çok dilli bir mobil uygulamadır. Uygulama, kamera ile ürün barkodu tarama ve ürün içerik listesi fotoğrafı çekme özelliklerine sahiptir. Kullanıcılar kendi tercihlerine göre filtreleme yapabilir ve çeşitli sertifika bilgilerini görüntüleyebilir.

## 2. Hedef Kitle

- Helal beslenmeye dikkat eden Müslüman tüketiciler
- Sağlıklı beslenmeye önem veren tüm tüketiciler
- Gıda alerjisi veya özel diyet kısıtlamaları olan kişiler
- Vegan veya vejetaryen beslenme tercih edenler
- Koşer beslenmeye dikkat eden Yahudi tüketiciler
- Farklı dillerde ürün bilgisi arayan uluslararası kullanıcılar

## 3. Temel Özellikler

### 3.1. Kamera İşlevselliği
- **Barkod Tarama**: Ürün barkodlarını tarayarak veritabanında arama yapma
- **İçerik Tarama**: Ürün içerik listesinin fotoğrafını çekerek OCR ile metne dönüştürme ve analiz etme

### 3.2. Ürün Analizi
- **Helal/Haram Analizi**: Ürün içeriklerinin İslami kurallara uygunluğunu değerlendirme (ayarlardan açılıp kapatılabilir)
- **Sağlık Analizi**: İçeriklerin sağlık açısından değerlendirilmesi
- **Zararlı Madde Tespiti**: Potansiyel zararlı katkı maddelerinin tespiti
- **Vegan/Vejetaryen Uygunluğu**: Ürünün vegan veya vejetaryen beslenmeye uygunluğu
- **Şüpheli İçerik Tespiti**: Kesin olarak sınıflandırılamayan içeriklerin belirlenmesi
- **Koşer Uygunluğu**: Ürünün Yahudi beslenme kurallarına uygunluğu

### 3.3. Veritabanı
- **Ürün Veritabanı**: Daha önce taranan ve analiz edilen ürünlerin saklanması
- **İçerik Veritabanı**: Bilinen içeriklerin Helal/Haram/Sağlıksız/Zararlı/Şüpheli/Vegan durumlarının saklanması
- **Sertifika Veritabanı**: Helal, Vegan, Koşer gibi sertifikaların ve bunları veren kurumların bilgilerinin saklanması
- **Firma Veritabanı**: Üretici firmaların ve sahip oldukları sertifikaların bilgilerinin saklanması

### 3.4. Kullanıcı Özellikleri
- **Kullanıcı Hesapları**: Kullanıcı kaydı ve girişi
- **Favori Ürünler**: Kullanıcıların favori ürünlerini kaydetmesi
- **Tarama Geçmişi**: Daha önce taranan ürünlerin geçmişi
- **Bildirimler**: Yeni eklenen ürünler veya güncellenen analizler hakkında bildirimler
- **Ürün Değerlendirme**: Kullanıcıların ürünler hakkında yorum ve değerlendirme yapabilmesi
- **Ürün İnceleme Gönderme**: Veritabanında olmayan ürünlerin incelenmesi için talep gönderme
- **Ürün Değişikliği Bildirme**: Ürün içeriğinde değişiklik olduğunda bildirim gönderme

### 3.5. AI Entegrasyonu
- **OpenAI/Deepseek Entegrasyonu**: Ürün içeriklerinin analizi için AI servislerinin kullanımı
- **İçerik Tanıma**: OCR ile çekilen fotoğraflardan metin çıkarma

### 3.6. Çoklu Dil Desteği
- **Arayüz Dili**: Uygulama arayüzünün farklı dillerde kullanılabilmesi
- **İçerik Analizi**: Farklı dillerdeki ürün içeriklerinin analiz edilebilmesi
- **Sonuç Gösterimi**: Analiz sonuçlarının seçilen dilde gösterilmesi

## 4. Teknik Gereksinimler

### 4.1. Mobil Uygulama
- **Platform**: Flutter ile iOS, Android ve web desteği
- **Minimum Android Sürümü**: Android 8.0 (API seviye 26)
- **Hedef Android Sürümü**: Android 13 (API seviye 33)
- **Minimum iOS Sürümü**: iOS 12

### 4.2. Backend
- **Veritabanı**: Firebase Firestore
- **Kimlik Doğrulama**: Firebase Authentication
- **API Entegrasyonu**: DeepSeek API ve Google Gemini API
- **Depolama**: Firebase Storage (ürün görselleri ve tarama sonuçları için)

### 4.3. Kamera ve Tarama
- **Kamera Erişimi**: Arka kamera kullanımı
- **Barkod Tarama**: 1D ve 2D barkod formatları desteği
- **OCR**: Metin tanıma için Google ML Kit veya benzer kütüphane

## 5. Kullanıcı Arayüzü

### 5.1. Ana Ekranlar
- **Ana Sayfa**: Hızlı tarama seçenekleri ve son taranan ürünler
- **Tarama Ekranı**: Barkod ve içerik tarama modları
- **Ürün Detay Sayfası**: Analiz sonuçları, detaylı içerik bilgileri ve sertifika bilgileri
- **Favoriler**: Kaydedilen ürünler
- **Profil**: Kullanıcı hesap bilgileri ve tarama geçmişi
- **Ayarlar**: Uygulama ayarları, dil seçenekleri ve analiz tercihleri (Helal/Haram analizi açma/kapama gibi)
- **Sertifikalar**: Helal, Vegan, Koşer gibi sertifikaların açıklamaları ve veren kurumlar hakkında bilgiler

### 5.2. Tasarım İlkeleri
- Kullanıcı dostu, sezgisel arayüz
- Helal/Haram/Şüpheli durumları için açık renk kodlaması
- Kolay okunabilir içerik analizi sonuçları
- Hızlı tarama ve sonuç alma odaklı tasarım

## 6. Geliştirme Planı

### 6.1. Proje Kurulumu
- Flutter SDK kurulumu
- Gerekli paketlerin eklenmesi
- Temel proje yapısının oluşturulması

### 6.2. Temel Yapı Geliştirme
- Tema ve stil tanımlamaları
- Rota yönetimi
- Ana ekranların iskelet yapısı

### 6.3. Kamera İşlevselliği
- Kamera izinleri yönetimi
- Kamera önizleme ekranı
- Barkod tarama modu
- İçerik tarama modu

### 6.4. Veri İşleme
- Barkod verilerini işleme
- OCR ile içerik metni çıkarma
- AI servisleri ile içerik analizi

### 6.5. Veritabanı Entegrasyonu
- Supabase bağlantısı
- Ürün veritabanı yapısı
- Kullanıcı favorileri

### 6.6. Kullanıcı Arayüzü Geliştirme
- Detaylı UI bileşenlerinin oluşturulması
- Animasyonlar ve geçişler
- Kullanıcı deneyimi iyileştirmeleri

### 6.7. Test ve Optimizasyon
- Birim testleri
- Entegrasyon testleri
- Performans optimizasyonu

### 6.8. Dağıtım
- Android APK oluşturma
- Google Play Store'a yükleme (opsiyonel)

## 7. Başarı Kriterleri

- Kamera ile barkod ve içerik tarama özelliklerinin sorunsuz çalışması
- Doğru ve güvenilir Helal/Haram analizi sonuçları
- Kullanıcı dostu ve sezgisel bir arayüz
- Hızlı tarama ve analiz süreci
- Güvenilir ve güncel ürün veritabanı

## 8. Gelecek Geliştirmeler

- Çevrimdışı çalışma modu
- Topluluk katkıları ve değerlendirmeleri
- Daha gelişmiş AI analiz özellikleri
- Diyet ve alerji filtreleme özellikleri
- Ürün karşılaştırma özelliği
- Alışveriş listesi oluşturma
- Ürün alternatifleri önerme
- Daha fazla sertifika ve standart ekleme
- Sesli komut desteği

## 9. Geliştirme Notları ve İlerleme

### 9.1. Veritabanı Değişiklikleri
- **Supabase'den Firebase'e Geçiş**: Proje başlangıçta Supabase ile planlanmış olsa da, daha sonra Firebase'e geçiş yapılmıştır. Tüm kullanıcı yönetimi, veri saklama ve kimlik doğrulama işlemleri Firebase üzerinden gerçekleştirilmektedir.
- **Firebase Firestore**: Katkı maddeleri, kullanıcı tercihleri ve ürün bilgileri Firestore veritabanında saklanmaktadır.
- **Firebase Authentication**: Kullanıcı kimlik doğrulama işlemleri için Firebase Authentication kullanılmaktadır.

### 9.2. AI Entegrasyonu
- **DeepSeek API**: OCR ile tanınan metinlerin düzeltilmesi ve analizi için DeepSeek API entegre edilmiştir.
- **Gemini API**: Alternatif olarak Google'ın Gemini API'si de entegre edilmiştir.
- **AI Model Seçimi**: Kullanıcılar ayarlar ekranından hangi AI modelini kullanmak istediklerini seçebilmektedir.

### 9.3. Katkı Maddeleri Veritabanı
- **JSON Formatında Veri**: Katkı maddeleri bilgileri JSON formatında hazırlanmış ve Firestore'a aktarılmıştır.
- **Çoklu Dil Desteği**: Katkı maddeleri Türkçe ve İngilizce olarak saklanmaktadır.
- **Kategori Bilgileri**: Her katkı maddesi için kategori bilgileri (Türkçe ve İngilizce) eklenmiştir.
- **Helal, Vegan, Koşer Durumları**: Katkı maddelerinin helal, vegan, vejetaryen ve koşer durumları belirtilmiştir.
- **Zararlılık ve Sağlıksızlık Seviyeleri**: Her katkı maddesi için zararlılık ve sağlıksızlık seviyeleri 0-5 arasında belirlenmiştir.

### 9.4. Tamamlanan Özellikler
- **Kullanıcı Kimlik Doğrulama**: Kayıt olma, giriş yapma ve çıkış yapma işlevleri
- **Çoklu Dil Desteği**: Türkçe ve İngilizce dil desteği
- **Katkı Maddeleri Yönetimi**: Katkı maddelerini listeleme, detaylarını görüntüleme, düzenleme ve ekleme
- **AI Model Seçimi**: Kullanıcıların tercih ettikleri AI modelini seçebilmesi
- **Tema Seçimi**: Açık ve koyu tema desteği
- **Kullanıcı Tercihleri**: Kullanıcı tercihlerinin Firebase'de saklanması

### 9.5. Devam Eden Çalışmalar
- **OCR İyileştirmeleri**: Kamera ile çekilen içerik listelerinin daha doğru tanınması
- **Barkod Tarama**: Ürün barkodlarını tarama ve veritabanında arama
- **Ürün Analizi**: Taranan ürünlerin içeriklerinin analiz edilmesi
- **Favoriler**: Kullanıcıların favori ürünlerini kaydetmesi
- **Tarama Geçmişi**: Daha önce taranan ürünlerin geçmişinin tutulması

### 9.6. Teknik Notlar
- **Retrofit**: API istekleri için Retrofit kütüphanesi kullanılmaktadır.
- **Jetpack Compose**: Kullanıcı arayüzü Jetpack Compose ile geliştirilmiştir.
- **Hilt**: Bağımlılık enjeksiyonu için Hilt kullanılmaktadır.
- **Coroutines**: Asenkron işlemler için Kotlin Coroutines kullanılmaktadır.
- **ViewModel**: MVVM mimarisi için ViewModel kullanılmaktadır.
- **Navigation Component**: Ekranlar arası geçişler için Navigation Component kullanılmaktadır.
