package com.healthyproducts.app.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Kullanıcı modeli
 */
@Serializable
data class User(
    @SerialName("id") val id: String,
    @SerialName("email") val email: String,
    @SerialName("username") val username: String? = null,
    @SerialName("full_name") val fullName: String? = null,
    @SerialName("avatar_url") val avatarUrl: String? = null,
    @SerialName("language") val language: String = "tr", // Varsayılan dil: Türkçe
    @SerialName("created_at") val createdAt: String? = null,
    @SerialName("updated_at") val updatedAt: String? = null
)

/**
 * Kullanıcı ayarları modeli
 */
@Serializable
data class UserSettings(
    @SerialName("user_id") val userId: String,
    @SerialName("language") val language: String = "tr", // Varsayılan dil: Türkçe
    @SerialName("theme") val theme: String = "system", // Varsayılan tema: Sistem
    @SerialName("notifications_enabled") val notificationsEnabled: Boolean = true,
    @SerialName("created_at") val createdAt: String? = null,
    @SerialName("updated_at") val updatedAt: String? = null
)

/**
 * Desteklenen diller
 */
enum class SupportedLanguage(val code: String, val displayName: String) {
    TURKISH("tr", "Türkçe"),
    ENGLISH("en", "English"),
    // Daha fazla dil eklenebilir
    ;
    
    companion object {
        fun fromCode(code: String): SupportedLanguage {
            return values().find { it.code == code } ?: TURKISH
        }
    }
}
