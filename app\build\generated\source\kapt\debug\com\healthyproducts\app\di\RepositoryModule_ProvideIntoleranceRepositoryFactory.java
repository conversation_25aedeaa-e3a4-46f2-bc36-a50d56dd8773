package com.healthyproducts.app.di;

import com.google.firebase.firestore.FirebaseFirestore;
import com.healthyproducts.app.data.repository.IntoleranceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideIntoleranceRepositoryFactory implements Factory<IntoleranceRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public RepositoryModule_ProvideIntoleranceRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public IntoleranceRepository get() {
    return provideIntoleranceRepository(firestoreProvider.get());
  }

  public static RepositoryModule_ProvideIntoleranceRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new RepositoryModule_ProvideIntoleranceRepositoryFactory(firestoreProvider);
  }

  public static IntoleranceRepository provideIntoleranceRepository(FirebaseFirestore firestore) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideIntoleranceRepository(firestore));
  }
}
