package com.healthyproducts.app.data.repository;

/**
 * Firebase Storage işlemlerini yöneten repository sınıfı
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u001e\u0010\f\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0013R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/healthyproducts/app/data/repository/FirebaseStorageRepository;", "", "storage", "Lcom/google/firebase/storage/FirebaseStorage;", "authRepository", "Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;", "(Lcom/google/firebase/storage/FirebaseStorage;Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;)V", "deleteFile", "", "fileUrl", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadImage", "fileRef", "Lcom/google/firebase/storage/StorageReference;", "imageUri", "Landroid/net/Uri;", "(Lcom/google/firebase/storage/StorageReference;Landroid/net/Uri;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadProductPhoto", "(Landroid/net/Uri;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadProfilePhoto", "app_debug"})
public final class FirebaseStorageRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.storage.FirebaseStorage storage = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FirebaseAuthRepository authRepository = null;
    
    @javax.inject.Inject()
    public FirebaseStorageRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.storage.FirebaseStorage storage, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirebaseAuthRepository authRepository) {
        super();
    }
    
    /**
     * Kullanıcı profil fotoğrafını yükler
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object uploadProfilePhoto(@org.jetbrains.annotations.NotNull()
    android.net.Uri imageUri, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Ürün fotoğrafını yükler
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object uploadProductPhoto(@org.jetbrains.annotations.NotNull()
    android.net.Uri imageUri, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Görüntüyü yükler ve indirme URL'sini döndürür
     */
    private final java.lang.Object uploadImage(com.google.firebase.storage.StorageReference fileRef, android.net.Uri imageUri, kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Dosyayı siler
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteFile(@org.jetbrains.annotations.NotNull()
    java.lang.String fileUrl, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}