package com.healthyproducts.app.data.repository;

/**
 * Firestore veritabanı işlemlerini yöneten repository sınıfı
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010 \n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u0016\u0010\u001d\u001a\u00020\u00192\u0006\u0010\u001e\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010\u001f\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010 J\u0016\u0010!\u001a\u00020\r2\u0006\u0010\"\u001a\u00020\u001bH\u0082@\u00a2\u0006\u0002\u0010\u001cJ\u0014\u0010#\u001a\b\u0012\u0004\u0012\u00020\u001b0$H\u0086@\u00a2\u0006\u0002\u0010 J\u0014\u0010%\u001a\b\u0012\u0004\u0012\u00020\u001b0$H\u0086@\u00a2\u0006\u0002\u0010 J\u0016\u0010\u0015\u001a\u00020\u00192\u0006\u0010\"\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u000e\u0010\u0017\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010 J\u0016\u0010&\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u0016\u0010\'\u001a\u00020\u00192\u0006\u0010(\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010)J\u0016\u0010*\u001a\u00020\u00192\u0006\u0010+\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010,R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0019\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013\u00a8\u0006-"}, d2 = {"Lcom/healthyproducts/app/data/repository/FirestoreRepository;", "", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "authRepository", "Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;", "(Lcom/google/firebase/firestore/FirebaseFirestore;Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;)V", "_userLanguage", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "_userPreferences", "Lcom/healthyproducts/app/model/UserPreferences;", "_userSettings", "Lcom/healthyproducts/app/data/model/UserSettings;", "getAuthRepository", "()Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;", "userLanguage", "Lkotlinx/coroutines/flow/StateFlow;", "getUserLanguage", "()Lkotlinx/coroutines/flow/StateFlow;", "userPreferences", "getUserPreferences", "userSettings", "getUserSettings", "addToFavorites", "", "productId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addToSearchHistory", "search", "clearSearchHistory", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createUserSettings", "userId", "getFavorites", "", "getSearchHistory", "removeFromFavorites", "updateUserLanguage", "language", "(Lcom/healthyproducts/app/data/model/SupportedLanguage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserPreferences", "preferences", "(Lcom/healthyproducts/app/model/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class FirestoreRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.firestore.FirebaseFirestore firestore = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FirebaseAuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.data.model.UserSettings> _userSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.UserSettings> userSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.data.model.SupportedLanguage> _userLanguage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.SupportedLanguage> userLanguage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.model.UserPreferences> _userPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.model.UserPreferences> userPreferences = null;
    
    @javax.inject.Inject()
    public FirestoreRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirebaseAuthRepository authRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.FirebaseAuthRepository getAuthRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.UserSettings> getUserSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.SupportedLanguage> getUserLanguage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.model.UserPreferences> getUserPreferences() {
        return null;
    }
    
    /**
     * Kullanıcı ayarlarını getirir
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUserSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.healthyproducts.app.data.model.UserSettings> $completion) {
        return null;
    }
    
    /**
     * Kullanıcı tercihlerini getirir
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUserPreferences(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Kullanıcı ayarlarını oluşturur
     */
    private final java.lang.Object createUserSettings(java.lang.String userId, kotlin.coroutines.Continuation<? super com.healthyproducts.app.data.model.UserSettings> $completion) {
        return null;
    }
    
    /**
     * Kullanıcı tercihlerini günceller
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateUserPreferences(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.model.UserPreferences preferences, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Kullanıcı dilini günceller
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateUserLanguage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Kullanıcının favorilerini getirir
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getFavorites(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * Ürünü favorilere ekler
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addToFavorites(@org.jetbrains.annotations.NotNull()
    java.lang.String productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Ürünü favorilerden çıkarır
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeFromFavorites(@org.jetbrains.annotations.NotNull()
    java.lang.String productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Kullanıcının arama geçmişini getirir
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSearchHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * Aramayı geçmişe ekler
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addToSearchHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String search, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Arama geçmişini temizler
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearSearchHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}