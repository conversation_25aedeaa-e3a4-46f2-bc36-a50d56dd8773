package com.healthyproducts.app.data.repository

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.tasks.await
import java.io.ByteArrayOutputStream
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ImageUploadRepository @Inject constructor(
    private val storage: FirebaseStorage,
    private val context: Context
) {
    companion object {
        private const val TAG = "ImageUploadRepository"
        private const val PRODUCTS_FOLDER = "product_images"
        private const val MAX_IMAGE_SIZE = 1024 * 1024 // 1MB
        private const val COMPRESSION_QUALITY = 80
    }

    /**
     * Resmi Firebase Storage'a yükler
     */
    suspend fun uploadProductImage(
        imageUri: Uri,
        productBarcode: String? = null
    ): Result<String> {
        return try {
            Log.d(TAG, "Starting image upload for URI: $imageUri")

            // Resmi bitmap'e çevir ve sıkıştır
            val compressedImageData = compressImage(imageUri)
            
            // Dosya adı oluştur
            val fileName = generateFileName(productBarcode)
            
            // Firebase Storage referansı
            val imageRef = storage.reference
                .child(PRODUCTS_FOLDER)
                .child(fileName)

            // Resmi yükle
            val uploadTask = imageRef.putBytes(compressedImageData).await()
            
            // Download URL'ini al
            val downloadUrl = imageRef.downloadUrl.await().toString()
            
            Log.d(TAG, "Image uploaded successfully: $downloadUrl")
            Result.success(downloadUrl)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error uploading image", e)
            Result.failure(e)
        }
    }

    /**
     * Bitmap'i Firebase Storage'a yükler
     */
    suspend fun uploadProductImage(
        bitmap: Bitmap,
        productBarcode: String? = null
    ): Result<String> {
        return try {
            Log.d(TAG, "Starting bitmap upload")

            // Bitmap'i sıkıştır
            val compressedImageData = compressBitmap(bitmap)
            
            // Dosya adı oluştur
            val fileName = generateFileName(productBarcode)
            
            // Firebase Storage referansı
            val imageRef = storage.reference
                .child(PRODUCTS_FOLDER)
                .child(fileName)

            // Resmi yükle
            val uploadTask = imageRef.putBytes(compressedImageData).await()
            
            // Download URL'ini al
            val downloadUrl = imageRef.downloadUrl.await().toString()
            
            Log.d(TAG, "Bitmap uploaded successfully: $downloadUrl")
            Result.success(downloadUrl)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error uploading bitmap", e)
            Result.failure(e)
        }
    }

    /**
     * Resmi sıkıştırır
     */
    private fun compressImage(imageUri: Uri): ByteArray {
        val inputStream = context.contentResolver.openInputStream(imageUri)
        val originalBitmap = BitmapFactory.decodeStream(inputStream)
        inputStream?.close()
        
        return compressBitmap(originalBitmap)
    }

    /**
     * Bitmap'i sıkıştırır
     */
    private fun compressBitmap(bitmap: Bitmap): ByteArray {
        val outputStream = ByteArrayOutputStream()
        
        // Resmi yeniden boyutlandır (max 1024x1024)
        val resizedBitmap = resizeBitmap(bitmap, 1024, 1024)
        
        // JPEG formatında sıkıştır
        resizedBitmap.compress(Bitmap.CompressFormat.JPEG, COMPRESSION_QUALITY, outputStream)
        
        val compressedData = outputStream.toByteArray()
        outputStream.close()
        
        // Bitmap'i temizle
        if (resizedBitmap != bitmap) {
            resizedBitmap.recycle()
        }
        
        Log.d(TAG, "Image compressed to ${compressedData.size} bytes")
        return compressedData
    }

    /**
     * Bitmap'i yeniden boyutlandırır
     */
    private fun resizeBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val aspectRatio = width.toFloat() / height.toFloat()
        
        val (newWidth, newHeight) = if (aspectRatio > 1) {
            // Landscape
            maxWidth to (maxWidth / aspectRatio).toInt()
        } else {
            // Portrait
            (maxHeight * aspectRatio).toInt() to maxHeight
        }
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * Dosya adı oluşturur
     */
    private fun generateFileName(productBarcode: String?): String {
        val timestamp = System.currentTimeMillis()
        val uuid = UUID.randomUUID().toString().take(8)
        
        return if (productBarcode != null) {
            "${productBarcode}_${timestamp}_${uuid}.jpg"
        } else {
            "product_${timestamp}_${uuid}.jpg"
        }
    }

    /**
     * Resmi siler
     */
    suspend fun deleteProductImage(imageUrl: String): Result<Unit> {
        return try {
            Log.d(TAG, "Deleting image: $imageUrl")
            
            val imageRef = storage.getReferenceFromUrl(imageUrl)
            imageRef.delete().await()
            
            Log.d(TAG, "Image deleted successfully")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting image", e)
            Result.failure(e)
        }
    }
}
