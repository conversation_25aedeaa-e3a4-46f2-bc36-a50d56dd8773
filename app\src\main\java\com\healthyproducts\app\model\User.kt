package com.healthyproducts.app.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName
import com.google.firebase.firestore.ServerTimestamp
import com.healthyproducts.app.data.model.AiModel
import java.util.Date

/**
 * Kullanıcıyı temsil eden veri sınıfı
 */
data class User(
    @DocumentId
    val id: String = "",
    val email: String = "",
    val name: String? = null,
    val photoUrl: String? = null,
    @ServerTimestamp
    val createdAt: Date = Date(),
    val lastLogin: Date = Date()
) {
    // Firestore için boş constructor
    constructor() : this("", "")
}

/**
 * Kullanıcı tercihlerini temsil eden veri sınıfı
 */
data class UserPreferences(
    @get:PropertyName("show_halal_analysis")
    @set:PropertyName("show_halal_analysis")
    var showHalalAnalysis: Boolean = true,

    @get:PropertyName("show_vegan_analysis")
    @set:PropertyName("show_vegan_analysis")
    var showVeganAnalysis: Boolean = true,

    @get:PropertyName("show_kosher_analysis")
    @set:PropertyName("show_kosher_analysis")
    var showKosherAnalysis: Boolean = false,

    @get:PropertyName("show_harmful_analysis")
    @set:PropertyName("show_harmful_analysis")
    var showHarmfulAnalysis: Boolean = true,

    @get:PropertyName("show_unhealthy_analysis")
    @set:PropertyName("show_unhealthy_analysis")
    var showUnhealthyAnalysis: Boolean = true,

    @get:PropertyName("language")
    @set:PropertyName("language")
    var language: String = "tr", // Varsayılan dil Türkçe

    @get:PropertyName("dark_mode")
    @set:PropertyName("dark_mode")
    var darkMode: Boolean = false,

    @get:PropertyName("ai_model")
    @set:PropertyName("ai_model")
    var aiModel: String = AiModel.GEMINI_LITE.name, // Varsayılan AI modeli Gemini Lite

    @ServerTimestamp
    @get:PropertyName("updated_at")
    @set:PropertyName("updated_at")
    var updatedAt: Date = Date()
) {
    // Firestore için boş constructor
    constructor() : this(true, true, false, true, true, "tr", false, AiModel.GEMINI_LITE.name)
}

/**
 * Kullanıcının favori ürünlerini temsil eden veri sınıfı
 */
data class FavoriteProduct(
    val userId: String,
    val productId: String,
    val addedAt: Date = Date()
)

/**
 * Kullanıcının tarama geçmişini temsil eden veri sınıfı
 */
data class ScanHistory(
    val id: String,
    val userId: String,
    val productId: String? = null,
    val barcode: String? = null,
    val imageUrl: String? = null,
    val scanDate: Date = Date(),
    val scanType: ScanType
)

/**
 * Tarama türünü temsil eden enum sınıfı
 */
enum class ScanType {
    BARCODE,    // Barkod tarama
    OCR,        // İçerik tarama (OCR)
    MANUAL      // Manuel giriş
}
