package com.healthyproducts.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthyproducts.app.data.repository.ScanHistoryRepository
import com.healthyproducts.app.model.ScanType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Tarama geçmişini yöneten ViewModel
 */
@HiltViewModel
class ScanHistoryViewModel @Inject constructor(
    private val scanHistoryRepository: ScanHistoryRepository
) : ViewModel() {

    // Tarama geçmişi durumu
    private val _scanHistoryState = MutableStateFlow<ScanHistoryState>(ScanHistoryState.Loading)
    val scanHistoryState: StateFlow<ScanHistoryState> = _scanHistoryState.asStateFlow()

    /**
     * <PERSON>llanı<PERSON>ının tarama geçmişini getirme
     */
    fun getScanHistory(userId: String, limit: Int = 20) {
        viewModelScope.launch {
            _scanHistoryState.value = ScanHistoryState.Loading

            scanHistoryRepository.getScanHistory(userId, limit)
                .onSuccess { scanHistoryList ->
                    _scanHistoryState.value = if (scanHistoryList.isEmpty()) {
                        ScanHistoryState.Empty
                    } else {
                        ScanHistoryState.Success(scanHistoryList)
                    }
                }
                .onFailure { error ->
                    _scanHistoryState.value = ScanHistoryState.Error(error.message ?: "Tarama geçmişini getirme hatası")
                }
        }
    }

    /**
     * Tarama geçmişine ekleme
     */
    fun addToScanHistory(
        userId: String,
        productId: String? = null,
        barcode: String? = null,
        imageUrl: String? = null,
        scanType: ScanType
    ) {
        viewModelScope.launch {
            scanHistoryRepository.addToScanHistory(userId, productId, barcode, imageUrl, scanType)
                .onSuccess {
                    // Tarama geçmişini yeniden yükle
                    getScanHistory(userId)
                }
        }
    }

    /**
     * Tarama geçmişini temizleme
     */
    fun clearScanHistory(userId: String) {
        viewModelScope.launch {
            _scanHistoryState.value = ScanHistoryState.Loading

            scanHistoryRepository.clearScanHistory(userId)
                .onSuccess {
                    _scanHistoryState.value = ScanHistoryState.Empty
                }
                .onFailure { error ->
                    _scanHistoryState.value = ScanHistoryState.Error(error.message ?: "Tarama geçmişini temizleme hatası")
                }
        }
    }

    /**
     * Tarama geçmişi durumunu temsil eden sealed class
     */
    sealed class ScanHistoryState {
        object Loading : ScanHistoryState()
        object Empty : ScanHistoryState()
        data class Success(val scanHistory: List<ScanHistoryRepository.ScanHistoryWithProduct>) : ScanHistoryState()
        data class Error(val message: String) : ScanHistoryState()
    }
}
