package com.healthyproducts.app.ui.viewmodel

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthyproducts.app.data.api.AiService
import com.healthyproducts.app.data.model.Allergen
import com.healthyproducts.app.data.model.Fat
import com.healthyproducts.app.data.model.FoodCertificate
import com.healthyproducts.app.data.model.FoodPreferenceType
import com.healthyproducts.app.data.model.Preservative
import com.healthyproducts.app.data.model.Sugar
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.model.UserFoodPreference
import com.healthyproducts.app.data.repository.AllergenRepository
import com.healthyproducts.app.data.repository.FatRepository
import com.healthyproducts.app.data.repository.FirestoreRepository
import com.healthyproducts.app.data.repository.FoodAnalysisRepository
import com.healthyproducts.app.data.repository.FoodCertificateRepository
import com.healthyproducts.app.data.repository.PreservativeRepository
import com.healthyproducts.app.data.repository.SugarRepository
import com.healthyproducts.app.data.repository.UserFoodPreferenceRepository
import com.healthyproducts.app.data.service.FoodAnalysisService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Gıda analizi için ViewModel
 */
@HiltViewModel
class FoodAnalysisViewModel @Inject constructor(
    private val foodAnalysisRepository: FoodAnalysisRepository,
    private val allergenRepository: AllergenRepository,
    private val fatRepository: FatRepository,
    private val sugarRepository: SugarRepository,
    private val preservativeRepository: PreservativeRepository,
    private val foodCertificateRepository: FoodCertificateRepository,
    private val userFoodPreferenceRepository: UserFoodPreferenceRepository,
    private val foodAnalysisService: FoodAnalysisService,
    private val aiService: AiService,
    private val firestoreRepository: FirestoreRepository
) : ViewModel() {

    // Alerjenler
    private val _allergensState = MutableStateFlow<AllergensState>(AllergensState.Loading)
    val allergensState: StateFlow<AllergensState> = _allergensState

    // Yağlar
    private val _fatsState = MutableStateFlow<FatsState>(FatsState.Loading)
    val fatsState: StateFlow<FatsState> = _fatsState

    // Şekerler
    private val _sugarsState = MutableStateFlow<SugarsState>(SugarsState.Loading)
    val sugarsState: StateFlow<SugarsState> = _sugarsState

    // Gıda sertifikaları
    private val _certificatesState = MutableStateFlow<CertificatesState>(CertificatesState.Loading)
    val certificatesState: StateFlow<CertificatesState> = _certificatesState

    // Seçilen alerjen
    private val _selectedAllergen = MutableStateFlow<Allergen?>(null)
    val selectedAllergen: StateFlow<Allergen?> = _selectedAllergen

    // Seçilen yağ
    private val _selectedFat = MutableStateFlow<Fat?>(null)
    val selectedFat: StateFlow<Fat?> = _selectedFat

    // Seçilen şeker
    private val _selectedSugar = MutableStateFlow<Sugar?>(null)
    val selectedSugar: StateFlow<Sugar?> = _selectedSugar

    // Seçilen sertifika
    private val _selectedCertificate = MutableStateFlow<FoodCertificate?>(null)
    val selectedCertificate: StateFlow<FoodCertificate?> = _selectedCertificate

    // Koruyucular
    private val _preservativesState = MutableStateFlow<PreservativesState>(PreservativesState.Loading)
    val preservativesState: StateFlow<PreservativesState> = _preservativesState

    // Seçilen koruyucu
    private val _preservativeState = MutableStateFlow<PreservativeState>(PreservativeState.Initial)
    val preservativeState: StateFlow<PreservativeState> = _preservativeState

    // Kullanıcı gıda tercihleri
    private val _userFoodPreferencesState = MutableStateFlow<UserFoodPreferencesState>(UserFoodPreferencesState.Initial)
    val userFoodPreferencesState: StateFlow<UserFoodPreferencesState> = _userFoodPreferencesState

    // Seçilen gıda tercihleri
    private val _selectedFoodPreferences = MutableStateFlow<Map<String, Boolean>>(emptyMap())
    val selectedFoodPreferences: StateFlow<Map<String, Boolean>> = _selectedFoodPreferences

    // Kullanıcı ID'si
    private var currentUserId: String = ""

    // Analiz sonucu
    private val _analysisResult = MutableStateFlow<AnalysisState>(AnalysisState.Initial)
    val analysisResult: StateFlow<AnalysisState> = _analysisResult

    /**
     * Dil değişikliği durumunda analiz cache'ini temizler
     */
    fun clearAnalysisCache() {
        _analysisResult.value = AnalysisState.Initial
        Log.d("FoodAnalysisViewModel", "Analysis cache cleared due to language change")
    }

    // Başlangıçta tüm kullanıcı tercihlerini yükle
    fun initUserPreferences(userId: String) {
        if (userId.isEmpty()) return

        currentUserId = userId

        // Tüm kullanıcı tercihlerini yükle
        viewModelScope.launch {
            Log.d("FoodAnalysisViewModel", "Initializing user preferences for user: $userId")
            loadUserFoodPreferences(userId)
        }
    }

    /**
     * Kullanıcı tercihlerini zorla yeniler
     */
    fun refreshUserPreferences() {
        if (currentUserId.isEmpty()) return

        viewModelScope.launch {
            Log.d("FoodAnalysisViewModel", "Force refreshing user preferences for user: $currentUserId")

            // Firestore'dan kullanıcı tercihlerini zorla yenile
            try {
                firestoreRepository.getUserPreferences(currentUserId)
                Log.d("FoodAnalysisViewModel", "User preferences refreshed from Firestore")
            } catch (e: Exception) {
                Log.e("FoodAnalysisViewModel", "Error refreshing user preferences", e)
            }
        }
    }

    /**
     * İçerikleri analiz eder
     */
    fun analyzeIngredients(ingredients: List<String>, language: SupportedLanguage) {
        viewModelScope.launch {
            try {
                Log.d("FoodAnalysisViewModel", "=== ANALYSIS DEBUG ===")
                Log.d("FoodAnalysisViewModel", "Received language: ${language.code}")
                Log.d("FoodAnalysisViewModel", "Language name: ${language.name}")
                Log.d("FoodAnalysisViewModel", "Ingredients: $ingredients")

                _analysisResult.value = AnalysisState.Loading

                // Kullanıcı tercihlerini yükle
                if (currentUserId.isNotEmpty()) {
                    // Önce kullanıcı tercihlerini yükle ve tamamlanmasını bekle
                    loadUserFoodPreferences(currentUserId)

                    // Yükleme işlemi tamamlanana kadar bekle (kısa bir gecikme)
                    kotlinx.coroutines.delay(500)

                    Log.d("FoodAnalysisViewModel", "User preferences loaded for user: $currentUserId")
                } else {
                    Log.d("FoodAnalysisViewModel", "No user ID available, proceeding without user preferences")
                }

                // Kullanıcı tercihlerini al
                val userPreferences = when (val state = userFoodPreferencesState.value) {
                    is UserFoodPreferencesState.Success -> {
                        Log.d("FoodAnalysisViewModel", "Successfully loaded ${state.preferences.size} user preferences")
                        state.preferences
                    }
                    else -> {
                        Log.d("FoodAnalysisViewModel", "No user preferences available: ${userFoodPreferencesState.value}")
                        emptyList()
                    }
                }

                // Kullanıcının seçtiği alerjenleri, yağları, şekerleri, sertifikaları ve koruyucuları al
                var userAllergens = userPreferences.filter { it.type == FoodPreferenceType.ALLERGEN.value }
                var userFats = userPreferences.filter { it.type == FoodPreferenceType.FAT.value }
                var userSugars = userPreferences.filter { it.type == FoodPreferenceType.SUGAR.value }
                var userCertificates = userPreferences.filter { it.type == FoodPreferenceType.CERTIFICATE.value }
                var userPreservatives = userPreferences.filter { it.type == FoodPreferenceType.PRESERVATIVE.value }

                // Eğer kullanıcı tercihleri boşsa, örnek tercihler ekle
                if (userAllergens.isEmpty() && userFats.isEmpty() && userSugars.isEmpty() &&
                    userCertificates.isEmpty() && userPreservatives.isEmpty()) {

                    Log.d("FoodAnalysisViewModel", "No user preferences found, using sample preferences")

                    // Örnek tercihler
                    userAllergens = listOf(
                        UserFoodPreference(
                            userId = currentUserId,
                            type = FoodPreferenceType.ALLERGEN.value,
                            itemId = "gluten",
                            itemName = if (language == SupportedLanguage.TURKISH) "Gluten" else "Gluten"
                        ),
                        UserFoodPreference(
                            userId = currentUserId,
                            type = FoodPreferenceType.ALLERGEN.value,
                            itemId = "lactose",
                            itemName = if (language == SupportedLanguage.TURKISH) "Laktoz" else "Lactose"
                        )
                    )

                    userFats = listOf(
                        UserFoodPreference(
                            userId = currentUserId,
                            type = FoodPreferenceType.FAT.value,
                            itemId = "palm_oil",
                            itemName = if (language == SupportedLanguage.TURKISH) "Palm Yağı" else "Palm Oil"
                        )
                    )

                    userSugars = listOf(
                        UserFoodPreference(
                            userId = currentUserId,
                            type = FoodPreferenceType.SUGAR.value,
                            itemId = "high_fructose_corn_syrup",
                            itemName = if (language == SupportedLanguage.TURKISH) "Yüksek Fruktozlu Mısır Şurubu" else "High Fructose Corn Syrup"
                        )
                    )

                    userPreservatives = listOf(
                        UserFoodPreference(
                            userId = currentUserId,
                            type = FoodPreferenceType.PRESERVATIVE.value,
                            itemId = "E211",
                            itemName = if (language == SupportedLanguage.TURKISH) "Sodyum Benzoat (E211)" else "Sodium Benzoate (E211)"
                        )
                    )
                }

                Log.d("FoodAnalysisViewModel", "User preferences breakdown: " +
                        "Allergens: ${userAllergens.size}, " +
                        "Fats: ${userFats.size}, " +
                        "Sugars: ${userSugars.size}, " +
                        "Certificates: ${userCertificates.size}, " +
                        "Preservatives: ${userPreservatives.size}")

                // Gemini API ile analiz yap
                val prompt = buildAnalysisPrompt(ingredients, language, userAllergens, userFats, userSugars, userCertificates, userPreservatives)

                Log.d("FoodAnalysisViewModel", "Language parameter: $language (code: ${language.code})")
                Log.d("FoodAnalysisViewModel", "Prompt preview: ${prompt.take(200)}...")

                val result = aiService.generateText(prompt, language.code)

                // Analiz sonucunu güncelle
                _analysisResult.value = AnalysisState.Success(result)

                Log.d("FoodAnalysisViewModel", "Analysis completed successfully")
            } catch (e: Exception) {
                _analysisResult.value = AnalysisState.Error(e.message ?: "Unknown error")
                Log.e("FoodAnalysisViewModel", "Error analyzing ingredients", e)
            }
        }
    }

    /**
     * Analiz için prompt oluşturur
     */
    private fun buildAnalysisPrompt(
        ingredients: List<String>,
        language: SupportedLanguage,
        userAllergens: List<UserFoodPreference>,
        userFats: List<UserFoodPreference>,
        userSugars: List<UserFoodPreference>,
        userCertificates: List<UserFoodPreference>,
        userPreservatives: List<UserFoodPreference>
    ): String {
        val ingredientsText = ingredients.joinToString(", ")

        return when (language) {
            SupportedLanguage.TURKISH -> {
                """
                Bu bir gıda ürününün içerik listesidir. İçerikleri analiz et ve aşağıdaki bilgileri Türkçe olarak sağla.

                **1. Potansiyel Zararlı Katkı Maddeleri:**
                (E kodları dahil, varsa listele - Türkçe açıklama)

                **2. Alerjenik Maddeler:**
                (Tespit edilen alerjenler - Türkçe)

                **3. Şeker ve Yapay Tatlandırıcılar:**
                (Şeker türleri ve tatlandırıcılar - Türkçe)

                **4. Koruyucular ve Renklendiriciler:**
                (Koruyucu maddeler - Türkçe)

                **5. Genel Sağlık Değerlendirmesi:**
                Sağlık puanı: X/10 (1-10 arası puan ver)
                (Türkçe açıklama)

                **Özet:**
                (Kısa Türkçe özet)

                Kullanıcının özel tercihleri:
                - Alerjenler: ${if (userAllergens.isEmpty()) "Belirtilmemiş" else userAllergens.joinToString(", ") { it.itemName }}
                - Yağlar: ${if (userFats.isEmpty()) "Belirtilmemiş" else userFats.joinToString(", ") { it.itemName }}
                - Şekerler: ${if (userSugars.isEmpty()) "Belirtilmemiş" else userSugars.joinToString(", ") { it.itemName }}
                - Sertifikalar: ${if (userCertificates.isEmpty()) "Belirtilmemiş" else userCertificates.joinToString(", ") { it.itemName }}
                - Koruyucular: ${if (userPreservatives.isEmpty()) "Belirtilmemiş" else userPreservatives.joinToString(", ") { it.itemName }}

                Kullanıcının tercihlerine göre özel uyarılar ekle (Türkçe).

                İçerikler: $ingredientsText
                """
            }
            SupportedLanguage.ENGLISH -> {
                """
                This is an ingredient list of a food product. Analyze the ingredients and provide the following information in English.

                **1. Potentially harmful additives:**
                (Including E codes, list if any - English explanation)

                **2. Allergenic substances:**
                (Detected allergens - English)

                **3. Sugar and artificial sweeteners:**
                (Sugar types and sweeteners - English)

                **4. Preservatives and colorants:**
                (Preservative substances - English)

                **5. General health assessment:**
                Health score: X/10 (provide a score from 1-10)
                (English explanation)

                **Summary:**
                (Brief English summary)

                User preferences:
                - Allergens: ${if (userAllergens.isEmpty()) "Not specified" else userAllergens.joinToString(", ") { it.itemName }}
                - Fats: ${if (userFats.isEmpty()) "Not specified" else userFats.joinToString(", ") { it.itemName }}
                - Sugars: ${if (userSugars.isEmpty()) "Not specified" else userSugars.joinToString(", ") { it.itemName }}
                - Certificates: ${if (userCertificates.isEmpty()) "Not specified" else userCertificates.joinToString(", ") { it.itemName }}
                - Preservatives: ${if (userPreservatives.isEmpty()) "Not specified" else userPreservatives.joinToString(", ") { it.itemName }}

                Add specific warnings based on user preferences (in English).

                Ingredients: $ingredientsText
                """
            }
        }
    }

    /**
     * Tüm alerjenleri yükler
     */
    fun loadAllergens() {
        viewModelScope.launch {
            _allergensState.value = AllergensState.Loading

            foodAnalysisRepository.getAllAllergens()
                .onSuccess { allergens ->
                    _allergensState.value = AllergensState.Success(allergens)
                }
                .onFailure { error ->
                    _allergensState.value = AllergensState.Error(error.message ?: "Unknown error")
                }
        }
    }

    /**
     * Belirli bir alerjeni yükler
     */
    fun loadAllergenByCode(code: String) {
        viewModelScope.launch {
            foodAnalysisRepository.getAllergenByCode(code)
                .onSuccess { allergen ->
                    _selectedAllergen.value = allergen
                }
                .onFailure { error ->
                    Log.e("FoodAnalysisViewModel", "Error loading allergen: $code", error)
                }
        }
    }

    /**
     * Tüm yağları yükler
     */
    fun loadFats() {
        viewModelScope.launch {
            _fatsState.value = FatsState.Loading

            foodAnalysisRepository.getAllFats()
                .onSuccess { fats ->
                    _fatsState.value = FatsState.Success(fats)
                }
                .onFailure { error ->
                    _fatsState.value = FatsState.Error(error.message ?: "Unknown error")
                }
        }
    }

    /**
     * Belirli bir yağı yükler
     */
    fun loadFatByCode(code: String) {
        viewModelScope.launch {
            foodAnalysisRepository.getFatByCode(code)
                .onSuccess { fat ->
                    _selectedFat.value = fat
                }
                .onFailure { error ->
                    Log.e("FoodAnalysisViewModel", "Error loading fat: $code", error)
                }
        }
    }

    /**
     * Tüm şekerleri yükler
     */
    fun loadSugars() {
        viewModelScope.launch {
            _sugarsState.value = SugarsState.Loading

            foodAnalysisRepository.getAllSugars()
                .onSuccess { sugars ->
                    _sugarsState.value = SugarsState.Success(sugars)
                }
                .onFailure { error ->
                    _sugarsState.value = SugarsState.Error(error.message ?: "Unknown error")
                }
        }
    }

    /**
     * Belirli bir şekeri yükler
     */
    fun loadSugarByCode(code: String) {
        viewModelScope.launch {
            foodAnalysisRepository.getSugarByCode(code)
                .onSuccess { sugar ->
                    _selectedSugar.value = sugar
                }
                .onFailure { error ->
                    Log.e("FoodAnalysisViewModel", "Error loading sugar: $code", error)
                }
        }
    }

    /**
     * Tüm gıda sertifikalarını yükler
     */
    fun loadFoodCertificates() {
        viewModelScope.launch {
            _certificatesState.value = CertificatesState.Loading

            // FoodCertificateRepository'den sertifikaları yükle
            foodCertificateRepository.getAllCertificates()
                .onSuccess { certificates ->
                    _certificatesState.value = CertificatesState.Success(certificates)
                    Log.d("FoodAnalysisViewModel", "Loaded ${certificates.size} certificates from FoodCertificateRepository")
                }
                .onFailure { error ->
                    _certificatesState.value = CertificatesState.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error loading certificates from FoodCertificateRepository", error)
                }
        }
    }

    /**
     * Belirli bir gıda sertifikasını yükler
     */
    fun loadFoodCertificateByCode(code: String) {
        viewModelScope.launch {
            // FoodCertificateRepository'den sertifikayı yükle
            foodCertificateRepository.getCertificateById(code)
                .onSuccess { certificate ->
                    _selectedCertificate.value = certificate
                    Log.d("FoodAnalysisViewModel", "Loaded certificate: $code")
                }
                .onFailure { error ->
                    Log.e("FoodAnalysisViewModel", "Error loading food certificate: $code", error)
                }
        }
    }

    /**
     * Seçilen alerjeni temizler
     */
    fun clearSelectedAllergen() {
        _selectedAllergen.value = null
    }

    /**
     * Seçilen yağı temizler
     */
    fun clearSelectedFat() {
        _selectedFat.value = null
    }

    /**
     * Seçilen şekeri temizler
     */
    fun clearSelectedSugar() {
        _selectedSugar.value = null
    }

    /**
     * Seçilen sertifikayı temizler
     */
    fun clearSelectedCertificate() {
        _selectedCertificate.value = null
    }

    // İçeri aktarma durumu
    private val _importStatus = MutableStateFlow<ImportStatus>(ImportStatus.Initial)
    val importStatus: StateFlow<ImportStatus> = _importStatus

    /**
     * JSON dosyasından alerjenleri içeri aktarır
     */
    fun importAllergensFromJson(context: Context) {
        viewModelScope.launch {
            _importStatus.value = ImportStatus.Loading

            allergenRepository.importAllergensFromJson(context)
                .onSuccess { count ->
                    _importStatus.value = ImportStatus.Success(count)
                    Log.d("FoodAnalysisViewModel", "Successfully imported $count allergens")
                    loadAllergens() // Listeyi yenile
                }
                .onFailure { error ->
                    _importStatus.value = ImportStatus.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error importing allergens", error)
                }
        }
    }

    /**
     * İçeri aktarma durumunu sıfırlar
     */
    fun resetImportStatus() {
        _importStatus.value = ImportStatus.Initial
    }

    /**
     * JSON dosyasından yağları içe aktarır
     */
    fun importFatsFromJson(context: Context) {
        viewModelScope.launch {
            _importStatus.value = ImportStatus.Loading

            fatRepository.importFatsFromJson(context)
                .onSuccess { count ->
                    _importStatus.value = ImportStatus.Success(count)
                    Log.d("FoodAnalysisViewModel", "Successfully imported $count fats")
                    loadFats() // Listeyi yenile
                }
                .onFailure { error ->
                    _importStatus.value = ImportStatus.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error importing fats", error)
                }
        }
    }

    /**
     * JSON dosyasından şekerleri içe aktarır
     */
    fun importSugarsFromJson(context: Context) {
        viewModelScope.launch {
            _importStatus.value = ImportStatus.Loading

            sugarRepository.importSugarsFromJson(context)
                .onSuccess { count ->
                    _importStatus.value = ImportStatus.Success(count)
                    Log.d("FoodAnalysisViewModel", "Successfully imported $count sugars")
                    loadSugars() // Listeyi yenile
                }
                .onFailure { error ->
                    _importStatus.value = ImportStatus.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error importing sugars", error)
                }
        }
    }

    /**
     * Tüm koruyucuları yükler
     */
    fun loadPreservatives() {
        viewModelScope.launch {
            _preservativesState.value = PreservativesState.Loading

            try {
                // Önce veritabanından koruyucuları getir
                preservativeRepository.getAllPreservatives()
                    .onSuccess { preservatives ->
                        Log.d("FoodAnalysisViewModel", "Loaded ${preservatives.size} preservatives")
                        _preservativesState.value = PreservativesState.Success(preservatives)
                    }
                    .onFailure { error ->
                        Log.e("FoodAnalysisViewModel", "Error loading preservatives", error)
                        _preservativesState.value = PreservativesState.Error(error.message ?: "Unknown error")
                    }
            } catch (e: Exception) {
                Log.e("FoodAnalysisViewModel", "Exception loading preservatives", e)
                _preservativesState.value = PreservativesState.Error(e.message ?: "Unknown error")
            }
        }
    }

    /**
     * Belirli bir koruyucuyu yükler
     */
    fun loadPreservative(preservativeId: String) {
        viewModelScope.launch {
            _preservativeState.value = PreservativeState.Loading

            preservativeRepository.getPreservativeById(preservativeId)
                .onSuccess { preservative ->
                    _preservativeState.value = PreservativeState.Success(preservative)
                }
                .onFailure { error ->
                    _preservativeState.value = PreservativeState.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error loading preservative: $preservativeId", error)
                }
        }
    }

    /**
     * Seçilen koruyucuyu temizler
     */
    fun clearPreservativeState() {
        _preservativeState.value = PreservativeState.Initial
    }

    /**
     * JSON dosyasından koruyucuları içe aktarır
     */
    fun importPreservativesFromJson() {
        viewModelScope.launch {
            _importStatus.value = ImportStatus.Loading

            preservativeRepository.importPreservativesFromJson()
                .onSuccess { count ->
                    _importStatus.value = ImportStatus.Success(count)
                    Log.d("FoodAnalysisViewModel", "Successfully imported $count preservatives")
                    loadPreservatives() // Listeyi yenile
                }
                .onFailure { error ->
                    _importStatus.value = ImportStatus.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error importing preservatives", error)
                }
        }
    }

    /**
     * JSON dosyasından gıda sertifikalarını içe aktarır
     */
    fun importCertificatesFromJson(context: Context) {
        viewModelScope.launch {
            _importStatus.value = ImportStatus.Loading

            try {
                // FoodCertificateRepository'ye context'i iletmek için yeni bir instance oluştur
                val tempRepository = FoodCertificateRepository(
                    firestore = foodCertificateRepository.getFirestore(),
                    context = context
                )

                tempRepository.importCertificatesFromJson()
                    .onSuccess { count ->
                        _importStatus.value = ImportStatus.Success(count)
                        Log.d("FoodAnalysisViewModel", "Successfully imported $count certificates")
                        loadFoodCertificates() // Listeyi yenile
                    }
                    .onFailure { error ->
                        _importStatus.value = ImportStatus.Error(error.message ?: "Unknown error")
                        Log.e("FoodAnalysisViewModel", "Error importing certificates", error)
                    }
            } catch (e: Exception) {
                _importStatus.value = ImportStatus.Error(e.message ?: "Unknown error")
                Log.e("FoodAnalysisViewModel", "Exception importing certificates", e)
            }
        }
    }

    /**
     * Kullanıcının gıda tercihlerini yükler
     */
    fun loadUserFoodPreferences(userId: String) {
        viewModelScope.launch {
            _userFoodPreferencesState.value = UserFoodPreferencesState.Loading

            userFoodPreferenceRepository.getUserFoodPreferences(userId)
                .onSuccess { preferences ->
                    _userFoodPreferencesState.value = UserFoodPreferencesState.Success(preferences)

                    // Seçilen tercihleri güncelle
                    val selectedMap = preferences.associate { it.itemId to true }
                    _selectedFoodPreferences.value = selectedMap

                    Log.d("FoodAnalysisViewModel", "Loaded ${preferences.size} food preferences for user: $userId")
                }
                .onFailure { error ->
                    _userFoodPreferencesState.value = UserFoodPreferencesState.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error loading food preferences", error)
                }
        }
    }

    /**
     * Kullanıcının belirli türdeki gıda tercihlerini yükler
     */
    fun loadUserFoodPreferencesByType(userId: String, type: FoodPreferenceType) {
        viewModelScope.launch {
            _userFoodPreferencesState.value = UserFoodPreferencesState.Loading

            userFoodPreferenceRepository.getUserFoodPreferencesByType(userId, type)
                .onSuccess { preferences ->
                    _userFoodPreferencesState.value = UserFoodPreferencesState.Success(preferences)

                    // Seçilen tercihleri güncelle
                    val selectedMap = preferences.associate { it.itemId to true }
                    _selectedFoodPreferences.value = selectedMap

                    Log.d("FoodAnalysisViewModel", "Loaded ${preferences.size} ${type.value} preferences for user: $userId")
                }
                .onFailure { error ->
                    _userFoodPreferencesState.value = UserFoodPreferencesState.Error(error.message ?: "Unknown error")
                    Log.e("FoodAnalysisViewModel", "Error loading ${type.value} preferences", error)
                }
        }
    }

    /**
     * Kullanıcının gıda tercihini kaydeder
     */
    fun saveUserFoodPreference(userId: String, type: FoodPreferenceType, itemId: String, itemName: String) {
        viewModelScope.launch {
            val preference = UserFoodPreference(
                userId = userId,
                type = type.value,
                itemId = itemId,
                itemName = itemName
            )

            userFoodPreferenceRepository.saveUserFoodPreference(preference)
                .onSuccess { savedPreference ->
                    Log.d("FoodAnalysisViewModel", "Saved food preference: $savedPreference")

                    // Tercihleri yeniden yükle
                    loadUserFoodPreferencesByType(userId, type)
                }
                .onFailure { error ->
                    Log.e("FoodAnalysisViewModel", "Error saving food preference", error)
                }
        }
    }

    /**
     * Kullanıcının gıda tercihini siler
     */
    fun deleteUserFoodPreference(preferenceId: String, userId: String, type: FoodPreferenceType) {
        viewModelScope.launch {
            userFoodPreferenceRepository.deleteUserFoodPreference(preferenceId, userId)
                .onSuccess {
                    Log.d("FoodAnalysisViewModel", "Deleted food preference: $preferenceId")

                    // Tercihleri yeniden yükle
                    loadUserFoodPreferencesByType(userId, type)
                }
                .onFailure { error ->
                    Log.e("FoodAnalysisViewModel", "Error deleting food preference", error)
                }
        }
    }

    /**
     * Kullanıcının özel gıda tercihini ekler
     */
    fun addCustomFoodPreference(userId: String, type: FoodPreferenceType, itemName: String) {
        viewModelScope.launch {
            userFoodPreferenceRepository.addCustomFoodPreference(userId, type, itemName)
                .onSuccess { preference ->
                    Log.d("FoodAnalysisViewModel", "Added custom food preference: $preference")

                    // Tercihleri yeniden yükle
                    loadUserFoodPreferencesByType(userId, type)
                }
                .onFailure { error ->
                    Log.e("FoodAnalysisViewModel", "Error adding custom food preference", error)
                }
        }
    }

    /**
     * Seçilen gıda tercihlerini günceller
     */
    fun updateSelectedFoodPreference(itemId: String, isSelected: Boolean) {
        val currentSelections = _selectedFoodPreferences.value.toMutableMap()
        currentSelections[itemId] = isSelected
        _selectedFoodPreferences.value = currentSelections
    }
}

/**
 * Alerjenler durumu
 */
sealed class AllergensState {
    object Loading : AllergensState()
    data class Success(val allergens: List<Allergen>) : AllergensState()
    data class Error(val message: String) : AllergensState()
}

/**
 * Yağlar durumu
 */
sealed class FatsState {
    object Loading : FatsState()
    data class Success(val fats: List<Fat>) : FatsState()
    data class Error(val message: String) : FatsState()
}

/**
 * Şekerler durumu
 */
sealed class SugarsState {
    object Loading : SugarsState()
    data class Success(val sugars: List<Sugar>) : SugarsState()
    data class Error(val message: String) : SugarsState()
}

/**
 * Gıda sertifikaları durumu
 */
sealed class CertificatesState {
    object Loading : CertificatesState()
    data class Success(val certificates: List<FoodCertificate>) : CertificatesState()
    data class Error(val message: String) : CertificatesState()
}

/**
 * İçeri aktarma durumu
 */
sealed class ImportStatus {
    object Initial : ImportStatus()
    object Loading : ImportStatus()
    data class Success(val count: Int) : ImportStatus()
    data class Error(val message: String) : ImportStatus()
}

/**
 * Analiz durumu
 */
sealed class AnalysisState {
    object Initial : AnalysisState()
    object Loading : AnalysisState()
    data class Success(val result: String) : AnalysisState()
    data class Error(val message: String) : AnalysisState()
}

/**
 * Koruyucular durumu
 */
sealed class PreservativesState {
    object Loading : PreservativesState()
    data class Success(val preservatives: List<Preservative>) : PreservativesState()
    data class Error(val message: String) : PreservativesState()
}

/**
 * Koruyucu durumu
 */
sealed class PreservativeState {
    object Initial : PreservativeState()
    object Loading : PreservativeState()
    data class Success(val preservative: Preservative) : PreservativeState()
    data class Error(val message: String) : PreservativeState()
}

/**
 * Kullanıcı gıda tercihleri durumu
 */
sealed class UserFoodPreferencesState {
    object Initial : UserFoodPreferencesState()
    object Loading : UserFoodPreferencesState()
    data class Success(val preferences: List<UserFoodPreference>) : UserFoodPreferencesState()
    data class Error(val message: String) : UserFoodPreferencesState()
}
