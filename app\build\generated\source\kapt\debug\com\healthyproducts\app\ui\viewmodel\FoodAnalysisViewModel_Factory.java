package com.healthyproducts.app.ui.viewmodel;

import com.healthyproducts.app.data.api.AiService;
import com.healthyproducts.app.data.repository.AllergenRepository;
import com.healthyproducts.app.data.repository.FatRepository;
import com.healthyproducts.app.data.repository.FoodAnalysisRepository;
import com.healthyproducts.app.data.repository.FoodCertificateRepository;
import com.healthyproducts.app.data.repository.PreservativeRepository;
import com.healthyproducts.app.data.repository.SugarRepository;
import com.healthyproducts.app.data.repository.UserFoodPreferenceRepository;
import com.healthyproducts.app.data.service.FoodAnalysisService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FoodAnalysisViewModel_Factory implements Factory<FoodAnalysisViewModel> {
  private final Provider<FoodAnalysisRepository> foodAnalysisRepositoryProvider;

  private final Provider<AllergenRepository> allergenRepositoryProvider;

  private final Provider<FatRepository> fatRepositoryProvider;

  private final Provider<SugarRepository> sugarRepositoryProvider;

  private final Provider<PreservativeRepository> preservativeRepositoryProvider;

  private final Provider<FoodCertificateRepository> foodCertificateRepositoryProvider;

  private final Provider<UserFoodPreferenceRepository> userFoodPreferenceRepositoryProvider;

  private final Provider<FoodAnalysisService> foodAnalysisServiceProvider;

  private final Provider<AiService> aiServiceProvider;

  public FoodAnalysisViewModel_Factory(
      Provider<FoodAnalysisRepository> foodAnalysisRepositoryProvider,
      Provider<AllergenRepository> allergenRepositoryProvider,
      Provider<FatRepository> fatRepositoryProvider,
      Provider<SugarRepository> sugarRepositoryProvider,
      Provider<PreservativeRepository> preservativeRepositoryProvider,
      Provider<FoodCertificateRepository> foodCertificateRepositoryProvider,
      Provider<UserFoodPreferenceRepository> userFoodPreferenceRepositoryProvider,
      Provider<FoodAnalysisService> foodAnalysisServiceProvider,
      Provider<AiService> aiServiceProvider) {
    this.foodAnalysisRepositoryProvider = foodAnalysisRepositoryProvider;
    this.allergenRepositoryProvider = allergenRepositoryProvider;
    this.fatRepositoryProvider = fatRepositoryProvider;
    this.sugarRepositoryProvider = sugarRepositoryProvider;
    this.preservativeRepositoryProvider = preservativeRepositoryProvider;
    this.foodCertificateRepositoryProvider = foodCertificateRepositoryProvider;
    this.userFoodPreferenceRepositoryProvider = userFoodPreferenceRepositoryProvider;
    this.foodAnalysisServiceProvider = foodAnalysisServiceProvider;
    this.aiServiceProvider = aiServiceProvider;
  }

  @Override
  public FoodAnalysisViewModel get() {
    return newInstance(foodAnalysisRepositoryProvider.get(), allergenRepositoryProvider.get(), fatRepositoryProvider.get(), sugarRepositoryProvider.get(), preservativeRepositoryProvider.get(), foodCertificateRepositoryProvider.get(), userFoodPreferenceRepositoryProvider.get(), foodAnalysisServiceProvider.get(), aiServiceProvider.get());
  }

  public static FoodAnalysisViewModel_Factory create(
      Provider<FoodAnalysisRepository> foodAnalysisRepositoryProvider,
      Provider<AllergenRepository> allergenRepositoryProvider,
      Provider<FatRepository> fatRepositoryProvider,
      Provider<SugarRepository> sugarRepositoryProvider,
      Provider<PreservativeRepository> preservativeRepositoryProvider,
      Provider<FoodCertificateRepository> foodCertificateRepositoryProvider,
      Provider<UserFoodPreferenceRepository> userFoodPreferenceRepositoryProvider,
      Provider<FoodAnalysisService> foodAnalysisServiceProvider,
      Provider<AiService> aiServiceProvider) {
    return new FoodAnalysisViewModel_Factory(foodAnalysisRepositoryProvider, allergenRepositoryProvider, fatRepositoryProvider, sugarRepositoryProvider, preservativeRepositoryProvider, foodCertificateRepositoryProvider, userFoodPreferenceRepositoryProvider, foodAnalysisServiceProvider, aiServiceProvider);
  }

  public static FoodAnalysisViewModel newInstance(FoodAnalysisRepository foodAnalysisRepository,
      AllergenRepository allergenRepository, FatRepository fatRepository,
      SugarRepository sugarRepository, PreservativeRepository preservativeRepository,
      FoodCertificateRepository foodCertificateRepository,
      UserFoodPreferenceRepository userFoodPreferenceRepository,
      FoodAnalysisService foodAnalysisService, AiService aiService) {
    return new FoodAnalysisViewModel(foodAnalysisRepository, allergenRepository, fatRepository, sugarRepository, preservativeRepository, foodCertificateRepository, userFoodPreferenceRepository, foodAnalysisService, aiService);
  }
}
