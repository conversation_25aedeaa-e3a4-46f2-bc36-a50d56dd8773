-- Merging decision tree log ---
manifest
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:2:1-57:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16fb963deb2021601edde78d41f5400d\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50c196bbc2e05ccab95ad36f80b43ae1\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b341f2bf9e26a9b9b6d9ac1cb5423684\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccfdf8fe6da9c4c34a4246aecbf96939\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdadd99ea4df4fc28a5f59f01e8e8195\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39045c8382443acd53f2bb8e054c3072\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba0672690dadfeabecd8225a6e5c2fec\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce7eb6ad019e4cf3abb1b411047242d7\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4223b329d8e0940d0002baaa9778a0ca\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9aeccd7e0ad8e98923275d3aaa5f52c\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19af07a6e550b0e28b93e92c57a3e5b1\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c308ad6a5cb3ffbae3557cea5538ff\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82a7a8f16b09c6b5659ed497e86f10d2\transformed\facebook-login-16.3.0\AndroidManifest.xml:9:1-19:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97ca12025ade2a2e5a767e7f40a3427d\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8137812cafa923ca51c2d126bd643fd\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6de32b01160391fe70f1240c4fa7c52a\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b616a44a83a35cc1ff94b844e1245adb\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25ad31b9d5ee235be384e44c485c1169\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:9:1-44:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aeba2917d45d1138de91ba9d1c8482e\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\559848d5dcc2d7c58713b19c26063072\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a42896bc1e72ea84c886f88868497142\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b92c5d39b597b8c399669551ca8aee\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6dc56213ebf330d331bbdc21265ee404\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8103600f204b96b181591e6ff975208\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99b293c60277545d5195272d2b459ddb\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d09099af744d27cfe09670d8b767e9c6\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd362f2df44c951726e528b041211eb2\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5f795537a8dcc7b0b5116083c56b3c7\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3097c4a004bbcebf7f03b7ea0110fff1\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d90779d7e42546f5e3492386947a1a5\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b73dc7478166cf12501eaa2923e89e\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f2dea34a0758b8b70a49614165144e\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41bd015f1a0f3c0ceebc8b89d09c39c6\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5092f4b0fc3229c87d6dc9ffd8fb80cd\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e77ac53be1c88383435fc57361156f4f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db8b2510384421d6d2bf67400bb3c749\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3f5a3660dc1a556892245eec6e80f46\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.mlkit:text-recognition:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75f9f01176deb15c0c8a84a8dd58da7b\transformed\text-recognition-16.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf33f2ad9ba20b60320925da1b8b29a8\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663eef6da17a713d98a45ad1fec7189c\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:text-recognition-bundled-common:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1844dcc7fcb06b70b56c323862223e2e\transformed\text-recognition-bundled-common-16.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad856e8e03e607399efae39c1221f71\transformed\play-services-mlkit-text-recognition-19.0.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7980129e1d6a9872b92ccffd3c45c1d\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5698eb67ec86681add89ca7336c98933\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5228474c9522c3bcc7499f70e108a19a\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ed94a9d664deacb7b095dcd9597a23\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ffd94ebd0fa9c9f2c3068d95f462416\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56f85aacf2857cdd00ddb119ddc440ac\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e85093a43bdfab26f6ccfa4cf87a66b\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a84695ef720a11c72aa363ddaae8b2e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300207ea80a4ccebe859fdac188f18c2\transformed\integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0abc1d944865e8ba6d2158fa577c48ef\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fe9b91b5586684f355e85aff8478818\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc2ccaa5efe1d5f386941cff3c3b5a4\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbbfa2073d4844da62c658c7c0d446fc\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe86126e590404f02831aec66bba2b20\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5411fb94070b9800635197c57c0f45ca\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cac439c8d2e2410178107b27a320bddc\transformed\generativeai-0.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba0e7b07a6d57e5e696b1eb2c3d13e7a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\764925be0a1268dceac7d457645955a4\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09dd1fb389472bed2b265066a833b2e8\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74dc61ac117c7bb37bdeab07b4ac29d0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a3c09467404fc49c7a66299a61cc2ec\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b0b167eaae9071f6eb78a60488f486\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb0faeb8e6beb83b76de450cfc560ab5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80f011d44b6485e2d9a63be0a2243946\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\493176613545dbc276f1afc7ab477348\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\981fd9293439b18a98e73358fb60a309\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eee114be36247d821703c54d772da235\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5fafc9901fe45bafab0af5eded1f721\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:9:1-50:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a655eac010c678b12797f8587be822d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76cea9b58239e19066a1772a7b239d33\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35c1aaccc4e0fdcb48773f1a8da50017\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167b5a10bb8d261a1d1c21747e97c5f2\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.android:facebook-bolts:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85241934921d9b47a8e2868f80604185\transformed\facebook-bolts-16.3.0\AndroidManifest.xml:9:1-19:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1bf99d2dddf6b3f731ce95e33fee49d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\499e93478f158f780b1830007568320c\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\783a2660b78f5db206eeac37e178f876\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6357c5db75b01221998099e3eb60d2bb\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93292ff7ca3ef385afc1297b2114d4c2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb05631900f84699a8bbb98032bf19d9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e520bbb7f153fcbb4e273d864eff355d\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea6020f2051ec50ecb0341f4ec224677\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14d03a95cc07898beae69a352dd60254\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eecf4e626fa17ec61ff48a21cf7ed910\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e09f9a8382cc7143ec1f2ed853cb949\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c36e1f051d57c59ccd6930c68000c21a\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ced8cc4e01061a0a7b865aa983665e4\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d383c8be3a1db483e9781ee5cf6d33c5\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\186cb02f0d71c17c1d537cdcb9922426\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86f59290e78bfc61b73aeeddcc751eda\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bb7c106ac39c0fde7aa1dd2e60c49de\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7376d3aae2927377aa61cfcdbe97c72d\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911aaa688b927b4d982590c41a85269\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ccc5722ca8455978d22b35e0684d747\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1099f3717c26695b5d285e2feff633fd\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b770141287ee7a3ad831613eb5366af\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa685b06b8478af99f535c1a0c3e2ae9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d26682bba9451dfa3cdc898df86373\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe1178617600db5fd0c80d7e37655d6c\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\856e9279244c37324ec12dc8b216b897\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\750cc87faf3f4c8dad220e0dd1723880\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e95091df6e04deac49b4f699b9aac04\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52458aa1e53c5234a8fbee2df20c8584\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c768a4caede6c4e85aa2745fe410ab6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8af8ac250606821b6be2458d68775622\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a674936fcbcc581ac63e1ab9ac183a28\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18896879e787fe333aca7e8fa8255b93\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8db584130df8174d2cb328141cb1ed0d\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cda5a1f9d8c110805098b8b6936f0e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78d6b23a4d59efa0c49ea17b57b3d0e\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e017041259519e45b4a5382f4ee20bc7\transformed\transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da7d99c88f6dbf06466ffc19eee2fc2\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0632335579b593b6b56131d5dd003758\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53964cca1f6d4484e00b6a471daaa3bb\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98cfce57cce805619f69fe73b0445aa4\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f545b31aa899495d489745f5e19f96f\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9d81a924b8851aca03208f829a558d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8505cb9b578c45e01d6047f2dc8b89f\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9485fc988c8f174474128816a08982b\transformed\grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1548d8c3df025ede4d223e7fa23a4d9a\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\082c637994de0cd7e9e40c8b1e416041\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\345431c4f0ac1cdf5a0bcbfe664e1031\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36e27a0f0a5cceebbea3af9c49242f2e\transformed\installreferrer-1.0\AndroidManifest.xml:2:1-13:12
	package
		INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.INTERNET
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cac439c8d2e2410178107b27a320bddc\transformed\generativeai-0.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cac439c8d2e2410178107b27a320bddc\transformed\generativeai-0.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:5-76
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:22-73
uses-feature#android.hardware.camera
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:17:5-19:36
	android:required
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:19:9-33
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:18:9-47
application
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:21:5-55:19
INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:21:5-55:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19af07a6e550b0e28b93e92c57a3e5b1\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19af07a6e550b0e28b93e92c57a3e5b1\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82a7a8f16b09c6b5659ed497e86f10d2\transformed\facebook-login-16.3.0\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82a7a8f16b09c6b5659ed497e86f10d2\transformed\facebook-login-16.3.0\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:21:5-42:19
MERGED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:21:5-42:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5228474c9522c3bcc7499f70e108a19a\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5228474c9522c3bcc7499f70e108a19a\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ffd94ebd0fa9c9f2c3068d95f462416\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ffd94ebd0fa9c9f2c3068d95f462416\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e85093a43bdfab26f6ccfa4cf87a66b\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e85093a43bdfab26f6ccfa4cf87a66b\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a84695ef720a11c72aa363ddaae8b2e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a84695ef720a11c72aa363ddaae8b2e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300207ea80a4ccebe859fdac188f18c2\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300207ea80a4ccebe859fdac188f18c2\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0abc1d944865e8ba6d2158fa577c48ef\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0abc1d944865e8ba6d2158fa577c48ef\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc2ccaa5efe1d5f386941cff3c3b5a4\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc2ccaa5efe1d5f386941cff3c3b5a4\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbbfa2073d4844da62c658c7c0d446fc\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbbfa2073d4844da62c658c7c0d446fc\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe86126e590404f02831aec66bba2b20\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe86126e590404f02831aec66bba2b20\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5411fb94070b9800635197c57c0f45ca\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5411fb94070b9800635197c57c0f45ca\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:18:5-48:19
MERGED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:18:5-48:19
MERGED from [com.facebook.android:facebook-bolts:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85241934921d9b47a8e2868f80604185\transformed\facebook-bolts-16.3.0\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-bolts:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85241934921d9b47a8e2868f80604185\transformed\facebook-bolts-16.3.0\AndroidManifest.xml:16:5-17:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911aaa688b927b4d982590c41a85269\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911aaa688b927b4d982590c41a85269\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8af8ac250606821b6be2458d68775622\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8af8ac250606821b6be2458d68775622\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cda5a1f9d8c110805098b8b6936f0e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cda5a1f9d8c110805098b8b6936f0e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\345431c4f0ac1cdf5a0bcbfe664e1031\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\345431c4f0ac1cdf5a0bcbfe664e1031\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36e27a0f0a5cceebbea3af9c49242f2e\transformed\installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36e27a0f0a5cceebbea3af9c49242f2e\transformed\installreferrer-1.0\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:29:9-35
	android:label
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:27:9-41
	android:fullBackupContent
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:25:9-54
	android:roundIcon
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:28:9-54
	tools:targetApi
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:31:9-29
	android:icon
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:26:9-43
	android:allowBackup
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:23:9-35
	android:theme
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:30:9-53
	android:dataExtractionRules
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:24:9-65
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:22:9-43
meta-data#com.google.android.gms.version
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:34:9-36:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:36:13-66
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:35:13-58
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:39:9-41:46
	android:value
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:41:13-43
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:40:13-64
activity#com.healthyproducts.app.MainActivity
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:43:9-54:20
	android:screenOrientation
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:48:13-49
	android:label
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:46:13-45
	android:exported
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:45:13-36
	android:configChanges
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:49:13-59
	android:theme
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:47:13-57
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:44:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:50:13-53:29
action#android.intent.action.MAIN
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:51:17-69
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:51:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:52:17-77
	android:name
		ADDED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:52:27-74
uses-sdk
INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16fb963deb2021601edde78d41f5400d\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16fb963deb2021601edde78d41f5400d\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50c196bbc2e05ccab95ad36f80b43ae1\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50c196bbc2e05ccab95ad36f80b43ae1\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b341f2bf9e26a9b9b6d9ac1cb5423684\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b341f2bf9e26a9b9b6d9ac1cb5423684\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccfdf8fe6da9c4c34a4246aecbf96939\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccfdf8fe6da9c4c34a4246aecbf96939\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdadd99ea4df4fc28a5f59f01e8e8195\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdadd99ea4df4fc28a5f59f01e8e8195\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39045c8382443acd53f2bb8e054c3072\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39045c8382443acd53f2bb8e054c3072\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba0672690dadfeabecd8225a6e5c2fec\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba0672690dadfeabecd8225a6e5c2fec\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce7eb6ad019e4cf3abb1b411047242d7\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce7eb6ad019e4cf3abb1b411047242d7\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4223b329d8e0940d0002baaa9778a0ca\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4223b329d8e0940d0002baaa9778a0ca\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9aeccd7e0ad8e98923275d3aaa5f52c\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9aeccd7e0ad8e98923275d3aaa5f52c\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19af07a6e550b0e28b93e92c57a3e5b1\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19af07a6e550b0e28b93e92c57a3e5b1\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c308ad6a5cb3ffbae3557cea5538ff\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21c308ad6a5cb3ffbae3557cea5538ff\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82a7a8f16b09c6b5659ed497e86f10d2\transformed\facebook-login-16.3.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82a7a8f16b09c6b5659ed497e86f10d2\transformed\facebook-login-16.3.0\AndroidManifest.xml:12:5-14:41
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97ca12025ade2a2e5a767e7f40a3427d\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97ca12025ade2a2e5a767e7f40a3427d\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8137812cafa923ca51c2d126bd643fd\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8137812cafa923ca51c2d126bd643fd\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6de32b01160391fe70f1240c4fa7c52a\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6de32b01160391fe70f1240c4fa7c52a\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b616a44a83a35cc1ff94b844e1245adb\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b616a44a83a35cc1ff94b844e1245adb\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25ad31b9d5ee235be384e44c485c1169\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25ad31b9d5ee235be384e44c485c1169\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:13:5-15:41
MERGED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:13:5-15:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aeba2917d45d1138de91ba9d1c8482e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aeba2917d45d1138de91ba9d1c8482e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\559848d5dcc2d7c58713b19c26063072\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\559848d5dcc2d7c58713b19c26063072\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a42896bc1e72ea84c886f88868497142\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a42896bc1e72ea84c886f88868497142\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b92c5d39b597b8c399669551ca8aee\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63b92c5d39b597b8c399669551ca8aee\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6dc56213ebf330d331bbdc21265ee404\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6dc56213ebf330d331bbdc21265ee404\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8103600f204b96b181591e6ff975208\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8103600f204b96b181591e6ff975208\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99b293c60277545d5195272d2b459ddb\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99b293c60277545d5195272d2b459ddb\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d09099af744d27cfe09670d8b767e9c6\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d09099af744d27cfe09670d8b767e9c6\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd362f2df44c951726e528b041211eb2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd362f2df44c951726e528b041211eb2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5f795537a8dcc7b0b5116083c56b3c7\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5f795537a8dcc7b0b5116083c56b3c7\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3097c4a004bbcebf7f03b7ea0110fff1\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3097c4a004bbcebf7f03b7ea0110fff1\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d90779d7e42546f5e3492386947a1a5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d90779d7e42546f5e3492386947a1a5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b73dc7478166cf12501eaa2923e89e\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b73dc7478166cf12501eaa2923e89e\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f2dea34a0758b8b70a49614165144e\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f2dea34a0758b8b70a49614165144e\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41bd015f1a0f3c0ceebc8b89d09c39c6\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41bd015f1a0f3c0ceebc8b89d09c39c6\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5092f4b0fc3229c87d6dc9ffd8fb80cd\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5092f4b0fc3229c87d6dc9ffd8fb80cd\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e77ac53be1c88383435fc57361156f4f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e77ac53be1c88383435fc57361156f4f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db8b2510384421d6d2bf67400bb3c749\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db8b2510384421d6d2bf67400bb3c749\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3f5a3660dc1a556892245eec6e80f46\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3f5a3660dc1a556892245eec6e80f46\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.mlkit:text-recognition:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75f9f01176deb15c0c8a84a8dd58da7b\transformed\text-recognition-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75f9f01176deb15c0c8a84a8dd58da7b\transformed\text-recognition-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf33f2ad9ba20b60320925da1b8b29a8\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf33f2ad9ba20b60320925da1b8b29a8\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663eef6da17a713d98a45ad1fec7189c\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663eef6da17a713d98a45ad1fec7189c\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1844dcc7fcb06b70b56c323862223e2e\transformed\text-recognition-bundled-common-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1844dcc7fcb06b70b56c323862223e2e\transformed\text-recognition-bundled-common-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad856e8e03e607399efae39c1221f71\transformed\play-services-mlkit-text-recognition-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad856e8e03e607399efae39c1221f71\transformed\play-services-mlkit-text-recognition-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7980129e1d6a9872b92ccffd3c45c1d\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7980129e1d6a9872b92ccffd3c45c1d\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5698eb67ec86681add89ca7336c98933\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5698eb67ec86681add89ca7336c98933\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5228474c9522c3bcc7499f70e108a19a\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5228474c9522c3bcc7499f70e108a19a\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ed94a9d664deacb7b095dcd9597a23\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47ed94a9d664deacb7b095dcd9597a23\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ffd94ebd0fa9c9f2c3068d95f462416\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ffd94ebd0fa9c9f2c3068d95f462416\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56f85aacf2857cdd00ddb119ddc440ac\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56f85aacf2857cdd00ddb119ddc440ac\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e85093a43bdfab26f6ccfa4cf87a66b\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e85093a43bdfab26f6ccfa4cf87a66b\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a84695ef720a11c72aa363ddaae8b2e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a84695ef720a11c72aa363ddaae8b2e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300207ea80a4ccebe859fdac188f18c2\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\300207ea80a4ccebe859fdac188f18c2\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0abc1d944865e8ba6d2158fa577c48ef\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0abc1d944865e8ba6d2158fa577c48ef\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fe9b91b5586684f355e85aff8478818\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fe9b91b5586684f355e85aff8478818\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc2ccaa5efe1d5f386941cff3c3b5a4\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc2ccaa5efe1d5f386941cff3c3b5a4\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbbfa2073d4844da62c658c7c0d446fc\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbbfa2073d4844da62c658c7c0d446fc\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe86126e590404f02831aec66bba2b20\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe86126e590404f02831aec66bba2b20\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5411fb94070b9800635197c57c0f45ca\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5411fb94070b9800635197c57c0f45ca\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cac439c8d2e2410178107b27a320bddc\transformed\generativeai-0.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cac439c8d2e2410178107b27a320bddc\transformed\generativeai-0.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba0e7b07a6d57e5e696b1eb2c3d13e7a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba0e7b07a6d57e5e696b1eb2c3d13e7a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\764925be0a1268dceac7d457645955a4\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\764925be0a1268dceac7d457645955a4\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09dd1fb389472bed2b265066a833b2e8\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09dd1fb389472bed2b265066a833b2e8\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74dc61ac117c7bb37bdeab07b4ac29d0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74dc61ac117c7bb37bdeab07b4ac29d0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a3c09467404fc49c7a66299a61cc2ec\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a3c09467404fc49c7a66299a61cc2ec\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b0b167eaae9071f6eb78a60488f486\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b0b167eaae9071f6eb78a60488f486\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb0faeb8e6beb83b76de450cfc560ab5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb0faeb8e6beb83b76de450cfc560ab5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80f011d44b6485e2d9a63be0a2243946\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80f011d44b6485e2d9a63be0a2243946\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\493176613545dbc276f1afc7ab477348\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\493176613545dbc276f1afc7ab477348\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\981fd9293439b18a98e73358fb60a309\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\981fd9293439b18a98e73358fb60a309\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eee114be36247d821703c54d772da235\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eee114be36247d821703c54d772da235\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5fafc9901fe45bafab0af5eded1f721\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5fafc9901fe45bafab0af5eded1f721\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:12:5-14:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a655eac010c678b12797f8587be822d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a655eac010c678b12797f8587be822d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76cea9b58239e19066a1772a7b239d33\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76cea9b58239e19066a1772a7b239d33\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35c1aaccc4e0fdcb48773f1a8da50017\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35c1aaccc4e0fdcb48773f1a8da50017\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167b5a10bb8d261a1d1c21747e97c5f2\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167b5a10bb8d261a1d1c21747e97c5f2\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-bolts:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85241934921d9b47a8e2868f80604185\transformed\facebook-bolts-16.3.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-bolts:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85241934921d9b47a8e2868f80604185\transformed\facebook-bolts-16.3.0\AndroidManifest.xml:12:5-14:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1bf99d2dddf6b3f731ce95e33fee49d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1bf99d2dddf6b3f731ce95e33fee49d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\499e93478f158f780b1830007568320c\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\499e93478f158f780b1830007568320c\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\783a2660b78f5db206eeac37e178f876\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\783a2660b78f5db206eeac37e178f876\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6357c5db75b01221998099e3eb60d2bb\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6357c5db75b01221998099e3eb60d2bb\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93292ff7ca3ef385afc1297b2114d4c2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93292ff7ca3ef385afc1297b2114d4c2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb05631900f84699a8bbb98032bf19d9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb05631900f84699a8bbb98032bf19d9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e520bbb7f153fcbb4e273d864eff355d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e520bbb7f153fcbb4e273d864eff355d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea6020f2051ec50ecb0341f4ec224677\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea6020f2051ec50ecb0341f4ec224677\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14d03a95cc07898beae69a352dd60254\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14d03a95cc07898beae69a352dd60254\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eecf4e626fa17ec61ff48a21cf7ed910\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eecf4e626fa17ec61ff48a21cf7ed910\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e09f9a8382cc7143ec1f2ed853cb949\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e09f9a8382cc7143ec1f2ed853cb949\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c36e1f051d57c59ccd6930c68000c21a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c36e1f051d57c59ccd6930c68000c21a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ced8cc4e01061a0a7b865aa983665e4\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ced8cc4e01061a0a7b865aa983665e4\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d383c8be3a1db483e9781ee5cf6d33c5\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d383c8be3a1db483e9781ee5cf6d33c5\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\186cb02f0d71c17c1d537cdcb9922426\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\186cb02f0d71c17c1d537cdcb9922426\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86f59290e78bfc61b73aeeddcc751eda\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86f59290e78bfc61b73aeeddcc751eda\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bb7c106ac39c0fde7aa1dd2e60c49de\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bb7c106ac39c0fde7aa1dd2e60c49de\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7376d3aae2927377aa61cfcdbe97c72d\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7376d3aae2927377aa61cfcdbe97c72d\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911aaa688b927b4d982590c41a85269\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911aaa688b927b4d982590c41a85269\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ccc5722ca8455978d22b35e0684d747\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ccc5722ca8455978d22b35e0684d747\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1099f3717c26695b5d285e2feff633fd\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1099f3717c26695b5d285e2feff633fd\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b770141287ee7a3ad831613eb5366af\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b770141287ee7a3ad831613eb5366af\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa685b06b8478af99f535c1a0c3e2ae9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa685b06b8478af99f535c1a0c3e2ae9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d26682bba9451dfa3cdc898df86373\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d26682bba9451dfa3cdc898df86373\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe1178617600db5fd0c80d7e37655d6c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe1178617600db5fd0c80d7e37655d6c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\856e9279244c37324ec12dc8b216b897\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\856e9279244c37324ec12dc8b216b897\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\750cc87faf3f4c8dad220e0dd1723880\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\750cc87faf3f4c8dad220e0dd1723880\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e95091df6e04deac49b4f699b9aac04\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e95091df6e04deac49b4f699b9aac04\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52458aa1e53c5234a8fbee2df20c8584\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52458aa1e53c5234a8fbee2df20c8584\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c768a4caede6c4e85aa2745fe410ab6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c768a4caede6c4e85aa2745fe410ab6\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8af8ac250606821b6be2458d68775622\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8af8ac250606821b6be2458d68775622\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a674936fcbcc581ac63e1ab9ac183a28\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a674936fcbcc581ac63e1ab9ac183a28\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18896879e787fe333aca7e8fa8255b93\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18896879e787fe333aca7e8fa8255b93\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8db584130df8174d2cb328141cb1ed0d\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8db584130df8174d2cb328141cb1ed0d\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cda5a1f9d8c110805098b8b6936f0e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cda5a1f9d8c110805098b8b6936f0e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78d6b23a4d59efa0c49ea17b57b3d0e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78d6b23a4d59efa0c49ea17b57b3d0e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e017041259519e45b4a5382f4ee20bc7\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e017041259519e45b4a5382f4ee20bc7\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da7d99c88f6dbf06466ffc19eee2fc2\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da7d99c88f6dbf06466ffc19eee2fc2\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0632335579b593b6b56131d5dd003758\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0632335579b593b6b56131d5dd003758\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53964cca1f6d4484e00b6a471daaa3bb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53964cca1f6d4484e00b6a471daaa3bb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98cfce57cce805619f69fe73b0445aa4\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98cfce57cce805619f69fe73b0445aa4\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f545b31aa899495d489745f5e19f96f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f545b31aa899495d489745f5e19f96f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9d81a924b8851aca03208f829a558d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9d81a924b8851aca03208f829a558d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8505cb9b578c45e01d6047f2dc8b89f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8505cb9b578c45e01d6047f2dc8b89f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9485fc988c8f174474128816a08982b\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9485fc988c8f174474128816a08982b\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1548d8c3df025ede4d223e7fa23a4d9a\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1548d8c3df025ede4d223e7fa23a4d9a\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\082c637994de0cd7e9e40c8b1e416041\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\082c637994de0cd7e9e40c8b1e416041\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\345431c4f0ac1cdf5a0bcbfe664e1031\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\345431c4f0ac1cdf5a0bcbfe664e1031\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36e27a0f0a5cceebbea3af9c49242f2e\transformed\installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36e27a0f0a5cceebbea3af9c49242f2e\transformed\installreferrer-1.0\AndroidManifest.xml:5:5-7:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19af07a6e550b0e28b93e92c57a3e5b1\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19af07a6e550b0e28b93e92c57a3e5b1\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
queries
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:17:5-19:15
package#com.facebook.katana
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:9-55
	android:name
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:18-52
activity#com.facebook.FacebookActivity
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:22:9-25:66
	android:configChanges
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:24:13-96
	android:theme
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:25:13-63
	android:name
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:23:13-57
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:19-68
activity#com.facebook.CustomTabActivity
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:27:9-41:20
	tools:node
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:30:13-31
	android:exported
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:29:13-36
	android:name
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:28:13-58
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.healthyproducts.app+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
action#android.intent.action.VIEW
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
	android:name
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
category#android.intent.category.DEFAULT
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
	android:name
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
	android:name
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
data
ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
	android:host
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
	android:scheme
		ADDED from [com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9485fc988c8f174474128816a08982b\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9485fc988c8f174474128816a08982b\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:26:5-110
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36e27a0f0a5cceebbea3af9c49242f2e\transformed\installreferrer-1.0\AndroidManifest.xml:9:5-110
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36e27a0f0a5cceebbea3af9c49242f2e\transformed\installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac1633172f08177d578687e678e15eed\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc2ccaa5efe1d5f386941cff3c3b5a4\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccc2ccaa5efe1d5f386941cff3c3b5a4\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:26:5-79
MERGED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:16:5-79
MERGED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:16:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f58255da777fb21e8d9faf913f81c284\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cda5a1f9d8c110805098b8b6936f0e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cda5a1f9d8c110805098b8b6936f0e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:29:9-32:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:31:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:30:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:34:9-40:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:35:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:37:13-39:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:25-92
receiver#com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:41:9-47:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:43:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:42:13-118
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:44:13-46:29
action#com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:17-103
	android:name
		ADDED from [com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:25-100
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
