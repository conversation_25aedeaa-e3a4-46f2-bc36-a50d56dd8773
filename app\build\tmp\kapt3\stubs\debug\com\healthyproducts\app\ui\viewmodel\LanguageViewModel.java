package com.healthyproducts.app.ui.viewmodel;

/**
 * Dil ayarlar<PERSON>nı yöneten ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u000bR\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006\u0018"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/LanguageViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "firestoreRepository", "Lcom/healthyproducts/app/data/repository/FirestoreRepository;", "(Landroid/app/Application;Lcom/healthyproducts/app/data/repository/FirestoreRepository;)V", "activity", "Landroid/app/Activity;", "currentLanguage", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/healthyproducts/app/util/LocaleHelper$Language;", "getCurrentLanguage", "()Lkotlinx/coroutines/flow/StateFlow;", "languagePreferences", "Lcom/healthyproducts/app/data/preferences/LanguagePreferences;", "supportedLanguages", "", "getSupportedLanguages", "()Ljava/util/List;", "setActivity", "", "setLanguage", "language", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class LanguageViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FirestoreRepository firestoreRepository = null;
    @org.jetbrains.annotations.Nullable()
    private android.app.Activity activity;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.preferences.LanguagePreferences languagePreferences = null;
    
    /**
     * Mevcut dil
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.util.LocaleHelper.Language> currentLanguage = null;
    
    /**
     * Desteklenen diller
     */
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.util.LocaleHelper.Language> supportedLanguages = null;
    
    @javax.inject.Inject()
    public LanguageViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirestoreRepository firestoreRepository) {
        super(null);
    }
    
    /**
     * Aktivite referansını ayarlar
     */
    public final void setActivity(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Mevcut dil
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.util.LocaleHelper.Language> getCurrentLanguage() {
        return null;
    }
    
    /**
     * Desteklenen diller
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.util.LocaleHelper.Language> getSupportedLanguages() {
        return null;
    }
    
    /**
     * Dili değiştirme
     */
    public final void setLanguage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.util.LocaleHelper.Language language) {
    }
}