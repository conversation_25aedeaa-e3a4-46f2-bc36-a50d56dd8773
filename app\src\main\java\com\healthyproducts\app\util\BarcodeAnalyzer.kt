package com.healthyproducts.app.util

import android.annotation.SuppressLint
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage

/**
 * Barkod analizi için ImageAnalysis.Analyzer sınıfı
 */
class BarcodeAnalyzer(
    private val onBarcodeDetected: (List<Barcode>) -> Unit
) : ImageAnalysis.Analyzer {
    
    // Barkod tarayıcı
    private val scanner = BarcodeScanning.getClient()
    
    @SuppressLint("UnsafeOptInUsageError")
    override fun analyze(imageProxy: ImageProxy) {
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(
                mediaImage,
                imageProxy.imageInfo.rotationDegrees
            )
            
            // Barkod tarama işlemi
            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    // Barkod bulundu
                    if (barcodes.isNotEmpty()) {
                        onBarcodeDetected(barcodes)
                    }
                }
                .addOnFailureListener {
                    // Barkod tarama hatası
                }
                .addOnCompleteListener {
                    // Görüntüyü kapat
                    imageProxy.close()
                }
        } else {
            // Görüntü yoksa kapat
            imageProxy.close()
        }
    }
}
