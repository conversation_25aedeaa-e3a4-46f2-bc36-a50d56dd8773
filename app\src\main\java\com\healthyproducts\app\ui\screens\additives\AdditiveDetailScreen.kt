package com.healthyproducts.app.ui.screens.additives

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.data.model.Additive
import com.healthyproducts.app.data.model.AdditiveCategory
import com.healthyproducts.app.data.model.HalalStatus
import com.healthyproducts.app.data.model.KosherStatus
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.model.VeganStatus
import com.healthyproducts.app.ui.viewmodel.AdditiveViewModel
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Katkı maddesi detay ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdditiveDetailScreen(
    navController: NavController,
    additiveCode: String,
    viewModel: AdditiveViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val selectedAdditive by viewModel.selectedAdditive.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()
    val userLanguage = SupportedLanguage.fromCode(userPreferences.language)

    // Katkı maddesini yükle
    LaunchedEffect(additiveCode) {
        viewModel.getAdditiveByCode(additiveCode)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = "Katkı Maddesi Detayı") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Geri"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (error != null) {
                Text(
                    text = error ?: "Bir hata oluştu",
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (selectedAdditive == null) {
                Text(
                    text = "Katkı maddesi bulunamadı",
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                AdditiveDetail(
                    additive = selectedAdditive!!,
                    language = userLanguage
                )
            }
        }
    }
}

/**
 * Katkı maddesi detayı
 */
@Composable
fun AdditiveDetail(
    additive: Additive,
    language: SupportedLanguage
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Başlık
        Text(
            text = "${additive.code} - ${additive.getName(language)}",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Kategori
        DetailItem(
            title = "Kategori",
            value = when (language) {
                SupportedLanguage.TURKISH -> additive.categoryTr.ifEmpty { additive.categoryEn }
                SupportedLanguage.ENGLISH -> additive.categoryEn.ifEmpty { additive.categoryTr }
            }
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Kaynak
        val origin = additive.getOrigin(language)
        if (origin.isNotBlank()) {
            DetailItem(
                title = "Kaynak",
                value = origin
            )

            Divider(modifier = Modifier.padding(vertical = 8.dp))
        }

        // Kullanım Amacı
        val usage = additive.getUsage(language)
        if (usage.isNotBlank()) {
            DetailItem(
                title = "Kullanım Amacı",
                value = usage
            )

            Divider(modifier = Modifier.padding(vertical = 8.dp))
        }

        // Sağlık Etkileri
        val healthEffect = additive.getHealthEffect(language)
        if (healthEffect.isNotBlank()) {
            DetailItem(
                title = "Sağlık Etkileri",
                value = healthEffect
            )

            Divider(modifier = Modifier.padding(vertical = 8.dp))
        }

        // Açıklama
        DetailItem(
            title = "Açıklama",
            value = additive.getDescription(language)
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Helal durumu
        DetailItem(
            title = "Helal Durumu",
            value = HalalStatus.values().find { it.name == additive.halalStatus }?.displayName ?: additive.halalStatus
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Vegan durumu
        DetailItem(
            title = "Vegan Durumu",
            value = VeganStatus.values().find { it.name == additive.veganStatus }?.displayName ?: additive.veganStatus
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Vejetaryen durumu
        if (additive.vegetarianStatus.isNotBlank()) {
            DetailItem(
                title = "Vejetaryen Durumu",
                value = additive.vegetarianStatus
            )

            Divider(modifier = Modifier.padding(vertical = 8.dp))
        }

        // Koşer durumu
        DetailItem(
            title = "Koşer Durumu",
            value = KosherStatus.values().find { it.name == additive.kosherStatus }?.displayName ?: additive.kosherStatus
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Riskli mi?
        DetailItem(
            title = "Riskli",
            value = if (additive.risky) "Evet" else "Hayır"
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Zararlılık seviyesi
        Text(
            text = "Zararlılık Seviyesi",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(4.dp))

        LinearProgressIndicator(
            progress = additive.harmfulLevel / 5f,
            modifier = Modifier.fillMaxWidth()
        )

        Text(
            text = "${additive.harmfulLevel}/5",
            style = MaterialTheme.typography.bodyMedium
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Sağlıksızlık seviyesi
        Text(
            text = "Sağlıksızlık Seviyesi",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(4.dp))

        LinearProgressIndicator(
            progress = additive.unhealthyLevel / 5f,
            modifier = Modifier.fillMaxWidth()
        )

        Text(
            text = "${additive.unhealthyLevel}/5",
            style = MaterialTheme.typography.bodyMedium
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        // Notlar
        val notes = additive.getNotes(language)
        if (notes.isNotBlank()) {
            DetailItem(
                title = "Notlar",
                value = notes
            )
        }
    }
}

/**
 * Detay öğesi
 */
@Composable
fun DetailItem(
    title: String,
    value: String
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
