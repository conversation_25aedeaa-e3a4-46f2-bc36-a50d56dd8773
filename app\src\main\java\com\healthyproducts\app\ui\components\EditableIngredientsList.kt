package com.healthyproducts.app.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp

/**
 * Düzenlenebilir içerik listesi bileşeni
 */
@Composable
fun EditableIngredientsList(
    ingredients: List<String>,
    onIngredientsUpdated: (List<String>) -> Unit
) {
    // Düzenlenebilir içerik listesi
    val editableIngredients = remember { mutableStateListOf<String>() }

    // İçerikleri başlangıçta yükle
    remember(ingredients) {
        editableIngredients.clear()
        editableIngredients.addAll(ingredients)
        true
    }

    // Düzenleme durumları
    val editingStates = remember { mutableStateListOf<Boolean>().apply {
        addAll(List(ingredients.size) { false })
    }}

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        editableIngredients.forEachIndexed { index, ingredient ->
            IngredientItem(
                ingredient = ingredient,
                isEditing = editingStates.getOrNull(index) ?: false,
                onEditToggle = {
                    // Düzenleme durumunu değiştir
                    if (index < editingStates.size) {
                        editingStates[index] = !editingStates[index]

                        // Düzenleme tamamlandığında içerikleri güncelle
                        if (!editingStates[index]) {
                            onIngredientsUpdated(editableIngredients.toList())
                        }
                    }
                },
                onIngredientChanged = { newValue ->
                    // İçeriği güncelle
                    if (index < editableIngredients.size) {
                        editableIngredients[index] = newValue
                    }
                }
            )
        }
    }
}

/**
 * İçerik öğesi bileşeni
 */
@Composable
fun IngredientItem(
    ingredient: String,
    isEditing: Boolean,
    onEditToggle: () -> Unit,
    onIngredientChanged: (String) -> Unit
) {
    var text by remember { mutableStateOf(ingredient) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (isEditing) {
            // Düzenleme modu
            OutlinedTextField(
                value = text,
                onValueChange = {
                    text = it
                    onIngredientChanged(it)
                },
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 8.dp),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                keyboardActions = KeyboardActions(
                    onDone = { onEditToggle() }
                ),
                singleLine = true
            )

            // Tamamla butonu
            IconButton(onClick = onEditToggle) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "Düzenlemeyi Tamamla",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        } else {
            // Görüntüleme modu
            Text(
                text = "• $ingredient",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = 8.dp, horizontal = 4.dp)
            )

            // Düzenle butonu
            IconButton(onClick = onEditToggle) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "Düzenle",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}
