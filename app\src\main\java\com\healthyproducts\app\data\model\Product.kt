package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import java.util.Date

/**
 * Ürün bilgilerini temsil eden data class
 */
data class Product(
    @DocumentId
    val id: String = "",
    val barcode: String = "",
    val name: String = "",
    val brand: String = "",
    val ingredients: List<String> = emptyList(),
    val analysisResult: String = "",
    val healthScore: Int = 0, // 1-10 arası
    val language: String = "tr", // Analiz dili
    val createdAt: Date = Date(),
    val updatedAt: Date = Date(),
    val createdBy: String = "", // Kullanıcı ID'si
    val isVerified: Boolean = false, // Admin tarafından doğrulandı mı
    val category: String = "", // Ürün kategorisi (isteğe bağlı)
    val imageUrl: String = "" // Ürün resmi (isteğe bağlı)
)

/**
 * Ürün arama sonuçları için data class
 */
data class ProductSearchResult(
    val product: Product,
    val matchType: ProductMatchType
)

/**
 * Ürün eşleşme türleri
 */
enum class ProductMatchType {
    EXACT_BARCODE, // Tam barkod eşleşmesi
    SIMILAR_INGREDIENTS, // Benzer içerikler
    SAME_BRAND // Aynı marka
}

/**
 * Ürün kaydetme isteği
 */
data class SaveProductRequest(
    val barcode: String,
    val name: String,
    val brand: String = "",
    val ingredients: List<String>,
    val analysisResult: String,
    val healthScore: Int,
    val language: String,
    val category: String = "",
    val imageUrl: String = ""
)
