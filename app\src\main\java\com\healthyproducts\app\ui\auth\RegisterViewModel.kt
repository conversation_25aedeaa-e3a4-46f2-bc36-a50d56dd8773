package com.healthyproducts.app.ui.auth

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthyproducts.app.data.repository.FirebaseAuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RegisterViewModel @Inject constructor(
    private val authRepository: FirebaseAuthRepository
) : ViewModel() {
    
    private val _email = MutableStateFlow("")
    val email: StateFlow<String> = _email.asStateFlow()
    
    private val _password = MutableStateFlow("")
    val password: StateFlow<String> = _password.asStateFlow()
    
    private val _confirmPassword = MutableStateFlow("")
    val confirmPassword: StateFlow<String> = _confirmPassword.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _registerError = MutableStateFlow<String?>(null)
    val registerError: StateFlow<String?> = _registerError.asStateFlow()
    
    private val _isRegistered = MutableStateFlow(false)
    val isRegistered: StateFlow<Boolean> = _isRegistered.asStateFlow()
    
    fun updateEmail(email: String) {
        _email.value = email
    }
    
    fun updatePassword(password: String) {
        _password.value = password
    }
    
    fun updateConfirmPassword(confirmPassword: String) {
        _confirmPassword.value = confirmPassword
    }
    
    fun register() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // Giriş alanlarını kontrol et
                if (_email.value.isBlank() || _password.value.isBlank() || _confirmPassword.value.isBlank()) {
                    _registerError.value = "Tüm alanları doldurun"
                    return@launch
                }
                
                // Şifrelerin eşleştiğini kontrol et
                if (_password.value != _confirmPassword.value) {
                    _registerError.value = "Şifreler eşleşmiyor"
                    return@launch
                }
                
                // Şifre uzunluğunu kontrol et
                if (_password.value.length < 6) {
                    _registerError.value = "Şifre en az 6 karakter olmalıdır"
                    return@launch
                }
                
                // Kayıt ol
                authRepository.signUp(_email.value, _password.value)
                _isRegistered.value = true
                
            } catch (e: Exception) {
                Log.e("RegisterViewModel", "Kayıt hatası", e)
                _registerError.value = e.message ?: "Kayıt olurken bir hata oluştu"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun clearError() {
        _registerError.value = null
    }
}
