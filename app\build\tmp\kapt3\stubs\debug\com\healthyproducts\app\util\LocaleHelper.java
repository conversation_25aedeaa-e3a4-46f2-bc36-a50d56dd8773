package com.healthyproducts.app.util;

/**
 * Dil ayarlarını yönetmek için yardımcı sınıf
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0001\u0014B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0004J\u0016\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u0004J\u0006\u0010\u000b\u001a\u00020\u0004J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u0007\u001a\u00020\u0004J\u000e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\r\u00a8\u0006\u0015"}, d2 = {"Lcom/healthyproducts/app/util/LocaleHelper;", "", "()V", "getCurrentLocale", "Lcom/healthyproducts/app/util/LocaleHelper$Language;", "getLocalizedConfiguration", "Landroid/content/res/Configuration;", "language", "getLocalizedContext", "Landroid/content/Context;", "context", "getSystemLocale", "languageToSupportedLanguage", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "setLocale", "", "languageCode", "", "supportedLanguageToLanguage", "supportedLanguage", "Language", "app_debug"})
public final class LocaleHelper {
    @org.jetbrains.annotations.NotNull()
    public static final com.healthyproducts.app.util.LocaleHelper INSTANCE = null;
    
    private LocaleHelper() {
        super();
    }
    
    /**
     * Uygulama dilini değiştirme
     */
    public final void setLocale(@org.jetbrains.annotations.NotNull()
    java.lang.String languageCode) {
    }
    
    /**
     * Mevcut dili alma
     */
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.util.LocaleHelper.Language getCurrentLocale() {
        return null;
    }
    
    /**
     * Sistem dilini alma
     */
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.util.LocaleHelper.Language getSystemLocale() {
        return null;
    }
    
    /**
     * Belirli bir dil için yapılandırma oluşturma
     */
    @org.jetbrains.annotations.NotNull()
    public final android.content.res.Configuration getLocalizedConfiguration(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.util.LocaleHelper.Language language) {
        return null;
    }
    
    /**
     * Context'i belirli bir dil için yapılandırma
     */
    @org.jetbrains.annotations.NotNull()
    public final android.content.Context getLocalizedContext(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.util.LocaleHelper.Language language) {
        return null;
    }
    
    /**
     * Language sınıfını SupportedLanguage sınıfına dönüştürür
     */
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.model.SupportedLanguage languageToSupportedLanguage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.util.LocaleHelper.Language language) {
        return null;
    }
    
    /**
     * SupportedLanguage sınıfını Language sınıfına dönüştürür
     */
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.util.LocaleHelper.Language supportedLanguageToLanguage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage supportedLanguage) {
        return null;
    }
    
    /**
     * Desteklenen diller
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u0000 \u000b2\b\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u000bB\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007j\u0002\b\tj\u0002\b\n\u00a8\u0006\f"}, d2 = {"Lcom/healthyproducts/app/util/LocaleHelper$Language;", "", "code", "", "displayName", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getCode", "()Ljava/lang/String;", "getDisplayName", "ENGLISH", "TURKISH", "Companion", "app_debug"})
    public static enum Language {
        /*public static final*/ ENGLISH /* = new ENGLISH(null, null) */,
        /*public static final*/ TURKISH /* = new TURKISH(null, null) */;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String code = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String displayName = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.util.LocaleHelper.Language.Companion Companion = null;
        
        Language(java.lang.String code, java.lang.String displayName) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCode() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDisplayName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.healthyproducts.app.util.LocaleHelper.Language> getEntries() {
            return null;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/healthyproducts/app/util/LocaleHelper$Language$Companion;", "", "()V", "fromCode", "Lcom/healthyproducts/app/util/LocaleHelper$Language;", "code", "", "app_debug"})
        public static final class Companion {
            
            private Companion() {
                super();
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.util.LocaleHelper.Language fromCode(@org.jetbrains.annotations.NotNull()
            java.lang.String code) {
                return null;
            }
        }
    }
}