package com.healthyproducts.app.ui.screens.foodanalysis

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.FoodPreferenceType
import com.healthyproducts.app.data.model.Sugar
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.ui.components.AddCustomItemDialog
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.ImportStatus
import com.healthyproducts.app.ui.viewmodel.SugarsState
import com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState
import com.healthyproducts.app.ui.viewmodel.UserViewModel
import kotlinx.coroutines.launch

/**
 * Şekerler ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SugarsScreen(
    navController: NavController,
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }

    val sugarsState by foodAnalysisViewModel.sugarsState.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()
    val importStatus by foodAnalysisViewModel.importStatus.collectAsState()
    val userFoodPreferencesState by foodAnalysisViewModel.userFoodPreferencesState.collectAsState()
    val selectedFoodPreferences by foodAnalysisViewModel.selectedFoodPreferences.collectAsState()

    // Kullanıcı ID'si
    val userState by userViewModel.userState.collectAsState()
    val userId = if (userState is UserViewModel.UserState.LoggedIn) {
        (userState as UserViewModel.UserState.LoggedIn).user.id
    } else {
        ""
    }

    // Dil tercihi
    val language = SupportedLanguage.fromCode(userPreferences.language)

    // Arama metni
    var searchText by remember { mutableStateOf("") }

    // Özel şeker ekleme diyaloğu
    var showAddCustomDialog by remember { mutableStateOf(false) }

    // İçeri aktarma durumunu izle
    LaunchedEffect(importStatus) {
        when (importStatus) {
            is ImportStatus.Success -> {
                val count = (importStatus as ImportStatus.Success).count
                snackbarHostState.showSnackbar(
                    message = context.getString(R.string.import_success, count)
                )
                foodAnalysisViewModel.resetImportStatus()
            }
            is ImportStatus.Error -> {
                val message = (importStatus as ImportStatus.Error).message
                snackbarHostState.showSnackbar(
                    message = context.getString(R.string.import_error, message)
                )
                foodAnalysisViewModel.resetImportStatus()
            }
            else -> {}
        }
    }

    // Şekerleri yükle
    LaunchedEffect(Unit) {
        foodAnalysisViewModel.loadSugars()
    }

    // Kullanıcı tercihlerini yükle (kullanıcı ID'si değiştiğinde)
    LaunchedEffect(userId) {
        if (userId.isNotEmpty()) {
            foodAnalysisViewModel.loadUserFoodPreferencesByType(userId, FoodPreferenceType.SUGAR)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.sugars)) },
                navigationIcon = { BackButton(navController) }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        floatingActionButton = {
            // Kullanıcı giriş yapmışsa özel şeker ekleme butonu göster
            if (userId.isNotEmpty()) {
                FloatingActionButton(
                    onClick = {
                        // Özel şeker ekleme diyaloğunu göster
                        showAddCustomDialog = true
                    }
                ) {
                    Icon(Icons.Default.Add, contentDescription = stringResource(R.string.add_custom_sugar))
                }
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
        ) {
            // Arama alanı
            OutlinedTextField(
                value = searchText,
                onValueChange = { searchText = it },
                modifier = Modifier.fillMaxWidth(),
                label = { Text(stringResource(R.string.search)) },
                leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Şekerler listesi
            when (val state = sugarsState) {
                is SugarsState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }

                is SugarsState.Success -> {
                    val sugars = state.sugars

                    // Arama filtresine göre şekerleri filtrele
                    val filteredSugars = if (searchText.isBlank()) {
                        sugars
                    } else {
                        sugars.filter { sugar ->
                            sugar.getName(language).contains(searchText, ignoreCase = true) ||
                            sugar.sugarId.contains(searchText, ignoreCase = true)
                        }
                    }

                    if (filteredSugars.isEmpty()) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.no_results),
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    } else {
                        LazyColumn(
                            contentPadding = PaddingValues(vertical = 8.dp)
                        ) {
                            items(filteredSugars) { sugar ->
                                SugarItem(
                                    sugar = sugar,
                                    language = language,
                                    isSelected = selectedFoodPreferences[sugar.sugarId] ?: false,
                                    onCheckedChange = { isChecked ->
                                        if (userId.isNotEmpty()) {
                                            if (isChecked) {
                                                // Şeker tercihini kaydet
                                                foodAnalysisViewModel.saveUserFoodPreference(
                                                    userId = userId,
                                                    type = FoodPreferenceType.SUGAR,
                                                    itemId = sugar.sugarId,
                                                    itemName = sugar.getName(language)
                                                )
                                            } else {
                                                // Şeker tercihini bul ve sil
                                                val preference = (userFoodPreferencesState as? UserFoodPreferencesState.Success)?.preferences?.find {
                                                    it.itemId == sugar.sugarId && it.type == FoodPreferenceType.SUGAR.value
                                                }

                                                if (preference != null) {
                                                    foodAnalysisViewModel.deleteUserFoodPreference(
                                                        preferenceId = preference.id,
                                                        userId = userId,
                                                        type = FoodPreferenceType.SUGAR
                                                    )
                                                }
                                            }
                                        } else {
                                            // Kullanıcı giriş yapmamışsa uyarı göster
                                            scope.launch {
                                                snackbarHostState.showSnackbar("Tercih eklemek için giriş yapmalısınız")
                                            }
                                        }
                                    },
                                    onClick = {
                                        // Şeker detay sayfasına git
                                        navController.navigate("sugar_detail/${sugar.sugarId}")
                                    }
                                )
                            }
                        }
                    }
                }

                is SugarsState.Error -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = state.message,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }

        // Özel şeker ekleme diyaloğu
        if (showAddCustomDialog) {
            AddCustomItemDialog(
                title = stringResource(R.string.add_custom_sugar),
                onDismiss = { showAddCustomDialog = false },
                onConfirm = { sugarName ->
                    scope.launch {
                        foodAnalysisViewModel.addCustomFoodPreference(userId, FoodPreferenceType.SUGAR, sugarName)
                        snackbarHostState.showSnackbar("Özel şeker eklendi: $sugarName")
                    }
                }
            )
        }
    }
}

/**
 * Şeker öğesi
 */
@Composable
fun SugarItem(
    sugar: Sugar,
    language: SupportedLanguage,
    isSelected: Boolean = false,
    onCheckedChange: (Boolean) -> Unit = {},
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable(onClick = onClick)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Checkbox
            Checkbox(
                checked = isSelected,
                onCheckedChange = onCheckedChange,
                modifier = Modifier.padding(end = 8.dp)
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                // Emoji veya ikon
                if (sugar.symbol.isNotEmpty()) {
                    Text(
                        text = sugar.symbol,
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                }

                Column {
                    Text(
                        text = sugar.getName(language),
                        style = MaterialTheme.typography.titleMedium
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = sugar.sugarId,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = sugar.getDescription(language),
                        style = MaterialTheme.typography.bodySmall,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Glisemik indeks göstergesi
            GlycemicIndexIndicator(glycemicIndex = sugar.glycemicIndex)
        }
    }
}

/**
 * Glisemik indeks göstergesi
 */
@Composable
fun GlycemicIndexIndicator(glycemicIndex: Int) {
    val color = when {
        glycemicIndex >= 70 -> MaterialTheme.colorScheme.error
        glycemicIndex >= 55 -> MaterialTheme.colorScheme.tertiary
        else -> MaterialTheme.colorScheme.primary
    }

    Box(
        modifier = Modifier
            .width(24.dp)
            .height(24.dp)
            .padding(2.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = glycemicIndex.toString(),
            style = MaterialTheme.typography.labelMedium,
            color = color
        )
    }
}
