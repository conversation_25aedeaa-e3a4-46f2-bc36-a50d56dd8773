package com.healthyproducts.app.ui.screens.profile

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Logout
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.model.User
import com.healthyproducts.app.navigation.Screen
import com.healthyproducts.app.ui.components.BottomNavBar
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Profil ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    navController: NavController,
    userViewModel: UserViewModel = hiltViewModel()
) {
    val userState by userViewModel.userState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Hata durumunda snackbar göster
    LaunchedEffect(userState) {
        if (userState is UserViewModel.UserState.Error) {
            snackbarHostState.showSnackbar((userState as UserViewModel.UserState.Error).message)
        }
    }

    Scaffold(
        bottomBar = { BottomNavBar(navController = navController) },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { innerPadding ->
        when (userState) {
            is UserViewModel.UserState.Loading -> {
                LoadingContent(paddingValues = innerPadding)
            }
            is UserViewModel.UserState.LoggedIn -> {
                ProfileContent(
                    navController = navController,
                    paddingValues = innerPadding,
                    user = (userState as UserViewModel.UserState.LoggedIn).user,
                    onLogout = { userViewModel.signOut() },
                    onHistoryClick = { /* TODO: Tarama geçmişi ekranına yönlendir */ }
                )
            }
            is UserViewModel.UserState.LoggedOut -> {
                NotLoggedInContent(
                    navController = navController,
                    paddingValues = innerPadding
                )
            }
            is UserViewModel.UserState.Error -> {
                // Hata durumu snackbar ile gösterildi, içerik olarak giriş yapmamış durumu göster
                NotLoggedInContent(
                    navController = navController,
                    paddingValues = innerPadding
                )
            }
        }
    }
}

/**
 * Yükleme içeriği
 */
@Composable
fun LoadingContent(paddingValues: PaddingValues) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        CircularProgressIndicator()
    }
}

/**
 * Profil ekranı içeriği (giriş yapmış kullanıcı)
 */
@Composable
fun ProfileContent(
    navController: NavController,
    paddingValues: PaddingValues,
    user: User,
    onLogout: () -> Unit,
    onHistoryClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Profil",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Kullanıcı bilgileri
        Text(
            text = user.name ?: "Kullanıcı",
            style = MaterialTheme.typography.titleLarge
        )

        Text(
            text = user.email,
            style = MaterialTheme.typography.bodyMedium
        )

        Spacer(modifier = Modifier.height(24.dp))
        Divider()
        Spacer(modifier = Modifier.height(24.dp))

        // Tarama geçmişi butonu
        OutlinedButton(
            onClick = onHistoryClick,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.History,
                contentDescription = null,
                modifier = Modifier.padding(end = 8.dp)
            )
            Text(text = stringResource(R.string.scan_history))
        }

        // Ayarlar butonu
        OutlinedButton(
            onClick = { navController.navigate(Screen.Settings.route) },
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = null,
                modifier = Modifier.padding(end = 8.dp)
            )
            Text(text = stringResource(R.string.settings))
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Çıkış yap butonu
        Button(
            onClick = onLogout,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.Logout,
                contentDescription = null,
                modifier = Modifier.padding(end = 8.dp)
            )
            Text(text = stringResource(R.string.logout))
        }
    }
}

/**
 * Giriş yapmamış kullanıcı için profil içeriği
 */
@Composable
fun NotLoggedInContent(
    navController: NavController,
    paddingValues: PaddingValues
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Profil",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        Text(
            text = "Favori ürünlerinizi kaydetmek ve daha fazla özelliğe erişmek için giriş yapın",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        // Giriş yap butonu
        Button(
            onClick = { navController.navigate("login") },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(text = stringResource(R.string.login))
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Kayıt ol butonu
        OutlinedButton(
            onClick = { navController.navigate("register") },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(text = stringResource(R.string.register))
        }

        Spacer(modifier = Modifier.height(24.dp))
        Divider()
        Spacer(modifier = Modifier.height(24.dp))

        // Ayarlar butonu
        OutlinedButton(
            onClick = { navController.navigate(Screen.Settings.route) },
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = null,
                modifier = Modifier.padding(end = 8.dp)
            )
            Text(text = stringResource(R.string.settings))
        }
    }
}
