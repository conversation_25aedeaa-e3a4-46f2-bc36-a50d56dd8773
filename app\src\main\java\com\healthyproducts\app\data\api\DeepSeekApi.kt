package com.healthyproducts.app.data.api

import com.healthyproducts.app.data.model.DeepSeekRequest
import com.healthyproducts.app.data.model.DeepSeekResponse
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * DeepSeek API ile iletişim kurmak için Retrofit arayüzü
 */
interface DeepSeekApi {
    
    /**
     * DeepSeek API'sine istek gönderir ve metin üretir
     */
    @POST("v1/chat/completions")
    suspend fun generateText(@Body request: DeepSeekRequest): DeepSeekResponse
}
