{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,312", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,25598", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,25673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "352,353", "startColumns": "4,4", "startOffsets": "28107,28191", "endColumns": "83,86", "endOffsets": "28186,28273"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values-tr\\strings.xml", "from": {"startLines": "68,180,230,234,231,235,233,232,46,185,182,184,179,186,189,188,183,191,190,187,178,114,66,67,143,139,115,111,110,101,109,211,2,224,99,21,105,23,91,208,146,41,118,170,171,172,169,163,85,100,216,82,107,103,243,236,60,130,134,129,133,92,148,153,93,181,29,80,209,96,144,140,7,18,221,220,142,174,173,32,215,147,167,166,168,24,42,44,165,113,151,5,194,226,199,195,197,198,196,200,212,40,31,36,108,30,28,156,59,126,123,125,124,95,77,238,86,79,214,15,83,106,242,217,17,162,161,27,22,98,149,81,213,240,239,205,204,203,117,69,39,8,120,102,12,78,87,154,47,210,97,225,152,90,237,6,13,84,14,26,25,94,227,104,9,74,72,73,61,64,63,65,62,35,34,33,50,51,55,159,160,54,158,52,56,53,157,145,141,116,119,45,206,70,207,136,164,135,131,132,150,241,112,43,155,71,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3372,9242,11879,12134,11945,12205,12063,12002,2273,9545,9366,9487,9177,9600,9796,9732,9427,9942,9867,9663,9123,5740,3264,3311,7225,7027,5802,5545,5482,4853,5403,10918,55,11601,4775,828,5075,918,4443,10760,7388,1976,5973,8758,8830,8897,8692,8329,4188,4813,11190,4036,5286,4956,12759,12263,2855,6492,6815,6426,6740,4484,7516,7761,4523,9302,1314,3951,10810,4653,7284,7076,222,747,11417,11355,7160,9034,8970,1557,11130,7452,8578,8518,8637,993,2030,2138,8456,5665,7649,141,10013,11722,10348,10080,10200,10272,10138,10419,10969,1924,1485,1747,5346,1410,1245,7947,2814,6338,6173,6280,6228,4603,3816,12384,4246,3906,11083,549,4096,5221,12698,11249,680,8273,8232,1167,875,4738,7565,3993,11036,12549,12472,10622,10558,10504,5911,3415,1865,270,6084,4899,384,3860,4313,7822,2334,10874,4692,11663,7708,4403,12321,184,439,4132,492,1097,1047,4564,11777,5015,313,3724,3586,3659,2906,3114,3044,3187,2975,1702,1657,1612,2433,2480,2685,8126,8174,2628,8059,2527,2736,2581,8002,7334,7116,5859,6035,2204,10672,3478,10719,6942,8390,6881,6613,6675,7606,12631,5603,2084,7883,3541,603", "endColumns": "42,59,65,70,56,57,70,60,60,54,60,57,64,62,70,63,59,49,74,68,53,61,46,60,58,48,56,57,62,45,78,50,53,61,37,46,145,74,40,49,63,53,61,71,66,72,65,60,57,39,58,59,59,58,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,74,58,42,66,54,70,57,71,75,61,56,66,51,71,88,56,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,35,64,60,70,66,55,40,77,42,36,40,42,46,81,76,49,63,53,61,62,58,42,65,56,54,45,68,60,68,43,45,58,52,39,62,37,52,55,56,69,49,38,65,59,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,51,48,68,46,62,40,56,65,60,61,64,42,66,61,53,63,44,76", "endOffsets": "3410,9297,11940,12200,11997,12258,12129,12058,2329,9595,9422,9540,9237,9658,9862,9791,9482,9987,9937,9727,9172,5797,3306,3367,7279,7071,5854,5598,5540,4894,5477,10964,104,11658,4808,870,5216,988,4479,10805,7447,2025,6030,8825,8892,8965,8753,8385,4241,4848,11244,4091,5341,5010,12803,12316,2901,6608,6876,6487,6810,4518,7560,7817,4559,9361,1405,3988,10869,4687,7329,7111,265,797,11564,11412,7220,9093,9029,1607,11185,7511,8632,8573,8687,1042,2079,2199,8513,5735,7703,179,10075,11772,10414,10133,10267,10343,10195,10471,11031,1971,1552,1831,5398,1480,1309,7997,2850,6395,6223,6333,6275,4648,3855,12467,4308,3946,11125,598,4127,5281,12754,11315,742,8324,8268,1240,913,4770,7601,4031,11078,12626,12544,10667,10617,10553,5968,3473,1919,308,6145,4951,434,3901,4377,7878,2398,10913,4733,11717,7756,4438,12379,217,487,4183,544,1162,1092,4598,11838,5070,353,3789,3654,3719,2970,3182,3109,3259,3039,1742,1697,1652,2475,2522,2731,8169,8227,2680,8121,2576,2786,2623,8054,7383,7155,5906,6079,2268,10714,3536,10755,6994,8451,6937,6670,6735,7644,12693,5660,2133,7942,3581,675"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,72,73,74,75,76,77,78,79,80,81,82,83,84,85,127,130,131,133,134,135,136,137,138,139,140,143,144,145,147,148,149,150,151,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,263,264,265,266,268,269,270,271,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,314,315,316,317,318,319,320,321,322,323,324,325,326,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,348,351,354,355,356,357,358,359,360,361,362,363,364,365,366", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,2840,2900,2966,3037,3094,3152,3223,3284,3345,3400,3461,3519,3584,3647,3718,3782,3842,3892,3967,4036,4090,4152,4199,4260,4319,4368,4425,4483,4546,4592,4671,4722,4776,4838,4876,5636,5782,5857,5898,5948,6012,6066,6128,6200,6267,6340,6406,6467,6525,11374,11577,11637,11797,11856,11905,11963,12014,12135,12201,12267,12537,12576,12625,12770,12811,12875,12971,13013,13077,13435,13485,13525,13573,13628,13780,13842,13907,13971,14035,14090,14150,14214,14273,14333,14388,14442,14496,14562,14624,14699,14758,14801,14868,14923,14994,15052,15124,15200,15262,15502,15569,15621,15693,15782,15839,15914,15983,16038,16079,16141,16196,16254,16306,16356,16400,16488,16555,22760,22807,22861,22897,23050,23111,23182,23249,23389,23430,23508,23551,23588,23629,23672,23719,23801,23878,23928,23992,24046,24108,24171,24230,24441,24507,24564,24619,24665,24734,24795,24864,24908,24954,25013,25066,25106,25169,25207,25260,25316,25373,25443,25493,25532,25749,25809,25854,25924,25997,26062,26131,26204,26274,26351,26420,26465,26510,26877,26924,26971,27022,27070,27128,27185,27252,27306,27361,27408,27465,27519,27563,27615,27664,27811,28044,28278,28319,28376,28442,28503,28565,28630,28673,28740,28802,28856,28920,28965", "endColumns": "42,59,65,70,56,57,70,60,60,54,60,57,64,62,70,63,59,49,74,68,53,61,46,60,58,48,56,57,62,45,78,50,53,61,37,46,145,74,40,49,63,53,61,71,66,72,65,60,57,39,58,59,59,58,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,74,58,42,66,54,70,57,71,75,61,56,66,51,71,88,56,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,35,64,60,70,66,55,40,77,42,36,40,42,46,81,76,49,63,53,61,62,58,42,65,56,54,45,68,60,68,43,45,58,52,39,62,37,52,55,56,69,49,38,65,59,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,51,48,68,46,62,40,56,65,60,61,64,42,66,61,53,63,44,76", "endOffsets": "2835,2895,2961,3032,3089,3147,3218,3279,3340,3395,3456,3514,3579,3642,3713,3777,3837,3887,3962,4031,4085,4147,4194,4255,4314,4363,4420,4478,4541,4587,4666,4717,4771,4833,4871,4918,5777,5852,5893,5943,6007,6061,6123,6195,6262,6335,6401,6462,6520,6560,11428,11632,11692,11851,11900,11958,12009,12130,12196,12262,12337,12571,12620,12681,12806,12870,12966,13008,13072,13111,13480,13520,13568,13623,13775,13837,13902,13966,14030,14085,14145,14209,14268,14328,14383,14437,14491,14557,14619,14694,14753,14796,14863,14918,14989,15047,15119,15195,15257,15314,15564,15616,15688,15777,15834,15909,15978,16033,16074,16136,16191,16249,16301,16351,16395,16483,16550,16595,22802,22856,22892,22957,23106,23177,23244,23300,23425,23503,23546,23583,23624,23667,23714,23796,23873,23923,23987,24041,24103,24166,24225,24268,24502,24559,24614,24660,24729,24790,24859,24903,24949,25008,25061,25101,25164,25202,25255,25311,25368,25438,25488,25527,25593,25804,25849,25919,25992,26057,26126,26199,26269,26346,26415,26460,26505,26550,26919,26966,27017,27065,27123,27180,27247,27301,27356,27403,27460,27514,27558,27610,27659,27728,27853,28102,28314,28371,28437,28498,28560,28625,28668,28735,28797,28851,28915,28960,29037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "132,153,154,155", "startColumns": "4,4,4,4", "startOffsets": "11697,13116,13222,13329", "endColumns": "99,105,106,105", "endOffsets": "11792,13217,13324,13430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "65,66,67,68,69,70,71,330", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4923,5020,5122,5220,5317,5419,5525,26776", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "5015,5117,5215,5312,5414,5520,5631,26872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,996,1084,1155,1229,1304,1376,1454,1522", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,991,1079,1150,1224,1299,1371,1449,1517,1635"}, "to": {"startLines": "86,87,141,142,146,186,187,267,272,289,290,313,327,328,329,347,349,350", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6565,6658,12342,12437,12686,15319,15402,22962,23305,24273,24353,25678,26555,26629,26704,27733,27858,27926", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "6653,6737,12432,12532,12765,15397,15497,23045,23384,24348,24436,25744,26624,26699,26771,27806,27921,28039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "111,112,113,114,115,116,117,118,120,121,122,123,124,125,126,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9267,9377,9532,9668,9773,9920,10050,10177,10430,10602,10709,10866,11000,11145,11312,11433,11497", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "9372,9527,9663,9768,9915,10045,10172,10278,10597,10704,10861,10995,11140,11307,11369,11492,11572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16600,16715,16828,16946,17061,17157,17253,17366,17499,17621,17761,17846,17944,18033,18130,18245,18366,18469,18606,18742,18864,19035,19153,19269,19387,19502,19592,19690,19814,19943,20044,20146,20252,20388,20528,20640,20742,20818,20915,21013,21123,21209,21294,21411,21491,21575,21675,21775,21871,21966,22054,22160,22260,22359,22480,22560,22667", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "16710,16823,16941,17056,17152,17248,17361,17494,17616,17756,17841,17939,18028,18125,18240,18361,18464,18601,18737,18859,19030,19148,19264,19382,19497,19587,19685,19809,19938,20039,20141,20247,20383,20523,20635,20737,20813,20910,21008,21118,21204,21289,21406,21486,21570,21670,21770,21866,21961,22049,22155,22255,22354,22475,22555,22662,22755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,361,497,610,695,781,859,947,1039,1152,1262,1355,1448,1555,1675,1757,1840,2017,2113,2222,2338,2441", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "219,356,492,605,690,776,854,942,1034,1147,1257,1350,1443,1550,1670,1752,1835,2012,2108,2217,2333,2436,2575"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6742,6911,7048,7184,7297,7382,7468,7546,7634,7726,7839,7949,8042,8135,8242,8362,8444,8527,8704,8800,8909,9025,9128", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "6906,7043,7179,7292,7377,7463,7541,7629,7721,7834,7944,8037,8130,8237,8357,8439,8522,8699,8795,8904,9020,9123,9262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "10283", "endColumns": "146", "endOffsets": "10425"}}]}]}