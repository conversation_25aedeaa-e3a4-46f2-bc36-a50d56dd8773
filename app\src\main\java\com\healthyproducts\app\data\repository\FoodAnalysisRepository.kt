package com.healthyproducts.app.data.repository

import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.healthyproducts.app.data.model.Allergen
import com.healthyproducts.app.data.model.Fat
import com.healthyproducts.app.data.model.FoodCertificate
import com.healthyproducts.app.data.model.Sugar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gıda analizi verileri için repository
 */
@Singleton
class FoodAnalysisRepository @Inject constructor() {
    private val firestore = FirebaseFirestore.getInstance()

    /**
     * Tüm alerjenleri getirir
     */
    suspend fun getAllAllergens(): Result<List<Allergen>> = withContext(Dispatchers.IO) {
        try {
            val snapshot = firestore.collection("allergens").get().await()
            val allergens = snapshot.documents.mapNotNull { it.toObject(Allergen::class.java) }
            Log.d("FoodAnalysisRepository", "Retrieved ${allergens.size} allergens")
            Result.success(allergens)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting allergens", e)
            Result.failure(e)
        }
    }

    /**
     * Belirli bir alerjeni getirir
     */
    suspend fun getAllergenByCode(code: String): Result<Allergen?> = withContext(Dispatchers.IO) {
        try {
            val snapshot = firestore.collection("allergens")
                .whereEqualTo("id", code)
                .get()
                .await()

            val allergen = snapshot.documents.firstOrNull()?.toObject(Allergen::class.java)

            // Eğer allergen bulunamazsa, doğrudan belge ID'si ile deneyelim
            if (allergen == null) {
                val document = firestore.collection("allergens").document(code).get().await()
                val allergenFromDoc = document.toObject(Allergen::class.java)
                Log.d("FoodAnalysisRepository", "Retrieved allergen by document ID: $code")
                return@withContext Result.success(allergenFromDoc)
            }

            Log.d("FoodAnalysisRepository", "Retrieved allergen: $code")
            Result.success(allergen)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting allergen: $code", e)
            Result.failure(e)
        }
    }

    /**
     * Tüm yağları getirir
     */
    suspend fun getAllFats(): Result<List<Fat>> = withContext(Dispatchers.IO) {
        try {
            val snapshot = firestore.collection("fats").get().await()
            val fats = snapshot.documents.mapNotNull { it.toObject(Fat::class.java) }
            Log.d("FoodAnalysisRepository", "Retrieved ${fats.size} fats")
            Result.success(fats)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting fats", e)
            Result.failure(e)
        }
    }

    /**
     * Belirli bir yağı getirir
     */
    suspend fun getFatByCode(code: String): Result<Fat?> = withContext(Dispatchers.IO) {
        try {
            val document = firestore.collection("fats").document(code).get().await()
            val fat = document.toObject(Fat::class.java)
            Log.d("FoodAnalysisRepository", "Retrieved fat: $code")
            Result.success(fat)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting fat: $code", e)
            Result.failure(e)
        }
    }

    /**
     * Tüm şekerleri getirir
     */
    suspend fun getAllSugars(): Result<List<Sugar>> = withContext(Dispatchers.IO) {
        try {
            val snapshot = firestore.collection("sugars").get().await()
            val sugars = snapshot.documents.mapNotNull { it.toObject(Sugar::class.java) }
            Log.d("FoodAnalysisRepository", "Retrieved ${sugars.size} sugars")
            Result.success(sugars)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting sugars", e)
            Result.failure(e)
        }
    }

    /**
     * Belirli bir şekeri getirir
     */
    suspend fun getSugarByCode(code: String): Result<Sugar?> = withContext(Dispatchers.IO) {
        try {
            val document = firestore.collection("sugars").document(code).get().await()
            val sugar = document.toObject(Sugar::class.java)
            Log.d("FoodAnalysisRepository", "Retrieved sugar: $code")
            Result.success(sugar)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting sugar: $code", e)
            Result.failure(e)
        }
    }

    /**
     * Tüm gıda sertifikalarını getirir
     */
    suspend fun getAllFoodCertificates(): Result<List<FoodCertificate>> = withContext(Dispatchers.IO) {
        try {
            val snapshot = firestore.collection("food_certificates").get().await()
            val certificates = snapshot.documents.mapNotNull { it.toObject(FoodCertificate::class.java) }
            Log.d("FoodAnalysisRepository", "Retrieved ${certificates.size} food certificates")
            Result.success(certificates)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting food certificates", e)
            Result.failure(e)
        }
    }

    /**
     * Belirli bir gıda sertifikasını getirir
     */
    suspend fun getFoodCertificateByCode(code: String): Result<FoodCertificate?> = withContext(Dispatchers.IO) {
        try {
            val document = firestore.collection("food_certificates").document(code).get().await()
            val certificate = document.toObject(FoodCertificate::class.java)
            Log.d("FoodAnalysisRepository", "Retrieved food certificate: $code")
            Result.success(certificate)
        } catch (e: Exception) {
            Log.e("FoodAnalysisRepository", "Error getting food certificate: $code", e)
            Result.failure(e)
        }
    }
}
