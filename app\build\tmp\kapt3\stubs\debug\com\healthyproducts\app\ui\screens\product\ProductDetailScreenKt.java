package com.healthyproducts.app.ui.screens.product;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000$\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0018\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0007\u001a \u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\rH\u0007\u00a8\u0006\u000e"}, d2 = {"IngredientItem", "", "ingredient", "Lcom/healthyproducts/app/model/Ingredient;", "ProductDetailScreen", "productId", "", "navController", "Landroidx/navigation/NavController;", "StatusCard", "title", "status", "isPositive", "", "app_debug"})
public final class ProductDetailScreenKt {
    
    /**
     * Ürün detay ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProductDetailScreen(@org.jetbrains.annotations.NotNull()
    java.lang.String productId, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    /**
     * Durum kartı bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void StatusCard(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String status, boolean isPositive) {
    }
    
    /**
     * İçerik öğesi bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void IngredientItem(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.model.Ingredient ingredient) {
    }
}