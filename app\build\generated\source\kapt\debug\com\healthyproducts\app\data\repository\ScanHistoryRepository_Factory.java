package com.healthyproducts.app.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ScanHistoryRepository_Factory implements Factory<ScanHistoryRepository> {
  @Override
  public ScanHistoryRepository get() {
    return newInstance();
  }

  public static ScanHistoryRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ScanHistoryRepository newInstance() {
    return new ScanHistoryRepository();
  }

  private static final class InstanceHolder {
    private static final ScanHistoryRepository_Factory INSTANCE = new ScanHistoryRepository_Factory();
  }
}
