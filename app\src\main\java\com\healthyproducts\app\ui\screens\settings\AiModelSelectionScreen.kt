package com.healthyproducts.app.ui.screens.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.AiModel
import com.healthyproducts.app.model.UserPreferences
import com.healthyproducts.app.ui.viewmodel.UserViewModel
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * AI model seçim ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AiModelSelectionScreen(
    navController: NavController,
    userViewModel: UserViewModel = hiltViewModel()
) {
    val userPreferences by userViewModel.userPreferences.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.ai_model_selection)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Geri"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        AiModelSelectionContent(
            modifier = Modifier.padding(innerPadding),
            selectedModel = userPreferences.aiModel,
            onModelSelected = { model ->
                // Seçilen modeli logla
                android.util.Log.d("AiModelSelection", "Selected model: ${model.name}")

                // Kullanıcı tercihlerini güncelle
                val updatedPreferences = userPreferences.copy(aiModel = model.name)
                android.util.Log.d("AiModelSelection", "Updating preferences with aiModel=${updatedPreferences.aiModel}")

                // Önce yerel değişkeni güncelle
                userViewModel.updateUserPreferences(updatedPreferences)

                // Kısa bir gecikme ekle (UI'ın güncellenmesi için)
                kotlinx.coroutines.MainScope().launch {
                    kotlinx.coroutines.delay(300)
                    // Ayarlar ekranına geri dön
                    navController.popBackStack()
                }
            }
        )
    }
}

/**
 * AI model seçim içeriği
 */
@Composable
fun AiModelSelectionContent(
    modifier: Modifier = Modifier,
    selectedModel: String,
    onModelSelected: (AiModel) -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "AI modelini seçin",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Tüm AI modelleri için seçenekler oluştur
        AiModel.values().forEach { model ->
            AiModelOption(
                model = model,
                isSelected = model.name == selectedModel,
                onSelected = { onModelSelected(model) }
            )
        }
    }
}

/**
 * AI model seçeneği
 */
@Composable
fun AiModelOption(
    model: AiModel,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    androidx.compose.material3.ListItem(
        headlineContent = { Text(text = model.displayName) },
        leadingContent = {
            RadioButton(
                selected = isSelected,
                onClick = onSelected
            )
        },
        modifier = Modifier.fillMaxWidth()
    )
}
