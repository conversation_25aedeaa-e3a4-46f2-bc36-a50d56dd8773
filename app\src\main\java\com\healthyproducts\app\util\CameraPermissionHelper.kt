package com.healthyproducts.app.util

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.core.content.ContextCompat

/**
 * Kamera izni durumunu temsil eden enum sınıfı
 */
enum class CameraPermissionState {
    GRANTED,        // İzin verildi
    DENIED,         // İzin reddedildi
    PERMANENTLY_DENIED  // İzin kalıcı olarak reddedildi
}

/**
 * Kamera izni yardımcı sınıfı
 */
object CameraPermissionHelper {
    
    /**
     * Kamera izni durumunu kontrol eder
     */
    fun checkCameraPermission(context: Context): CameraPermissionState {
        val permission = Manifest.permission.CAMERA
        val permissionState = ContextCompat.checkSelfPermission(context, permission)
        
        return when {
            permissionState == PackageManager.PERMISSION_GRANTED -> CameraPermissionState.GRANTED
            else -> CameraPermissionState.DENIED
        }
    }
    
    /**
     * Uygulama ayarları sayfasını açar
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
        }
        context.startActivity(intent)
    }
}

/**
 * Kamera izni için Composable fonksiyon
 */
@Composable
fun rememberCameraPermissionState(
    onPermissionResult: (CameraPermissionState) -> Unit
): CameraPermissionState {
    val context = androidx.compose.ui.platform.LocalContext.current
    var permissionState by remember { mutableStateOf(CameraPermissionHelper.checkCameraPermission(context)) }
    
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = { isGranted ->
            permissionState = if (isGranted) {
                CameraPermissionState.GRANTED
            } else {
                CameraPermissionState.PERMANENTLY_DENIED
            }
            onPermissionResult(permissionState)
        }
    )
    
    DisposableEffect(Unit) {
        if (permissionState == CameraPermissionState.DENIED) {
            permissionLauncher.launch(Manifest.permission.CAMERA)
        } else {
            onPermissionResult(permissionState)
        }
        
        onDispose { }
    }
    
    return permissionState
}
