package com.healthyproducts.app.di;

import com.google.firebase.auth.FirebaseAuth;
import com.healthyproducts.app.data.repository.FirebaseAuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideFirebaseAuthRepositoryFactory implements Factory<FirebaseAuthRepository> {
  private final Provider<FirebaseAuth> authProvider;

  public RepositoryModule_ProvideFirebaseAuthRepositoryFactory(
      Provider<FirebaseAuth> authProvider) {
    this.authProvider = authProvider;
  }

  @Override
  public FirebaseAuthRepository get() {
    return provideFirebaseAuthRepository(authProvider.get());
  }

  public static RepositoryModule_ProvideFirebaseAuthRepositoryFactory create(
      Provider<FirebaseAuth> authProvider) {
    return new RepositoryModule_ProvideFirebaseAuthRepositoryFactory(authProvider);
  }

  public static FirebaseAuthRepository provideFirebaseAuthRepository(FirebaseAuth auth) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideFirebaseAuthRepository(auth));
  }
}
