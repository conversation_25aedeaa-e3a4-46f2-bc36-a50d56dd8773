package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0016\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u0007\u00a8\u0006\u0007"}, d2 = {"RiskLevelIndicator", "", "title", "", "level", "", "maxLevel", "app_debug"})
public final class RiskLevelIndicatorKt {
    
    /**
     * Risk seviyesi göstergesi
     */
    @androidx.compose.runtime.Composable()
    public static final void RiskLevelIndicator(@org.jetbrains.annotations.NotNull()
    java.lang.String title, int level, int maxLevel) {
    }
}