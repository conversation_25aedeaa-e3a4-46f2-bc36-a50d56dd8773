package com.healthyproducts.app.data.service

import android.util.Log
import com.healthyproducts.app.data.model.Additive
import com.healthyproducts.app.data.model.Allergen
import com.healthyproducts.app.data.model.Fat
import com.healthyproducts.app.data.model.FoodCertificate
import com.healthyproducts.app.data.model.Intolerance
import com.healthyproducts.app.data.model.Preservative
import com.healthyproducts.app.data.model.Sugar
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.repository.AdditiveRepository
import com.healthyproducts.app.data.repository.AllergenRepository
import com.healthyproducts.app.data.repository.FatRepository
import com.healthyproducts.app.data.repository.FoodCertificateRepository
import com.healthyproducts.app.data.repository.IntoleranceRepository
import com.healthyproducts.app.data.repository.PreservativeRepository
import com.healthyproducts.app.data.repository.SugarRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gıda analiz sonuçlarını temsil eden veri sınıfı
 */
data class FoodAnalysisResult(
    val allergenWarnings: List<AllergenWarning> = emptyList(),
    val sugarWarnings: List<SugarWarning> = emptyList(),
    val fatWarnings: List<FatWarning> = emptyList(),
    val intoleranceWarnings: List<IntoleranceWarning> = emptyList(),
    val preservativeWarnings: List<PreservativeWarning> = emptyList(),
    val additiveWarnings: List<AdditiveWarning> = emptyList(),
    val certificateVerifications: List<CertificateVerification> = emptyList(),
    val overallRiskLevel: Int = 0 // 0-3 arası risk seviyesi (0: Düşük, 3: Yüksek)
)

/**
 * Alerjen uyarısını temsil eden veri sınıfı
 */
data class AllergenWarning(
    val allergen: Allergen,
    val foundInIngredient: String,
    val riskLevel: Int // 1-3 arası risk seviyesi
)

/**
 * Şeker uyarısını temsil eden veri sınıfı
 */
data class SugarWarning(
    val sugar: Sugar,
    val foundInIngredient: String,
    val riskLevel: Int // 1-3 arası risk seviyesi
)

/**
 * Yağ uyarısını temsil eden veri sınıfı
 */
data class FatWarning(
    val fat: Fat,
    val foundInIngredient: String,
    val riskLevel: Int // 1-3 arası risk seviyesi
)

/**
 * İntolerans uyarısını temsil eden veri sınıfı
 */
data class IntoleranceWarning(
    val intolerance: Intolerance,
    val foundInIngredient: String,
    val riskLevel: Int // 1-3 arası risk seviyesi
)

/**
 * Koruyucu uyarısını temsil eden veri sınıfı
 */
data class PreservativeWarning(
    val preservative: Preservative,
    val foundInIngredient: String,
    val riskLevel: Int // 1-3 arası risk seviyesi
)

/**
 * Katkı maddesi uyarısını temsil eden veri sınıfı
 */
data class AdditiveWarning(
    val additive: Additive,
    val foundInIngredient: String,
    val riskLevel: Int // 1-3 arası risk seviyesi
)

/**
 * Sertifika doğrulamasını temsil eden veri sınıfı
 */
data class CertificateVerification(
    val certificate: FoodCertificate,
    val isVerified: Boolean,
    val verificationMessage: String
)

/**
 * Gıda analiz servisi
 */
@Singleton
class FoodAnalysisService @Inject constructor(
    private val additiveRepository: AdditiveRepository,
    private val allergenRepository: AllergenRepository,
    private val sugarRepository: SugarRepository,
    private val fatRepository: FatRepository,
    private val intoleranceRepository: IntoleranceRepository,
    private val preservativeRepository: PreservativeRepository,
    private val certificateRepository: FoodCertificateRepository
) {
    companion object {
        private const val TAG = "FoodAnalysisService"
    }

    /**
     * İçerik listesini analiz eder ve risk değerlendirmesi yapar
     */
    suspend fun analyzeIngredients(
        ingredients: List<String>,
        language: SupportedLanguage
    ): FoodAnalysisResult {
        Log.d(TAG, "Analyzing ingredients: ${ingredients.joinToString(", ")}")

        // Veritabanından gerekli verileri getir
        val additives = additiveRepository.getAllAdditives().getOrDefault(emptyList())
        val allergens = allergenRepository.getAllAllergens().getOrDefault(emptyList())
        val sugars = sugarRepository.getAllSugars().getOrDefault(emptyList())
        val fats = fatRepository.getAllFats().getOrDefault(emptyList())
        val intolerances = intoleranceRepository.getAllIntolerances().getOrDefault(emptyList())
        val preservatives = preservativeRepository.getAllPreservatives().getOrDefault(emptyList())
        val certificates = certificateRepository.getAllCertificates().getOrDefault(emptyList())

        // Uyarıları topla
        val allergenWarnings = mutableListOf<AllergenWarning>()
        val sugarWarnings = mutableListOf<SugarWarning>()
        val fatWarnings = mutableListOf<FatWarning>()
        val intoleranceWarnings = mutableListOf<IntoleranceWarning>()
        val preservativeWarnings = mutableListOf<PreservativeWarning>()
        val additiveWarnings = mutableListOf<AdditiveWarning>()
        val certificateVerifications = mutableListOf<CertificateVerification>()

        // Her bir içeriği analiz et
        ingredients.forEach { ingredient ->
            // İçeriği küçük harfe çevir ve boşlukları temizle
            val normalizedIngredient = ingredient.trim().lowercase()

            // Alerjenleri kontrol et
            allergens.forEach { allergen ->
                val allergenNames = allergen.getHiddenNames(language) + listOf(
                    allergen.getName(language).lowercase()
                )
                if (allergenNames.any { normalizedIngredient.contains(it.lowercase()) }) {
                    allergenWarnings.add(
                        AllergenWarning(
                            allergen = allergen,
                            foundInIngredient = ingredient,
                            riskLevel = allergen.riskLevel
                        )
                    )
                }
            }

            // Şekerleri kontrol et
            sugars.forEach { sugar ->
                val sugarLabels = sugar.getLabels(language) + listOf(
                    sugar.getName(language).lowercase()
                )
                if (sugarLabels.any { normalizedIngredient.contains(it.lowercase()) }) {
                    sugarWarnings.add(
                        SugarWarning(
                            sugar = sugar,
                            foundInIngredient = ingredient,
                            riskLevel = sugar.riskLevel
                        )
                    )
                }
            }

            // Yağları kontrol et
            fats.forEach { fat ->
                val fatLabels = fat.getLabels(language) + listOf(
                    fat.getName(language).lowercase()
                )
                if (fatLabels.any { normalizedIngredient.contains(it.lowercase()) }) {
                    fatWarnings.add(
                        FatWarning(
                            fat = fat,
                            foundInIngredient = ingredient,
                            riskLevel = fat.riskLevel
                        )
                    )
                }
            }

            // İntoleransları kontrol et
            intolerances.forEach { intolerance ->
                val intoleranceIngredients = intolerance.getRelatedIngredients(language)
                if (intoleranceIngredients.any { normalizedIngredient.contains(it.lowercase()) }) {
                    intoleranceWarnings.add(
                        IntoleranceWarning(
                            intolerance = intolerance,
                            foundInIngredient = ingredient,
                            riskLevel = intolerance.riskLevel
                        )
                    )
                }
            }

            // Koruyucuları kontrol et
            preservatives.forEach { preservative ->
                if (normalizedIngredient.contains(preservative.getName(language).lowercase())) {
                    preservativeWarnings.add(
                        PreservativeWarning(
                            preservative = preservative,
                            foundInIngredient = ingredient,
                            riskLevel = preservative.riskLevel
                        )
                    )
                }
            }

            // Katkı maddelerini kontrol et
            additives.forEach { additive ->
                // E-kodunu kontrol et (örn. E100, E202)
                if (normalizedIngredient.contains(additive.code.lowercase())) {
                    additiveWarnings.add(
                        AdditiveWarning(
                            additive = additive,
                            foundInIngredient = ingredient,
                            riskLevel = additive.harmfulLevel
                        )
                    )
                }
                // Katkı maddesinin adını kontrol et
                else if (normalizedIngredient.contains(additive.getName(language).lowercase())) {
                    additiveWarnings.add(
                        AdditiveWarning(
                            additive = additive,
                            foundInIngredient = ingredient,
                            riskLevel = additive.harmfulLevel
                        )
                    )
                }
            }
        }

        // Sertifikaları doğrula
        certificates.forEach { certificate ->
            // Sertifika doğrulaması için daha karmaşık bir mantık gerekebilir
            // Şimdilik basit bir doğrulama yapıyoruz
            val isVerified = false // Varsayılan olarak doğrulanmamış
            val verificationMessage = when (language) {
                SupportedLanguage.TURKISH -> "Bu ürün için ${certificate.getName(language)} doğrulaması yapılamadı."
                SupportedLanguage.ENGLISH -> "${certificate.getName(language)} verification could not be performed for this product."
            }
            certificateVerifications.add(
                CertificateVerification(
                    certificate = certificate,
                    isVerified = isVerified,
                    verificationMessage = verificationMessage
                )
            )
        }

        // Genel risk seviyesini hesapla
        val overallRiskLevel = calculateOverallRiskLevel(
            allergenWarnings,
            sugarWarnings,
            fatWarnings,
            intoleranceWarnings,
            preservativeWarnings,
            additiveWarnings
        )

        return FoodAnalysisResult(
            allergenWarnings = allergenWarnings,
            sugarWarnings = sugarWarnings,
            fatWarnings = fatWarnings,
            intoleranceWarnings = intoleranceWarnings,
            preservativeWarnings = preservativeWarnings,
            additiveWarnings = additiveWarnings,
            certificateVerifications = certificateVerifications,
            overallRiskLevel = overallRiskLevel
        )
    }

    /**
     * Genel risk seviyesini hesaplar
     */
    private fun calculateOverallRiskLevel(
        allergenWarnings: List<AllergenWarning>,
        sugarWarnings: List<SugarWarning>,
        fatWarnings: List<FatWarning>,
        intoleranceWarnings: List<IntoleranceWarning>,
        preservativeWarnings: List<PreservativeWarning>,
        additiveWarnings: List<AdditiveWarning>
    ): Int {
        // En yüksek risk seviyesini bul
        val maxRiskLevel = maxOf(
            allergenWarnings.maxOfOrNull { it.riskLevel } ?: 0,
            sugarWarnings.maxOfOrNull { it.riskLevel } ?: 0,
            fatWarnings.maxOfOrNull { it.riskLevel } ?: 0,
            intoleranceWarnings.maxOfOrNull { it.riskLevel } ?: 0,
            preservativeWarnings.maxOfOrNull { it.riskLevel } ?: 0,
            additiveWarnings.maxOfOrNull { it.riskLevel } ?: 0
        )

        // Uyarı sayısına göre risk seviyesini ayarla
        val totalWarnings = allergenWarnings.size + sugarWarnings.size + fatWarnings.size +
                intoleranceWarnings.size + preservativeWarnings.size + additiveWarnings.size

        return when {
            totalWarnings == 0 -> 0 // Hiç uyarı yoksa risk yok
            totalWarnings <= 2 && maxRiskLevel <= 1 -> 1 // Az sayıda düşük riskli uyarı
            totalWarnings <= 5 && maxRiskLevel <= 2 -> 2 // Orta sayıda orta riskli uyarı
            else -> 3 // Çok sayıda veya yüksek riskli uyarı
        }
    }
}
