{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,106", "endOffsets": "151,252,363,470"}, "to": {"startLines": "56,60,61,62", "startColumns": "4,4,4,4", "startOffsets": "5969,6345,6446,6557", "endColumns": "100,100,110,106", "endOffsets": "6065,6441,6552,6659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,13518", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,13599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4722", "endColumns": "151", "endOffsets": "4869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,86", "endOffsets": "135,222"}, "to": {"startLines": "135,136", "startColumns": "4,4", "startOffsets": "14263,14348", "endColumns": "84,86", "endOffsets": "14343,14430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,282,372,469,557,638,731,819,905,988,1073,1148,1226,1300,1373,1448,1514", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,77,73,72,74,65,116", "endOffsets": "194,277,367,464,552,633,726,814,900,983,1068,1143,1221,1295,1368,1443,1509,1626"}, "to": {"startLines": "36,37,57,58,59,63,64,122,123,124,125,127,128,129,130,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3553,3647,6070,6160,6257,6664,6745,13176,13264,13350,13433,13604,13679,13757,13831,14005,14080,14146", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,77,73,72,74,65,116", "endOffsets": "3642,3725,6155,6252,6340,6740,6833,13259,13345,13428,13513,13674,13752,13826,13899,14075,14141,14258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values-ur\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,457,579,686,822,945,1053,1153,1301,1407,1575,1699,1841,2006,2065,2128", "endColumns": "103,159,121,106,135,122,107,99,147,105,167,123,141,164,58,62,83", "endOffsets": "296,456,578,685,821,944,1052,1152,1300,1406,1574,1698,1840,2005,2064,2127,2211"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3730,3838,4002,4128,4239,4379,4506,4618,4874,5026,5136,5308,5436,5582,5751,5814,5881", "endColumns": "107,163,125,110,139,126,111,103,151,109,171,127,145,168,62,66,87", "endOffsets": "3833,3997,4123,4234,4374,4501,4613,4717,5021,5131,5303,5431,5577,5746,5809,5876,5964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "29,30,31,32,33,34,35,131", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2832,2930,3032,3134,3238,3341,3439,13904", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "2925,3027,3129,3233,3336,3434,3548,14000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,295,411,529,627,724,839,974,1098,1238,1323,1427,1523,1623,1740,1870,1979,2123,2266,2395,2593,2718,2837,2960,3098,3195,3290,3414,3538,3639,3744,3850,3993,4142,4248,4352,4428,4524,4621,4733,4823,4914,5029,5109,5194,5297,5403,5500,5603,5688,5794,5893,5996,6117,6197,6299", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "168,290,406,524,622,719,834,969,1093,1233,1318,1422,1518,1618,1735,1865,1974,2118,2261,2390,2588,2713,2832,2955,3093,3190,3285,3409,3533,3634,3739,3845,3988,4137,4243,4347,4423,4519,4616,4728,4818,4909,5024,5104,5189,5292,5398,5495,5598,5683,5789,5888,5991,6112,6192,6294,6388"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6838,6956,7078,7194,7312,7410,7507,7622,7757,7881,8021,8106,8210,8306,8406,8523,8653,8762,8906,9049,9178,9376,9501,9620,9743,9881,9978,10073,10197,10321,10422,10527,10633,10776,10925,11031,11135,11211,11307,11404,11516,11606,11697,11812,11892,11977,12080,12186,12283,12386,12471,12577,12676,12779,12900,12980,13082", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "6951,7073,7189,7307,7405,7502,7617,7752,7876,8016,8101,8205,8301,8401,8518,8648,8757,8901,9044,9173,9371,9496,9615,9738,9876,9973,10068,10192,10316,10417,10522,10628,10771,10920,11026,11130,11206,11302,11399,11511,11601,11692,11807,11887,11972,12075,12181,12278,12381,12466,12572,12671,12774,12895,12975,13077,13171"}}]}]}