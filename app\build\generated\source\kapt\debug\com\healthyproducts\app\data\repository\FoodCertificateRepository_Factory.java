package com.healthyproducts.app.data.repository;

import android.content.Context;
import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FoodCertificateRepository_Factory implements Factory<FoodCertificateRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  private final Provider<Context> contextProvider;

  public FoodCertificateRepository_Factory(Provider<FirebaseFirestore> firestoreProvider,
      Provider<Context> contextProvider) {
    this.firestoreProvider = firestoreProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public FoodCertificateRepository get() {
    return newInstance(firestoreProvider.get(), contextProvider.get());
  }

  public static FoodCertificateRepository_Factory create(
      Provider<FirebaseFirestore> firestoreProvider, Provider<Context> contextProvider) {
    return new FoodCertificateRepository_Factory(firestoreProvider, contextProvider);
  }

  public static FoodCertificateRepository newInstance(FirebaseFirestore firestore,
      Context context) {
    return new FoodCertificateRepository(firestore, context);
  }
}
