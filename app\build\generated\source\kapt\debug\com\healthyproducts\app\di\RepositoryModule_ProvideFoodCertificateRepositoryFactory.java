package com.healthyproducts.app.di;

import com.google.firebase.firestore.FirebaseFirestore;
import com.healthyproducts.app.data.repository.FoodCertificateRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideFoodCertificateRepositoryFactory implements Factory<FoodCertificateRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public RepositoryModule_ProvideFoodCertificateRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public FoodCertificateRepository get() {
    return provideFoodCertificateRepository(firestoreProvider.get());
  }

  public static RepositoryModule_ProvideFoodCertificateRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new RepositoryModule_ProvideFoodCertificateRepositoryFactory(firestoreProvider);
  }

  public static FoodCertificateRepository provideFoodCertificateRepository(
      FirebaseFirestore firestore) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideFoodCertificateRepository(firestore));
  }
}
