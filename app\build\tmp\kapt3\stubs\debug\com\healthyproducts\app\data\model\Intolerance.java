package com.healthyproducts.app.data.model;

/**
 * G<PERSON>da intoleransını temsil eden veri sınıfı (Gluten/Laktoz Hassasiyeti)
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b6\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u00a9\u0001\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0004\u0012\b\b\u0002\u0010\b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\t\u001a\u00020\u0004\u0012\b\b\u0002\u0010\n\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u0012\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0014J\t\u00106\u001a\u00020\u0004H\u00c6\u0003J\t\u00107\u001a\u00020\u000fH\u00c6\u0003J\u000f\u00108\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\u000f\u00109\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\t\u0010:\u001a\u00020\u0004H\u00c6\u0003J\t\u0010;\u001a\u00020\u0004H\u00c6\u0003J\t\u0010<\u001a\u00020\u0004H\u00c6\u0003J\t\u0010=\u001a\u00020\u0004H\u00c6\u0003J\t\u0010>\u001a\u00020\u0004H\u00c6\u0003J\t\u0010?\u001a\u00020\u0004H\u00c6\u0003J\t\u0010@\u001a\u00020\u0004H\u00c6\u0003J\t\u0010A\u001a\u00020\u0004H\u00c6\u0003J\u000f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\u000f\u0010C\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\u00ad\u0001\u0010D\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\u00042\b\b\u0002\u0010\t\u001a\u00020\u00042\b\b\u0002\u0010\n\u001a\u00020\u00042\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\b\b\u0002\u0010\u0012\u001a\u00020\u00042\b\b\u0002\u0010\u0013\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010E\u001a\u00020F2\b\u0010G\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010H\u001a\u00020\u00042\u0006\u0010I\u001a\u00020JJ\u0014\u0010K\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u0006\u0010I\u001a\u00020JJ\u000e\u0010L\u001a\u00020\u00042\u0006\u0010I\u001a\u00020JJ\u000e\u0010M\u001a\u00020\u00042\u0006\u0010I\u001a\u00020JJ\u0014\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u0006\u0010I\u001a\u00020JJ\t\u0010O\u001a\u00020\u000fH\u00d6\u0001J\t\u0010P\u001a\u00020\u0004H\u00d6\u0001R\u001e\u0010\n\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u0016\"\u0004\b\u0017\u0010\u0018R\u001e\u0010\t\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u0016\"\u0004\b\u001a\u0010\u0018R\u0016\u0010\u0003\u001a\u00020\u00048\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0016R\u001e\u0010\u0005\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u0010\u0016\"\u0004\b\u001d\u0010\u0018R$\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\f8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!R$\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\f8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010\u001f\"\u0004\b#\u0010!R\u001e\u0010\u0007\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010\u0016\"\u0004\b%\u0010\u0018R\u001e\u0010\u0006\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\u0016\"\u0004\b\'\u0010\u0018R\u001e\u0010\u0013\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010\u0016\"\u0004\b)\u0010\u0018R\u001e\u0010\u0012\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b*\u0010\u0016\"\u0004\b+\u0010\u0018R$\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\f8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010\u001f\"\u0004\b-\u0010!R$\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\f8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010\u001f\"\u0004\b/\u0010!R\u001e\u0010\u000e\u001a\u00020\u000f8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u00101\"\u0004\b2\u00103R\u001e\u0010\b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u0010\u0016\"\u0004\b5\u0010\u0018\u00a8\u0006Q"}, d2 = {"Lcom/healthyproducts/app/data/model/Intolerance;", "", "()V", "id", "", "intoleranceId", "nameTr", "nameEn", "symbol", "descriptionTr", "descriptionEn", "relatedIngredientsTr", "", "relatedIngredientsEn", "riskLevel", "", "labelsToLookForTr", "labelsToLookForEn", "notesTr", "notesEn", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;ILjava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V", "getDescriptionEn", "()Ljava/lang/String;", "setDescriptionEn", "(Ljava/lang/String;)V", "getDescriptionTr", "setDescriptionTr", "getId", "getIntoleranceId", "setIntoleranceId", "getLabelsToLookForEn", "()Ljava/util/List;", "setLabelsToLookForEn", "(Ljava/util/List;)V", "getLabelsToLookForTr", "setLabelsToLookForTr", "getNameEn", "setNameEn", "getNameTr", "setNameTr", "getNotesEn", "setNotesEn", "getNotesTr", "setNotesTr", "getRelatedIngredientsEn", "setRelatedIngredientsEn", "getRelatedIngredientsTr", "setRelatedIngredientsTr", "getRiskLevel", "()I", "setRiskLevel", "(I)V", "getSymbol", "setSymbol", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "getDescription", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "getLabelsToLookFor", "getName", "getNotes", "getRelatedIngredients", "hashCode", "toString", "app_debug"})
public final class Intolerance {
    @com.google.firebase.firestore.DocumentId()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String intoleranceId;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String symbol;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionEn;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> relatedIngredientsTr;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> relatedIngredientsEn;
    private int riskLevel;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> labelsToLookForTr;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> labelsToLookForEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesEn;
    
    public Intolerance(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String intoleranceId, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> relatedIngredientsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> relatedIngredientsEn, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsToLookForTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsToLookForEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIntoleranceId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    public final void setIntoleranceId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    public final void setNameTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    public final void setNameEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSymbol() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    public final void setSymbol(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    public final void setDescriptionTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    public final void setDescriptionEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "related_ingredients_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRelatedIngredientsTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "related_ingredients_tr")
    public final void setRelatedIngredientsTr(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "related_ingredients_en")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRelatedIngredientsEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "related_ingredients_en")
    public final void setRelatedIngredientsEn(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final int getRiskLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final void setRiskLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_to_look_for_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsToLookForTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_to_look_for_tr")
    public final void setLabelsToLookForTr(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_to_look_for_en")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsToLookForEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_to_look_for_en")
    public final void setLabelsToLookForEn(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    public final void setNotesTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    public final void setNotesEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public Intolerance() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRelatedIngredients(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsToLookFor(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotes(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final int component10() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.model.Intolerance copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String intoleranceId, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> relatedIngredientsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> relatedIngredientsEn, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsToLookForTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsToLookForEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}