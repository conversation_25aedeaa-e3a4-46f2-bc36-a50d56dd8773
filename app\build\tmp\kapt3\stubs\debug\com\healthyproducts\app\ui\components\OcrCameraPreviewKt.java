package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000N\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a+\u0010\u0000\u001a\u00020\u00012!\u0010\u0002\u001a\u001d\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0005\u0012\b\b\u0006\u0012\u0004\b\b(\u0007\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u001e\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e\u001a.\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0013\u001a\u000e\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0007\u001a\u00020\u0018\u001a\u0016\u0010\u0019\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u001a\u001a\u00020\u001b\u001a\n\u0010\u001c\u001a\u00020\u0010*\u00020\u001d\u00a8\u0006\u001e"}, d2 = {"OcrCameraPreview", "", "onTextDetected", "Lkotlin/Function1;", "Lcom/google/mlkit/vision/text/Text;", "Lkotlin/ParameterName;", "name", "text", "captureImage", "imageCapture", "Landroidx/camera/core/ImageCapture;", "executor", "Ljava/util/concurrent/Executor;", "callback", "Landroidx/camera/core/ImageCapture$OnImageCapturedCallback;", "cropBitmap", "Landroid/graphics/Bitmap;", "bitmap", "x", "", "y", "width", "height", "extractIngredientsSection", "", "rotateBitmap", "degrees", "", "toBitmap", "Landroidx/camera/core/ImageProxy;", "app_debug"})
public final class OcrCameraPreviewKt {
    
    /**
     * OCR kamera önizleme bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void OcrCameraPreview(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.google.mlkit.vision.text.Text, kotlin.Unit> onTextDetected) {
    }
    
    /**
     * Görüntü yakalama ve analiz fonksiyonu
     */
    public static final void captureImage(@org.jetbrains.annotations.NotNull()
    androidx.camera.core.ImageCapture imageCapture, @org.jetbrains.annotations.NotNull()
    java.util.concurrent.Executor executor, @org.jetbrains.annotations.NotNull()
    androidx.camera.core.ImageCapture.OnImageCapturedCallback callback) {
    }
    
    /**
     * ImageProxy'yi Bitmap'e dönüştürme
     */
    @org.jetbrains.annotations.NotNull()
    public static final android.graphics.Bitmap toBitmap(@org.jetbrains.annotations.NotNull()
    androidx.camera.core.ImageProxy $this$toBitmap) {
        return null;
    }
    
    /**
     * Bitmap'i döndürme
     */
    @org.jetbrains.annotations.NotNull()
    public static final android.graphics.Bitmap rotateBitmap(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, float degrees) {
        return null;
    }
    
    /**
     * Bitmap'i kesme
     */
    @org.jetbrains.annotations.NotNull()
    public static final android.graphics.Bitmap cropBitmap(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, int x, int y, int width, int height) {
        return null;
    }
    
    /**
     * OCR sonucundan içerik/bileşenler kısmını çıkarma
     * Farklı dillerde "içerikler", "içindekiler", "ingredients" gibi anahtar kelimeleri arar
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String extractIngredientsSection(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
        return null;
    }
}