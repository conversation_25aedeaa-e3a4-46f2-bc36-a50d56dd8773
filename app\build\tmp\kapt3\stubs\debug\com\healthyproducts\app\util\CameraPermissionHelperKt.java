package com.healthyproducts.app.util;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\u001a\u001c\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00040\u0003H\u0007\u00a8\u0006\u0005"}, d2 = {"rememberCameraPermissionState", "Lcom/healthyproducts/app/util/CameraPermissionState;", "onPermissionResult", "Lkotlin/Function1;", "", "app_debug"})
public final class CameraPermissionHelperKt {
    
    /**
     * Kamera izni için Composable fonksiyon
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.healthyproducts.app.util.CameraPermissionState rememberCameraPermissionState(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.healthyproducts.app.util.CameraPermissionState, kotlin.Unit> onPermissionResult) {
        return null;
    }
}