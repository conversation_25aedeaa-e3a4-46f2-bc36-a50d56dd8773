package com.healthyproducts.app.data.model;

/**
 * <PERSON><PERSON><PERSON> maddesi kategorisi için enum sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u001a\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018j\u0002\b\u0019j\u0002\b\u001aj\u0002\b\u001bj\u0002\b\u001c\u00a8\u0006\u001d"}, d2 = {"Lcom/healthyproducts/app/data/model/AdditiveCategory;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "COLORANT", "PRESERVATIVE", "ANTIOXIDANT", "EMULSIFIER", "STABILIZER", "THICKENER", "SWEETENER", "FLAVOR_ENHANCER", "ACID", "ACIDITY_REGULATOR", "ANTI_CAKING", "ANTI_FOAMING", "BULKING_AGENT", "CARRIER", "GLAZING_AGENT", "HUMECTANT", "MODIFIED_STARCH", "PACKAGING_GAS", "PROPELLANT", "RAISING_AGENT", "SEQUESTRANT", "OTHER", "app_debug"})
public enum AdditiveCategory {
    /*public static final*/ COLORANT /* = new COLORANT(null) */,
    /*public static final*/ PRESERVATIVE /* = new PRESERVATIVE(null) */,
    /*public static final*/ ANTIOXIDANT /* = new ANTIOXIDANT(null) */,
    /*public static final*/ EMULSIFIER /* = new EMULSIFIER(null) */,
    /*public static final*/ STABILIZER /* = new STABILIZER(null) */,
    /*public static final*/ THICKENER /* = new THICKENER(null) */,
    /*public static final*/ SWEETENER /* = new SWEETENER(null) */,
    /*public static final*/ FLAVOR_ENHANCER /* = new FLAVOR_ENHANCER(null) */,
    /*public static final*/ ACID /* = new ACID(null) */,
    /*public static final*/ ACIDITY_REGULATOR /* = new ACIDITY_REGULATOR(null) */,
    /*public static final*/ ANTI_CAKING /* = new ANTI_CAKING(null) */,
    /*public static final*/ ANTI_FOAMING /* = new ANTI_FOAMING(null) */,
    /*public static final*/ BULKING_AGENT /* = new BULKING_AGENT(null) */,
    /*public static final*/ CARRIER /* = new CARRIER(null) */,
    /*public static final*/ GLAZING_AGENT /* = new GLAZING_AGENT(null) */,
    /*public static final*/ HUMECTANT /* = new HUMECTANT(null) */,
    /*public static final*/ MODIFIED_STARCH /* = new MODIFIED_STARCH(null) */,
    /*public static final*/ PACKAGING_GAS /* = new PACKAGING_GAS(null) */,
    /*public static final*/ PROPELLANT /* = new PROPELLANT(null) */,
    /*public static final*/ RAISING_AGENT /* = new RAISING_AGENT(null) */,
    /*public static final*/ SEQUESTRANT /* = new SEQUESTRANT(null) */,
    /*public static final*/ OTHER /* = new OTHER(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    
    AdditiveCategory(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.healthyproducts.app.data.model.AdditiveCategory> getEntries() {
        return null;
    }
}