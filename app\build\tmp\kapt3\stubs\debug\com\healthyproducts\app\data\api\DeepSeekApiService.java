package com.healthyproducts.app.data.api;

/**
 * DeepSeek API servisi
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u0000 \t2\u00020\u0001:\u0001\tJ\"\u0010\u0002\u001a\u00020\u00032\b\b\u0001\u0010\u0004\u001a\u00020\u00052\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\n"}, d2 = {"Lcom/healthyproducts/app/data/api/DeepSeekApiService;", "", "getCompletion", "Lcom/healthyproducts/app/data/model/DeepSeekResponse;", "authorization", "", "request", "Lcom/healthyproducts/app/data/model/DeepSeekRequest;", "(Ljava/lang/String;Lcom/healthyproducts/app/data/model/DeepSeekRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public abstract interface DeepSeekApiService {
    @org.jetbrains.annotations.NotNull()
    public static final com.healthyproducts.app.data.api.DeepSeekApiService.Companion Companion = null;
    
    @retrofit2.http.POST(value = "v1/chat/completions")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCompletion(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.DeepSeekRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.healthyproducts.app.data.model.DeepSeekResponse> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/healthyproducts/app/data/api/DeepSeekApiService$Companion;", "", "()V", "BASE_URL", "", "app_debug"})
    public static final class Companion {
        @org.jetbrains.annotations.NotNull()
        private static final java.lang.String BASE_URL = "https://api.deepseek.com/";
        
        private Companion() {
            super();
        }
    }
}