package com.healthyproducts.app.ui.screens.foodanalysis

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.Preservative
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.components.ChipGroup
import com.healthyproducts.app.ui.components.RiskLevelIndicator
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.PreservativeState
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Koruyucu detay ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PreservativeDetailScreen(
    navController: NavController,
    preservativeId: String,
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val preservativeState by foodAnalysisViewModel.preservativeState.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()

    // Dil tercihi
    val language = SupportedLanguage.fromCode(userPreferences.language)

    // Koruyucuyu yükle
    LaunchedEffect(preservativeId) {
        foodAnalysisViewModel.loadPreservative(preservativeId)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.preservative_detail)) },
                navigationIcon = {
                    BackButton(navController = navController)
                }
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            when (val state = preservativeState) {
                is PreservativeState.Loading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                is PreservativeState.Success -> {
                    val preservative = state.preservative
                    PreservativeDetailContent(
                        preservative = preservative,
                        language = language
                    )
                }
                is PreservativeState.Error -> {
                    Text(
                        text = state.message,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                else -> {
                    // Başlangıç durumu
                }
            }
        }
    }
}

/**
 * Koruyucu detay içeriği
 */
@Composable
fun PreservativeDetailContent(
    preservative: Preservative,
    language: SupportedLanguage
) {
    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(scrollState)
    ) {
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Emoji veya ikon
                    if (preservative.symbol.isNotEmpty()) {
                        Text(
                            text = preservative.symbol,
                            style = MaterialTheme.typography.headlineMedium,
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.width(16.dp))
                    }

                    // Koruyucu adı
                    Text(
                        text = preservative.getName(language),
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Koruyucu açıklaması
                Text(
                    text = preservative.getDescription(language),
                    style = MaterialTheme.typography.bodyLarge
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Risk seviyesi
                RiskLevelIndicator(
                    title = stringResource(R.string.risk_level),
                    level = preservative.riskLevel,
                    maxLevel = 3
                )

                Divider(modifier = Modifier.padding(vertical = 16.dp))

                // Ana risk
                Text(
                    text = stringResource(R.string.main_risk),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = preservative.getMainRisk(language),
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Fonksiyonel türler
                if (preservative.functionalType.isNotEmpty()) {
                    Text(
                        text = stringResource(R.string.functional_type),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    ChipGroup(
                        items = preservative.functionalType.map { type ->
                            when (type) {
                                "preservative" -> stringResource(R.string.preservative)
                                "synthetic" -> stringResource(R.string.synthetic)
                                "toxic" -> stringResource(R.string.toxic)
                                "carcinogen" -> stringResource(R.string.carcinogen)
                                "endocrine_disruptor" -> stringResource(R.string.endocrine_disruptor)
                                "residue" -> stringResource(R.string.residue)
                                "antibiotic" -> stringResource(R.string.antibiotic)
                                "industrial_residue" -> stringResource(R.string.industrial_residue)
                                "pesticide" -> stringResource(R.string.pesticide)
                                else -> type
                            }
                        }
                    )

                    Spacer(modifier = Modifier.height(16.dp))
                }

                // Yaygın ürünler
                val commonProducts = preservative.getCommonProducts(language)
                if (commonProducts.isNotEmpty()) {
                    Text(
                        text = stringResource(R.string.common_products),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    ChipGroup(items = commonProducts)

                    Spacer(modifier = Modifier.height(16.dp))
                }

                // Notlar
                val notes = preservative.getNotes(language)
                if (notes.isNotEmpty()) {
                    Text(
                        text = stringResource(R.string.notes),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = notes,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}
