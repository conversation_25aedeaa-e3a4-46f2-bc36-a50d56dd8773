package com.healthyproducts.app.di;

import com.healthyproducts.app.data.api.GeminiApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideGeminiApiServiceFactory implements Factory<GeminiApiService> {
  @Override
  public GeminiApiService get() {
    return provideGeminiApiService();
  }

  public static NetworkModule_ProvideGeminiApiServiceFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static GeminiApiService provideGeminiApiService() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideGeminiApiService());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideGeminiApiServiceFactory INSTANCE = new NetworkModule_ProvideGeminiApiServiceFactory();
  }
}
