package com.healthyproducts.app.data.repository

import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import com.healthyproducts.app.model.User
import com.healthyproducts.app.model.UserPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Kullanıcı işlemlerini yöneten repository sınıfı
 */
@Singleton
class UserRepository @Inject constructor(
    private val firebaseAuthRepository: FirebaseAuthRepository,
    private val firestore: FirebaseFirestore
) {

    /**
     * E-posta ve şifre ile kayıt olma
     */
    suspend fun signUp(email: String, password: String, name: String? = null): Result<User> = withContext(Dispatchers.IO) {
        try {
            // Firebase ile kayıt ol
            val firebaseUser = firebaseAuthRepository.signUp(email, password)

            // <PERSON>llanıcı profilini güncelle (isim ekle)
            if (name != null) {
                firebaseAuthRepository.updateProfile(name, null)
            }

            // Firestore'a kullanıcı bilgilerini kaydet
            val user = createUserFromFirebaseUser(firebaseUser)
            saveUserToFirestore(user)

            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * E-posta ve şifre ile giriş yapma
     */
    suspend fun signIn(email: String, password: String): Result<User> = withContext(Dispatchers.IO) {
        try {
            // Firebase ile giriş yap
            val firebaseUser = firebaseAuthRepository.signIn(email, password)

            // Kullanıcı bilgilerini Firestore'dan al veya oluştur
            val user = getUserFromFirestore(firebaseUser.uid) ?: createUserFromFirebaseUser(firebaseUser)

            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Google ile giriş yapma
     */
    suspend fun signInWithGoogle(token: String): Result<User> = withContext(Dispatchers.IO) {
        try {
            // Firebase ile Google girişi yap
            val firebaseUser = firebaseAuthRepository.signInWithGoogle(token)

            // Kullanıcı bilgilerini Firestore'dan al veya oluştur
            val user = getUserFromFirestore(firebaseUser.uid) ?: createUserFromFirebaseUser(firebaseUser)

            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Çıkış yapma
     */
    suspend fun signOut(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Firebase'den çıkış yap
            firebaseAuthRepository.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Mevcut kullanıcıyı getirme
     */
    suspend fun getCurrentUser(): User? = withContext(Dispatchers.IO) {
        // Firebase'den mevcut kullanıcıyı al
        val firebaseUser = firebaseAuthRepository.getCurrentUser() ?: return@withContext null

        // Kullanıcı bilgilerini Firestore'dan al veya oluştur
        return@withContext getUserFromFirestore(firebaseUser.uid) ?: createUserFromFirebaseUser(firebaseUser)
    }

    /**
     * Kullanıcı tercihlerini güncelleme
     */
    suspend fun updateUserPreferences(userId: String, preferences: UserPreferences): Result<UserPreferences> = withContext(Dispatchers.IO) {
        try {
            android.util.Log.d("UserRepository", "Updating preferences for user $userId: aiModel=${preferences.aiModel}")

            // AI model değerini özellikle kontrol et
            if (preferences.aiModel.isNullOrBlank()) {
                android.util.Log.w("UserRepository", "AI model is null or blank, using default GEMINI_LITE")
                preferences.aiModel = com.healthyproducts.app.data.model.AiModel.GEMINI_LITE.name
            }

            // Firestore'a tercihleri kaydet
            val preferencesMap = mapOf(
                "show_halal_analysis" to preferences.showHalalAnalysis,
                "show_vegan_analysis" to preferences.showVeganAnalysis,
                "show_kosher_analysis" to preferences.showKosherAnalysis,
                "show_harmful_analysis" to preferences.showHarmfulAnalysis,
                "show_unhealthy_analysis" to preferences.showUnhealthyAnalysis,
                "language" to preferences.language,
                "dark_mode" to preferences.darkMode,
                "ai_model" to preferences.aiModel
            )

            firestore.collection("user_preferences")
                .document(userId)
                .set(preferencesMap)
                .await()

            android.util.Log.d("UserRepository", "Preferences updated successfully")

            // Tercihlerin güncellendiğini doğrula
            val updatedDocument = firestore.collection("user_preferences")
                .document(userId)
                .get()
                .await()

            if (updatedDocument.exists()) {
                val updatedPreferences = updatedDocument.toObject(UserPreferences::class.java)
                if (updatedPreferences != null) {
                    android.util.Log.d("UserRepository", "Verified updated preferences: aiModel=${updatedPreferences.aiModel}")

                    // AI model değerini kontrol et
                    if (updatedPreferences.aiModel != preferences.aiModel) {
                        android.util.Log.w("UserRepository", "AI model mismatch after update: expected=${preferences.aiModel}, got=${updatedPreferences.aiModel}")
                        // Düzeltilmiş tercihleri döndür
                        val correctedPreferences = updatedPreferences.copy(aiModel = preferences.aiModel)
                        return@withContext Result.success(correctedPreferences)
                    }

                    return@withContext Result.success(updatedPreferences)
                }
            }

            Result.success(preferences)
        } catch (e: Exception) {
            android.util.Log.e("UserRepository", "Failed to update preferences", e)
            Result.failure(e)
        }
    }

    /**
     * Kullanıcı tercihlerini getirme
     */
    suspend fun getUserPreferences(userId: String): Result<UserPreferences> = withContext(Dispatchers.IO) {
        try {
            // Firestore'dan tercihleri getir
            val document = firestore.collection("user_preferences")
                .document(userId)
                .get()
                .await()

            val preferences = if (document.exists()) {
                // Doğrudan alanları oku
                val showHalalAnalysis = document.getBoolean("show_halal_analysis") ?: true
                val showVeganAnalysis = document.getBoolean("show_vegan_analysis") ?: true
                val showKosherAnalysis = document.getBoolean("show_kosher_analysis") ?: false
                val showHarmfulAnalysis = document.getBoolean("show_harmful_analysis") ?: true
                val showUnhealthyAnalysis = document.getBoolean("show_unhealthy_analysis") ?: true
                // Firebase'den değerleri oku ve sabitlere ata
                val firebaseLanguage = document.getString("language") ?: "tr"
                val firebaseDarkMode = document.getBoolean("dark_mode") ?: false
                val firebaseAiModel = document.getString("ai_model")

                android.util.Log.d("UserRepository", "Firebase'den okunan değerler:")
                android.util.Log.d("UserRepository", "  language: $firebaseLanguage")
                android.util.Log.d("UserRepository", "  aiModel: $firebaseAiModel")
                android.util.Log.d("UserRepository", "  darkMode: $firebaseDarkMode")

                // Sabitleri kullan
                val language = firebaseLanguage
                val darkMode = firebaseDarkMode
                val aiModel = firebaseAiModel

                // AI model değerini kontrol et
                val validAiModel = if (!aiModel.isNullOrBlank()) {
                    try {
                        com.healthyproducts.app.data.model.AiModel.valueOf(aiModel).name
                    } catch (e: IllegalArgumentException) {
                        android.util.Log.w("UserRepository", "Invalid AI model: $aiModel, using default GEMINI_LITE")
                        com.healthyproducts.app.data.model.AiModel.GEMINI_LITE.name
                    }
                } else {
                    android.util.Log.w("UserRepository", "AI model is null or blank, using default GEMINI_LITE")
                    com.healthyproducts.app.data.model.AiModel.GEMINI_LITE.name
                }

                UserPreferences(
                    showHalalAnalysis = showHalalAnalysis,
                    showVeganAnalysis = showVeganAnalysis,
                    showKosherAnalysis = showKosherAnalysis,
                    showHarmfulAnalysis = showHarmfulAnalysis,
                    showUnhealthyAnalysis = showUnhealthyAnalysis,
                    language = language,
                    darkMode = darkMode,
                    aiModel = validAiModel
                )
            } else {
                UserPreferences()
            }

            android.util.Log.d("UserRepository", "Retrieved user preferences: aiModel=${preferences.aiModel}")
            Result.success(preferences)
        } catch (e: Exception) {
            android.util.Log.e("UserRepository", "Failed to get user preferences", e)
            Result.failure(e)
        }
    }

    /**
     * Firebase kullanıcısından User nesnesi oluşturur
     */
    private fun createUserFromFirebaseUser(firebaseUser: FirebaseUser): User {
        return User(
            id = firebaseUser.uid,
            email = firebaseUser.email ?: "",
            name = firebaseUser.displayName,
            photoUrl = firebaseUser.photoUrl?.toString(),
            createdAt = Date(firebaseUser.metadata?.creationTimestamp ?: System.currentTimeMillis())
        )
    }

    /**
     * Firestore'a kullanıcı bilgilerini kaydeder
     */
    private suspend fun saveUserToFirestore(user: User) {
        firestore.collection("users")
            .document(user.id)
            .set(user)
            .await()
    }

    /**
     * Firestore'dan kullanıcı bilgilerini getirir
     */
    private suspend fun getUserFromFirestore(userId: String): User? {
        val document = firestore.collection("users")
            .document(userId)
            .get()
            .await()

        return if (document.exists()) {
            document.toObject(User::class.java)
        } else {
            null
        }
    }
}
