package com.healthyproducts.app.ui.auth

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseAuth
import com.healthyproducts.app.data.repository.FirebaseAuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val authRepository: FirebaseAuthRepository
) : ViewModel() {

    private val _email = MutableStateFlow("")
    val email: StateFlow<String> = _email.asStateFlow()

    private val _password = MutableStateFlow("")
    val password: StateFlow<String> = _password.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _loginError = MutableStateFlow<String?>(null)
    val loginError: StateFlow<String?> = _loginError.asStateFlow()

    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()

    init {
        // Kullanıcı zaten giriş yapmış mı kontrol et
        _isLoggedIn.value = authRepository.isUserLoggedIn()
    }

    fun updateEmail(email: String) {
        _email.value = email
    }

    fun updatePassword(password: String) {
        _password.value = password
    }

    fun login() {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                if (_email.value.isBlank() || _password.value.isBlank()) {
                    _loginError.value = "E-posta ve şifre alanları boş olamaz"
                    return@launch
                }

                authRepository.signIn(_email.value, _password.value)
                _isLoggedIn.value = true

            } catch (e: Exception) {
                Log.e("LoginViewModel", "Giriş hatası", e)
                _loginError.value = e.message ?: "Giriş yapılırken bir hata oluştu"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loginWithGoogle() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // Google ile giriş işlemi MainActivity'de yapılacak
                // Bu ViewModel'de sadece kullanıcıya bilgi veriyoruz
                _loginError.value = "Google ile giriş için önce Google hesabınızı seçmeniz gerekiyor."

                // MainActivity'ye Google ile giriş işlemini başlatması için sinyal gönder
                // Bu işlem normalde bir event bus veya callback ile yapılır
                // Ancak basitlik için burada sadece bir log mesajı yazıyoruz
                Log.d("LoginViewModel", "Google ile giriş başlatılıyor")

                // Not: Gerçek uygulamada, MainActivity'ye bir sinyal göndermek için
                // SharedFlow veya SingleLiveEvent kullanılabilir
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Google giriş hatası", e)
                _loginError.value = e.message ?: "Google ile giriş yapılırken bir hata oluştu"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _loginError.value = null
    }
}
