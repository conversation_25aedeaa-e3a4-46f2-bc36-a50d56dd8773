package com.healthyproducts.app.di;

import com.healthyproducts.app.data.repository.AdditiveRepository;
import com.healthyproducts.app.data.repository.AllergenRepository;
import com.healthyproducts.app.data.repository.FatRepository;
import com.healthyproducts.app.data.repository.FoodCertificateRepository;
import com.healthyproducts.app.data.repository.IntoleranceRepository;
import com.healthyproducts.app.data.repository.PreservativeRepository;
import com.healthyproducts.app.data.repository.SugarRepository;
import com.healthyproducts.app.data.service.FoodAnalysisService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideFoodAnalysisServiceFactory implements Factory<FoodAnalysisService> {
  private final Provider<AdditiveRepository> additiveRepositoryProvider;

  private final Provider<AllergenRepository> allergenRepositoryProvider;

  private final Provider<SugarRepository> sugarRepositoryProvider;

  private final Provider<FatRepository> fatRepositoryProvider;

  private final Provider<IntoleranceRepository> intoleranceRepositoryProvider;

  private final Provider<PreservativeRepository> preservativeRepositoryProvider;

  private final Provider<FoodCertificateRepository> certificateRepositoryProvider;

  public RepositoryModule_ProvideFoodAnalysisServiceFactory(
      Provider<AdditiveRepository> additiveRepositoryProvider,
      Provider<AllergenRepository> allergenRepositoryProvider,
      Provider<SugarRepository> sugarRepositoryProvider,
      Provider<FatRepository> fatRepositoryProvider,
      Provider<IntoleranceRepository> intoleranceRepositoryProvider,
      Provider<PreservativeRepository> preservativeRepositoryProvider,
      Provider<FoodCertificateRepository> certificateRepositoryProvider) {
    this.additiveRepositoryProvider = additiveRepositoryProvider;
    this.allergenRepositoryProvider = allergenRepositoryProvider;
    this.sugarRepositoryProvider = sugarRepositoryProvider;
    this.fatRepositoryProvider = fatRepositoryProvider;
    this.intoleranceRepositoryProvider = intoleranceRepositoryProvider;
    this.preservativeRepositoryProvider = preservativeRepositoryProvider;
    this.certificateRepositoryProvider = certificateRepositoryProvider;
  }

  @Override
  public FoodAnalysisService get() {
    return provideFoodAnalysisService(additiveRepositoryProvider.get(), allergenRepositoryProvider.get(), sugarRepositoryProvider.get(), fatRepositoryProvider.get(), intoleranceRepositoryProvider.get(), preservativeRepositoryProvider.get(), certificateRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideFoodAnalysisServiceFactory create(
      Provider<AdditiveRepository> additiveRepositoryProvider,
      Provider<AllergenRepository> allergenRepositoryProvider,
      Provider<SugarRepository> sugarRepositoryProvider,
      Provider<FatRepository> fatRepositoryProvider,
      Provider<IntoleranceRepository> intoleranceRepositoryProvider,
      Provider<PreservativeRepository> preservativeRepositoryProvider,
      Provider<FoodCertificateRepository> certificateRepositoryProvider) {
    return new RepositoryModule_ProvideFoodAnalysisServiceFactory(additiveRepositoryProvider, allergenRepositoryProvider, sugarRepositoryProvider, fatRepositoryProvider, intoleranceRepositoryProvider, preservativeRepositoryProvider, certificateRepositoryProvider);
  }

  public static FoodAnalysisService provideFoodAnalysisService(
      AdditiveRepository additiveRepository, AllergenRepository allergenRepository,
      SugarRepository sugarRepository, FatRepository fatRepository,
      IntoleranceRepository intoleranceRepository, PreservativeRepository preservativeRepository,
      FoodCertificateRepository certificateRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideFoodAnalysisService(additiveRepository, allergenRepository, sugarRepository, fatRepository, intoleranceRepository, preservativeRepository, certificateRepository));
  }
}
