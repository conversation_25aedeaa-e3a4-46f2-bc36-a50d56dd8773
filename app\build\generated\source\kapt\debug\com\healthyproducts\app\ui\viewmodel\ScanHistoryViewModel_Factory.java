package com.healthyproducts.app.ui.viewmodel;

import com.healthyproducts.app.data.repository.ScanHistoryRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ScanHistoryViewModel_Factory implements Factory<ScanHistoryViewModel> {
  private final Provider<ScanHistoryRepository> scanHistoryRepositoryProvider;

  public ScanHistoryViewModel_Factory(
      Provider<ScanHistoryRepository> scanHistoryRepositoryProvider) {
    this.scanHistoryRepositoryProvider = scanHistoryRepositoryProvider;
  }

  @Override
  public ScanHistoryViewModel get() {
    return newInstance(scanHistoryRepositoryProvider.get());
  }

  public static ScanHistoryViewModel_Factory create(
      Provider<ScanHistoryRepository> scanHistoryRepositoryProvider) {
    return new ScanHistoryViewModel_Factory(scanHistoryRepositoryProvider);
  }

  public static ScanHistoryViewModel newInstance(ScanHistoryRepository scanHistoryRepository) {
    return new ScanHistoryViewModel(scanHistoryRepository);
  }
}
