package com.healthyproducts.app.model;

/**
 * <PERSON><PERSON><PERSON><PERSON> türünü temsil eden enum sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/healthyproducts/app/model/CertificateType;", "", "(<PERSON><PERSON><PERSON>/lang/String;I)V", "HALAL", "VEGAN", "KOSH<PERSON>", "ORGANIC", "NON_GMO", "app_debug"})
public enum CertificateType {
    /*public static final*/ HALAL /* = new HALAL() */,
    /*public static final*/ VEGAN /* = new VEGAN() */,
    /*public static final*/ KOSHER /* = new KOSHER() */,
    /*public static final*/ ORGANIC /* = new ORGANIC() */,
    /*public static final*/ NON_GMO /* = new NON_GMO() */;
    
    CertificateType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.healthyproducts.app.model.CertificateType> getEntries() {
        return null;
    }
}