package com.healthyproducts.app.data.model;

/**
 * G<PERSON><PERSON> katkı maddesini temsil eden veri sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0013\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\bU\n\u0002\u0018\u0002\n\u0002\b\b\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u00f5\u0001\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0004\u0012\b\b\u0002\u0010\b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\t\u001a\u00020\u0004\u0012\b\b\u0002\u0010\n\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\f\u001a\u00020\u0004\u0012\b\b\u0002\u0010\r\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u001b\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u001eJ\t\u0010T\u001a\u00020\u0004H\u00c6\u0003J\t\u0010U\u001a\u00020\u0004H\u00c6\u0003J\t\u0010V\u001a\u00020\u0004H\u00c6\u0003J\t\u0010W\u001a\u00020\u0004H\u00c6\u0003J\t\u0010X\u001a\u00020\u0004H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0004H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0004H\u00c6\u0003J\t\u0010[\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0004H\u00c6\u0003J\t\u0010]\u001a\u00020\u0004H\u00c6\u0003J\t\u0010^\u001a\u00020\u0004H\u00c6\u0003J\t\u0010_\u001a\u00020\u0004H\u00c6\u0003J\t\u0010`\u001a\u00020\u0018H\u00c6\u0003J\t\u0010a\u001a\u00020\u0018H\u00c6\u0003J\t\u0010b\u001a\u00020\u001bH\u00c6\u0003J\t\u0010c\u001a\u00020\u0004H\u00c6\u0003J\t\u0010d\u001a\u00020\u0004H\u00c6\u0003J\t\u0010e\u001a\u00020\u0004H\u00c6\u0003J\t\u0010f\u001a\u00020\u0004H\u00c6\u0003J\t\u0010g\u001a\u00020\u0004H\u00c6\u0003J\t\u0010h\u001a\u00020\u0004H\u00c6\u0003J\t\u0010i\u001a\u00020\u0004H\u00c6\u0003J\t\u0010j\u001a\u00020\u0004H\u00c6\u0003J\t\u0010k\u001a\u00020\u0004H\u00c6\u0003J\u00f9\u0001\u0010l\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\u00042\b\b\u0002\u0010\t\u001a\u00020\u00042\b\b\u0002\u0010\n\u001a\u00020\u00042\b\b\u0002\u0010\u000b\u001a\u00020\u00042\b\b\u0002\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\r\u001a\u00020\u00042\b\b\u0002\u0010\u000e\u001a\u00020\u00042\b\b\u0002\u0010\u000f\u001a\u00020\u00042\b\b\u0002\u0010\u0010\u001a\u00020\u00042\b\b\u0002\u0010\u0011\u001a\u00020\u00042\b\b\u0002\u0010\u0012\u001a\u00020\u00042\b\b\u0002\u0010\u0013\u001a\u00020\u00042\b\b\u0002\u0010\u0014\u001a\u00020\u00042\b\b\u0002\u0010\u0015\u001a\u00020\u00042\b\b\u0002\u0010\u0016\u001a\u00020\u00042\b\b\u0002\u0010\u0017\u001a\u00020\u00182\b\b\u0002\u0010\u0019\u001a\u00020\u00182\b\b\u0002\u0010\u001a\u001a\u00020\u001b2\b\b\u0002\u0010\u001c\u001a\u00020\u00042\b\b\u0002\u0010\u001d\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010m\u001a\u00020\u001b2\b\u0010n\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010o\u001a\u00020\u00042\u0006\u0010p\u001a\u00020qJ\u000e\u0010r\u001a\u00020\u00042\u0006\u0010p\u001a\u00020qJ\u000e\u0010s\u001a\u00020\u00042\u0006\u0010p\u001a\u00020qJ\u000e\u0010t\u001a\u00020\u00042\u0006\u0010p\u001a\u00020qJ\u000e\u0010u\u001a\u00020\u00042\u0006\u0010p\u001a\u00020qJ\u000e\u0010v\u001a\u00020\u00042\u0006\u0010p\u001a\u00020qJ\t\u0010w\u001a\u00020\u0018H\u00d6\u0001J\t\u0010x\u001a\u00020\u0004H\u00d6\u0001R\u001e\u0010\n\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\u001e\u0010\f\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010 \"\u0004\b$\u0010\"R\u001e\u0010\u000b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b%\u0010 \"\u0004\b&\u0010\"R\u001e\u0010\u0005\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010 \"\u0004\b(\u0010\"R\u001e\u0010\t\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b)\u0010 \"\u0004\b*\u0010\"R\u001e\u0010\b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b+\u0010 \"\u0004\b,\u0010\"R\u001e\u0010\u0013\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b-\u0010 \"\u0004\b.\u0010\"R\u001e\u0010\u0017\u001a\u00020\u00188\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u00100\"\u0004\b1\u00102R\u001e\u0010\u0012\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010 \"\u0004\b4\u0010\"R\u001e\u0010\u0011\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u0010 \"\u0004\b6\u0010\"R\u0016\u0010\u0003\u001a\u00020\u00048\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010 R\u001e\u0010\u0016\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b8\u0010 \"\u0004\b9\u0010\"R\u001e\u0010\u0007\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b:\u0010 \"\u0004\b;\u0010\"R\u001e\u0010\u0006\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b<\u0010 \"\u0004\b=\u0010\"R\u001e\u0010\u001d\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b>\u0010 \"\u0004\b?\u0010\"R\u001e\u0010\u001c\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b@\u0010 \"\u0004\bA\u0010\"R\u001e\u0010\u000e\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bB\u0010 \"\u0004\bC\u0010\"R\u001e\u0010\r\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bD\u0010 \"\u0004\bE\u0010\"R\u001e\u0010\u001a\u001a\u00020\u001b8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bF\u0010G\"\u0004\bH\u0010IR\u001e\u0010\u0019\u001a\u00020\u00188\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bJ\u00100\"\u0004\bK\u00102R\u001e\u0010\u0010\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bL\u0010 \"\u0004\bM\u0010\"R\u001e\u0010\u000f\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bN\u0010 \"\u0004\bO\u0010\"R\u001e\u0010\u0014\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bP\u0010 \"\u0004\bQ\u0010\"R\u001e\u0010\u0015\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bR\u0010 \"\u0004\bS\u0010\"\u00a8\u0006y"}, d2 = {"Lcom/healthyproducts/app/data/model/Additive;", "", "()V", "id", "", "code", "nameTr", "nameEn", "descriptionTr", "descriptionEn", "category", "categoryTr", "categoryEn", "originTr", "originEn", "usageTr", "usageEn", "healthEffectTr", "healthEffectEn", "halalStatus", "veganStatus", "vegetarianStatus", "kosherStatus", "harmfulLevel", "", "unhealthyLevel", "risky", "", "notesTr", "notesEn", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIZLjava/lang/String;Ljava/lang/String;)V", "getCategory", "()Ljava/lang/String;", "setCategory", "(Ljava/lang/String;)V", "getCategoryEn", "setCategoryEn", "getCategoryTr", "setCategoryTr", "getCode", "setCode", "getDescriptionEn", "setDescriptionEn", "getDescriptionTr", "setDescriptionTr", "getHalalStatus", "setHalalStatus", "getHarmfulLevel", "()I", "setHarmfulLevel", "(I)V", "getHealthEffectEn", "setHealthEffectEn", "getHealthEffectTr", "setHealthEffectTr", "getId", "getKosherStatus", "setKosherStatus", "getNameEn", "setNameEn", "getNameTr", "setNameTr", "getNotesEn", "setNotesEn", "getNotesTr", "setNotesTr", "getOriginEn", "setOriginEn", "getOriginTr", "setOriginTr", "getRisky", "()Z", "setRisky", "(Z)V", "getUnhealthyLevel", "setUnhealthyLevel", "getUsageEn", "setUsageEn", "getUsageTr", "setUsageTr", "getVeganStatus", "setVeganStatus", "getVegetarianStatus", "setVegetarianStatus", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "getDescription", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "getHealthEffect", "getName", "getNotes", "getOrigin", "getUsage", "hashCode", "toString", "app_debug"})
public final class Additive {
    @com.google.firebase.firestore.DocumentId()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String code;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String category;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String categoryTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String categoryEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String originTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String originEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String usageTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String usageEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String healthEffectTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String healthEffectEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String halalStatus;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String veganStatus;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String vegetarianStatus;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String kosherStatus;
    private int harmfulLevel;
    private int unhealthyLevel;
    private boolean risky;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesEn;
    
    public Additive(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String code, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.lang.String category, @org.jetbrains.annotations.NotNull()
    java.lang.String categoryTr, @org.jetbrains.annotations.NotNull()
    java.lang.String categoryEn, @org.jetbrains.annotations.NotNull()
    java.lang.String originTr, @org.jetbrains.annotations.NotNull()
    java.lang.String originEn, @org.jetbrains.annotations.NotNull()
    java.lang.String usageTr, @org.jetbrains.annotations.NotNull()
    java.lang.String usageEn, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectTr, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectEn, @org.jetbrains.annotations.NotNull()
    java.lang.String halalStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String veganStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String vegetarianStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String kosherStatus, int harmfulLevel, int unhealthyLevel, boolean risky, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "code")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCode() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "code")
    public final void setCode(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    public final void setNameTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    public final void setNameEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    public final void setDescriptionTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    public final void setDescriptionEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "category")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCategory() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "category")
    public final void setCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "category_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCategoryTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "category_tr")
    public final void setCategoryTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "category_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCategoryEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "category_en")
    public final void setCategoryEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOriginTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_tr")
    public final void setOriginTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOriginEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_en")
    public final void setOriginEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "usage_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUsageTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "usage_tr")
    public final void setUsageTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "usage_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUsageEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "usage_en")
    public final void setUsageEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffectTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_tr")
    public final void setHealthEffectTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffectEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_en")
    public final void setHealthEffectEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "halal_status")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHalalStatus() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "halal_status")
    public final void setHalalStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "vegan_status")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVeganStatus() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "vegan_status")
    public final void setVeganStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "vegetarian_status")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVegetarianStatus() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "vegetarian_status")
    public final void setVegetarianStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "kosher_status")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getKosherStatus() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "kosher_status")
    public final void setKosherStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "harmful_level")
    public final int getHarmfulLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "harmful_level")
    public final void setHarmfulLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "unhealthy_level")
    public final int getUnhealthyLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "unhealthy_level")
    public final void setUnhealthyLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risky")
    public final boolean getRisky() {
        return false;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risky")
    public final void setRisky(boolean p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    public final void setNotesTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    public final void setNotesEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public Additive() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrigin(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUsage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffect(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotes(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component20() {
        return 0;
    }
    
    public final int component21() {
        return 0;
    }
    
    public final boolean component22() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component23() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component24() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.model.Additive copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String code, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.lang.String category, @org.jetbrains.annotations.NotNull()
    java.lang.String categoryTr, @org.jetbrains.annotations.NotNull()
    java.lang.String categoryEn, @org.jetbrains.annotations.NotNull()
    java.lang.String originTr, @org.jetbrains.annotations.NotNull()
    java.lang.String originEn, @org.jetbrains.annotations.NotNull()
    java.lang.String usageTr, @org.jetbrains.annotations.NotNull()
    java.lang.String usageEn, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectTr, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectEn, @org.jetbrains.annotations.NotNull()
    java.lang.String halalStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String veganStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String vegetarianStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String kosherStatus, int harmfulLevel, int unhealthyLevel, boolean risky, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}