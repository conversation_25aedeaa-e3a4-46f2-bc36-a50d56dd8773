package com.healthyproducts.app.ui.viewmodel;

/**
 * Kat<PERSON><PERSON> maddeleri işlemlerini yöneten ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u0002\n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001:\u00014B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\fJ\u000e\u0010\'\u001a\u00020%2\u0006\u0010(\u001a\u00020\bJ\b\u0010)\u001a\u00020%H\u0002J\u000e\u0010*\u001a\u00020%2\u0006\u0010(\u001a\u00020\bJ\u0006\u0010+\u001a\u00020%J\u0006\u0010,\u001a\u00020%J\b\u0010-\u001a\u00020%H\u0002J\u0010\u0010.\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\fJ\u000e\u0010/\u001a\u00020%2\u0006\u0010&\u001a\u00020\fJ\u0010\u00100\u001a\u00020%2\b\u00101\u001a\u0004\u0018\u00010\bJ\u000e\u00102\u001a\u00020%2\u0006\u00103\u001a\u00020\bR\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0019\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u0019\u0010\u001b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0018R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00100\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0018R\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00120\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0018R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\b0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0018R\u0019\u0010\"\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0018\u00a8\u00065"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel;", "Landroidx/lifecycle/ViewModel;", "additiveRepository", "Lcom/healthyproducts/app/data/repository/AdditiveRepository;", "context", "Landroid/content/Context;", "(Lcom/healthyproducts/app/data/repository/AdditiveRepository;Landroid/content/Context;)V", "TAG", "", "_additives", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/healthyproducts/app/data/model/Additive;", "_error", "_filterCategory", "_importStatus", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus;", "_isLoading", "", "_searchQuery", "_selectedAdditive", "additives", "Lkotlinx/coroutines/flow/StateFlow;", "getAdditives", "()Lkotlinx/coroutines/flow/StateFlow;", "error", "getError", "filterCategory", "getFilterCategory", "importStatus", "getImportStatus", "isLoading", "searchQuery", "getSearchQuery", "selectedAdditive", "getSelectedAdditive", "addAdditive", "", "additive", "deleteAdditive", "code", "filterAdditives", "getAdditiveByCode", "importAdditivesFromJson", "loadAdditives", "searchAdditives", "selectAdditive", "updateAdditive", "updateFilterCategory", "category", "updateSearchQuery", "query", "ImportStatus", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AdditiveViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.AdditiveRepository additiveRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "AdditiveViewModel";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.healthyproducts.app.data.model.Additive>> _additives = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.healthyproducts.app.data.model.Additive>> additives = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.data.model.Additive> _selectedAdditive = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Additive> selectedAdditive = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _searchQuery = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> searchQuery = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus> _importStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus> importStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _filterCategory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> filterCategory = null;
    
    @javax.inject.Inject()
    public AdditiveViewModel(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.AdditiveRepository additiveRepository, @dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.healthyproducts.app.data.model.Additive>> getAdditives() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.Additive> getSelectedAdditive() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getSearchQuery() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus> getImportStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getFilterCategory() {
        return null;
    }
    
    /**
     * Tüm katkı maddelerini yükler
     */
    public final void loadAdditives() {
    }
    
    /**
     * Katkı maddesi ekler
     */
    public final void addAdditive(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.Additive additive) {
    }
    
    /**
     * Katkı maddesini günceller
     */
    public final void updateAdditive(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.Additive additive) {
    }
    
    /**
     * Katkı maddesini siler
     */
    public final void deleteAdditive(@org.jetbrains.annotations.NotNull()
    java.lang.String code) {
    }
    
    /**
     * Katkı maddesini seçer
     */
    public final void selectAdditive(@org.jetbrains.annotations.Nullable()
    com.healthyproducts.app.data.model.Additive additive) {
    }
    
    /**
     * Arama sorgusunu günceller
     */
    public final void updateSearchQuery(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    /**
     * Filtreleme kategorisini günceller
     */
    public final void updateFilterCategory(@org.jetbrains.annotations.Nullable()
    java.lang.String category) {
    }
    
    /**
     * Katkı maddelerini arar
     */
    private final void searchAdditives() {
    }
    
    /**
     * Katkı maddelerini filtreler
     */
    private final void filterAdditives() {
    }
    
    /**
     * Katkı maddesini kodu ile getirir
     */
    public final void getAdditiveByCode(@org.jetbrains.annotations.NotNull()
    java.lang.String code) {
    }
    
    /**
     * JSON dosyasından katkı maddelerini içe aktarır
     */
    public final void importAdditivesFromJson() {
    }
    
    /**
     * İçe aktarma durumunu temsil eden sealed class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus;", "", "()V", "Error", "Idle", "Loading", "Success", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Error;", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Success;", "app_debug"})
    public static abstract class ImportStatus {
        
        private ImportStatus() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Error;", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Error extends com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus;", "()V", "app_debug"})
        public static final class Idle extends com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.Idle INSTANCE = null;
            
            private Idle() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus;", "()V", "app_debug"})
        public static final class Loading extends com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.Loading INSTANCE = null;
            
            private Loading() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus$Success;", "Lcom/healthyproducts/app/ui/viewmodel/AdditiveViewModel$ImportStatus;", "count", "", "(I)V", "getCount", "()I", "component1", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_debug"})
        public static final class Success extends com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus {
            private final int count = 0;
            
            public Success(int count) {
            }
            
            public final int getCount() {
                return 0;
            }
            
            public final int component1() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.Success copy(int count) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
}