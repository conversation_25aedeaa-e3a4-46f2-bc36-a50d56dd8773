package com.healthyproducts.app.ui.screens.foodanalysis

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Science
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material.icons.filled.Verified
import androidx.compose.material.icons.filled.Sanitizer
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController

import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import android.util.Log
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.FoodPreferenceType
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.navigation.Screen
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.viewmodel.AnalysisState
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Gıda analizi ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FoodAnalysisScreen(
    navController: NavController,
    ingredients: String = "",
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    // Kullanıcı tercihleri - UserViewModel'den al (geri döndük)
    val userPreferences by userViewModel.userPreferences.collectAsState()
    val userState by userViewModel.userState.collectAsState()
    val userId = if (userState is UserViewModel.UserState.LoggedIn) {
        (userState as UserViewModel.UserState.LoggedIn).user.id
    } else {
        ""
    }

    // Kullanıcı gıda tercihleri
    val userFoodPreferencesState by foodAnalysisViewModel.userFoodPreferencesState.collectAsState()

    // Analiz durumu
    val analysisState by foodAnalysisViewModel.analysisResult.collectAsState()

    // Coroutine scope
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    // İçerikler - URL parametresini decode et
    val decodedIngredients = remember(ingredients) {
        if (ingredients.isNotEmpty()) {
            try {
                java.net.URLDecoder.decode(ingredients, "UTF-8")
            } catch (e: Exception) {
                ingredients // Decode edilemezse orijinalini kullan
            }
        } else {
            ""
        }
    }

    val ingredientsList = remember(decodedIngredients) {
        if (decodedIngredients.isNotEmpty()) {
            decodedIngredients.split(",").map { it.trim() }
        } else {
            emptyList()
        }
    }

    // Kullanıcı tercihlerini yükle ve kullanıcı ID'sini ayarla
    LaunchedEffect(userId) {
        if (userId.isNotEmpty()) {
            Log.d("FoodAnalysisScreen", "Setting user ID: $userId")
            foodAnalysisViewModel.initUserPreferences(userId)

            // Kullanıcı tercihlerini zorla yenile (UserViewModel'den)
            Log.d("FoodAnalysisScreen", "Force refreshing user preferences from UserViewModel")
            userViewModel.refreshUserPreferences()

            // FoodAnalysisViewModel'den de yenile
            Log.d("FoodAnalysisScreen", "Force refreshing user preferences from FoodAnalysisViewModel")
            foodAnalysisViewModel.refreshUserPreferences()
        }
    }

    // İçerikler varsa analiz et - sadece ilk kez
    LaunchedEffect(ingredientsList) {
        if (ingredientsList.isNotEmpty()) {
            // Firebase'den güncel dil ayarını al (coroutine ile)
            val currentUser = (userState as? UserState.LoggedIn)?.user
            val firebaseLanguage = if (currentUser != null) {
                // Firebase'den async oku
                val firestore = com.google.firebase.firestore.FirebaseFirestore.getInstance()
                try {
                    val document = firestore.collection("user_preferences")
                        .document(currentUser.id)
                        .get()
                        .await()

                    val fbLang = if (document.exists()) {
                        document.getString("language") ?: "tr"
                    } else {
                        "tr"
                    }
                    Log.d("FoodAnalysisScreen", "Firebase'den okunan dil (sabit): $fbLang")
                    fbLang
                } catch (e: Exception) {
                    Log.e("FoodAnalysisScreen", "Error reading Firebase language", e)
                    "tr"
                }
            } else {
                userPreferences.language
            }

            // Debug: Dil ayarlarını logla
            Log.d("FoodAnalysisScreen", "=== LANGUAGE DEBUG ===")
            Log.d("FoodAnalysisScreen", "UserViewModel state: ${userViewModel.userPreferences.value}")
            Log.d("FoodAnalysisScreen", "User preferences language: ${userPreferences.language}")
            Log.d("FoodAnalysisScreen", "Firebase language (sabit): $firebaseLanguage")
            Log.d("FoodAnalysisScreen", "User preferences aiModel: ${userPreferences.aiModel}")
            Log.d("FoodAnalysisScreen", "Current locale: ${java.util.Locale.getDefault()}")
            Log.d("FoodAnalysisScreen", "System language: ${android.content.res.Resources.getSystem().configuration.locales[0]}")

            // Firebase'den okunan dil ayarını kullan (sabit atama)
            val finalLanguage = firebaseLanguage
            val language = SupportedLanguage.fromCode(finalLanguage)
            Log.d("FoodAnalysisScreen", "Starting analysis with language: ${language.code} (Firebase language: $finalLanguage)")
            Log.d("FoodAnalysisScreen", "SupportedLanguage object: $language")
            foodAnalysisViewModel.analyzeIngredients(ingredientsList, language)
        }
    }

    // Dil değişikliği durumunda sadece cache'i temizle, yeniden analiz yapma
    LaunchedEffect(userPreferences.language) {
        Log.d("FoodAnalysisScreen", "Language changed to: ${userPreferences.language}, clearing cache")
        foodAnalysisViewModel.clearAnalysisCache()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.food_analysis_screen)) },
                navigationIcon = { BackButton(navController) }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // İçerikler varsa analiz sonuçlarını göster
            if (ingredientsList.isNotEmpty()) {
                Text(
                    text = stringResource(R.string.content_analysis),
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // İçerik listesi
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.ingredients_label),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        ingredientsList.forEach { ingredient ->
                            Text(
                                text = "• $ingredient",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }

                // Analiz sonuçları
                when (analysisState) {
                    is AnalysisState.Loading -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                CircularProgressIndicator()
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = stringResource(R.string.analyzing_ingredients),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                    is AnalysisState.Success -> {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.analysis_results),
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )

                                // Analiz sonucunu görselleştir
                                AnalysisResultView(
                                    analysisResult = (analysisState as AnalysisState.Success).result,
                                    language = SupportedLanguage.fromCode(userPreferences.language)
                                )
                            }
                        }
                    }
                    is AnalysisState.Error -> {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.analysis_error),
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )

                                Text(
                                    text = (analysisState as AnalysisState.Error).message,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    }
                    else -> { /* Initial state, do nothing */ }
                }

                // Butonlar
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Yeniden analiz butonu
                    Button(
                        onClick = {
                            // Yeniden analiz
                            val language = SupportedLanguage.fromCode(userPreferences.language)
                            Log.d("FoodAnalysisScreen", "Manual re-analysis with language: ${language.code} (language preference: ${userPreferences.language})")
                            foodAnalysisViewModel.analyzeIngredients(ingredientsList, language)
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(text = stringResource(R.string.re_analyze))
                    }

                    // Barkod Ekle butonu
                    Button(
                        onClick = {
                            // Barkod tarama ekranına git
                            navController.navigate(Screen.BarcodeScan.route)
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(text = stringResource(R.string.add_barcode))
                    }
                }
            } else {
                // İçerik yoksa gıda analizi seçeneklerini göster
                Text(
                    text = stringResource(R.string.food_analysis_description),
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // Gıda analizi seçenekleri
                FoodAnalysisOptions(
                    onAllergensClick = { navController.navigate(Screen.Allergens.route) },
                    onFatsClick = { navController.navigate(Screen.Fats.route) },
                    onSugarsClick = { navController.navigate(Screen.Sugars.route) },
                    onCertificatesClick = { navController.navigate(Screen.FoodCertificates.route) },
                    onPreservativesClick = { navController.navigate(Screen.Preservatives.route) }
                )
            }
        }
    }
}

/**
 * Gıda analizi seçenekleri bileşeni
 */
@Composable
fun FoodAnalysisOptions(
    onAllergensClick: () -> Unit,
    onFatsClick: () -> Unit,
    onSugarsClick: () -> Unit,
    onCertificatesClick: () -> Unit,
    onPreservativesClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            FoodAnalysisOptionCard(
                title = stringResource(R.string.allergens),
                icon = Icons.Default.Warning,
                modifier = Modifier.weight(1f),
                onClick = onAllergensClick
            )

            FoodAnalysisOptionCard(
                title = stringResource(R.string.fats),
                icon = Icons.Default.Science,
                modifier = Modifier.weight(1f),
                onClick = onFatsClick
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            FoodAnalysisOptionCard(
                title = stringResource(R.string.sugars),
                icon = Icons.Default.Science,
                modifier = Modifier.weight(1f),
                onClick = onSugarsClick
            )

            FoodAnalysisOptionCard(
                title = stringResource(R.string.food_certificates),
                icon = Icons.Default.Verified,
                modifier = Modifier.weight(1f),
                onClick = onCertificatesClick
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            FoodAnalysisOptionCard(
                title = stringResource(R.string.preservatives),
                icon = Icons.Default.Sanitizer,
                modifier = Modifier.weight(1f),
                onClick = onPreservativesClick
            )

            // Boş alan
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

/**
 * Gıda analizi seçenek kartı
 */
@Composable
fun FoodAnalysisOptionCard(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Analiz sonuçlarını görselleştiren bileşen
 */
@Composable
fun AnalysisResultView(
    analysisResult: String,
    language: SupportedLanguage
) {
    // Analiz sonucunu parçalara ayır
    val sections = parseAnalysisResult(analysisResult)

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // Skor Badge'i (En Üstte)
        // Hem tam analiz sonucundan hem de healthScore bölümünden skor çıkarmaya çalış
        val scoreRegex = "\\b(\\d+(?:[.,]\\d+)?)/10\\b".toRegex()
        val scoreMatch = scoreRegex.find(analysisResult) ?: scoreRegex.find(sections["healthScore"] ?: "")
        val score = scoreMatch?.groupValues?.get(1)?.replace(",", ".")?.toFloatOrNull() ?: 0f

        // Debug için log ekle
        android.util.Log.d("AnalysisResultView", "Raw analysis result: $analysisResult")
        android.util.Log.d("AnalysisResultView", "Health score section: ${sections["healthScore"]}")
        android.util.Log.d("AnalysisResultView", "Score match: ${scoreMatch?.value}")
        android.util.Log.d("AnalysisResultView", "Extracted score: $score")

        ScoreBadge(
            score = score,
            language = language,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        // Uyarı ve Onay Badge'leri
        BadgeSection(
            sections = sections,
            language = language,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Kullanıcı uyarıları (varsa)
        if (sections["userWarnings"]?.isNotEmpty() == true) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.user_warnings),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    sections["userWarnings"]?.split("\n")?.forEach { warning ->
                        if (warning.isNotEmpty()) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Warning,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.padding(end = 8.dp)
                                )
                                Text(
                                    text = warning.trim(),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onErrorContainer
                                )
                            }
                        }
                    }
                }
            }
        }

        // Genel sağlık değerlendirmesi
        if (sections["healthScore"]?.isNotEmpty() == true) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.health_assessment),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Sağlık puanını çıkar
                    val scoreRegex = if (language == SupportedLanguage.TURKISH)
                        "\\b(\\d+)/10\\b".toRegex() else "\\b(\\d+)/10\\b".toRegex()
                    val scoreMatch = scoreRegex.find(sections["healthScore"] ?: "")
                    val score = scoreMatch?.groupValues?.get(1)?.toIntOrNull() ?: 5

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "$score/10",
                            style = MaterialTheme.typography.headlineMedium,
                            fontWeight = FontWeight.Bold,
                            color = when {
                                score >= 8 -> MaterialTheme.colorScheme.primary
                                score >= 5 -> MaterialTheme.colorScheme.secondary
                                else -> MaterialTheme.colorScheme.error
                            },
                            modifier = Modifier.padding(end = 16.dp)
                        )

                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            // İlerleme çubuğu
                            LinearProgressIndicator(
                                progress = { score / 10f },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(8.dp),
                                color = when {
                                    score >= 8 -> MaterialTheme.colorScheme.primary
                                    score >= 5 -> MaterialTheme.colorScheme.secondary
                                    else -> MaterialTheme.colorScheme.error
                                }
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            // Sağlık değerlendirmesi açıklaması
                            Text(
                                text = sections["healthScore"]?.replace(scoreRegex, "")?.trim() ?: "",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                }
            }
        }

        // Diğer bölümler
        val context = LocalContext.current
        val sectionOrder = listOf(
            "additives" to context.getString(R.string.additives_section),
            "allergens" to context.getString(R.string.allergens_section),
            "sugars" to context.getString(R.string.sugars_section),
            "preservatives" to context.getString(R.string.preservatives_section),
            "certificates" to context.getString(R.string.certificates_section),
            "summary" to context.getString(R.string.summary_section)
        )

        sectionOrder.forEach { (key, title) ->
            if (sections[key]?.isNotEmpty() == true) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        sections[key]?.split("\n")?.forEach { line ->
                            if (line.isNotEmpty()) {
                                Text(
                                    text = line.trim(),
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(vertical = 2.dp)
                                )
                            }
                        }
                    }
                }
            }
        }

        // Ham analiz sonucu (geliştirme amaçlı, daha sonra kaldırılabilir)
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = stringResource(R.string.raw_analysis_result),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                Text(
                    text = analysisResult,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Analiz sonucunu parçalara ayırır
 */
private fun parseAnalysisResult(analysisResult: String): Map<String, String> {
    val sections = mutableMapOf<String, String>()

    // Kullanıcı uyarılarını çıkar
    val userWarnings = StringBuilder()
    val userWarningRegexTR = "\\*\\*Kullanıcı Uyarısı:([^*]+)\\*\\*".toRegex()
    val userWarningRegexEN = "\\*\\*User Warning:([^*]+)\\*\\*".toRegex()

    userWarningRegexTR.findAll(analysisResult).forEach { match ->
        userWarnings.appendLine("• ${match.groupValues[1].trim()}")
    }
    userWarningRegexEN.findAll(analysisResult).forEach { match ->
        userWarnings.appendLine("• ${match.groupValues[1].trim()}")
    }
    sections["userWarnings"] = userWarnings.toString().trim()

    // Katkı maddeleri bölümünü çıkar
    val additivesRegexTR = "(?s)\\*\\*1\\. Potansiyel Zararlı Katkı Maddelesi[^*]*?\\*\\*([^*]*?)(?=\\*\\*2\\.)".toRegex()
    val additivesRegexEN = "(?s)\\*\\*1\\. Potentially harmful additives[^*]*?\\*\\*([^*]*?)(?=\\*\\*2\\.)".toRegex()

    additivesRegexTR.find(analysisResult)?.let {
        sections["additives"] = it.groupValues[1].trim()
    } ?: additivesRegexEN.find(analysisResult)?.let {
        sections["additives"] = it.groupValues[1].trim()
    }

    // Alerjenler bölümünü çıkar
    val allergensRegexTR = "(?s)\\*\\*2\\. Alerjenik Maddeler:[^*]*?\\*\\*([^*]*?)(?=\\*\\*3\\.)".toRegex()
    val allergensRegexEN = "(?s)\\*\\*2\\. Allergenic substances:[^*]*?\\*\\*([^*]*?)(?=\\*\\*3\\.)".toRegex()

    allergensRegexTR.find(analysisResult)?.let {
        sections["allergens"] = it.groupValues[1].trim()
    } ?: allergensRegexEN.find(analysisResult)?.let {
        sections["allergens"] = it.groupValues[1].trim()
    }

    // Şekerler bölümünü çıkar
    val sugarsRegexTR = "(?s)\\*\\*3\\. Şeker ve Yapay Tatlandırıcılar:[^*]*?\\*\\*([^*]*?)(?=\\*\\*4\\.)".toRegex()
    val sugarsRegexEN = "(?s)\\*\\*3\\. Sugar and artificial sweeteners:[^*]*?\\*\\*([^*]*?)(?=\\*\\*4\\.)".toRegex()

    sugarsRegexTR.find(analysisResult)?.let {
        sections["sugars"] = it.groupValues[1].trim()
    } ?: sugarsRegexEN.find(analysisResult)?.let {
        sections["sugars"] = it.groupValues[1].trim()
    }

    // Koruyucular bölümünü çıkar
    val preservativesRegexTR = "(?s)\\*\\*4\\. Koruyucular ve Renklendirici:[^*]*?\\*\\*([^*]*?)(?=\\*\\*5\\.)".toRegex()
    val preservativesRegexEN = "(?s)\\*\\*4\\. Preservatives and colorants:[^*]*?\\*\\*([^*]*?)(?=\\*\\*5\\.)".toRegex()

    preservativesRegexTR.find(analysisResult)?.let {
        sections["preservatives"] = it.groupValues[1].trim()
    } ?: preservativesRegexEN.find(analysisResult)?.let {
        sections["preservatives"] = it.groupValues[1].trim()
    }

    // Sağlık değerlendirmesi bölümünü çıkar
    val healthScoreRegexTR = "(?s)\\*\\*5\\. Genel Sağlık Değerlendirmesi[^*]*?\\*\\*([^*]*?)(?=\\*\\*Ek Bilgiler|\\*\\*Özet)".toRegex()
    val healthScoreRegexEN = "(?s)\\*\\*5\\. General health assessment[^*]*?\\*\\*([^*]*?)(?=\\*\\*Additional|\\*\\*Summary)".toRegex()

    healthScoreRegexTR.find(analysisResult)?.let {
        sections["healthScore"] = it.groupValues[1].trim()
    } ?: healthScoreRegexEN.find(analysisResult)?.let {
        sections["healthScore"] = it.groupValues[1].trim()
    }

    // Sertifikalar bölümünü çıkar (varsa)
    val certificatesRegexTR = "(?s)\\*\\*Sertifikalar:[^*]*?\\*\\*([^*]*?)(?=\\*\\*Ek Bilgiler|\\*\\*Özet)".toRegex()
    val certificatesRegexEN = "(?s)\\*\\*Certificates:[^*]*?\\*\\*([^*]*?)(?=\\*\\*Additional|\\*\\*Summary)".toRegex()

    certificatesRegexTR.find(analysisResult)?.let {
        sections["certificates"] = it.groupValues[1].trim()
    } ?: certificatesRegexEN.find(analysisResult)?.let {
        sections["certificates"] = it.groupValues[1].trim()
    }

    // Özet bölümünü çıkar
    val summaryRegexTR = "(?s)\\*\\*Özet:[^*]*?\\*\\*([^*]*)".toRegex()
    val summaryRegexEN = "(?s)\\*\\*Summary:[^*]*?\\*\\*([^*]*)".toRegex()

    summaryRegexTR.find(analysisResult)?.let {
        sections["summary"] = it.groupValues[1].trim()
    } ?: summaryRegexEN.find(analysisResult)?.let {
        sections["summary"] = it.groupValues[1].trim()
    }

    return sections
}

/**
 * Skor Badge'i - Sayfanın en üstünde gösterilen büyük puan göstergesi
 */
@Composable
fun ScoreBadge(
    score: Float,
    language: SupportedLanguage,
    modifier: Modifier = Modifier
) {
    val backgroundColor = when {
        score >= 8f -> MaterialTheme.colorScheme.primary
        score >= 5f -> MaterialTheme.colorScheme.tertiary
        else -> MaterialTheme.colorScheme.error
    }

    val textColor = when {
        score >= 8f -> MaterialTheme.colorScheme.onPrimary
        score >= 5f -> MaterialTheme.colorScheme.onTertiary
        else -> MaterialTheme.colorScheme.onError
    }

    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = if (language == SupportedLanguage.TURKISH) "Sağlık Puanı" else "Health Score",
                style = MaterialTheme.typography.titleMedium,
                color = textColor,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "${score.toInt()}/10",
                style = MaterialTheme.typography.displayMedium,
                color = textColor,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = when {
                    score >= 8f -> if (language == SupportedLanguage.TURKISH) "Mükemmel" else "Excellent"
                    score >= 6f -> if (language == SupportedLanguage.TURKISH) "İyi" else "Good"
                    score >= 4f -> if (language == SupportedLanguage.TURKISH) "Orta" else "Average"
                    else -> if (language == SupportedLanguage.TURKISH) "Dikkat" else "Caution"
                },
                style = MaterialTheme.typography.titleSmall,
                color = textColor,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * Badge Section - Uyarı ve onay badge'lerini gösteren bölüm
 */
@Composable
fun BadgeSection(
    sections: Map<String, String>,
    language: SupportedLanguage,
    modifier: Modifier = Modifier
) {
    val warningBadges = mutableListOf<String>()
    val approvalBadges = mutableListOf<String>()

    // Katkı maddelerinden uyarı badge'leri çıkar
    sections["additives"]?.let { additives ->
        val eCodeRegex = "E\\d+".toRegex()
        eCodeRegex.findAll(additives).forEach { match ->
            warningBadges.add(match.value)
        }
    }

    // Alerjenlerden uyarı badge'leri çıkar
    sections["allergens"]?.let { allergens ->
        val commonAllergens = listOf("Gluten", "Süt", "Yumurta", "Fıstık", "Soya", "Balık", "Kabuklu deniz ürünleri", "Susam")
        commonAllergens.forEach { allergen ->
            if (allergens.contains(allergen, ignoreCase = true)) {
                warningBadges.add(allergen)
            }
        }
    }

    // Sertifikalardan onay badge'leri çıkar
    sections["certificates"]?.let { certificates ->
        val certificationKeywords = listOf("Halal", "Vegan", "Vegetarian", "Organic", "Non-GMO", "Gluten-Free")
        certificationKeywords.forEach { cert ->
            if (certificates.contains(cert, ignoreCase = true)) {
                approvalBadges.add(cert)
            }
        }
    }

    if (warningBadges.isNotEmpty() || approvalBadges.isNotEmpty()) {
        Column(modifier = modifier) {
            // Uyarı Badge'leri
            if (warningBadges.isNotEmpty()) {
                Text(
                    text = if (language == SupportedLanguage.TURKISH) "⚠️ Dikkat Edilmesi Gerekenler" else "⚠️ Warnings",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.padding(bottom = 16.dp)
                ) {
                    items(warningBadges) { badge ->
                        WarningBadge(text = badge)
                    }
                }
            }

            // Onay Badge'leri
            if (approvalBadges.isNotEmpty()) {
                Text(
                    text = if (language == SupportedLanguage.TURKISH) "✅ Olumlu Özellikler" else "✅ Positive Features",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(approvalBadges) { badge ->
                        ApprovalBadge(text = badge)
                    }
                }
            }
        }
    }
}

/**
 * Uyarı Badge'i - Kırmızı renkli uyarı göstergesi
 */
@Composable
fun WarningBadge(
    text: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        ),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.error)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(16.dp)
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = text,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * Onay Badge'i - Yeşil renkli onay göstergesi
 */
@Composable
fun ApprovalBadge(
    text: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(16.dp)
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = text,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
