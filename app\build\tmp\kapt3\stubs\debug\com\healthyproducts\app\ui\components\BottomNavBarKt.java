package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0007\"\u0017\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006\t"}, d2 = {"bottomNavItems", "", "Lcom/healthyproducts/app/ui/components/BottomNavItem;", "getBottomNavItems", "()Ljava/util/List;", "BottomNavBar", "", "navController", "Landroidx/navigation/NavController;", "app_debug"})
public final class BottomNavBarKt {
    
    /**
     * Alt navigasyon çubuğu öğeleri listesi
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.healthyproducts.app.ui.components.BottomNavItem> bottomNavItems = null;
    
    /**
     * Alt navigasyon çubuğu öğeleri listesi
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.healthyproducts.app.ui.components.BottomNavItem> getBottomNavItems() {
        return null;
    }
    
    /**
     * Alt navigasyon çubuğu bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void BottomNavBar(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
}