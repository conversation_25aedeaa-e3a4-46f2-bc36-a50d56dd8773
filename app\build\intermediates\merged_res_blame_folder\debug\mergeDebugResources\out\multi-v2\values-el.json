{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,417,539,643,746,866,1017,1145,1303,1393,1493,1592,1697,1815,1941,2046,2188,2324,2468,2648,2786,2906,3033,3157,3257,3356,3492,3629,3735,3841,3951,4095,4248,4362,4468,4555,4653,4750,4863,4953,5042,5145,5225,5308,5407,5509,5606,5704,5791,5897,5996,6098,6219,6299,6415", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "174,298,412,534,638,741,861,1012,1140,1298,1388,1488,1587,1692,1810,1936,2041,2183,2319,2463,2643,2781,2901,3028,3152,3252,3351,3487,3624,3730,3836,3946,4090,4243,4357,4463,4550,4648,4745,4858,4948,5037,5140,5220,5303,5402,5504,5601,5699,5786,5892,5991,6093,6214,6294,6410,6517"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9734,9858,9982,10096,10218,10322,10425,10545,10696,10824,10982,11072,11172,11271,11376,11494,11620,11725,11867,12003,12147,12327,12465,12585,12712,12836,12936,13035,13171,13308,13414,13520,13630,13774,13927,14041,14147,14234,14332,14429,14542,14632,14721,14824,14904,14987,15086,15188,15285,15383,15470,15576,15675,15777,15898,15978,16094", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "9853,9977,10091,10213,10317,10420,10540,10691,10819,10977,11067,11167,11266,11371,11489,11615,11720,11862,11998,12142,12322,12460,12580,12707,12831,12931,13030,13166,13303,13409,13515,13625,13769,13922,14036,14142,14229,14327,14424,14537,14627,14716,14819,14899,14982,15081,15183,15280,15378,15465,15571,15670,15772,15893,15973,16089,16196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2886,2984,3087,3187,3290,3398,3504,16932", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "2979,3082,3182,3285,3393,3499,3616,17028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,388,540,653,740,832,909,999,1089,1204,1316,1410,1504,1609,1730,1814,1902,2105,2198,2305,2417,2527", "endColumns": "182,149,151,112,86,91,76,89,89,114,111,93,93,104,120,83,87,202,92,106,111,109,164", "endOffsets": "233,383,535,648,735,827,904,994,1084,1199,1311,1405,1499,1604,1725,1809,1897,2100,2193,2300,2412,2522,2687"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3806,3989,4139,4291,4404,4491,4583,4660,4750,4840,4955,5067,5161,5255,5360,5481,5565,5653,5856,5949,6056,6168,6278", "endColumns": "182,149,151,112,86,91,76,89,89,114,111,93,93,104,120,83,87,202,92,106,111,109,164", "endOffsets": "3984,4134,4286,4399,4486,4578,4655,4745,4835,4950,5062,5156,5250,5355,5476,5560,5648,5851,5944,6051,6163,6273,6438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,16543", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,16624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "69", "startColumns": "4", "startOffsets": "7512", "endColumns": "163", "endOffsets": "7671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1023,1105,1180,1255,1333,1408,1487,1557", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1018,1100,1175,1250,1328,1403,1482,1552,1675"}, "to": {"startLines": "36,37,80,81,82,86,87,145,146,147,148,150,151,152,153,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3621,3720,8919,9020,9125,9559,9640,16201,16290,16380,16461,16629,16704,16779,16857,17033,17112,17182", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "3715,3801,9015,9120,9212,9635,9729,16285,16375,16456,16538,16699,16774,16852,16927,17107,17177,17300"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "79,83,84,85", "startColumns": "4,4,4,4", "startOffsets": "8809,9217,9324,9449", "endColumns": "109,106,124,109", "endOffsets": "8914,9319,9444,9554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "158,159", "startColumns": "4,4", "startOffsets": "17305,17403", "endColumns": "97,101", "endOffsets": "17398,17500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,482,610,717,894,1015,1129,1230,1415,1519,1685,1810,1984,2125,2190,2248", "endColumns": "106,181,127,106,176,120,113,100,184,103,165,124,173,140,64,57,78", "endOffsets": "299,481,609,716,893,1014,1128,1229,1414,1518,1684,1809,1983,2124,2189,2247,2326"}, "to": {"startLines": "61,62,63,64,65,66,67,68,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6443,6554,6740,6872,6983,7164,7289,7407,7676,7865,7973,8143,8272,8450,8595,8664,8726", "endColumns": "110,185,131,110,180,124,117,104,188,107,169,128,177,144,68,61,82", "endOffsets": "6549,6735,6867,6978,7159,7284,7402,7507,7860,7968,8138,8267,8445,8590,8659,8721,8804"}}]}]}