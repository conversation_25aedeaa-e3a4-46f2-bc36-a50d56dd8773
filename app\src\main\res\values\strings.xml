<resources>
    <string name="app_name">Healthy Products</string>

    <!-- Bottom Navigation -->
    <string name="home">Home</string>
    <string name="scan">Scan</string>
    <string name="favorites">Favorites</string>
    <string name="profile">Profile</string>
    <string name="settings">Settings</string>

    <!-- Home Screen -->
    <string name="recent_scans">Recent Scans</string>
    <string name="scan_barcode">Scan Barcode</string>
    <string name="scan_ingredients">Scan Ingredients</string>
    <string name="manual_entry">Manual Entry</string>
    <string name="welcome_message">Welcome to Healthy Products</string>
    <string name="no_recent_scans">No scans yet</string>
    <string name="food_analysis">Food Analysis</string>

    <!-- Scan Screen -->
    <string name="barcode_tab">Barcode</string>
    <string name="ocr_tab">Ingredients</string>
    <string name="camera_permission_required">Camera permission required</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="scanning">Scanning...</string>
    <string name="scan_instructions">Place the barcode inside the frame</string>
    <string name="ocr_instructions">Place the ingredients list inside the frame</string>
    <string name="ingredients_recognized">Ingredients recognized</string>
    <string name="edit_recognized_ingredients">You can edit the recognized ingredients:</string>
    <string name="ingredients_raw">Raw OCR Result (Uncorrected)</string>
    <string name="ingredients_corrected">Corrected Ingredients</string>
    <string name="frame_size">Frame Size:</string>
    <string name="size_small">Small</string>
    <string name="size_medium">Medium</string>
    <string name="size_large">Large</string>
    <string name="ingredients_guide_text">Align INGREDIENTS section here</string>

    <!-- Product Detail -->
    <string name="product_details">Product Details</string>
    <string name="ingredients">Ingredients</string>
    <string name="certificates">Certificates</string>
    <string name="halal_status">Halal Status</string>
    <string name="vegan_status">Vegan Status</string>
    <string name="harmful_ingredients">Harmful Ingredients</string>
    <string name="suspicious_ingredients">Suspicious Ingredients</string>
    <string name="add_to_favorites">Add to Favorites</string>
    <string name="remove_from_favorites">Remove from Favorites</string>

    <!-- Status Messages -->
    <string name="status_halal">Halal</string>
    <string name="status_haram">Haram</string>
    <string name="status_suspicious">Suspicious</string>
    <string name="status_vegan">Vegan</string>
    <string name="status_not_vegan">Not Vegan</string>
    <string name="status_harmful">Harmful</string>
    <string name="status_unhealthy">Unhealthy</string>

    <!-- Settings -->
    <string name="language">Language</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="show_halal_analysis">Show Halal Analysis</string>
    <string name="show_vegan_analysis">Show Vegan Analysis</string>
    <string name="show_kosher_analysis">Show Kosher Analysis</string>
    <string name="show_harmful_analysis">Show Harmful Analysis</string>
    <string name="show_unhealthy_analysis">Show Unhealthy Analysis</string>
    <string name="ai_model">AI Model</string>
    <string name="ai_model_selection">Select AI Model</string>
    <string name="about">About</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="terms_of_service">Terms of Service</string>
    <string name="version">Version</string>
    <string name="settings_category_analysis">Analysis Preferences</string>
    <string name="settings_category_appearance">Appearance</string>
    <string name="settings_category_about">About App</string>

    <!-- Profile -->
    <string name="login">Login</string>
    <string name="register">Register</string>
    <string name="logout">Logout</string>
    <string name="email">Email</string>
    <string name="password">Password</string>
    <string name="confirm_password">Confirm Password</string>
    <string name="name">Name</string>
    <string name="scan_history">Scan History</string>
    <string name="clear_history">Clear History</string>
    <string name="login_with_google">Login with Google</string>
    <string name="register_with_google">Register with Google</string>

    <!-- Firebase -->
    <string name="default_web_client_id" translatable="false">860618467975-sjnn72ruqqp99g3sm4j992qm0on8su7q.apps.googleusercontent.com
</string>

    <!-- Common -->
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="search">Search</string>
    <string name="loading">Loading...</string>
    <string name="error">Error</string>
    <string name="retry">Retry</string>
    <string name="ok">OK</string>
    <string name="back">Back</string>
    <string name="close">Close</string>
    <string name="analyze">Analyze</string>
    <string name="re_analyze">Re-analyze</string>
    <string name="correct_content">Correct Content</string>
    <string name="send_to_analysis">Send to Analysis</string>
    <string name="camera_permission_denied">Camera permission denied. You need to grant permission from app settings to use camera.</string>
    <string name="name_cannot_be_empty">Name cannot be empty</string>
    <string name="content_analysis">Content Analysis</string>
    <string name="ingredients_label">Ingredients:</string>
    <string name="analyzing_ingredients">Analyzing ingredients...</string>
    <string name="analysis_results">Analysis Results:</string>
    <string name="analysis_error">Analysis Error:</string>
    <string name="user_warnings">User Warnings</string>
    <string name="health_assessment">Health Assessment</string>
    <string name="additives_section">Additives</string>
    <string name="allergens_section">Allergens</string>
    <string name="sugars_section">Sugars</string>
    <string name="preservatives_section">Preservatives</string>
    <string name="certificates_section">Certificates</string>

    <!-- Additives -->
    <string name="additives">Additives</string>
    <string name="additive_detail">Additive Detail</string>
    <string name="add_additive">Add Additive</string>
    <string name="edit_additive">Edit Additive</string>
    <string name="additive_code">Additive Code</string>
    <string name="additive_name">Additive Name</string>
    <string name="additive_description">Description</string>
    <string name="additive_category">Category</string>
    <string name="additive_halal_status">Halal Status</string>
    <string name="additive_vegan_status">Vegan Status</string>
    <string name="additive_kosher_status">Kosher Status</string>
    <string name="additive_harmful_level">Harmful Level</string>
    <string name="additive_unhealthy_level">Unhealthy Level</string>
    <string name="additive_notes">Notes</string>

    <!-- Language -->
    <string name="language_english">English</string>
    <string name="language_turkish">Turkish</string>
    <string name="language_selection">Select Language</string>
    <string name="language_changed">Language changed</string>

    <!-- Data Upload -->
    <string name="data_upload_screen_title">Data Upload</string>
    <string name="data_upload_description">Upload food data to Firebase. This operation may take some time.</string>
    <string name="upload_data_button">Upload Data</string>
    <string name="uploading_data">Uploading data...</string>
    <string name="data_upload_success">Data uploaded successfully</string>
    <string name="data_upload_error">Error uploading data</string>
    <string name="upload_again_button">Upload Again</string>
    <string name="try_again_button">Try Again</string>

    <!-- Food Analysis -->
    <string name="allergens">Allergens</string>
    <string name="fats">Fats</string>
    <string name="sugars">Sugars</string>
    <string name="food_certificates">Food Certificates</string>
    <string name="allergen_detail">Allergen Detail</string>
    <string name="fat_detail">Fat Detail</string>
    <string name="sugar_detail">Sugar Detail</string>
    <string name="certificate_detail">Certificate Detail</string>
    <string name="functional_types">Functional Types</string>
    <string name="description">Description</string>
    <string name="origin">Origin</string>
    <string name="usage">Usage</string>
    <string name="health_effects">Health Effects</string>
    <string name="risk_level">Risk Level</string>
    <string name="dietary_statuses">Dietary Statuses</string>
    <string name="religious_statuses">Religious Statuses</string>
    <string name="vegetarian_status">Vegetarian Status</string>
    <string name="kosher_status">Kosher Status</string>
    <string name="status_vegetarian">Vegetarian</string>
    <string name="status_not_vegetarian">Not Vegetarian</string>
    <string name="status_kosher">Kosher</string>
    <string name="status_not_kosher">Not Kosher</string>
    <string name="notes">Notes</string>
    <string name="no_results">No results found</string>
    <string name="chemical_structure">Chemical Structure</string>
    <string name="unhealthy_level">Unhealthy Level</string>
    <string name="harmful_level">Harmful Level</string>
    <string name="glycemic_info">Glycemic Information</string>
    <string name="glycemic_index">Glycemic Index</string>
    <string name="glycemic_load">Glycemic Load</string>
    <string name="certifying_body">Certifying Body</string>
    <string name="certification_criteria">Certification Criteria</string>
    <string name="certification_process">Certification Process</string>
    <string name="certification_validity">Certification Validity</string>
    <string name="food_vegan_status">Food Vegan Status</string>
    <string name="food_halal_status">Food Halal Status</string>

    <!-- Import -->
    <string name="import_allergens">Import Allergens</string>
    <string name="import_fats">Import Fats</string>
    <string name="import_sugars">Import Sugars</string>
    <string name="import_preservatives">Import Preservatives</string>
    <string name="import_success">Successfully imported %1$d items</string>
    <string name="import_error">Error importing data: %1$s</string>
    <string name="importing">Importing...</string>

    <!-- Preservatives -->
    <string name="preservatives">Preservatives</string>
    <string name="preservative_detail">Preservative Detail</string>
    <string name="preservative">Preservative</string>
    <string name="synthetic">Synthetic</string>
    <string name="toxic">Toxic</string>
    <string name="carcinogen">Carcinogen</string>
    <string name="endocrine_disruptor">Endocrine Disruptor</string>
    <string name="residue">Residue</string>
    <string name="antibiotic">Antibiotic</string>
    <string name="industrial_residue">Industrial Residue</string>
    <string name="pesticide">Pesticide</string>
    <string name="main_risk">Main Risk</string>
    <string name="functional_type">Functional Type</string>
    <string name="common_products">Common Products</string>
    <string name="no_preservatives_found">No preservatives found</string>

    <!-- Food Analysis Screen -->
    <string name="food_analysis_screen">Food Analysis</string>
    <string name="food_analysis_description">Analyze food ingredients for allergens, fats, sugars, preservatives and certifications</string>

    <!-- Food Certificates -->
    <string name="applies_to">Applies To</string>
    <string name="risk_if_missing">Risk If Missing</string>
    <string name="import_data">Import Data</string>
    <string name="search_certificates">Search Certificates</string>

    <!-- User Food Preferences -->
    <string name="add_custom_allergen">Add Custom Allergen</string>
    <string name="add_custom_fat">Add Custom Fat</string>
    <string name="add_custom_sugar">Add Custom Sugar</string>
    <string name="add_custom_preservative">Add Custom Preservative</string>
    <string name="add_custom_certificate">Add Custom Certificate</string>
    <string name="add_custom_item">Add Custom Item</string>
    <string name="custom_item_name">Custom Item Name</string>
    <string name="save_preferences">Save Preferences</string>
    <string name="login_required">Login required to save preferences</string>
    <string name="preferences_saved">Preferences saved successfully</string>
    <string name="preferences_error">Error saving preferences</string>
    <string name="user_food_preferences">My Food Preferences</string>
    <string name="no_preferences">No preferences found</string>
    <string name="custom_item">Custom Item</string>

    <!-- Product/Barcode -->
    <string name="add_barcode">Add Barcode</string>
    <string name="save_product">Save Product</string>
    <string name="product_name">Product Name</string>
    <string name="product_brand">Product Brand</string>
    <string name="product_saved">Product saved successfully</string>
    <string name="product_save_error">Error saving product</string>
    <string name="barcode_scan_instruction">Point camera at barcode to scan</string>
    <string name="barcode_detected">Barcode detected: %1$s</string>
    <string name="product_found">Product found in database</string>
    <string name="product_not_found">Product not found, performing OCR analysis</string>
    <string name="summary_section">Summary</string>
    <string name="raw_analysis_result">Raw Analysis Result</string>

</resources>