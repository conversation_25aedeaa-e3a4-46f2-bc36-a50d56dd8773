package com.healthyproducts.app;

/**
 * Uygulama sınıfı
 */
@dagger.hilt.android.HiltAndroidApp()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u000b\u001a\u00020\fH\u0002J\b\u0010\r\u001a\u00020\fH\u0002J\b\u0010\u000e\u001a\u00020\fH\u0016J\u0016\u0010\u000f\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0082@\u00a2\u0006\u0002\u0010\u0012R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u0005\u001a\u00020\u00068\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\n\u00a8\u0006\u0013"}, d2 = {"Lcom/healthyproducts/app/HealthyProductsApp;", "Landroid/app/Application;", "()V", "applicationScope", "Lkotlinx/coroutines/CoroutineScope;", "firestoreRepository", "Lcom/healthyproducts/app/data/repository/FirestoreRepository;", "getFirestoreRepository", "()Lcom/healthyproducts/app/data/repository/FirestoreRepository;", "setFirestoreRepository", "(Lcom/healthyproducts/app/data/repository/FirestoreRepository;)V", "initializeFirebase", "", "initializeLanguage", "onCreate", "updateFirestoreLanguage", "languageCode", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class HealthyProductsApp extends android.app.Application {
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope applicationScope = null;
    @javax.inject.Inject()
    public com.healthyproducts.app.data.repository.FirestoreRepository firestoreRepository;
    
    public HealthyProductsApp() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.FirestoreRepository getFirestoreRepository() {
        return null;
    }
    
    public final void setFirestoreRepository(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirestoreRepository p0) {
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    /**
     * Firebase'i başlatma
     */
    private final void initializeFirebase() {
    }
    
    /**
     * Dil ayarlarını başlatma
     */
    private final void initializeLanguage() {
    }
    
    /**
     * Firestore'daki dil ayarını günceller
     */
    private final java.lang.Object updateFirestoreLanguage(java.lang.String languageCode, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}