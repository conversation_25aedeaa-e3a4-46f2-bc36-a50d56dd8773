package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0018\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\u001a \u0010\u0000\u001a\u00020\u0001*\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"clickable", "Landroidx/compose/ui/Modifier;", "enabled", "", "onClick", "Lkotlin/Function0;", "", "app_debug"})
public final class ModifierExtensionsKt {
    
    /**
     * Clickable modifier extension
     *
     * @param enabled T<PERSON><PERSON><PERSON><PERSON>n etkin olup olmadığı
     * @param onClick Tıklama işlemi
     */
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.Modifier clickable(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier $this$clickable, boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
        return null;
    }
}