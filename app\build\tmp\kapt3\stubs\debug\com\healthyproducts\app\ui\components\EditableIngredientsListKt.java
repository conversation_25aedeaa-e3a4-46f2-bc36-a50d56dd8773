package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a0\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0018\u0010\u0005\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a:\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u00a8\u0006\u000e"}, d2 = {"EditableIngredientsList", "", "ingredients", "", "", "onIngredientsUpdated", "Lkotlin/Function1;", "IngredientItem", "ingredient", "isEditing", "", "onEditToggle", "Lkotlin/Function0;", "onIngredientChanged", "app_debug"})
public final class EditableIngredientsListKt {
    
    /**
     * Düzenlenebilir içerik listesi bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void EditableIngredientsList(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> ingredients, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<java.lang.String>, kotlin.Unit> onIngredientsUpdated) {
    }
    
    /**
     * İçerik öğesi bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void IngredientItem(@org.jetbrains.annotations.NotNull()
    java.lang.String ingredient, boolean isEditing, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditToggle, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onIngredientChanged) {
    }
}