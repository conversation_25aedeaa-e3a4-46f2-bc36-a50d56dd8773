package com.healthyproducts.app.data.service;

/**
 * Gıda analiz sonuçlarını temsil eden veri sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u007f\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0003\u0012\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\u0002\u0010\u0013J\u000f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010 \u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J\u000f\u0010!\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\f0\u0003H\u00c6\u0003J\u000f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0003H\u00c6\u0003J\u000f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0012H\u00c6\u0003J\u0083\u0001\u0010&\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u00032\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u00c6\u0001J\u0013\u0010\'\u001a\u00020(2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020\u0012H\u00d6\u0001J\t\u0010+\u001a\u00020,H\u00d6\u0001R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0015R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0015R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0015\u00a8\u0006-"}, d2 = {"Lcom/healthyproducts/app/data/service/FoodAnalysisResult;", "", "allergenWarnings", "", "Lcom/healthyproducts/app/data/service/AllergenWarning;", "sugarWarnings", "Lcom/healthyproducts/app/data/service/SugarWarning;", "fatWarnings", "Lcom/healthyproducts/app/data/service/FatWarning;", "intoleranceWarnings", "Lcom/healthyproducts/app/data/service/IntoleranceWarning;", "preservativeWarnings", "Lcom/healthyproducts/app/data/service/PreservativeWarning;", "additiveWarnings", "Lcom/healthyproducts/app/data/service/AdditiveWarning;", "certificateVerifications", "Lcom/healthyproducts/app/data/service/CertificateVerification;", "overallRiskLevel", "", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;I)V", "getAdditiveWarnings", "()Ljava/util/List;", "getAllergenWarnings", "getCertificateVerifications", "getFatWarnings", "getIntoleranceWarnings", "getOverallRiskLevel", "()I", "getPreservativeWarnings", "getSugarWarnings", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
public final class FoodAnalysisResult {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.data.service.AllergenWarning> allergenWarnings = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.data.service.SugarWarning> sugarWarnings = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.data.service.FatWarning> fatWarnings = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.data.service.IntoleranceWarning> intoleranceWarnings = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.data.service.PreservativeWarning> preservativeWarnings = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.data.service.AdditiveWarning> additiveWarnings = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.healthyproducts.app.data.service.CertificateVerification> certificateVerifications = null;
    private final int overallRiskLevel = 0;
    
    public FoodAnalysisResult(@org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.AllergenWarning> allergenWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.SugarWarning> sugarWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.FatWarning> fatWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.IntoleranceWarning> intoleranceWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.PreservativeWarning> preservativeWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.AdditiveWarning> additiveWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.CertificateVerification> certificateVerifications, int overallRiskLevel) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.AllergenWarning> getAllergenWarnings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.SugarWarning> getSugarWarnings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.FatWarning> getFatWarnings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.IntoleranceWarning> getIntoleranceWarnings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.PreservativeWarning> getPreservativeWarnings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.AdditiveWarning> getAdditiveWarnings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.CertificateVerification> getCertificateVerifications() {
        return null;
    }
    
    public final int getOverallRiskLevel() {
        return 0;
    }
    
    public FoodAnalysisResult() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.AllergenWarning> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.SugarWarning> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.FatWarning> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.IntoleranceWarning> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.PreservativeWarning> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.AdditiveWarning> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.healthyproducts.app.data.service.CertificateVerification> component7() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.service.FoodAnalysisResult copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.AllergenWarning> allergenWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.SugarWarning> sugarWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.FatWarning> fatWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.IntoleranceWarning> intoleranceWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.PreservativeWarning> preservativeWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.AdditiveWarning> additiveWarnings, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.data.service.CertificateVerification> certificateVerifications, int overallRiskLevel) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}