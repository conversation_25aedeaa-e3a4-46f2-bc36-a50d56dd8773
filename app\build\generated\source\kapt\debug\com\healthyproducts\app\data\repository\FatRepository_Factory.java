package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FatRepository_Factory implements Factory<FatRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public FatRepository_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public FatRepository get() {
    return newInstance(firestoreProvider.get());
  }

  public static FatRepository_Factory create(Provider<FirebaseFirestore> firestoreProvider) {
    return new FatRepository_Factory(firestoreProvider);
  }

  public static FatRepository newInstance(FirebaseFirestore firestore) {
    return new FatRepository(firestore);
  }
}
