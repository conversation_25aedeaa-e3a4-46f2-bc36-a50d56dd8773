package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a0\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u001a,\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u00a8\u0006\u000e"}, d2 = {"BarcodeCameraPreview", "", "modifier", "Landroidx/compose/ui/Modifier;", "flashEnabled", "", "onBarcodeDetected", "Lkotlin/Function1;", "", "processImageProxy", "barcodeScanner", "Lcom/google/mlkit/vision/barcode/BarcodeScanner;", "imageProxy", "Landroidx/camera/core/ImageProxy;", "app_debug"})
public final class BarcodeCameraPreviewKt {
    
    /**
     * Barkod tarama için kamera preview component'i
     */
    @androidx.compose.runtime.Composable()
    public static final void BarcodeCameraPreview(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, boolean flashEnabled, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onBarcodeDetected) {
    }
    
    /**
     * Image proxy'yi işleyerek barkod tespiti yapar
     */
    @androidx.camera.core.ExperimentalGetImage()
    private static final void processImageProxy(com.google.mlkit.vision.barcode.BarcodeScanner barcodeScanner, androidx.camera.core.ImageProxy imageProxy, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onBarcodeDetected) {
    }
}