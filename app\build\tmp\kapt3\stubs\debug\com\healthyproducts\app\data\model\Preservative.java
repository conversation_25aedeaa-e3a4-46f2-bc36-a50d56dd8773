package com.healthyproducts.app.data.model;

/**
 * <PERSON>ru<PERSON><PERSON> ve antibakteriyel kalıntıları temsil eden veri sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\b<\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u00ad\u0001\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0004\u0012\b\b\u0002\u0010\b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\t\u001a\u00020\u0004\u0012\b\b\u0002\u0010\n\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e\u0012\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0015J\t\u0010:\u001a\u00020\u0004H\u00c6\u0003J\t\u0010;\u001a\u00020\u0004H\u00c6\u0003J\t\u0010<\u001a\u00020\u0004H\u00c6\u0003J\u000f\u0010=\u001a\b\u0012\u0004\u0012\u00020\u00040\u000eH\u00c6\u0003J\u000f\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00040\u000eH\u00c6\u0003J\t\u0010?\u001a\u00020\u0004H\u00c6\u0003J\t\u0010@\u001a\u00020\u0004H\u00c6\u0003J\t\u0010A\u001a\u00020\u0004H\u00c6\u0003J\t\u0010B\u001a\u00020\u0004H\u00c6\u0003J\t\u0010C\u001a\u00020\u0004H\u00c6\u0003J\t\u0010D\u001a\u00020\u0004H\u00c6\u0003J\t\u0010E\u001a\u00020\u0004H\u00c6\u0003J\t\u0010F\u001a\u00020\u0004H\u00c6\u0003J\t\u0010G\u001a\u00020\fH\u00c6\u0003J\u000f\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00040\u000eH\u00c6\u0003J\u00b1\u0001\u0010I\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\u00042\b\b\u0002\u0010\t\u001a\u00020\u00042\b\b\u0002\u0010\n\u001a\u00020\u00042\b\b\u0002\u0010\u000b\u001a\u00020\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00042\b\b\u0002\u0010\u0010\u001a\u00020\u00042\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e2\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e2\b\b\u0002\u0010\u0013\u001a\u00020\u00042\b\b\u0002\u0010\u0014\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010J\u001a\u00020K2\b\u0010L\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0014\u0010M\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e2\u0006\u0010N\u001a\u00020OJ\u000e\u0010P\u001a\u00020\u00042\u0006\u0010N\u001a\u00020OJ\u000e\u0010Q\u001a\u00020\u00042\u0006\u0010N\u001a\u00020OJ\u000e\u0010R\u001a\u00020\u00042\u0006\u0010N\u001a\u00020OJ\u000e\u0010S\u001a\u00020\u00042\u0006\u0010N\u001a\u00020OJ\u000e\u0010T\u001a\u00020\u00042\u0006\u0010N\u001a\u00020OJ\u000e\u0010U\u001a\u00020\u00042\u0006\u0010N\u001a\u00020OJ\t\u0010V\u001a\u00020\fH\u00d6\u0001J\u0006\u0010W\u001a\u00020KJ\u0006\u0010X\u001a\u00020KJ\u0006\u0010Y\u001a\u00020KJ\u0006\u0010Z\u001a\u00020KJ\u0006\u0010[\u001a\u00020KJ\t\u0010\\\u001a\u00020\u0004H\u00d6\u0001R$\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019R$\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u0017\"\u0004\b\u001b\u0010\u0019R\u001e\u0010\n\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u0010\u001d\"\u0004\b\u001e\u0010\u001fR\u001e\u0010\t\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010\u001d\"\u0004\b!\u0010\u001fR$\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010\u0017\"\u0004\b#\u0010\u0019R\u001e\u0010\u0003\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010\u001d\"\u0004\b%\u0010\u001fR\u001e\u0010\u0010\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\u001d\"\u0004\b\'\u0010\u001fR\u001e\u0010\u000f\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010\u001d\"\u0004\b)\u0010\u001fR\u001e\u0010\u0007\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b*\u0010\u001d\"\u0004\b+\u0010\u001fR\u001e\u0010\u0006\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010\u001d\"\u0004\b-\u0010\u001fR\u001e\u0010\u0014\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010\u001d\"\u0004\b/\u0010\u001fR\u001e\u0010\u0013\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u0010\u001d\"\u0004\b1\u0010\u001fR\u001e\u0010\u0005\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b2\u0010\u001d\"\u0004\b3\u0010\u001fR\u001e\u0010\u000b\u001a\u00020\f8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u00105\"\u0004\b6\u00107R\u001e\u0010\b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b8\u0010\u001d\"\u0004\b9\u0010\u001f\u00a8\u0006]"}, d2 = {"Lcom/healthyproducts/app/data/model/Preservative;", "", "()V", "id", "", "preservativeId", "nameTr", "nameEn", "symbol", "descriptionTr", "descriptionEn", "riskLevel", "", "functionalType", "", "mainRiskTr", "mainRiskEn", "commonProductsTr", "commonProductsEn", "notesTr", "notesEn", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V", "getCommonProductsEn", "()Ljava/util/List;", "setCommonProductsEn", "(Ljava/util/List;)V", "getCommonProductsTr", "setCommonProductsTr", "getDescriptionEn", "()Ljava/lang/String;", "setDescriptionEn", "(Ljava/lang/String;)V", "getDescriptionTr", "setDescriptionTr", "getFunctionalType", "setFunctionalType", "getId", "setId", "getMainRiskEn", "setMainRiskEn", "getMainRiskTr", "setMainRiskTr", "getNameEn", "setNameEn", "getNameTr", "setNameTr", "getNotesEn", "setNotesEn", "getNotesTr", "setNotesTr", "getPreservativeId", "setPreservativeId", "getRiskLevel", "()I", "setRiskLevel", "(I)V", "getSymbol", "setSymbol", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "getCommonProducts", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "getDescription", "getHealthEffects", "getMainRisk", "getName", "getNotes", "getUsage", "hashCode", "isCarcinogen", "isEndocrineDisruptor", "isPreservative", "isSynthetic", "isToxic", "toString", "app_debug"})
public final class Preservative {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String id;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String preservativeId;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String symbol;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionEn;
    private int riskLevel;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> functionalType;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String mainRiskTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String mainRiskEn;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> commonProductsTr;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> commonProductsEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesEn;
    
    public Preservative(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String preservativeId, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType, @org.jetbrains.annotations.NotNull()
    java.lang.String mainRiskTr, @org.jetbrains.annotations.NotNull()
    java.lang.String mainRiskEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> commonProductsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> commonProductsEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn) {
        super();
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    public final void setId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "preservativeId")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPreservativeId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "preservativeId")
    public final void setPreservativeId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    public final void setNameTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    public final void setNameEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSymbol() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    public final void setSymbol(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    public final void setDescriptionTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    public final void setDescriptionEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final int getRiskLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final void setRiskLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getFunctionalType() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    public final void setFunctionalType(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "main_risk_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMainRiskTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "main_risk_tr")
    public final void setMainRiskTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "main_risk_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMainRiskEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "main_risk_en")
    public final void setMainRiskEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "common_products_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getCommonProductsTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "common_products_tr")
    public final void setCommonProductsTr(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "common_products_en")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getCommonProductsEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "common_products_en")
    public final void setCommonProductsEn(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    public final void setNotesTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    public final void setNotesEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public Preservative() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMainRisk(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getCommonProducts(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotes(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffects(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUsage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    public final boolean isPreservative() {
        return false;
    }
    
    public final boolean isSynthetic() {
        return false;
    }
    
    public final boolean isToxic() {
        return false;
    }
    
    public final boolean isCarcinogen() {
        return false;
    }
    
    public final boolean isEndocrineDisruptor() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.model.Preservative copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String preservativeId, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType, @org.jetbrains.annotations.NotNull()
    java.lang.String mainRiskTr, @org.jetbrains.annotations.NotNull()
    java.lang.String mainRiskEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> commonProductsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> commonProductsEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}