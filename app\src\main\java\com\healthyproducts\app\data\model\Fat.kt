package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName

/**
 * Yağ türünü temsil eden veri sınıfı
 */
data class Fat(
    @get:PropertyName("id")
    @set:PropertyName("id")
    var id: String = "",

    // Benzersiz tanımlayıcı (örn. "palm_oil", "olive_oil")
    @get:PropertyName("fatId")
    @set:PropertyName("fatId")
    var fatId: String = "",

    // Zararlılık seviyesi (0-5)
    @get:PropertyName("harmful_level")
    @set:PropertyName("harmful_level")
    var harmfulLevel: Int = 0,

    // Sağlıksızlık seviyesi (0-5)
    @get:PropertyName("unhealthy_level")
    @set:PropertyName("unhealthy_level")
    var unhealthyLevel: Int = 0,

    @get:PropertyName("name_tr")
    @set:PropertyName("name_tr")
    var nameTr: String = "", // Yağın Türkçe adı

    @get:PropertyName("name_en")
    @set:PropertyName("name_en")
    var nameEn: String = "", // Yağın İngilizce adı

    @get:PropertyName("symbol")
    @set:PropertyName("symbol")
    var symbol: String = "", // Emoji veya ikon referansı

    @get:PropertyName("description_tr")
    @set:PropertyName("description_tr")
    var descriptionTr: String = "", // Yağın Türkçe açıklaması

    @get:PropertyName("description_en")
    @set:PropertyName("description_en")
    var descriptionEn: String = "", // Yağın İngilizce açıklaması

    @get:PropertyName("origin_tr")
    @set:PropertyName("origin_tr")
    var originTr: String = "", // Yağın Türkçe kökeni

    @get:PropertyName("origin_en")
    @set:PropertyName("origin_en")
    var originEn: String = "", // Yağın İngilizce kökeni

    @get:PropertyName("type_tr")
    @set:PropertyName("type_tr")
    var typeTr: String = "", // Yağın Türkçe türü

    @get:PropertyName("type_en")
    @set:PropertyName("type_en")
    var typeEn: String = "", // Yağın İngilizce türü

    @get:PropertyName("health_effect_tr")
    @set:PropertyName("health_effect_tr")
    var healthEffectTr: String = "", // Sağlık üzerindeki Türkçe etkileri

    @get:PropertyName("health_effect_en")
    @set:PropertyName("health_effect_en")
    var healthEffectEn: String = "", // Sağlık üzerindeki İngilizce etkileri

    @get:PropertyName("risk_level")
    @set:PropertyName("risk_level")
    var riskLevel: Int = 0, // Risk seviyesi (1-3)

    @get:PropertyName("labels_tr")
    @set:PropertyName("labels_tr")
    var labelsTr: List<String> = listOf(), // Türkçe alternatif etiketler

    @get:PropertyName("labels_en")
    @set:PropertyName("labels_en")
    var labelsEn: List<String> = listOf(), // İngilizce alternatif etiketler

    @get:PropertyName("notes_tr")
    @set:PropertyName("notes_tr")
    var notesTr: String = "", // Türkçe ek notlar

    @get:PropertyName("notes_en")
    @set:PropertyName("notes_en")
    var notesEn: String = "", // İngilizce ek notlar

    @get:PropertyName("functional_type")
    @set:PropertyName("functional_type")
    var functionalType: List<String> = listOf() // Fonksiyonel tür (örn. "plant_based", "animal_based", "healthy")
) {
    // Firestore için boş constructor
    constructor() : this(
        id = "",
        fatId = "",
        harmfulLevel = 0,
        unhealthyLevel = 0,
        nameTr = "",
        nameEn = "",
        symbol = "",
        descriptionTr = "",
        descriptionEn = "",
        originTr = "",
        originEn = "",
        typeTr = "",
        typeEn = "",
        healthEffectTr = "",
        healthEffectEn = "",
        riskLevel = 0,
        labelsTr = listOf(),
        labelsEn = listOf(),
        notesTr = "",
        notesEn = "",
        functionalType = listOf()
    )

    // Kullanıcının dil tercihine göre adı döndürür
    fun getName(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> nameTr.ifEmpty { nameEn }
            SupportedLanguage.ENGLISH -> nameEn.ifEmpty { nameTr }
        }
    }

    // Kullanıcının dil tercihine göre açıklamayı döndürür
    fun getDescription(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> descriptionTr.ifEmpty { descriptionEn }
            SupportedLanguage.ENGLISH -> descriptionEn.ifEmpty { descriptionTr }
        }
    }

    // Kullanıcının dil tercihine göre kökeni döndürür
    fun getOrigin(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> originTr.ifEmpty { originEn }
            SupportedLanguage.ENGLISH -> originEn.ifEmpty { originTr }
        }
    }

    // Kullanıcının dil tercihine göre türü döndürür
    fun getType(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> typeTr.ifEmpty { typeEn }
            SupportedLanguage.ENGLISH -> typeEn.ifEmpty { typeTr }
        }
    }

    // Kullanıcının dil tercihine göre sağlık etkilerini döndürür
    fun getHealthEffect(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> healthEffectTr.ifEmpty { healthEffectEn }
            SupportedLanguage.ENGLISH -> healthEffectEn.ifEmpty { healthEffectTr }
        }
    }

    // Kullanıcının dil tercihine göre etiketleri döndürür
    fun getLabels(language: SupportedLanguage): List<String> {
        return when (language) {
            SupportedLanguage.TURKISH -> labelsTr.ifEmpty { labelsEn }
            SupportedLanguage.ENGLISH -> labelsEn.ifEmpty { labelsTr }
        }
    }

    // Kullanıcının dil tercihine göre notları döndürür
    fun getNotes(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> notesTr.ifEmpty { notesEn }
            SupportedLanguage.ENGLISH -> notesEn.ifEmpty { notesTr }
        }
    }

    // Kullanıcının dil tercihine göre sağlık etkilerini döndürür
    fun getHealthEffects(language: SupportedLanguage): String {
        return getHealthEffect(language)
    }

    // Kullanıcının dil tercihine göre kimyasal yapıyı döndürür
    fun getChemicalStructure(language: SupportedLanguage): String {
        return getType(language)
    }

    // Kullanıcının dil tercihine göre kullanımı döndürür
    fun getUsage(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> "Kullanım bilgisi mevcut değil"
            SupportedLanguage.ENGLISH -> "Usage information not available"
        }
    }

    // Vegan durumlarını döndürür
    val veganStatuses: List<String>
        get() = functionalType.filter { it == "vegan" || it == "vegetarian" }

    // Dini durumları döndürür
    val religiousStatuses: List<String>
        get() = functionalType.filter { it == "halal" || it == "kosher" || it == "haram" }
}
