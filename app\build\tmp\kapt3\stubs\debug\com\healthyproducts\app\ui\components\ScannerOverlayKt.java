package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\u001aN\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\r\u0010\u000e\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u000f"}, d2 = {"ScannerOverlay", "", "modifier", "Landroidx/compose/ui/Modifier;", "frameColor", "Landroidx/compose/ui/graphics/Color;", "frameRatio", "", "frameHeightRatio", "frameSize", "Lcom/healthyproducts/app/ui/components/FrameSize;", "showIngredientsGuide", "", "ScannerOverlay-3IgeMak", "(Landroidx/compose/ui/Modifier;JFFLcom/healthyproducts/app/ui/components/FrameSize;Z)V", "app_debug"})
public final class ScannerOverlayKt {
}