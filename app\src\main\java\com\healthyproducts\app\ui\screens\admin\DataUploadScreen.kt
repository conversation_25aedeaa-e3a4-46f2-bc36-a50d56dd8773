package com.healthyproducts.app.ui.screens.admin

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.util.DataUploader
import kotlinx.coroutines.launch

/**
 * Verileri Firebase'e yüklemek için ekran
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataUploadScreen(
    navController: NavController
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }

    var isLoading by remember { mutableStateOf(false) }
    var uploadStatus by remember { mutableStateOf<UploadStatus>(UploadStatus.Initial) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.data_upload_screen_title)) },
                navigationIcon = { BackButton(navController) }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.data_upload_description),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(bottom = 24.dp)
            )

            when (uploadStatus) {
                UploadStatus.Initial -> {
                    Button(
                        onClick = {
                            isLoading = true
                            scope.launch {
                                try {
                                    val dataUploader = DataUploader(context)

                                    // Veri yükleme işlemini başlat
                                    val result = dataUploader.uploadAllData()

                                    if (result.isSuccess) {
                                        uploadStatus = UploadStatus.Success
                                        snackbarHostState.showSnackbar(
                                            context.getString(R.string.data_upload_success)
                                        )
                                    } else {
                                        val errorMessage = result.exceptionOrNull()?.message ?: "Unknown error"
                                        uploadStatus = UploadStatus.Error(errorMessage)
                                        snackbarHostState.showSnackbar(
                                            context.getString(R.string.data_upload_error)
                                        )
                                        android.util.Log.e("DataUploadScreen", "Error uploading data: $errorMessage")
                                    }
                                } catch (e: Exception) {
                                    val errorMessage = e.message ?: "Unknown error"
                                    uploadStatus = UploadStatus.Error(errorMessage)
                                    snackbarHostState.showSnackbar(
                                        context.getString(R.string.data_upload_error)
                                    )
                                    android.util.Log.e("DataUploadScreen", "Exception during data upload", e)
                                } finally {
                                    isLoading = false
                                }
                            }
                        },
                        modifier = Modifier.fillMaxWidth(0.8f),
                        enabled = !isLoading
                    ) {
                        Text(stringResource(R.string.upload_data_button))
                    }

                    if (isLoading) {
                        Spacer(modifier = Modifier.height(16.dp))
                        CircularProgressIndicator()
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = stringResource(R.string.uploading_data),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }

                UploadStatus.Success -> {
                    Text(
                        text = stringResource(R.string.data_upload_success),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Button(
                        onClick = {
                            uploadStatus = UploadStatus.Initial
                        },
                        modifier = Modifier.fillMaxWidth(0.8f)
                    ) {
                        Text(stringResource(R.string.upload_again_button))
                    }
                }

                is UploadStatus.Error -> {
                    Text(
                        text = stringResource(R.string.data_upload_error),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.error
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = (uploadStatus as UploadStatus.Error).message,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.error
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Button(
                        onClick = {
                            uploadStatus = UploadStatus.Initial
                        },
                        modifier = Modifier.fillMaxWidth(0.8f)
                    ) {
                        Text(stringResource(R.string.try_again_button))
                    }
                }
            }
        }
    }
}

/**
 * Yükleme durumu
 */
sealed class UploadStatus {
    object Initial : UploadStatus()
    object Success : UploadStatus()
    data class Error(val message: String) : UploadStatus()
}
