package com.healthyproducts.app.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideDeepSeekBaseUrlFactory implements Factory<String> {
  @Override
  public String get() {
    return provideDeepSeekBaseUrl();
  }

  public static NetworkModule_ProvideDeepSeekBaseUrlFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static String provideDeepSeekBaseUrl() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideDeepSeekBaseUrl());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideDeepSeekBaseUrlFactory INSTANCE = new NetworkModule_ProvideDeepSeekBaseUrlFactory();
  }
}
