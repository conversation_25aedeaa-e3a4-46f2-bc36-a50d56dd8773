package com.healthyproducts.app.util

import android.annotation.SuppressLint
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions

/**
 * Metin tanıma için ImageAnalysis.Analyzer sınıfı
 */
class TextRecognitionAnalyzer(
    private val onTextDetected: (Text) -> Unit
) : ImageAnalysis.Analyzer {
    
    // Metin tanıyıcı
    private val recognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    
    @SuppressLint("UnsafeOptInUsageError")
    override fun analyze(imageProxy: ImageProxy) {
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(
                mediaImage,
                imageProxy.imageInfo.rotationDegrees
            )
            
            // Metin tanıma işlemi
            recognizer.process(image)
                .addOnSuccessListener { text ->
                    // Metin bulundu
                    if (text.text.isNotEmpty()) {
                        onTextDetected(text)
                    }
                }
                .addOnFailureListener {
                    // Metin tanıma hatası
                }
                .addOnCompleteListener {
                    // Görüntüyü kapat
                    imageProxy.close()
                }
        } else {
            // Görüntü yoksa kapat
            imageProxy.close()
        }
    }
}
