package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserFoodPreferenceRepository_Factory implements Factory<UserFoodPreferenceRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public UserFoodPreferenceRepository_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public UserFoodPreferenceRepository get() {
    return newInstance(firestoreProvider.get());
  }

  public static UserFoodPreferenceRepository_Factory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new UserFoodPreferenceRepository_Factory(firestoreProvider);
  }

  public static UserFoodPreferenceRepository newInstance(FirebaseFirestore firestore) {
    return new UserFoodPreferenceRepository(firestore);
  }
}
