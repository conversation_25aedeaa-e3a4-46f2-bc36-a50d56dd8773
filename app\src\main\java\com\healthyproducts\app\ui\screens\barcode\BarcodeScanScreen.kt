package com.healthyproducts.app.ui.screens.barcode

import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.FlashOff
import androidx.compose.material.icons.filled.FlashOn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.google.firebase.auth.FirebaseAuth
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.SaveProductRequest
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.repository.ImageUploadRepository
import com.healthyproducts.app.data.repository.ProductRepository
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.components.BarcodeCameraPreview
import com.healthyproducts.app.ui.components.ImagePickerComponent
import com.healthyproducts.app.ui.components.SaveProductDialog
import com.healthyproducts.app.ui.components.uriToBitmap
import kotlinx.coroutines.launch

/**
 * Barkod tarama ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BarcodeScanScreen(
    navController: NavController
) {
    // Hilt injection
    val productRepository: ProductRepository = hiltViewModel()
    val imageUploadRepository: ImageUploadRepository = hiltViewModel()
    val auth: FirebaseAuth = hiltViewModel()
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // State variables
    var scannedBarcode by remember { mutableStateOf("") }
    var isScanning by remember { mutableStateOf(true) }
    var hasPermission by remember { mutableStateOf(false) }
    var flashEnabled by remember { mutableStateOf(false) }
    var selectedImageBitmap by remember { mutableStateOf<android.graphics.Bitmap?>(null) }
    var isUploadingImage by remember { mutableStateOf(false) }
    var showSaveDialog by remember { mutableStateOf(false) }
    var isSavingProduct by remember { mutableStateOf(false) }
    var foundProduct by remember { mutableStateOf<com.healthyproducts.app.data.model.Product?>(null) }
    var isSearchingProduct by remember { mutableStateOf(false) }

    // Barkod tarandığında ürün ara
    LaunchedEffect(scannedBarcode) {
        if (scannedBarcode.isNotEmpty()) {
            isSearchingProduct = true
            val result = productRepository.getProductByBarcode(scannedBarcode)
            foundProduct = result.getOrNull()
            isSearchingProduct = false
        }
    }

    // Kamera izni launcher
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasPermission = isGranted
        if (!isGranted) {
            isScanning = false
        }
    }

    // İzin kontrolü
    LaunchedEffect(Unit) {
        permissionLauncher.launch(Manifest.permission.CAMERA)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.scan_barcode)) },
                navigationIcon = { BackButton(navController) },
                actions = {
                    if (hasPermission && isScanning) {
                        IconButton(
                            onClick = { flashEnabled = !flashEnabled }
                        ) {
                            Icon(
                                imageVector = if (flashEnabled) Icons.Default.FlashOn else Icons.Default.FlashOff,
                                contentDescription = stringResource(R.string.use_flash)
                            )
                        }
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            if (scannedBarcode.isEmpty()) {
                if (!hasPermission) {
                    // İzin verilmedi
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = stringResource(R.string.camera_permission_denied),
                                style = MaterialTheme.typography.bodyLarge,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(16.dp)
                            )

                            Button(
                                onClick = {
                                    permissionLauncher.launch(Manifest.permission.CAMERA)
                                }
                            ) {
                                Text(text = stringResource(R.string.grant_permission))
                            }
                        }
                    }
                } else {
                    // Kamera görüntüsü
                    Box(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        // Kamera preview
                        BarcodeCameraPreview(
                            modifier = Modifier.fillMaxSize(),
                            flashEnabled = flashEnabled,
                            onBarcodeDetected = { barcode ->
                                scannedBarcode = barcode
                                isScanning = false
                            }
                        )

                        // Tarama çerçevesi
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(64.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(250.dp, 150.dp)
                                    .border(
                                        width = 2.dp,
                                        color = Color.White,
                                        shape = RoundedCornerShape(12.dp)
                                    )
                            )
                        }

                        // Alt kısımda talimatlar
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .align(Alignment.BottomCenter)
                                .background(
                                    Color.Black.copy(alpha = 0.7f),
                                    RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                                )
                                .padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.barcode_scan_instruction),
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color.White,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            } else {
                // Barkod tarandı - Sonuç ekranı
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // Başarı ikonu
                            Icon(
                                imageVector = Icons.Default.FlashOn, // Geçici ikon
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(48.dp)
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Text(
                                text = stringResource(R.string.barcode_detected, scannedBarcode),
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            if (isSearchingProduct) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        strokeWidth = 2.dp
                                    )
                                    Text(
                                        text = stringResource(R.string.searching_product),
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            } else {
                                Text(
                                    text = if (foundProduct != null) {
                                        stringResource(R.string.product_found)
                                    } else {
                                        stringResource(R.string.product_not_found)
                                    },
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = TextAlign.Center,
                                    color = if (foundProduct != null) {
                                        MaterialTheme.colorScheme.primary
                                    } else {
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                    }
                                )
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            // Ürün resmi ekleme
                            ImagePickerComponent(
                                modifier = Modifier.fillMaxWidth(),
                                currentImageBitmap = selectedImageBitmap,
                                onImageSelected = { uri ->
                                    // URI'den bitmap'e çevir
                                    val bitmap = uriToBitmap(context, uri)
                                    selectedImageBitmap = bitmap
                                },
                                onImageCaptured = { bitmap ->
                                    selectedImageBitmap = bitmap
                                },
                                isLoading = isUploadingImage
                            )

                            Spacer(modifier = Modifier.height(24.dp))

                            // Butonlar
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                if (foundProduct != null) {
                                    // Ürün bulundu - Analiz et butonu
                                    Button(
                                        onClick = {
                                            // Ürün analizi ekranına git
                                            navController.navigate("food_analysis?ingredients=${foundProduct!!.ingredients.joinToString(",")}")
                                        },
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        Text(text = stringResource(R.string.analyze_product))
                                    }
                                } else {
                                    // Ürün bulunamadı - Kaydet butonu
                                    Button(
                                        onClick = {
                                            showSaveDialog = true
                                        },
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        Text(text = stringResource(R.string.save_product))
                                    }
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    OutlinedButton(
                                        onClick = {
                                            // OCR tarama ekranına git
                                            navController.navigate("scan")
                                        },
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        Text(text = stringResource(R.string.scan_ingredients))
                                    }

                                    OutlinedButton(
                                        onClick = {
                                            scannedBarcode = ""
                                            selectedImageBitmap = null
                                            foundProduct = null
                                            isScanning = true
                                        },
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        Text(text = stringResource(R.string.scan_again))
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Ürün kaydetme dialog'u
    SaveProductDialog(
        isVisible = showSaveDialog,
        barcode = scannedBarcode,
        productImage = selectedImageBitmap,
        onDismiss = { showSaveDialog = false },
        onSave = { productName, productBrand, ingredients ->
            scope.launch {
                saveProduct(
                    productRepository = productRepository,
                    imageUploadRepository = imageUploadRepository,
                    auth = auth,
                    barcode = scannedBarcode,
                    productName = productName,
                    productBrand = productBrand,
                    ingredients = ingredients,
                    productImage = selectedImageBitmap,
                    language = SupportedLanguage.TURKISH, // Default language
                    onLoading = { isSavingProduct = it },
                    onSuccess = {
                        showSaveDialog = false
                        // Başarı mesajı göster ve geri git
                        navController.popBackStack()
                    },
                    onError = { error ->
                        // Hata mesajı göster
                        showSaveDialog = false
                    }
                )
            }
        },
        isLoading = isSavingProduct
    )
}

/**
 * Ürün kaydetme fonksiyonu
 */
private suspend fun saveProduct(
    productRepository: ProductRepository,
    imageUploadRepository: ImageUploadRepository,
    auth: FirebaseAuth,
    barcode: String,
    productName: String,
    productBrand: String,
    ingredients: String,
    productImage: android.graphics.Bitmap?,
    language: SupportedLanguage,
    onLoading: (Boolean) -> Unit,
    onSuccess: () -> Unit,
    onError: (String) -> Unit
) {
    try {
        onLoading(true)

        val currentUser = auth.currentUser
        if (currentUser == null) {
            onError("User not logged in")
            return
        }

        // Resmi yükle (varsa)
        var imageUrl: String? = null
        if (productImage != null) {
            val uploadResult = imageUploadRepository.uploadProductImage(productImage, barcode)
            if (uploadResult.isSuccess) {
                imageUrl = uploadResult.getOrNull()
            } else {
                onError("Failed to upload image")
                return
            }
        }

        // Ürünü kaydet
        val saveRequest = SaveProductRequest(
            barcode = barcode,
            name = productName,
            brand = productBrand,
            ingredients = ingredients.split(",").map { it.trim() },
            analysisResult = "", // Henüz analiz yapılmadı
            healthScore = 0,
            language = language.code,
            category = "",
            imageUrl = imageUrl ?: ""
        )

        val saveResult = productRepository.saveProduct(saveRequest, currentUser.uid)
        if (saveResult.isSuccess) {
            onSuccess()
        } else {
            onError(saveResult.exceptionOrNull()?.message ?: "Failed to save product")
        }

    } catch (e: Exception) {
        onError(e.message ?: "Unknown error")
    } finally {
        onLoading(false)
    }
}
