package com.healthyproducts.app

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatDelegate
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.Modifier
import androidx.core.os.LocaleListCompat
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.rememberNavController
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import com.healthyproducts.app.data.preferences.LanguagePreferences
import com.healthyproducts.app.navigation.AppNavigation
import com.healthyproducts.app.ui.theme.HealthyProductsTheme
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.UserViewModel
import com.healthyproducts.app.util.LocaleHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Locale
import javax.inject.Inject

/**
 * Uygulamanın ana aktivitesi
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var firebaseAuth: FirebaseAuth

    private lateinit var googleSignInClient: GoogleSignInClient
    private lateinit var googleSignInLauncher: ActivityResultLauncher<Intent>
    private lateinit var languagePreferences: LanguagePreferences

    override fun attachBaseContext(newBase: Context) {
        // Dil ayarını context'e uygula
        val languagePrefs = LanguagePreferences(newBase)
        val languageCode = languagePrefs.getLanguageSync()

        // Debug: Dil ayarını kontrol et
        Log.d("MainActivity", "=== LANGUAGE LOADING DEBUG ===")
        Log.d("MainActivity", "Language code from LanguagePreferences: $languageCode")

        // SharedPreferences'ı da kontrol et
        val sharedPrefs = newBase.getSharedPreferences("language_preferences", MODE_PRIVATE)
        val sharedLanguageCode = sharedPrefs.getString("language_code", "NOT_FOUND")
        Log.d("MainActivity", "Language code from SharedPreferences: $sharedLanguageCode")
        Log.d("MainActivity", "SharedPreferences contains language_code: ${sharedPrefs.contains("language_code")}")

        val locale = Locale(languageCode)
        val config = newBase.resources.configuration
        config.setLocale(locale)

        val context = newBase.createConfigurationContext(config)
        super.attachBaseContext(context)

        Log.d("MainActivity", "attachBaseContext: Applied language $languageCode")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // Dil tercihlerini başlat (super.onCreate'den önce)
        languagePreferences = LanguagePreferences(this)

        // Dil ayarlarını hemen yükle ve uygula (super.onCreate'den önce)
        applyLanguageBeforeCreate()

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Intent'ten dil değişikliği bilgisini kontrol et
        val languageChanged = intent.getBooleanExtra("LANGUAGE_CHANGED", false)
        if (languageChanged) {
            val languageCode = intent.getStringExtra("LANGUAGE_CODE") ?: "en"
            Log.d("MainActivity", "Language change detected from intent: $languageCode")

            // Locale'i doğrudan ayarla
            val locale = Locale(languageCode)
            Locale.setDefault(locale)
            val config = resources.configuration
            config.setLocale(locale)
            resources.updateConfiguration(config, resources.displayMetrics)

            // AppCompatDelegate ile de ayarla
            val localeList = LocaleListCompat.forLanguageTags(languageCode)
            AppCompatDelegate.setApplicationLocales(localeList)
        }

        // Google Sign-In yapılandırması
        setupGoogleSignIn()

        // Google Sign-In sonuç işleyicisi
        googleSignInLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
            handleGoogleSignInResult(task)
        }

        setContent {
            HealthyProductsAppUI()
        }
    }

    /**
     * Uygulama oluşturulmadan önce dil ayarlarını uygular
     */
    private fun applyLanguageBeforeCreate() {
        try {
            // SharedPreferences'tan direkt oku
            val sharedPrefs = getSharedPreferences("language_preferences", MODE_PRIVATE)
            val languageCode = sharedPrefs.getString("language_code", "tr") ?: "tr"
            Log.d("MainActivity", "Applying language BEFORE onCreate: $languageCode")

            // Locale'i ayarla
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            // AppCompatDelegate ile ayarla (bu string resources'ları etkiler)
            val localeList = LocaleListCompat.forLanguageTags(languageCode)
            AppCompatDelegate.setApplicationLocales(localeList)

            Log.d("MainActivity", "Language applied BEFORE onCreate: $languageCode")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error applying language before create", e)
            // Varsayılan dil olarak İngilizce kullan
            val locale = Locale("en")
            Locale.setDefault(locale)
            val localeList = LocaleListCompat.forLanguageTags("en")
            AppCompatDelegate.setApplicationLocales(localeList)
        }
    }

    /**
     * Dil ayarlarını senkron olarak yükler
     */
    private fun loadLanguageSettingsSync() {
        try {
            // SharedPreferences'tan direkt oku
            val sharedPrefs = getSharedPreferences("language_preferences", MODE_PRIVATE)
            val languageCode = sharedPrefs.getString("language_code", "tr") ?: "tr"
            Log.d("MainActivity", "Loading language settings sync: $languageCode")

            // Locale'i doğrudan ayarla
            val locale = Locale(languageCode)
            Locale.setDefault(locale)
            val config = resources.configuration
            config.setLocale(locale)
            resources.updateConfiguration(config, resources.displayMetrics)

            // Uygulama dilini AppCompatDelegate ile de ayarla
            val localeList = LocaleListCompat.forLanguageTags(languageCode)
            AppCompatDelegate.setApplicationLocales(localeList)

            Log.d("MainActivity", "Language settings applied sync: $languageCode")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error loading language settings sync", e)
        }
    }

    /**
     * Dil ayarlarını yükler (asenkron)
     */
    private fun loadLanguageSettings() {
        lifecycleScope.launch {
            try {
                val languageCode = languagePreferences.languageCode.first()
                Log.d("MainActivity", "Loading language settings: $languageCode")

                // Locale'i doğrudan ayarla
                val locale = Locale(languageCode)
                Locale.setDefault(locale)
                val config = resources.configuration
                config.setLocale(locale)
                resources.updateConfiguration(config, resources.displayMetrics)

                // Uygulama dilini AppCompatDelegate ile de ayarla
                val localeList = LocaleListCompat.forLanguageTags(languageCode)
                AppCompatDelegate.setApplicationLocales(localeList)

                Log.d("MainActivity", "Language settings applied: $languageCode")
            } catch (e: Exception) {
                Log.e("MainActivity", "Error loading language settings", e)
            }
        }
    }

    /**
     * Yapılandırma değişikliklerini işler
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d("MainActivity", "Configuration changed: ${newConfig.locales}")
    }

    /**
     * Aktiviteyi yeniden başlatır
     */
    private fun recreateActivity() {
        Log.d("MainActivity", "Recreating activity to apply language changes")

        // Yeni bir intent oluştur ve flags ekle
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_CLEAR_TASK or
                       Intent.FLAG_ACTIVITY_NEW_TASK)

        // Aktiviteyi yeniden başlat
        finish()
        startActivity(intent)
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }

    /**
     * Google Sign-In yapılandırması
     */
    private fun setupGoogleSignIn() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(getString(R.string.default_web_client_id))
            .requestEmail()
            .build()

        googleSignInClient = GoogleSignIn.getClient(this, gso)
    }

    /**
     * Google Sign-In sonucunu işler
     */
    private fun handleGoogleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account = completedTask.getResult(ApiException::class.java)
            // Google Sign-In başarılı, Firebase ile kimlik doğrulama yap
            firebaseAuthWithGoogle(account.idToken!!)
        } catch (e: ApiException) {
            // Google Sign-In başarısız
            Log.w("MainActivity", "Google sign in failed", e)
        }
    }

    /**
     * Google kimlik bilgileriyle Firebase'de kimlik doğrulama yapar
     */
    private fun firebaseAuthWithGoogle(idToken: String) {
        val credential = GoogleAuthProvider.getCredential(idToken, null)
        firebaseAuth.signInWithCredential(credential)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    // Giriş başarılı
                    Log.d("MainActivity", "signInWithCredential:success")
                } else {
                    // Giriş başarısız
                    Log.w("MainActivity", "signInWithCredential:failure", task.exception)
                }
            }
    }

    /**
     * Google Sign-In işlemini başlatır
     */
    fun startGoogleSignIn() {
        val signInIntent = googleSignInClient.signInIntent
        googleSignInLauncher.launch(signInIntent)
    }
}

/**
 * Uygulamanın ana Composable fonksiyonu
 */
@Composable
fun HealthyProductsAppUI() {
    // ViewModels
    val foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel()
    val userViewModel: UserViewModel = hiltViewModel()

    // Kullanıcı durumu
    val userState by userViewModel.userState.collectAsState()

    // Kullanıcı giriş yapmışsa, kullanıcı tercihlerini yükle
    LaunchedEffect(userState) {
        if (userState is UserViewModel.UserState.LoggedIn) {
            val userId = (userState as UserViewModel.UserState.LoggedIn).user.id
            foodAnalysisViewModel.initUserPreferences(userId)
        }
    }

    HealthyProductsTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            val navController = rememberNavController()
            AppNavigation(navController = navController)
        }
    }
}