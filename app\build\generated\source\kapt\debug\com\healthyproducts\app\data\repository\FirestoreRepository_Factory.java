package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FirestoreRepository_Factory implements Factory<FirestoreRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  private final Provider<FirebaseAuthRepository> authRepositoryProvider;

  public FirestoreRepository_Factory(Provider<FirebaseFirestore> firestoreProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    this.firestoreProvider = firestoreProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public FirestoreRepository get() {
    return newInstance(firestoreProvider.get(), authRepositoryProvider.get());
  }

  public static FirestoreRepository_Factory create(Provider<FirebaseFirestore> firestoreProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    return new FirestoreRepository_Factory(firestoreProvider, authRepositoryProvider);
  }

  public static FirestoreRepository newInstance(FirebaseFirestore firestore,
      FirebaseAuthRepository authRepository) {
    return new FirestoreRepository(firestore, authRepository);
  }
}
