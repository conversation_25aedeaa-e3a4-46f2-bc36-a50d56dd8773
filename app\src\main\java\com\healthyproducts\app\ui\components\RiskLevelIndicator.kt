package com.healthyproducts.app.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * Risk seviyesi göstergesi
 */
@Composable
fun RiskLevelIndicator(
    title: String,
    level: Int,
    maxLevel: Int = 5
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(4.dp))

        LinearProgressIndicator(
            progress = level.toFloat() / maxLevel,
            modifier = Modifier.fillMaxWidth()
        )

        Text(
            text = "$level/$maxLevel",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
