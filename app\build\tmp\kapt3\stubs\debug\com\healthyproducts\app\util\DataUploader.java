package com.healthyproducts.app.util;

/**
 * JSON dosyalarındaki verileri Firebase'e yüklemek için yardımcı sınıf
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0002J\u000e\u0010\f\u001a\u00020\rH\u0082@\u00a2\u0006\u0002\u0010\u000eJ\u001c\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\r0\u0010H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0011\u0010\u000eJ\u000e\u0010\u0012\u001a\u00020\rH\u0082@\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u0013\u001a\u00020\rH\u0082@\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u0014\u001a\u00020\rH\u0082@\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u0015\u001a\u00020\rH\u0082@\u00a2\u0006\u0002\u0010\u000eR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0016"}, d2 = {"Lcom/healthyproducts/app/util/DataUploader;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "gson", "Lcom/google/gson/Gson;", "readJsonFromAssets", "", "fileName", "uploadAdditives", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadAllData", "Lkotlin/Result;", "uploadAllData-IoAF18A", "uploadAllergens", "uploadFats", "uploadFoodCertificates", "uploadSugars", "app_debug"})
public final class DataUploader {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.firestore.FirebaseFirestore firestore = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    
    public DataUploader(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Katkı maddelerini Firebase'e yükler
     */
    private final java.lang.Object uploadAdditives(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Alerjenleri Firebase'e yükler
     */
    private final java.lang.Object uploadAllergens(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Yağları Firebase'e yükler
     */
    private final java.lang.Object uploadFats(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Şekerleri Firebase'e yükler
     */
    private final java.lang.Object uploadSugars(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Gıda sertifikalarını Firebase'e yükler
     */
    private final java.lang.Object uploadFoodCertificates(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * JSON dosyasını okur
     */
    private final java.lang.String readJsonFromAssets(java.lang.String fileName) {
        return null;
    }
}