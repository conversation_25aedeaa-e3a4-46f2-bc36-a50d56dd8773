package com.healthyproducts.app.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.healthyproducts.app.util.LocaleHelper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * Dil tercihlerini yönetmek için DataStore yardımcı sınıfı
 */
class LanguagePreferences(private val context: Context) {
    
    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "language_preferences")
        private val LANGUAGE_CODE = stringPreferencesKey("language_code")
    }
    
    /**
     * Dil kodunu akış olarak alma
     */
    val languageCode: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[LANGUAGE_CODE] ?: LocaleHelper.getSystemLocale().code
        }
    
    /**
     * Dil kodunu kaydetme
     */
    suspend fun saveLanguageCode(languageCode: String) {
        context.dataStore.edit { preferences ->
            preferences[LANGUAGE_CODE] = languageCode
        }
    }
}
