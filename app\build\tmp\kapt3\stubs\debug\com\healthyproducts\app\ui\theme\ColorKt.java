package com.healthyproducts.app.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b>\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\"\u0013\u00109\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b:\u0010\u0003\"\u0013\u0010;\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b<\u0010\u0003\"\u0013\u0010=\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b>\u0010\u0003\u00a8\u0006?"}, d2 = {"Background", "Landroidx/compose/ui/graphics/Color;", "getBackground", "()J", "J", "BackgroundDark", "getBackgroundDark", "Error", "getError", "ErrorDark", "getErrorDark", "Halal", "getHalal", "Haram", "getHaram", "Harmful", "getHarmful", "OnBackground", "getOnBackground", "OnBackgroundDark", "getOnBackgroundDark", "OnError", "getOnError", "OnErrorDark", "getOnErrorDark", "OnPrimary", "getOnPrimary", "OnPrimaryDark", "getOnPrimaryDark", "OnSecondary", "getOnSecondary", "OnSecondaryDark", "getOnSecondaryDark", "OnSurface", "getOnSurface", "OnSurfaceDark", "getOnSurfaceDark", "Primary", "getPrimary", "PrimaryDark", "getPrimaryDark", "PrimaryVariant", "getPrimaryVariant", "PrimaryVariantDark", "getPrimaryVariantDark", "Secondary", "getSecondary", "SecondaryDark", "getSecondaryDark", "SecondaryVariant", "getSecondaryVariant", "SecondaryVariantDark", "getSecondaryVariantDark", "Surface", "getSurface", "SurfaceDark", "getSurfaceDark", "Suspicious", "getSuspicious", "Unhealthy", "getUnhealthy", "Vegan", "getVegan", "app_debug"})
public final class ColorKt {
    private static final long Primary = 0L;
    private static final long PrimaryVariant = 0L;
    private static final long Secondary = 0L;
    private static final long SecondaryVariant = 0L;
    private static final long Background = 0L;
    private static final long Surface = 0L;
    private static final long Error = 0L;
    private static final long OnPrimary = 0L;
    private static final long OnSecondary = 0L;
    private static final long OnBackground = 0L;
    private static final long OnSurface = 0L;
    private static final long OnError = 0L;
    private static final long PrimaryDark = 0L;
    private static final long PrimaryVariantDark = 0L;
    private static final long SecondaryDark = 0L;
    private static final long SecondaryVariantDark = 0L;
    private static final long BackgroundDark = 0L;
    private static final long SurfaceDark = 0L;
    private static final long ErrorDark = 0L;
    private static final long OnPrimaryDark = 0L;
    private static final long OnSecondaryDark = 0L;
    private static final long OnBackgroundDark = 0L;
    private static final long OnSurfaceDark = 0L;
    private static final long OnErrorDark = 0L;
    private static final long Halal = 0L;
    private static final long Haram = 0L;
    private static final long Suspicious = 0L;
    private static final long Vegan = 0L;
    private static final long Harmful = 0L;
    private static final long Unhealthy = 0L;
    
    public static final long getPrimary() {
        return 0L;
    }
    
    public static final long getPrimaryVariant() {
        return 0L;
    }
    
    public static final long getSecondary() {
        return 0L;
    }
    
    public static final long getSecondaryVariant() {
        return 0L;
    }
    
    public static final long getBackground() {
        return 0L;
    }
    
    public static final long getSurface() {
        return 0L;
    }
    
    public static final long getError() {
        return 0L;
    }
    
    public static final long getOnPrimary() {
        return 0L;
    }
    
    public static final long getOnSecondary() {
        return 0L;
    }
    
    public static final long getOnBackground() {
        return 0L;
    }
    
    public static final long getOnSurface() {
        return 0L;
    }
    
    public static final long getOnError() {
        return 0L;
    }
    
    public static final long getPrimaryDark() {
        return 0L;
    }
    
    public static final long getPrimaryVariantDark() {
        return 0L;
    }
    
    public static final long getSecondaryDark() {
        return 0L;
    }
    
    public static final long getSecondaryVariantDark() {
        return 0L;
    }
    
    public static final long getBackgroundDark() {
        return 0L;
    }
    
    public static final long getSurfaceDark() {
        return 0L;
    }
    
    public static final long getErrorDark() {
        return 0L;
    }
    
    public static final long getOnPrimaryDark() {
        return 0L;
    }
    
    public static final long getOnSecondaryDark() {
        return 0L;
    }
    
    public static final long getOnBackgroundDark() {
        return 0L;
    }
    
    public static final long getOnSurfaceDark() {
        return 0L;
    }
    
    public static final long getOnErrorDark() {
        return 0L;
    }
    
    public static final long getHalal() {
        return 0L;
    }
    
    public static final long getHaram() {
        return 0L;
    }
    
    public static final long getSuspicious() {
        return 0L;
    }
    
    public static final long getVegan() {
        return 0L;
    }
    
    public static final long getHarmful() {
        return 0L;
    }
    
    public static final long getUnhealthy() {
        return 0L;
    }
}