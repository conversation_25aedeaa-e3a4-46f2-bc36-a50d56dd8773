package com.healthyproducts.app.data.repository;

import android.content.Context;
import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PreservativeRepository_Factory implements Factory<PreservativeRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  private final Provider<Context> contextProvider;

  public PreservativeRepository_Factory(Provider<FirebaseFirestore> firestoreProvider,
      Provider<Context> contextProvider) {
    this.firestoreProvider = firestoreProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public PreservativeRepository get() {
    return newInstance(firestoreProvider.get(), contextProvider.get());
  }

  public static PreservativeRepository_Factory create(Provider<FirebaseFirestore> firestoreProvider,
      Provider<Context> contextProvider) {
    return new PreservativeRepository_Factory(firestoreProvider, contextProvider);
  }

  public static PreservativeRepository newInstance(FirebaseFirestore firestore, Context context) {
    return new PreservativeRepository(firestore, context);
  }
}
