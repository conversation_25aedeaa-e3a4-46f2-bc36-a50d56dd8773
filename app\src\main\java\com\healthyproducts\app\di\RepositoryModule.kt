package com.healthyproducts.app.di

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.storage.FirebaseStorage
import com.healthyproducts.app.data.repository.AdditiveRepository
import com.healthyproducts.app.data.repository.AllergenRepository
import com.healthyproducts.app.data.repository.FatRepository
import com.healthyproducts.app.data.repository.FirebaseAuthRepository
import com.healthyproducts.app.data.repository.FirebaseStorageRepository
import com.healthyproducts.app.data.repository.FirestoreRepository
import com.healthyproducts.app.data.repository.FoodCertificateRepository
import com.healthyproducts.app.data.repository.IntoleranceRepository
import com.healthyproducts.app.data.repository.PreservativeRepository
import com.healthyproducts.app.data.repository.ProductRepository
import com.healthyproducts.app.data.repository.SugarRepository
import com.healthyproducts.app.data.service.FoodAnalysisService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Repository sınıflarını sağlayan Hilt modülü
 */
@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    /**
     * Firebase Authentication Repository sağlar
     */
    @Provides
    @Singleton
    fun provideFirebaseAuthRepository(
        auth: FirebaseAuth
    ): FirebaseAuthRepository {
        return FirebaseAuthRepository(auth)
    }

    /**
     * Firestore Repository sağlar
     */
    @Provides
    @Singleton
    fun provideFirestoreRepository(
        firestore: FirebaseFirestore,
        authRepository: FirebaseAuthRepository
    ): FirestoreRepository {
        return FirestoreRepository(firestore, authRepository)
    }

    /**
     * Firebase Storage Repository sağlar
     */
    @Provides
    @Singleton
    fun provideFirebaseStorageRepository(
        storage: FirebaseStorage,
        authRepository: FirebaseAuthRepository
    ): FirebaseStorageRepository {
        return FirebaseStorageRepository(storage, authRepository)
    }

    /**
     * Additive Repository sağlar
     */
    @Provides
    @Singleton
    fun provideAdditiveRepository(
        firestore: FirebaseFirestore
    ): AdditiveRepository {
        return AdditiveRepository(firestore)
    }

    /**
     * Allergen Repository sağlar
     */
    @Provides
    @Singleton
    fun provideAllergenRepository(
        firestore: FirebaseFirestore
    ): AllergenRepository {
        return AllergenRepository(firestore)
    }

    /**
     * Sugar Repository sağlar
     */
    @Provides
    @Singleton
    fun provideSugarRepository(
        firestore: FirebaseFirestore
    ): SugarRepository {
        return SugarRepository(firestore)
    }

    /**
     * Fat Repository sağlar
     */
    @Provides
    @Singleton
    fun provideFatRepository(
        firestore: FirebaseFirestore
    ): FatRepository {
        return FatRepository(firestore)
    }

    /**
     * Food Certificate Repository sağlar
     */
    @Provides
    @Singleton
    fun provideFoodCertificateRepository(
        firestore: FirebaseFirestore
    ): FoodCertificateRepository {
        return FoodCertificateRepository(firestore)
    }

    /**
     * Intolerance Repository sağlar
     */
    @Provides
    @Singleton
    fun provideIntoleranceRepository(
        firestore: FirebaseFirestore
    ): IntoleranceRepository {
        return IntoleranceRepository(firestore)
    }

    /**
     * Preservative Repository sağlar
     */
    @Provides
    @Singleton
    fun providePreservativeRepository(
        firestore: FirebaseFirestore,
        context: android.content.Context
    ): PreservativeRepository {
        return PreservativeRepository(firestore, context)
    }

    /**
     * Product Repository sağlar
     */
    @Provides
    @Singleton
    fun provideProductRepository(
        firestore: FirebaseFirestore
    ): ProductRepository {
        return ProductRepository(firestore)
    }

    /**
     * Food Analysis Service sağlar
     */
    @Provides
    @Singleton
    fun provideFoodAnalysisService(
        additiveRepository: AdditiveRepository,
        allergenRepository: AllergenRepository,
        sugarRepository: SugarRepository,
        fatRepository: FatRepository,
        intoleranceRepository: IntoleranceRepository,
        preservativeRepository: PreservativeRepository,
        certificateRepository: FoodCertificateRepository
    ): FoodAnalysisService {
        return FoodAnalysisService(
            additiveRepository,
            allergenRepository,
            sugarRepository,
            fatRepository,
            intoleranceRepository,
            preservativeRepository,
            certificateRepository
        )
    }
}
