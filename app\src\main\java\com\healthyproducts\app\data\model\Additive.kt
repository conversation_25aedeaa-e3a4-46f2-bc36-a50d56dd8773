package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName
import com.healthyproducts.app.data.model.SupportedLanguage

/**
 * Gıda katkı maddesini temsil eden veri sınıfı
 */
data class Additive(
    @DocumentId
    val id: String = "",

    @get:PropertyName("code")
    @set:PropertyName("code")
    var code: String = "", // E-kodu (örn. E100, E202)

    @get:PropertyName("name_tr")
    @set:PropertyName("name_tr")
    var nameTr: String = "", // Katkı maddesinin Türkçe adı

    @get:PropertyName("name_en")
    @set:PropertyName("name_en")
    var nameEn: String = "", // Katkı maddesinin İngilizce adı

    @get:PropertyName("description_tr")
    @set:PropertyName("description_tr")
    var descriptionTr: String = "", // Katkı maddesinin Türkçe açıklaması

    @get:PropertyName("description_en")
    @set:PropertyName("description_en")
    var descriptionEn: String = "", // Katkı maddesinin İngilizce açıklaması

    @get:PropertyName("category")
    @set:PropertyName("category")
    var category: String = "", // Katkı maddesinin kategorisi (enum değeri)

    @get:PropertyName("category_tr")
    @set:PropertyName("category_tr")
    var categoryTr: String = "", // Katkı maddesinin Türkçe kategorisi

    @get:PropertyName("category_en")
    @set:PropertyName("category_en")
    var categoryEn: String = "", // Katkı maddesinin İngilizce kategorisi

    @get:PropertyName("origin_tr")
    @set:PropertyName("origin_tr")
    var originTr: String = "", // Katkı maddesinin Türkçe kaynağı

    @get:PropertyName("origin_en")
    @set:PropertyName("origin_en")
    var originEn: String = "", // Katkı maddesinin İngilizce kaynağı

    @get:PropertyName("usage_tr")
    @set:PropertyName("usage_tr")
    var usageTr: String = "", // Katkı maddesinin Türkçe kullanım amacı

    @get:PropertyName("usage_en")
    @set:PropertyName("usage_en")
    var usageEn: String = "", // Katkı maddesinin İngilizce kullanım amacı

    @get:PropertyName("health_effect_tr")
    @set:PropertyName("health_effect_tr")
    var healthEffectTr: String = "", // Sağlık üzerindeki Türkçe etkileri

    @get:PropertyName("health_effect_en")
    @set:PropertyName("health_effect_en")
    var healthEffectEn: String = "", // Sağlık üzerindeki İngilizce etkileri

    @get:PropertyName("halal_status")
    @set:PropertyName("halal_status")
    var halalStatus: String = "", // Helal durumu (HALAL, HARAM, MUSHBOOH)

    @get:PropertyName("vegan_status")
    @set:PropertyName("vegan_status")
    var veganStatus: String = "", // Vegan durumu (VEGAN, NON_VEGAN)

    @get:PropertyName("vegetarian_status")
    @set:PropertyName("vegetarian_status")
    var vegetarianStatus: String = "", // Vejetaryen durumu (VEGETARIAN, NON_VEGETARIAN)

    @get:PropertyName("kosher_status")
    @set:PropertyName("kosher_status")
    var kosherStatus: String = "", // Koşer durumu (KOSHER, NON_KOSHER)

    @get:PropertyName("harmful_level")
    @set:PropertyName("harmful_level")
    var harmfulLevel: Int = 0, // Zararlılık seviyesi (0-5, 0: Zararsız, 5: Çok zararlı)

    @get:PropertyName("unhealthy_level")
    @set:PropertyName("unhealthy_level")
    var unhealthyLevel: Int = 0, // Sağlıksızlık seviyesi (0-5, 0: Sağlıklı, 5: Çok sağlıksız)

    @get:PropertyName("risky")
    @set:PropertyName("risky")
    var risky: Boolean = false, // Riskli mi?

    @get:PropertyName("notes_tr")
    @set:PropertyName("notes_tr")
    var notesTr: String = "", // Türkçe ek notlar

    @get:PropertyName("notes_en")
    @set:PropertyName("notes_en")
    var notesEn: String = "" // İngilizce ek notlar
) {
    // Firestore için boş constructor
    constructor() : this("", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", 0, 0, false, "", "")

    // Kullanıcının dil tercihine göre adı döndürür
    fun getName(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> nameTr.ifEmpty { nameEn }
            SupportedLanguage.ENGLISH -> nameEn.ifEmpty { nameTr }
        }
    }

    // Kullanıcının dil tercihine göre açıklamayı döndürür
    fun getDescription(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> descriptionTr.ifEmpty { descriptionEn }
            SupportedLanguage.ENGLISH -> descriptionEn.ifEmpty { descriptionTr }
        }
    }

    // Kullanıcının dil tercihine göre kaynağı döndürür
    fun getOrigin(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> originTr.ifEmpty { originEn }
            SupportedLanguage.ENGLISH -> originEn.ifEmpty { originTr }
        }
    }

    // Kullanıcının dil tercihine göre kullanım amacını döndürür
    fun getUsage(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> usageTr.ifEmpty { usageEn }
            SupportedLanguage.ENGLISH -> usageEn.ifEmpty { usageTr }
        }
    }

    // Kullanıcının dil tercihine göre sağlık etkilerini döndürür
    fun getHealthEffect(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> healthEffectTr.ifEmpty { healthEffectEn }
            SupportedLanguage.ENGLISH -> healthEffectEn.ifEmpty { healthEffectTr }
        }
    }

    // Kullanıcının dil tercihine göre notları döndürür
    fun getNotes(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> notesTr.ifEmpty { notesEn }
            SupportedLanguage.ENGLISH -> notesEn.ifEmpty { notesTr }
        }
    }
}

/**
 * Helal durumu için enum sınıfı
 */
enum class HalalStatus(val displayName: String) {
    HALAL("Helal"),
    HARAM("Haram"),
    MUSHBOOH("Şüpheli")
}

/**
 * Vegan durumu için enum sınıfı
 */
enum class VeganStatus(val displayName: String) {
    VEGAN("Vegan"),
    NON_VEGAN("Vegan Değil")
}

/**
 * Koşer durumu için enum sınıfı
 */
enum class KosherStatus(val displayName: String) {
    KOSHER("Koşer"),
    NON_KOSHER("Koşer Değil")
}

/**
 * Katkı maddesi kategorisi için enum sınıfı
 */
enum class AdditiveCategory(val displayName: String) {
    COLORANT("Renklendirici"),
    PRESERVATIVE("Koruyucu"),
    ANTIOXIDANT("Antioksidan"),
    EMULSIFIER("Emülgatör"),
    STABILIZER("Stabilizatör"),
    THICKENER("Kıvam Arttırıcı"),
    SWEETENER("Tatlandırıcı"),
    FLAVOR_ENHANCER("Aroma Arttırıcı"),
    ACID("Asit"),
    ACIDITY_REGULATOR("Asitlik Düzenleyici"),
    ANTI_CAKING("Topaklanmayı Önleyici"),
    ANTI_FOAMING("Köpük Önleyici"),
    BULKING_AGENT("Hacim Arttırıcı"),
    CARRIER("Taşıyıcı"),
    GLAZING_AGENT("Parlatıcı"),
    HUMECTANT("Nem Tutucu"),
    MODIFIED_STARCH("Modifiye Nişasta"),
    PACKAGING_GAS("Paketleme Gazı"),
    PROPELLANT("İtici Gaz"),
    RAISING_AGENT("Kabartıcı"),
    SEQUESTRANT("Sekestrant"),
    OTHER("Diğer")
}
