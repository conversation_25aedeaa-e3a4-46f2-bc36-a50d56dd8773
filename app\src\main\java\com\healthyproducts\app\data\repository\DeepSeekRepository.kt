package com.healthyproducts.app.data.repository

import com.healthyproducts.app.data.api.DeepSeekApi
import com.healthyproducts.app.data.model.DeepSeekRequest
import com.healthyproducts.app.data.model.DeepSeekResponse
import com.healthyproducts.app.data.model.Message
import com.healthyproducts.app.data.model.SupportedLanguage
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DeepSeek API işlemlerini yöneten repository sınıfı
 */
@Singleton
class DeepSeekRepository @Inject constructor(
    private val deepSeekApi: DeepSeekApi,
    private val firestoreRepository: FirestoreRepository
) {
    /**
     * OCR ile tanınan metni düzeltir ve kullanıcının diline göre çevirir
     */
    suspend fun correctOcrText(ocrText: String): String {
        val userLanguage = firestoreRepository.userLanguage.value

        // Kullanıcının diline göre prompt oluştur
        val prompt = when (userLanguage) {
            SupportedLanguage.TURKISH -> {
                """
                Bu bir gıda ürününün içerik listesidir. OCR ile tanınmış metni düzelt ve Türkçe'ye çevir.
                Metindeki yazım hatalarını düzelt, eksik veya yanlış karakterleri tamamla.
                Sadece içerik listesini döndür, başka açıklama ekleme.

                OCR Metni: $ocrText
                """
            }
            SupportedLanguage.ENGLISH -> {
                """
                This is an ingredient list of a food product. Correct the OCR recognized text and translate it to English.
                Fix any spelling errors, complete missing or incorrect characters.
                Return only the ingredient list, do not add any explanation.

                OCR Text: $ocrText
                """
            }
        }

        val request = DeepSeekRequest(
            model = "deepseek-chat",
            messages = listOf(
                Message(
                    role = "user",
                    content = prompt
                )
            ),
            temperature = 0.3,
            maxTokens = 1000
        )

        val response = deepSeekApi.generateText(request)
        return parseDeepSeekResponse(response)
    }

    /**
     * İçerik listesini analiz eder ve zararlı maddeleri belirler
     */
    suspend fun analyzeIngredients(ingredients: String): String {
        val userLanguage = firestoreRepository.userLanguage.value

        // Kullanıcının diline göre prompt oluştur
        val prompt = when (userLanguage) {
            SupportedLanguage.TURKISH -> {
                """
                Bu bir gıda ürününün içerik listesidir. İçerikleri analiz et ve aşağıdaki bilgileri Türkçe olarak ver:

                1. Zararlı olabilecek katkı maddeleri (E kodları dahil)
                2. Alerjik maddeler
                3. Şeker ve yapay tatlandırıcılar
                4. Koruyucular ve renklendiriciler
                5. Genel sağlık değerlendirmesi (1-10 arası puanlama)

                Yanıtını madde madde, anlaşılır bir şekilde düzenle.

                İçerikler: $ingredients
                """
            }
            SupportedLanguage.ENGLISH -> {
                """
                This is an ingredient list of a food product. Analyze the ingredients and provide the following information in English:

                1. Potentially harmful additives (including E codes)
                2. Allergenic substances
                3. Sugar and artificial sweeteners
                4. Preservatives and colorants
                5. General health assessment (score from 1-10)

                Organize your response in a clear, point-by-point format.

                Ingredients: $ingredients
                """
            }
        }

        val request = DeepSeekRequest(
            model = "deepseek-chat",
            messages = listOf(
                Message(
                    role = "user",
                    content = prompt
                )
            ),
            temperature = 0.7,
            maxTokens = 2000
        )

        val response = deepSeekApi.generateText(request)
        return parseDeepSeekResponse(response)
    }

    /**
     * DeepSeek API yanıtını ayrıştırır
     */
    private fun parseDeepSeekResponse(response: DeepSeekResponse): String {
        return response.choices.firstOrNull()?.message?.content
            ?: throw Exception("DeepSeek API yanıtı alınamadı")
    }
}
