package com.healthyproducts.app.di;

import com.google.firebase.storage.FirebaseStorage;
import com.healthyproducts.app.data.repository.FirebaseAuthRepository;
import com.healthyproducts.app.data.repository.FirebaseStorageRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideFirebaseStorageRepositoryFactory implements Factory<FirebaseStorageRepository> {
  private final Provider<FirebaseStorage> storageProvider;

  private final Provider<FirebaseAuthRepository> authRepositoryProvider;

  public RepositoryModule_ProvideFirebaseStorageRepositoryFactory(
      Provider<FirebaseStorage> storageProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    this.storageProvider = storageProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public FirebaseStorageRepository get() {
    return provideFirebaseStorageRepository(storageProvider.get(), authRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideFirebaseStorageRepositoryFactory create(
      Provider<FirebaseStorage> storageProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    return new RepositoryModule_ProvideFirebaseStorageRepositoryFactory(storageProvider, authRepositoryProvider);
  }

  public static FirebaseStorageRepository provideFirebaseStorageRepository(FirebaseStorage storage,
      FirebaseAuthRepository authRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideFirebaseStorageRepository(storage, authRepository));
  }
}
