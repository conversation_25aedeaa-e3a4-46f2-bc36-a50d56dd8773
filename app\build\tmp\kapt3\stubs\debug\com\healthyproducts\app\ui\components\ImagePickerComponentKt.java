package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\\\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\t2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a\u0018\u0010\u000e\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\n\u00a8\u0006\u0012"}, d2 = {"ImagePickerComponent", "", "modifier", "Landroidx/compose/ui/Modifier;", "currentImageUrl", "", "currentImageBitmap", "Landroid/graphics/Bitmap;", "onImageSelected", "Lkotlin/Function1;", "Landroid/net/Uri;", "onImageCaptured", "isLoading", "", "uriToBitmap", "context", "Landroid/content/Context;", "uri", "app_debug"})
public final class ImagePickerComponentKt {
    
    /**
     * Resim seçme/çekme component'i
     */
    @androidx.compose.runtime.Composable()
    public static final void ImagePickerComponent(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable()
    java.lang.String currentImageUrl, @org.jetbrains.annotations.Nullable()
    android.graphics.Bitmap currentImageBitmap, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.net.Uri, kotlin.Unit> onImageSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Bitmap, kotlin.Unit> onImageCaptured, boolean isLoading) {
    }
    
    /**
     * Uri'den Bitmap oluşturur
     */
    @org.jetbrains.annotations.Nullable()
    public static final android.graphics.Bitmap uriToBitmap(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.net.Uri uri) {
        return null;
    }
}