1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.healthyproducts.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Kamera izni -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:5-65
12-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- İnternet izni -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:5-67
15-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:22-64
16
17    <!-- Depolama izinleri -->
18    <uses-permission
18-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:22-77
20        android:maxSdkVersion="32" />
20-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:5-76
21-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:22-73
22
23    <!-- Kamera özelliği gereksinimi -->
24    <uses-feature
24-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:17:5-19:36
25        android:name="android.hardware.camera"
25-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:18:9-47
26        android:required="false" />
26-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:19:9-33
27
28    <queries>
28-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:17:5-19:15
29        <package android:name="com.facebook.katana" />
29-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:9-55
29-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:18-52
30    </queries>
31
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
32-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
32-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
33    <uses-permission android:name="android.permission.WAKE_LOCK" />
33-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:5-68
33-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:22-65
34    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
34-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
34-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
35    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
35-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
35-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
36-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
36-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
37-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
37-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
38    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
38-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
38-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
46-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:21:5-55:19
47        android:name="com.healthyproducts.app.HealthyProductsApp"
47-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:22:9-43
48        android:allowBackup="true"
48-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:23:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:24:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:25:9-54
54        android:icon="@mipmap/ic_launcher"
54-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:26:9-43
55        android:label="@string/app_name"
55-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:27:9-41
56        android:roundIcon="@mipmap/ic_launcher_round"
56-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:28:9-54
57        android:supportsRtl="true"
57-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:29:9-35
58        android:testOnly="true"
59        android:theme="@style/Theme.HealthyProducts" >
59-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:30:9-53
60
61        <!-- Google Play Services meta-data -->
62        <meta-data
62-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:34:9-36:69
63            android:name="com.google.android.gms.version"
63-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:35:13-58
64            android:value="@integer/google_play_services_version" />
64-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:36:13-66
65
66        <!-- ML Kit için gerekli meta-data -->
67        <meta-data
67-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:39:9-41:46
68            android:name="com.google.mlkit.vision.DEPENDENCIES"
68-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:40:13-64
69            android:value="barcode_ui,ocr" />
69-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:41:13-43
70
71        <activity
71-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:43:9-54:20
72            android:name="com.healthyproducts.app.MainActivity"
72-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:44:13-41
73            android:configChanges="orientation|screenSize"
73-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:49:13-59
74            android:exported="true"
74-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:45:13-36
75            android:label="@string/app_name"
75-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:46:13-45
76            android:screenOrientation="portrait"
76-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:48:13-49
77            android:theme="@style/Theme.HealthyProducts" >
77-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:47:13-57
78            <intent-filter>
78-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:50:13-53:29
79                <action android:name="android.intent.action.MAIN" />
79-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:51:17-69
79-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:51:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:52:17-77
81-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:52:27-74
82            </intent-filter>
83        </activity>
84
85        <service
85-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
86            android:name="androidx.camera.core.impl.MetadataHolderService"
86-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
87            android:enabled="false"
87-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
88            android:exported="false" >
88-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
89            <meta-data
89-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
90                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
90-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
91                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
91-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
92        </service>
93
94        <activity
94-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:22:9-25:66
95            android:name="com.facebook.FacebookActivity"
95-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:23:13-57
96            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
96-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:24:13-96
97            android:theme="@style/com_facebook_activity_theme" />
97-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:25:13-63
98        <activity android:name="com.facebook.CustomTabMainActivity" />
98-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:9-71
98-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:19-68
99        <activity
99-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:27:9-41:20
100            android:name="com.facebook.CustomTabActivity"
100-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:28:13-58
101            android:exported="true" >
101-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:29:13-36
102            <intent-filter>
102-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
103                <action android:name="android.intent.action.VIEW" />
103-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
103-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
104
105                <category android:name="android.intent.category.DEFAULT" />
105-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
105-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
106                <category android:name="android.intent.category.BROWSABLE" />
106-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
106-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
107
108                <data
108-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
109                    android:host="cct.com.healthyproducts.app"
109-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
110                    android:scheme="fbconnect" />
110-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
111            </intent-filter>
112        </activity>
113        <activity
113-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
114            android:name="androidx.compose.ui.tooling.PreviewActivity"
114-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
115            android:exported="true" />
115-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
116        <activity
116-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
117            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
117-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
118            android:excludeFromRecents="true"
118-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
119            android:exported="false"
119-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
120            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
120-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
121        <!--
122            Service handling Google Sign-In user revocation. For apps that do not integrate with
123            Google Sign-In, this service will never be started.
124        -->
125        <service
125-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
126            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
126-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
127            android:exported="true"
127-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
128            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
128-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
129            android:visibleToInstantApps="true" />
129-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
130
131        <activity
131-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
132            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
132-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
133            android:excludeFromRecents="true"
133-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
134            android:exported="true"
134-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
135            android:launchMode="singleTask"
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
136            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
136-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
137            <intent-filter>
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
138                <action android:name="android.intent.action.VIEW" />
138-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
138-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
139
140                <category android:name="android.intent.category.DEFAULT" />
140-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
140-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
141                <category android:name="android.intent.category.BROWSABLE" />
141-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
141-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
142
143                <data
143-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
144                    android:host="firebase.auth"
144-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
145                    android:path="/"
146                    android:scheme="genericidp" />
146-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
147            </intent-filter>
148        </activity>
149        <activity
149-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
150            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
150-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
151            android:excludeFromRecents="true"
151-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
152            android:exported="true"
152-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
153            android:launchMode="singleTask"
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
154            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
154-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
155            <intent-filter>
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
156                <action android:name="android.intent.action.VIEW" />
156-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
156-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
157
158                <category android:name="android.intent.category.DEFAULT" />
158-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
158-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
159                <category android:name="android.intent.category.BROWSABLE" />
159-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
159-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
160
161                <data
161-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
162                    android:host="firebase.auth"
162-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
163                    android:path="/"
164                    android:scheme="recaptcha" />
164-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
165            </intent-filter>
166        </activity>
167
168        <service
168-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
169            android:name="com.google.firebase.components.ComponentDiscoveryService"
169-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
170            android:directBootAware="true"
170-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
171            android:exported="false" >
171-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
172            <meta-data
172-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
173                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
173-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
175            <meta-data
175-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:17:13-19:85
176                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
176-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:18:17-122
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:19:17-82
178            <meta-data
178-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:20:13-22:85
179                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
179-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:21:17-111
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:22:17-82
181            <meta-data
181-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
182                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
182-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
184            <meta-data
184-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
185                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
185-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
187            <meta-data
187-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
188                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
188-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
190            <meta-data
190-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
191                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
191-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
193            <meta-data
193-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
194                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
194-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
196            <meta-data
196-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
197                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
197-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
199            <meta-data
199-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
200                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
200-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
202            <meta-data
202-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
203                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
203-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
205            <meta-data
205-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
206                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
206-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
208        </service>
209        <service
209-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
210            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
210-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
211            android:directBootAware="true"
211-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
212            android:exported="false" >
212-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
213            <meta-data
213-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
214                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
214-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
216            <meta-data
216-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
217                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
217-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
219            <meta-data
219-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
220                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
220-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
222            <meta-data
222-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
223                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
223-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
225        </service>
226
227        <provider
227-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
228            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
228-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
229            android:authorities="com.healthyproducts.app.mlkitinitprovider"
229-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
230            android:exported="false"
230-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
231            android:initOrder="99" />
231-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
232
233        <activity
233-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
234            android:name="com.google.android.gms.common.api.GoogleApiActivity"
234-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
235            android:exported="false"
235-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
236            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
236-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
237
238        <receiver
238-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
239            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
239-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
240            android:enabled="true"
240-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
241            android:exported="false" >
241-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
242        </receiver>
243
244        <service
244-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
245            android:name="com.google.android.gms.measurement.AppMeasurementService"
245-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
246            android:enabled="true"
246-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
247            android:exported="false" />
247-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
248        <service
248-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
249            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
249-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
250            android:enabled="true"
250-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
251            android:exported="false"
251-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
252            android:permission="android.permission.BIND_JOB_SERVICE" />
252-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
253
254        <property
254-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
255            android:name="android.adservices.AD_SERVICES_CONFIG"
255-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
256            android:resource="@xml/ga_ad_services_config" />
256-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
257
258        <provider
258-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
259            android:name="com.google.firebase.provider.FirebaseInitProvider"
259-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
260            android:authorities="com.healthyproducts.app.firebaseinitprovider"
260-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
261            android:directBootAware="true"
261-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
262            android:exported="false"
262-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
263            android:initOrder="100" />
263-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
264        <provider
264-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
265            android:name="androidx.startup.InitializationProvider"
265-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
266            android:authorities="com.healthyproducts.app.androidx-startup"
266-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
267            android:exported="false" >
267-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
268            <meta-data
268-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
269                android:name="androidx.emoji2.text.EmojiCompatInitializer"
269-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
270                android:value="androidx.startup" />
270-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
271            <meta-data
271-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
272                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
272-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
273                android:value="androidx.startup" />
273-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
274            <meta-data
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
275                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
276                android:value="androidx.startup" />
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
277        </provider>
278        <!--
279         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
280         with the application context. This config is merged in with the host app's manifest,
281         but there can only be one provider with the same authority activated at any given
282         point; so if the end user has two or more different apps that use Facebook SDK, only the
283         first one will be able to use the provider. To work around this problem, we use the
284         following placeholder in the authority to identify each host application as if it was
285         a completely different provider.
286        -->
287        <provider
287-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:29:9-32:40
288            android:name="com.facebook.internal.FacebookInitProvider"
288-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:30:13-70
289            android:authorities="com.healthyproducts.app.FacebookInitProvider"
289-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:31:13-72
290            android:exported="false" />
290-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:32:13-37
291
292        <receiver
292-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:34:9-40:20
293            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
293-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:35:13-86
294            android:exported="false" >
294-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:36:13-37
295            <intent-filter>
295-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:37:13-39:29
296                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
296-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:17-95
296-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:25-92
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:41:9-47:20
300            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
300-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:42:13-118
301            android:exported="false" >
301-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:43:13-37
302            <intent-filter>
302-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:44:13-46:29
303                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
303-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:17-103
303-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:25-100
304            </intent-filter>
305        </receiver>
306
307        <uses-library
307-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
308            android:name="android.ext.adservices"
308-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
309            android:required="false" />
309-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
310
311        <receiver
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
312            android:name="androidx.profileinstaller.ProfileInstallReceiver"
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
313            android:directBootAware="false"
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
314            android:enabled="true"
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
315            android:exported="true"
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
316            android:permission="android.permission.DUMP" >
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
317            <intent-filter>
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
318                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
319            </intent-filter>
320            <intent-filter>
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
321                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
322            </intent-filter>
323            <intent-filter>
323-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
324                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
325            </intent-filter>
326            <intent-filter>
326-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
327                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
327-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
327-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
328            </intent-filter>
329        </receiver>
330
331        <activity
331-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
332            android:name="androidx.activity.ComponentActivity"
332-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
333            android:exported="true" />
333-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
334
335        <service
335-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
336            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
336-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
337            android:exported="false" >
337-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
338            <meta-data
338-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
339                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
339-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
340                android:value="cct" />
340-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
341        </service>
342        <service
342-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
343            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
343-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
344            android:exported="false"
344-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
345            android:permission="android.permission.BIND_JOB_SERVICE" >
345-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
346        </service>
347
348        <receiver
348-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
349            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
349-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
350            android:exported="false" />
350-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
351    </application>
352
353</manifest>
