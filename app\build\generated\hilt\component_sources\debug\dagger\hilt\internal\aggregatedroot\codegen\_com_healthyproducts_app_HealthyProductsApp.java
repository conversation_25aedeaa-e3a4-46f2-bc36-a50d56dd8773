package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.healthyproducts.app.HealthyProductsApp",
    rootPackage = "com.healthyproducts.app",
    originatingRoot = "com.healthyproducts.app.HealthyProductsApp",
    originatingRootPackage = "com.healthyproducts.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "HealthyProductsApp",
    originatingRootSimpleNames = "HealthyProductsApp"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_healthyproducts_app_HealthyProductsApp {
}
