package com.healthyproducts.app.ui.screens.settings;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a$\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u00a8\u0006\r"}, d2 = {"UserFoodPreferenceItem", "", "preference", "Lcom/healthyproducts/app/data/model/UserFoodPreference;", "onDelete", "Lkotlin/Function0;", "UserFoodPreferencesScreen", "navController", "Landroidx/navigation/NavController;", "foodAnalysisViewModel", "Lcom/healthyproducts/app/ui/viewmodel/FoodAnalysisViewModel;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "app_debug"})
public final class UserFoodPreferencesScreenKt {
    
    /**
     * Kullanıcı gıda tercihleri ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void UserFoodPreferencesScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel foodAnalysisViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * Kullanıcı gıda tercihi öğesi
     */
    @androidx.compose.runtime.Composable()
    public static final void UserFoodPreferenceItem(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.UserFoodPreference preference, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete) {
    }
}