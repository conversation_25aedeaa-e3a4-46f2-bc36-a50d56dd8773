package com.healthyproducts.app.data.repository

import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.healthyproducts.app.data.model.Product
import com.healthyproducts.app.data.model.ProductSearchResult
import com.healthyproducts.app.data.model.ProductMatchType
import com.healthyproducts.app.data.model.SaveProductRequest
import kotlinx.coroutines.tasks.await
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ProductRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val COLLECTION_PRODUCTS = "products"
        private const val TAG = "ProductRepository"
    }

    /**
     * Barkoda göre ürün arar
     */
    suspend fun getProductByBarcode(barcode: String): Result<Product?> {
        return try {
            Log.d(TAG, "Searching product by barcode: $barcode")
            
            val querySnapshot = firestore.collection(COLLECTION_PRODUCTS)
                .whereEqualTo("barcode", barcode)
                .limit(1)
                .get()
                .await()

            val product = querySnapshot.documents.firstOrNull()?.toObject(Product::class.java)
            
            Log.d(TAG, "Product found: ${product != null}")
            Result.success(product)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting product by barcode: $barcode", e)
            Result.failure(e)
        }
    }

    /**
     * Yeni ürün kaydeder
     */
    suspend fun saveProduct(request: SaveProductRequest, userId: String): Result<Product> {
        return try {
            Log.d(TAG, "Saving product with barcode: ${request.barcode}")
            
            val now = Date()
            val product = Product(
                barcode = request.barcode,
                name = request.name,
                brand = request.brand,
                ingredients = request.ingredients,
                analysisResult = request.analysisResult,
                healthScore = request.healthScore,
                language = request.language,
                category = request.category,
                imageUrl = request.imageUrl,
                createdAt = now,
                updatedAt = now,
                createdBy = userId,
                isVerified = false
            )

            val documentRef = firestore.collection(COLLECTION_PRODUCTS).add(product).await()
            val savedProduct = product.copy(id = documentRef.id)
            
            Log.d(TAG, "Product saved successfully with ID: ${documentRef.id}")
            Result.success(savedProduct)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving product", e)
            Result.failure(e)
        }
    }

    /**
     * Ürün günceller
     */
    suspend fun updateProduct(productId: String, request: SaveProductRequest): Result<Product> {
        return try {
            Log.d(TAG, "Updating product: $productId")
            
            val updates = mapOf(
                "name" to request.name,
                "brand" to request.brand,
                "ingredients" to request.ingredients,
                "analysisResult" to request.analysisResult,
                "healthScore" to request.healthScore,
                "language" to request.language,
                "category" to request.category,
                "imageUrl" to request.imageUrl,
                "updatedAt" to Date()
            )

            firestore.collection(COLLECTION_PRODUCTS)
                .document(productId)
                .update(updates)
                .await()

            // Güncellenmiş ürünü getir
            val updatedDoc = firestore.collection(COLLECTION_PRODUCTS)
                .document(productId)
                .get()
                .await()

            val updatedProduct = updatedDoc.toObject(Product::class.java)
                ?: throw Exception("Updated product not found")

            Log.d(TAG, "Product updated successfully")
            Result.success(updatedProduct)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating product: $productId", e)
            Result.failure(e)
        }
    }

    /**
     * Kullanıcının kaydettiği ürünleri getirir
     */
    suspend fun getUserProducts(userId: String, limit: Int = 50): Result<List<Product>> {
        return try {
            Log.d(TAG, "Getting products for user: $userId")
            
            val querySnapshot = firestore.collection(COLLECTION_PRODUCTS)
                .whereEqualTo("createdBy", userId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val products = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)
            }
            
            Log.d(TAG, "Found ${products.size} products for user")
            Result.success(products)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user products", e)
            Result.failure(e)
        }
    }

    /**
     * Ürün siler
     */
    suspend fun deleteProduct(productId: String, userId: String): Result<Unit> {
        return try {
            Log.d(TAG, "Deleting product: $productId")
            
            // Önce ürünün kullanıcıya ait olduğunu kontrol et
            val doc = firestore.collection(COLLECTION_PRODUCTS)
                .document(productId)
                .get()
                .await()

            val product = doc.toObject(Product::class.java)
            if (product?.createdBy != userId) {
                throw Exception("Unauthorized: Product does not belong to user")
            }

            firestore.collection(COLLECTION_PRODUCTS)
                .document(productId)
                .delete()
                .await()

            Log.d(TAG, "Product deleted successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting product: $productId", e)
            Result.failure(e)
        }
    }

    /**
     * Benzer ürünleri arar (içeriklere göre)
     */
    suspend fun searchSimilarProducts(ingredients: List<String>, limit: Int = 10): Result<List<ProductSearchResult>> {
        return try {
            Log.d(TAG, "Searching similar products for ${ingredients.size} ingredients")
            
            // Bu basit bir implementasyon - gelecekte daha gelişmiş arama algoritması eklenebilir
            val querySnapshot = firestore.collection(COLLECTION_PRODUCTS)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val results = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.let { product ->
                    val similarity = calculateIngredientSimilarity(ingredients, product.ingredients)
                    if (similarity > 0.5) { // %50'den fazla benzerlik
                        ProductSearchResult(product, ProductMatchType.SIMILAR_INGREDIENTS)
                    } else null
                }
            }
            
            Log.d(TAG, "Found ${results.size} similar products")
            Result.success(results)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching similar products", e)
            Result.failure(e)
        }
    }

    /**
     * İçerik benzerliği hesaplar (basit algoritma)
     */
    private fun calculateIngredientSimilarity(ingredients1: List<String>, ingredients2: List<String>): Double {
        if (ingredients1.isEmpty() || ingredients2.isEmpty()) return 0.0
        
        val set1 = ingredients1.map { it.lowercase().trim() }.toSet()
        val set2 = ingredients2.map { it.lowercase().trim() }.toSet()
        
        val intersection = set1.intersect(set2).size
        val union = set1.union(set2).size
        
        return if (union > 0) intersection.toDouble() / union.toDouble() else 0.0
    }
}
