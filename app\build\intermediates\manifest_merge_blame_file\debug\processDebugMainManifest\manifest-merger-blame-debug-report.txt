1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.healthyproducts.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Kamera izni -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:5-65
12-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- İnternet izni -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:5-67
15-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:22-64
16
17    <!-- Depolama izinleri -->
18    <uses-permission
18-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:22-77
20        android:maxSdkVersion="32" />
20-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:5-76
21-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:22-73
22
23    <!-- Kamera özelliği gereksinimi -->
24    <uses-feature
24-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:17:5-19:36
25        android:name="android.hardware.camera"
25-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:18:9-47
26        android:required="false" />
26-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:19:9-33
27
28    <queries>
28-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:17:5-19:15
29        <package android:name="com.facebook.katana" />
29-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:9-55
29-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:18-52
30    </queries>
31
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
32-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
32-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
33    <uses-permission android:name="android.permission.WAKE_LOCK" />
33-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:5-68
33-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:22-65
34    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
34-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
34-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
35    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
35-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
35-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
36-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
36-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
37-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
37-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
38    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
38-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
38-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
46-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:21:5-44:19
47        android:name="com.healthyproducts.app.HealthyProductsApp"
47-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:22:9-43
48        android:allowBackup="true"
48-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:23:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:24:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:25:9-54
54        android:icon="@mipmap/ic_launcher"
54-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:26:9-43
55        android:label="@string/app_name"
55-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:27:9-41
56        android:roundIcon="@mipmap/ic_launcher_round"
56-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:28:9-54
57        android:supportsRtl="true"
57-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:29:9-35
58        android:testOnly="true"
59        android:theme="@style/Theme.HealthyProducts" >
59-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:30:9-53
60        <activity
60-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:32:9-43:20
61            android:name="com.healthyproducts.app.MainActivity"
61-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:33:13-41
62            android:configChanges="orientation|screenSize"
62-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:38:13-59
63            android:exported="true"
63-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:34:13-36
64            android:label="@string/app_name"
64-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:35:13-45
65            android:screenOrientation="portrait"
65-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:37:13-49
66            android:theme="@style/Theme.HealthyProducts" >
66-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:36:13-57
67            <intent-filter>
67-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:39:13-42:29
68                <action android:name="android.intent.action.MAIN" />
68-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:40:17-69
68-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:40:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:41:17-77
70-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:41:27-74
71            </intent-filter>
72        </activity>
73
74        <service
74-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
75            android:name="androidx.camera.core.impl.MetadataHolderService"
75-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
76            android:enabled="false"
76-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
77            android:exported="false" >
77-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
78            <meta-data
78-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
79                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
79-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
80                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
80-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
81        </service>
82
83        <activity
83-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:22:9-25:66
84            android:name="com.facebook.FacebookActivity"
84-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:23:13-57
85            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
85-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:24:13-96
86            android:theme="@style/com_facebook_activity_theme" />
86-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:25:13-63
87        <activity android:name="com.facebook.CustomTabMainActivity" />
87-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:9-71
87-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:19-68
88        <activity
88-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:27:9-41:20
89            android:name="com.facebook.CustomTabActivity"
89-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:28:13-58
90            android:exported="true" >
90-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:29:13-36
91            <intent-filter>
91-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
92                <action android:name="android.intent.action.VIEW" />
92-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
92-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
93
94                <category android:name="android.intent.category.DEFAULT" />
94-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
94-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
95                <category android:name="android.intent.category.BROWSABLE" />
95-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
95-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
96
97                <data
97-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
98                    android:host="cct.com.healthyproducts.app"
98-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
99                    android:scheme="fbconnect" />
99-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
100            </intent-filter>
101        </activity>
102        <activity
102-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
103            android:name="androidx.compose.ui.tooling.PreviewActivity"
103-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
104            android:exported="true" />
104-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
105        <activity
105-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
106            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
106-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
107            android:excludeFromRecents="true"
107-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
108            android:exported="false"
108-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
109            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
109-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
110        <!--
111            Service handling Google Sign-In user revocation. For apps that do not integrate with
112            Google Sign-In, this service will never be started.
113        -->
114        <service
114-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
115            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
115-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
116            android:exported="true"
116-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
117            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
117-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
118            android:visibleToInstantApps="true" />
118-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
119
120        <activity
120-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
121            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
121-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
122            android:excludeFromRecents="true"
122-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
123            android:exported="true"
123-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
124            android:launchMode="singleTask"
124-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
125            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
125-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
126            <intent-filter>
126-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
127                <action android:name="android.intent.action.VIEW" />
127-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
127-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
129-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
130                <category android:name="android.intent.category.BROWSABLE" />
130-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
130-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
131
132                <data
132-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
133                    android:host="firebase.auth"
133-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
134                    android:path="/"
135                    android:scheme="genericidp" />
135-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
136            </intent-filter>
137        </activity>
138        <activity
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
139            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
139-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
140            android:excludeFromRecents="true"
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
141            android:exported="true"
141-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
142            android:launchMode="singleTask"
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
143            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
144            <intent-filter>
144-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
145                <action android:name="android.intent.action.VIEW" />
145-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
145-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
146
147                <category android:name="android.intent.category.DEFAULT" />
147-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
147-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
148                <category android:name="android.intent.category.BROWSABLE" />
148-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
148-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
149
150                <data
150-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
151                    android:host="firebase.auth"
151-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
152                    android:path="/"
153                    android:scheme="recaptcha" />
153-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
154            </intent-filter>
155        </activity>
156
157        <service
157-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
158            android:name="com.google.firebase.components.ComponentDiscoveryService"
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
159            android:directBootAware="true"
159-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
160            android:exported="false" >
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
161            <meta-data
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
162                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
162-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
164            <meta-data
164-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:17:13-19:85
165                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
165-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:18:17-122
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:19:17-82
167            <meta-data
167-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:20:13-22:85
168                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
168-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:21:17-111
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:22:17-82
170            <meta-data
170-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
171                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
171-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
173            <meta-data
173-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
174                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
174-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
176            <meta-data
176-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
177                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
177-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
179            <meta-data
179-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
180                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
180-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
182            <meta-data
182-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
183                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
183-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
185            <meta-data
185-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
186                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
186-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
188            <meta-data
188-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
189                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
189-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
191            <meta-data
191-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
192                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
192-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
194            <meta-data
194-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
195                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
195-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
197        </service>
198        <service
198-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
199            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
199-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
200            android:directBootAware="true"
200-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
201            android:exported="false" >
201-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
202            <meta-data
202-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
203                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
203-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
205            <meta-data
205-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
206                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
206-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
208            <meta-data
208-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
209                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
209-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
211            <meta-data
211-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
212                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
212-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
214        </service>
215
216        <provider
216-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
217            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
217-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
218            android:authorities="com.healthyproducts.app.mlkitinitprovider"
218-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
219            android:exported="false"
219-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
220            android:initOrder="99" />
220-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
221
222        <activity
222-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
223            android:name="com.google.android.gms.common.api.GoogleApiActivity"
223-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
224            android:exported="false"
224-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
225            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
225-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
226
227        <receiver
227-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
228            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
228-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
229            android:enabled="true"
229-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
230            android:exported="false" >
230-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
231        </receiver>
232
233        <service
233-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
234            android:name="com.google.android.gms.measurement.AppMeasurementService"
234-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
235            android:enabled="true"
235-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
236            android:exported="false" />
236-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
237        <service
237-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
238            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
238-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
239            android:enabled="true"
239-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
240            android:exported="false"
240-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
241            android:permission="android.permission.BIND_JOB_SERVICE" />
241-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
242
243        <property
243-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
244            android:name="android.adservices.AD_SERVICES_CONFIG"
244-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
245            android:resource="@xml/ga_ad_services_config" />
245-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
246
247        <provider
247-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
248            android:name="com.google.firebase.provider.FirebaseInitProvider"
248-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
249            android:authorities="com.healthyproducts.app.firebaseinitprovider"
249-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
250            android:directBootAware="true"
250-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
251            android:exported="false"
251-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
252            android:initOrder="100" />
252-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
253        <provider
253-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
254            android:name="androidx.startup.InitializationProvider"
254-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
255            android:authorities="com.healthyproducts.app.androidx-startup"
255-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
256            android:exported="false" >
256-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
257            <meta-data
257-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
258                android:name="androidx.emoji2.text.EmojiCompatInitializer"
258-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
259                android:value="androidx.startup" />
259-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
260            <meta-data
260-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
261                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
261-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
262                android:value="androidx.startup" />
262-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
263            <meta-data
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
264                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
265                android:value="androidx.startup" />
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
266        </provider>
267        <!--
268         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
269         with the application context. This config is merged in with the host app's manifest,
270         but there can only be one provider with the same authority activated at any given
271         point; so if the end user has two or more different apps that use Facebook SDK, only the
272         first one will be able to use the provider. To work around this problem, we use the
273         following placeholder in the authority to identify each host application as if it was
274         a completely different provider.
275        -->
276        <provider
276-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:29:9-32:40
277            android:name="com.facebook.internal.FacebookInitProvider"
277-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:30:13-70
278            android:authorities="com.healthyproducts.app.FacebookInitProvider"
278-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:31:13-72
279            android:exported="false" />
279-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:32:13-37
280
281        <receiver
281-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:34:9-40:20
282            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
282-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:35:13-86
283            android:exported="false" >
283-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:36:13-37
284            <intent-filter>
284-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:37:13-39:29
285                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
285-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:17-95
285-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:25-92
286            </intent-filter>
287        </receiver>
288        <receiver
288-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:41:9-47:20
289            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
289-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:42:13-118
290            android:exported="false" >
290-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:43:13-37
291            <intent-filter>
291-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:44:13-46:29
292                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
292-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:17-103
292-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:25-100
293            </intent-filter>
294        </receiver>
295
296        <uses-library
296-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
297            android:name="android.ext.adservices"
297-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
298            android:required="false" />
298-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
299
300        <receiver
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
301            android:name="androidx.profileinstaller.ProfileInstallReceiver"
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
302            android:directBootAware="false"
302-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
303            android:enabled="true"
303-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
304            android:exported="true"
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
305            android:permission="android.permission.DUMP" >
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
306            <intent-filter>
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
307                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
308            </intent-filter>
309            <intent-filter>
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
310                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
311            </intent-filter>
312            <intent-filter>
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
313                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
314            </intent-filter>
315            <intent-filter>
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
316                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
317            </intent-filter>
318        </receiver>
319
320        <meta-data
320-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
321            android:name="com.google.android.gms.version"
321-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
322            android:value="@integer/google_play_services_version" />
322-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
323
324        <activity
324-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
325            android:name="androidx.activity.ComponentActivity"
325-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
326            android:exported="true" />
326-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
327
328        <service
328-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
329            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
329-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
330            android:exported="false" >
330-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
331            <meta-data
331-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
332                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
332-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
333                android:value="cct" />
333-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
334        </service>
335        <service
335-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
336            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
336-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
337            android:exported="false"
337-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
338            android:permission="android.permission.BIND_JOB_SERVICE" >
338-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
339        </service>
340
341        <receiver
341-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
342            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
342-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
343            android:exported="false" />
343-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
344    </application>
345
346</manifest>
