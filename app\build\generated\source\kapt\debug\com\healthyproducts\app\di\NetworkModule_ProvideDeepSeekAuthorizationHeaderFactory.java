package com.healthyproducts.app.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideDeepSeekAuthorizationHeaderFactory implements Factory<String> {
  @Override
  public String get() {
    return provideDeepSeekAuthorizationHeader();
  }

  public static NetworkModule_ProvideDeepSeekAuthorizationHeaderFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static String provideDeepSeekAuthorizationHeader() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideDeepSeekAuthorizationHeader());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideDeepSeekAuthorizationHeaderFactory INSTANCE = new NetworkModule_ProvideDeepSeekAuthorizationHeaderFactory();
  }
}
