package com.healthyproducts.app.ui.screens.favorites;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a&\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0007\u001a$\u0010\n\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a\u0010\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u001e\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u00a8\u0006\u0013"}, d2 = {"EmptyFavoritesContent", "", "paddingValues", "Landroidx/compose/foundation/layout/PaddingValues;", "FavoritesContent", "navController", "Landroidx/navigation/NavController;", "favoriteProducts", "", "Lcom/healthyproducts/app/model/Product;", "FavoritesScreen", "favoritesViewModel", "Lcom/healthyproducts/app/ui/viewmodel/FavoritesViewModel;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "LoadingContent", "NotLoggedInContent", "onLoginClick", "Lkotlin/Function0;", "app_debug"})
public final class FavoritesScreenKt {
    
    /**
     * Favoriler ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void FavoritesScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.FavoritesViewModel favoritesViewModel, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * Yükleme içeriği
     */
    @androidx.compose.runtime.Composable()
    public static final void LoadingContent(@org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.layout.PaddingValues paddingValues) {
    }
    
    /**
     * Giriş yapmamış kullanıcı için içerik
     */
    @androidx.compose.runtime.Composable()
    public static final void NotLoggedInContent(@org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.layout.PaddingValues paddingValues, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLoginClick) {
    }
    
    /**
     * Boş favoriler içeriği
     */
    @androidx.compose.runtime.Composable()
    public static final void EmptyFavoritesContent(@org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.layout.PaddingValues paddingValues) {
    }
    
    /**
     * Favoriler ekranı içeriği
     */
    @androidx.compose.runtime.Composable()
    public static final void FavoritesContent(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.layout.PaddingValues paddingValues, @org.jetbrains.annotations.NotNull()
    java.util.List<com.healthyproducts.app.model.Product> favoriteProducts) {
    }
}