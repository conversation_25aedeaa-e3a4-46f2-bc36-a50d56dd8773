package com.healthyproducts.app.ui.screens.scan

import android.content.Intent
import android.provider.Settings
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Analytics
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.foundation.clickable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.navigation.Screen
import com.healthyproducts.app.ui.components.BottomNavBar
import com.healthyproducts.app.ui.components.CameraPreview
import com.healthyproducts.app.ui.components.EditableIngredientsList
import com.healthyproducts.app.ui.components.OcrCameraPreview
import com.healthyproducts.app.ui.components.ScannerOverlay
import com.healthyproducts.app.ui.components.FrameSize
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.ScanViewModel
import com.healthyproducts.app.util.CameraPermissionHelper
import com.healthyproducts.app.util.CameraPermissionState
import com.healthyproducts.app.util.rememberCameraPermissionState
import androidx.hilt.navigation.compose.hiltViewModel
import com.healthyproducts.app.data.repository.ProductRepository
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope

/**
 * Tarama ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScanScreen(
    navController: NavController,
    scanViewModel: ScanViewModel = hiltViewModel(),
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel()
) {
    var selectedTabIndex by remember { mutableStateOf(0) }
    val scanState by scanViewModel.scanState.collectAsState()
    val context = LocalContext.current

    // Barkod başarıyla tarandığında ürün detay sayfasına yönlendir
    LaunchedEffect(scanState) {
        if (scanState is ScanViewModel.ScanState.Success) {
            val barcode = (scanState as ScanViewModel.ScanState.Success).barcode
            // Barkod değerini ürün ID'si olarak kullan
            // Gerçek uygulamada burada veritabanından ürün bilgilerini getirmeliyiz
            navController.navigate(Screen.ProductDetail.createRoute(barcode))
            // Tarama durumunu sıfırla
            scanViewModel.resetScanState()
        }
    }

    Scaffold(
        bottomBar = { BottomNavBar(navController = navController) }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // Tarama türü sekmeleri
            TabRow(selectedTabIndex = selectedTabIndex) {
                Tab(
                    selected = selectedTabIndex == 0,
                    onClick = { selectedTabIndex = 0 },
                    text = { Text(text = stringResource(R.string.barcode_tab)) }
                )

                Tab(
                    selected = selectedTabIndex == 1,
                    onClick = { selectedTabIndex = 1 },
                    text = { Text(text = stringResource(R.string.ocr_tab)) }
                )
            }

            // Seçilen sekmeye göre içerik gösterimi
            when (selectedTabIndex) {
                0 -> BarcodeScanContent(scanViewModel)
                1 -> OcrScanContent(scanViewModel, foodAnalysisViewModel, navController)
            }
        }
    }
}

/**
 * Barkod tarama içeriği
 */
@Composable
fun BarcodeScanContent(
    scanViewModel: ScanViewModel
) {
    val context = LocalContext.current
    val scanState by scanViewModel.scanState.collectAsState()

    // Kamera izni durumunu kontrol et
    val permissionState = rememberCameraPermissionState { state ->
        scanViewModel.updateCameraPermissionState(state)
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when (permissionState) {
            CameraPermissionState.GRANTED -> {
                // Kamera izni verildi, kamera önizlemeyi göster
                Box(modifier = Modifier.fillMaxSize()) {
                    CameraPreview(
                        onBarcodeDetected = { barcodes ->
                            scanViewModel.onBarcodeDetected(barcodes)
                        }
                    )

                    // Hedef dikdörtgen overlay'i
                    val frameSize by scanViewModel.frameSize.collectAsState()
                    ScannerOverlay(
                        frameHeightRatio = 1f, // Barkod genellikle kare şeklindedir
                        frameRatio = 0.7f,
                        frameSize = frameSize,
                        showIngredientsGuide = false // İçerik kılavuzunu gösterme
                    )
                }

                // Tarama durumuna göre UI göster
                when (scanState) {
                    is ScanViewModel.ScanState.Idle -> {
                        // Tarama talimatları ve çerçeve boyutu seçenekleri
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                                .align(Alignment.BottomCenter)
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.8f))
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = stringResource(R.string.scan_instructions),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            // Çerçeve boyutu seçenekleri
                            val frameSize by scanViewModel.frameSize.collectAsState()

                            Text(
                                text = stringResource(R.string.frame_size),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(bottom = 4.dp)
                            )

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                FrameSizeOption(
                                    text = stringResource(R.string.size_small),
                                    selected = frameSize == FrameSize.SMALL,
                                    onClick = { scanViewModel.updateFrameSize(FrameSize.SMALL) }
                                )

                                FrameSizeOption(
                                    text = stringResource(R.string.size_medium),
                                    selected = frameSize == FrameSize.MEDIUM,
                                    onClick = { scanViewModel.updateFrameSize(FrameSize.MEDIUM) }
                                )

                                FrameSizeOption(
                                    text = stringResource(R.string.size_large),
                                    selected = frameSize == FrameSize.LARGE,
                                    onClick = { scanViewModel.updateFrameSize(FrameSize.LARGE) }
                                )
                            }
                        }
                    }
                    is ScanViewModel.ScanState.Scanning -> {
                        // Taranıyor göstergesi
                        CircularProgressIndicator(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(16.dp)
                        )
                    }
                    is ScanViewModel.ScanState.Success -> {
                        // Başarılı tarama göstergesi
                        Box(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(16.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.primaryContainer)
                                .padding(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                    is ScanViewModel.ScanState.Error -> {
                        // Hata mesajı
                        Text(
                            text = (scanState as ScanViewModel.ScanState.Error).message,
                            color = MaterialTheme.colorScheme.error,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                                .align(Alignment.BottomCenter)
                        )
                    }
                }
            }
            CameraPermissionState.DENIED -> {
                // Kamera izni reddedildi, izin iste
                Text(
                    text = stringResource(R.string.camera_permission_required),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(16.dp)
                )
            }
            CameraPermissionState.PERMANENTLY_DENIED -> {
                // Kamera izni kalıcı olarak reddedildi, ayarlar sayfasına yönlendir
                var showDialog by remember { mutableStateOf(true) }

                if (showDialog) {
                    AlertDialog(
                        onDismissRequest = { showDialog = false },
                        title = { Text(text = stringResource(R.string.camera_permission_required)) },
                        text = { Text(text = "Kamera izni reddedildi. Kamera kullanmak için uygulama ayarlarından izin vermeniz gerekiyor.") },
                        confirmButton = {
                            Button(
                                onClick = {
                                    showDialog = false
                                    CameraPermissionHelper.openAppSettings(context)
                                }
                            ) {
                                Text(text = stringResource(R.string.grant_permission))
                            }
                        },
                        dismissButton = {
                            TextButton(
                                onClick = { showDialog = false }
                            ) {
                                Text(text = stringResource(R.string.cancel))
                            }
                        }
                    )
                }

                Text(
                    text = stringResource(R.string.camera_permission_required),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}

/**
 * Çerçeve boyutu seçeneği bileşeni
 */
@Composable
fun FrameSizeOption(
    text: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (selected) {
        MaterialTheme.colorScheme.primaryContainer
    } else {
        MaterialTheme.colorScheme.surface
    }

    val textColor = if (selected) {
        MaterialTheme.colorScheme.onPrimaryContainer
    } else {
        MaterialTheme.colorScheme.onSurface
    }

    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(4.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            color = textColor,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

/**
 * OCR tarama içeriği
 */
@Composable
fun OcrScanContent(
    scanViewModel: ScanViewModel,
    foodAnalysisViewModel: FoodAnalysisViewModel,
    navController: NavController
) {
    // Repository injection
    val productRepository: ProductRepository = hiltViewModel()
    val scope = rememberCoroutineScope()

    // State for barcode checking
    var isCheckingBarcode by remember { mutableStateOf(false) }
    var foundProduct by remember { mutableStateOf<com.healthyproducts.app.data.model.Product?>(null) }
    val context = LocalContext.current
    val ocrScanState by scanViewModel.ocrScanState.collectAsState()
    val recognizedIngredients by scanViewModel.recognizedIngredients.collectAsState()

    // Kamera izni durumunu kontrol et
    val permissionState = rememberCameraPermissionState { state ->
        scanViewModel.updateCameraPermissionState(state)
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when (permissionState) {
            CameraPermissionState.GRANTED -> {
                // Kamera izni verildi, kamera önizlemeyi göster
                Box(modifier = Modifier.fillMaxSize()) {
                    OcrCameraPreview(
                        onTextDetected = { text ->
                            scanViewModel.onTextDetected(text)
                        }
                    )

                    // Hedef dikdörtgen overlay'i
                    val frameSize by scanViewModel.frameSize.collectAsState()
                    ScannerOverlay(
                        frameHeightRatio = 0.5f, // İçerik listesi genellikle dikdörtgen şeklindedir
                        frameRatio = 0.8f,
                        frameSize = frameSize,
                        showIngredientsGuide = true // İçerik kılavuzunu göster
                    )
                }

                // Tarama durumuna göre UI göster
                when (ocrScanState) {
                    is ScanViewModel.OcrScanState.Idle -> {
                        // Tarama talimatları ve tara düğmesi
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                                .align(Alignment.BottomCenter)
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.8f))
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = stringResource(R.string.ocr_instructions),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            // Çerçeve boyutu seçenekleri
                            val frameSize by scanViewModel.frameSize.collectAsState()

                            Text(
                                text = stringResource(R.string.frame_size),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(bottom = 4.dp)
                            )

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                FrameSizeOption(
                                    text = stringResource(R.string.size_small),
                                    selected = frameSize == FrameSize.SMALL,
                                    onClick = { scanViewModel.updateFrameSize(FrameSize.SMALL) }
                                )

                                FrameSizeOption(
                                    text = stringResource(R.string.size_medium),
                                    selected = frameSize == FrameSize.MEDIUM,
                                    onClick = { scanViewModel.updateFrameSize(FrameSize.MEDIUM) }
                                )

                                FrameSizeOption(
                                    text = stringResource(R.string.size_large),
                                    selected = frameSize == FrameSize.LARGE,
                                    onClick = { scanViewModel.updateFrameSize(FrameSize.LARGE) }
                                )
                            }

                            Spacer(modifier = Modifier.height(16.dp))

                            Button(
                                onClick = {
                                    // Önce barkod kontrolü yap
                                    scope.launch {
                                        isCheckingBarcode = true

                                        // Kamera görüntüsünü yakala ve barkod ara
                                        scanViewModel.captureImageForBarcodeCheck { capturedImage ->
                                            scope.launch {
                                                // Barkod algılama işlemi
                                                val detectedBarcodes = scanViewModel.detectBarcodesInImage(capturedImage)

                                                if (detectedBarcodes.isNotEmpty()) {
                                                    // Barkod bulundu, veritabanında ara
                                                    val barcode = detectedBarcodes.first()
                                                    val result = productRepository.getProductByBarcode(barcode)
                                                    foundProduct = result.getOrNull()

                                                    if (foundProduct != null) {
                                                        // Ürün bulundu, analiz ekranına git
                                                        isCheckingBarcode = false
                                                        navController.navigate("food_analysis?ingredients=${foundProduct!!.ingredients.joinToString(",")}")
                                                        return@launch
                                                    }
                                                }

                                                // Barkod bulunamadı veya ürün yok, OCR işlemi başlat
                                                isCheckingBarcode = false
                                                scanViewModel.captureAndAnalyzeImage()
                                            }
                                        }
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                enabled = !isCheckingBarcode
                            ) {
                                if (isCheckingBarcode) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(16.dp),
                                            strokeWidth = 2.dp,
                                            color = MaterialTheme.colorScheme.onPrimary
                                        )
                                        Text(text = stringResource(R.string.checking_barcode))
                                    }
                                } else {
                                    Text(text = stringResource(R.string.scan_ingredients))
                                }
                            }
                        }
                    }
                    is ScanViewModel.OcrScanState.Scanning -> {
                        // Taranıyor göstergesi
                        CircularProgressIndicator(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(16.dp)
                        )
                    }
                    is ScanViewModel.OcrScanState.Success -> {
                        // Başarılı tarama göstergesi ve tanınan içerikler
                        val correctionMode by scanViewModel.correctionMode.collectAsState()
                        val correctionState by scanViewModel.correctionState.collectAsState()
                        val rawOcrText by scanViewModel.rawOcrText.collectAsState()

                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                                .align(Alignment.BottomCenter)
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.9f))
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            Text(
                                text = stringResource(R.string.ingredients_recognized),
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.primary,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            // Tanınan içerikleri düzenlenebilir şekilde göster
                            Text(
                                text = if (correctionMode)
                                    stringResource(R.string.edit_recognized_ingredients)
                                else
                                    "Ham OCR Sonucu (Düzeltilmemiş)",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            // Düzeltme durumu göstergesi (düzeltme modunda ve düzeltme devam ediyorsa)
                            if (correctionMode && correctionState is ScanViewModel.CorrectionState.Correcting) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(bottom = 8.dp),
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        strokeWidth = 2.dp
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "İçerikler düzeltiliyor...",
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            }

                            // Düzenlenebilir içerik listesi
                            val scrollState = rememberScrollState()
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .heightIn(max = 300.dp)
                                    .verticalScroll(scrollState)
                            ) {
                                EditableIngredientsList(
                                    ingredients = recognizedIngredients,
                                    onIngredientsUpdated = { updatedIngredients ->
                                        // Düzenlenen içerikleri güncelle
                                        scanViewModel.updateRecognizedIngredients(updatedIngredients)
                                    }
                                )
                            }

                            // Buton satırı
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                // İçeriği Düzelt ve Analize Gönder butonları
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    // İçeriği Düzelt butonu
                                    Button(
                                        onClick = {
                                            // İçerik düzeltme modunu aktifleştir
                                            scanViewModel.setCorrectionMode(true)
                                        },
                                        modifier = Modifier.weight(1f),
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = if (correctionMode)
                                                MaterialTheme.colorScheme.secondary.copy(alpha = 0.7f)
                                            else
                                                MaterialTheme.colorScheme.secondary
                                        ),
                                        enabled = !correctionMode || correctionState !is ScanViewModel.CorrectionState.Correcting
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Edit,
                                                contentDescription = null,
                                                modifier = Modifier.size(18.dp)
                                            )
                                            Text(text = "İçeriği Düzelt")
                                        }
                                    }

                                    // Analize Gönder butonu
                                    Button(
                                        onClick = {
                                            // Analiz ekranına git ve içerikleri gönder
                                            navController.navigate(
                                                Screen.FoodAnalysis.createRoute(
                                                    ingredients = recognizedIngredients.joinToString(",")
                                                )
                                            )
                                            // Tarama durumunu sıfırla
                                            scanViewModel.resetOcrScanState()
                                        },
                                        modifier = Modifier.weight(1f),
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = MaterialTheme.colorScheme.primary
                                        )
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Analytics,
                                                contentDescription = null,
                                                modifier = Modifier.size(18.dp)
                                            )
                                            Text(text = "Analize Gönder")
                                        }
                                    }
                                }

                                // Kapat butonu
                                Button(
                                    onClick = { scanViewModel.resetOcrScanState() },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 8.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = MaterialTheme.colorScheme.surfaceVariant,
                                        contentColor = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                ) {
                                    Text(text = stringResource(R.string.close))
                                }
                            }
                        }
                    }
                    is ScanViewModel.OcrScanState.Error -> {
                        // Hata mesajı
                        Text(
                            text = (ocrScanState as ScanViewModel.OcrScanState.Error).message,
                            color = MaterialTheme.colorScheme.error,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                                .align(Alignment.BottomCenter)
                        )
                    }
                }
            }
            CameraPermissionState.DENIED -> {
                // Kamera izni reddedildi, izin iste
                Text(
                    text = stringResource(R.string.camera_permission_required),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(16.dp)
                )
            }
            CameraPermissionState.PERMANENTLY_DENIED -> {
                // Kamera izni kalıcı olarak reddedildi, ayarlar sayfasına yönlendir
                var showDialog by remember { mutableStateOf(true) }

                if (showDialog) {
                    AlertDialog(
                        onDismissRequest = { showDialog = false },
                        title = { Text(text = stringResource(R.string.camera_permission_required)) },
                        text = { Text(text = "Kamera izni reddedildi. Kamera kullanmak için uygulama ayarlarından izin vermeniz gerekiyor.") },
                        confirmButton = {
                            Button(
                                onClick = {
                                    showDialog = false
                                    CameraPermissionHelper.openAppSettings(context)
                                }
                            ) {
                                Text(text = stringResource(R.string.grant_permission))
                            }
                        },
                        dismissButton = {
                            TextButton(
                                onClick = { showDialog = false }
                            ) {
                                Text(text = stringResource(R.string.cancel))
                            }
                        }
                    )
                }

                Text(
                    text = stringResource(R.string.camera_permission_required),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}
