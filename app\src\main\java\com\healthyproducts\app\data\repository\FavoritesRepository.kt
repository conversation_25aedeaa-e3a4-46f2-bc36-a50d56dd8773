package com.healthyproducts.app.data.repository

import com.healthyproducts.app.model.FavoriteProduct
import com.healthyproducts.app.model.Product
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Date

/**
 * Favori ürünleri yöneten repository sınıfı
 * Not: Şu an için mock veri kullanıyoruz, gerçek Supabase entegrasyonu daha sonra eklenecek
 */
class FavoritesRepository {

    // Mock favori ürünler
    private val mockFavorites = mutableMapOf<String, MutableList<FavoriteProduct>>()

    // Mock ürünler
    private val mockProducts = mutableMapOf<String, Product>()

    init {
        // Örnek ürünler ekle
        val product1 = Product(
            id = "1",
            barcode = "1234567890123",
            name = "Örnek Ürün 1",
            brand = "Örnek Marka",
            ingredients = emptyList(),
            createdAt = Date()
        )

        val product2 = Product(
            id = "2",
            barcode = "2234567890123",
            name = "Örnek Ürün 2",
            brand = "Örnek Marka",
            ingredients = emptyList(),
            createdAt = Date()
        )

        mockProducts["1"] = product1
        mockProducts["2"] = product2
    }

    /**
     * Kullanıcının favori ürünlerini getirme
     */
    suspend fun getFavoriteProducts(userId: String): Result<List<Product>> = withContext(Dispatchers.IO) {
        try {
            // Kullanıcının favori ürünlerini al
            val favorites = mockFavorites[userId] ?: emptyList()

            // Favori ürün ID'lerini al
            val favoriteIds = favorites.map { it.productId }

            if (favoriteIds.isEmpty()) {
                return@withContext Result.success(emptyList())
            }

            // Ürünleri getir
            val products = favoriteIds.mapNotNull { mockProducts[it] }

            Result.success(products)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Ürünü favorilere ekleme
     */
    suspend fun addToFavorites(userId: String, productId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Kullanıcının favori listesini al veya oluştur
            val userFavorites = mockFavorites.getOrPut(userId) { mutableListOf() }

            // Ürün zaten favorilerde mi kontrol et
            if (userFavorites.none { it.productId == productId }) {
                // Favorilere ekle
                userFavorites.add(
                    FavoriteProduct(
                        userId = userId,
                        productId = productId,
                        addedAt = Date()
                    )
                )
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Ürünü favorilerden çıkarma
     */
    suspend fun removeFromFavorites(userId: String, productId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Kullanıcının favori listesini al
            val userFavorites = mockFavorites[userId]

            // Ürünü favorilerden çıkar
            userFavorites?.removeIf { it.productId == productId }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Ürünün favori olup olmadığını kontrol etme
     */
    suspend fun isFavorite(userId: String, productId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            // Kullanıcının favori listesini al
            val userFavorites = mockFavorites[userId] ?: emptyList()

            // Ürün favorilerde mi kontrol et
            val isFavorite = userFavorites.any { it.productId == productId }

            Result.success(isFavorite)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
