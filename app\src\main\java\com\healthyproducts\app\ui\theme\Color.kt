package com.healthyproducts.app.ui.theme

import androidx.compose.ui.graphics.Color

// Light Theme Colors
val Primary = Color(0xFF4CAF50)
val PrimaryVariant = Color(0xFF388E3C)
val Secondary = Color(0xFF8BC34A)
val SecondaryVariant = Color(0xFF689F38)
val Background = Color(0xFFF5F5F5)
val Surface = Color(0xFFFFFFFF)
val Error = Color(0xFFB00020)
val OnPrimary = Color(0xFFFFFFFF)
val OnSecondary = Color(0xFF000000)
val OnBackground = Color(0xFF000000)
val OnSurface = Color(0xFF000000)
val OnError = Color(0xFFFFFFFF)

// Dark Theme Colors
val PrimaryDark = Color(0xFF388E3C)
val PrimaryVariantDark = Color(0xFF1B5E20)
val SecondaryDark = Color(0xFF689F38)
val SecondaryVariantDark = Color(0xFF33691E)
val BackgroundDark = Color(0xFF121212)
val SurfaceDark = Color(0xFF1E1E1E)
val ErrorDark = Color(0xFFCF6679)
val OnPrimaryDark = Color(0xFFFFFFFF)
val OnSecondaryDark = Color(0xFF000000)
val OnBackgroundDark = Color(0xFFFFFFFF)
val OnSurfaceDark = Color(0xFFFFFFFF)
val OnErrorDark = Color(0xFF000000)

// Status Colors
val Halal = Color(0xFF4CAF50)
val Haram = Color(0xFFF44336)
val Suspicious = Color(0xFFFF9800)
val Vegan = Color(0xFF8BC34A)
val Harmful = Color(0xFFE91E63)
val Unhealthy = Color(0xFFFF5722)