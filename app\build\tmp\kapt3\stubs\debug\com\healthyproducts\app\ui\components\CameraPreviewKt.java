package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a1\u0010\u0000\u001a\u00020\u00012\'\u0010\u0002\u001a#\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\f\b\u0006\u0012\b\b\u0007\u0012\u0004\b\b(\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u00a8\u0006\t"}, d2 = {"CameraPreview", "", "onBarcodeDetected", "Lkotlin/Function1;", "", "Lcom/google/mlkit/vision/barcode/common/Barcode;", "Lkotlin/ParameterName;", "name", "barcodes", "app_debug"})
public final class CameraPreviewKt {
    
    /**
     * Kamera önizleme bileşeni
     */
    @androidx.compose.runtime.Composable()
    public static final void CameraPreview(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<? extends com.google.mlkit.vision.barcode.common.Barcode>, kotlin.Unit> onBarcodeDetected) {
    }
}