package com.healthyproducts.app.data.repository

import android.net.Uri
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageReference
import kotlinx.coroutines.tasks.await
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase Storage işlemlerini yöneten repository sınıfı
 */
@Singleton
class FirebaseStorageRepository @Inject constructor(
    private val storage: FirebaseStorage,
    private val authRepository: FirebaseAuthRepository
) {
    /**
     * Kullanıcı profil fotoğrafını yükler
     */
    suspend fun uploadProfilePhoto(imageUri: Uri): String {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")
        
        val fileRef = storage.reference
            .child("profile_photos")
            .child(userId)
            .child("${UUID.randomUUID()}.jpg")
        
        return uploadImage(fileRef, imageUri)
    }
    
    /**
     * Ürün fotoğrafını yükler
     */
    suspend fun uploadProductPhoto(imageUri: Uri): String {
        val fileRef = storage.reference
            .child("product_photos")
            .child("${UUID.randomUUID()}.jpg")
        
        return uploadImage(fileRef, imageUri)
    }
    
    /**
     * Görüntüyü yükler ve indirme URL'sini döndürür
     */
    private suspend fun uploadImage(fileRef: StorageReference, imageUri: Uri): String {
        fileRef.putFile(imageUri).await()
        return fileRef.downloadUrl.await().toString()
    }
    
    /**
     * Dosyayı siler
     */
    suspend fun deleteFile(fileUrl: String) {
        val fileRef = storage.getReferenceFromUrl(fileUrl)
        fileRef.delete().await()
    }
}
