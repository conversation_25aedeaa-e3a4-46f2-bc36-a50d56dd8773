package com.healthyproducts.app.ui.screens.barcode;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000H\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u008e\u0001\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\u00152\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u00182\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u0015H\u0082@\u00a2\u0006\u0002\u0010\u001a\u00a8\u0006\u001b"}, d2 = {"BarcodeScanScreen", "", "navController", "Landroidx/navigation/NavController;", "saveProduct", "productRepository", "Lcom/healthyproducts/app/data/repository/ProductRepository;", "imageUploadRepository", "Lcom/healthyproducts/app/data/repository/ImageUploadRepository;", "auth", "Lcom/google/firebase/auth/FirebaseAuth;", "barcode", "", "productName", "productBrand", "ingredients", "productImage", "Landroid/graphics/Bitmap;", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "onLoading", "Lkotlin/Function1;", "", "onSuccess", "Lkotlin/Function0;", "onError", "(Lcom/healthyproducts/app/data/repository/ProductRepository;Lcom/healthyproducts/app/data/repository/ImageUploadRepository;Lcom/google/firebase/auth/FirebaseAuth;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroid/graphics/Bitmap;Lcom/healthyproducts/app/data/model/SupportedLanguage;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class BarcodeScanScreenKt {
    
    /**
     * Barkod tarama ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void BarcodeScanScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    /**
     * Ürün kaydetme fonksiyonu
     */
    private static final java.lang.Object saveProduct(com.healthyproducts.app.data.repository.ProductRepository productRepository, com.healthyproducts.app.data.repository.ImageUploadRepository imageUploadRepository, com.google.firebase.auth.FirebaseAuth auth, java.lang.String barcode, java.lang.String productName, java.lang.String productBrand, java.lang.String ingredients, android.graphics.Bitmap productImage, com.healthyproducts.app.data.model.SupportedLanguage language, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onLoading, kotlin.jvm.functions.Function0<kotlin.Unit> onSuccess, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}