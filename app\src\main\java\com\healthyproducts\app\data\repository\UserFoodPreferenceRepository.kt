package com.healthyproducts.app.data.repository

import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.healthyproducts.app.data.model.FoodPreferenceType
import com.healthyproducts.app.data.model.UserFoodPreference
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Kullanıcının gıda tercihlerini yöneten repository
 */
@Singleton
class UserFoodPreferenceRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val TAG = "UserFoodPreferenceRepo"
        private const val COLLECTION_NAME = "user_food_preferences"
    }

    // Kullanıcının gıda tercihleri
    private val _userFoodPreferences = MutableStateFlow<List<UserFoodPreference>>(emptyList())
    val userFoodPreferences: Flow<List<UserFoodPreference>> = _userFoodPreferences.asStateFlow()

    /**
     * Kullanıcının gıda tercihlerini getirir
     */
    suspend fun getUserFoodPreferences(userId: String): Result<List<UserFoodPreference>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting food preferences for user: $userId")
            
            val snapshot = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("user_id", userId)
                .get()
                .await()
            
            val preferences = snapshot.documents.mapNotNull { doc ->
                doc.toObject(UserFoodPreference::class.java)
            }
            
            Log.d(TAG, "Retrieved ${preferences.size} food preferences")
            _userFoodPreferences.value = preferences
            
            Result.success(preferences)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting food preferences", e)
            Result.failure(e)
        }
    }

    /**
     * Kullanıcının belirli türdeki gıda tercihlerini getirir
     */
    suspend fun getUserFoodPreferencesByType(
        userId: String, 
        type: FoodPreferenceType
    ): Result<List<UserFoodPreference>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting ${type.value} preferences for user: $userId")
            
            val snapshot = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("user_id", userId)
                .whereEqualTo("type", type.value)
                .get()
                .await()
            
            val preferences = snapshot.documents.mapNotNull { doc ->
                doc.toObject(UserFoodPreference::class.java)
            }
            
            Log.d(TAG, "Retrieved ${preferences.size} ${type.value} preferences")
            
            Result.success(preferences)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting ${type.value} preferences", e)
            Result.failure(e)
        }
    }

    /**
     * Kullanıcının gıda tercihini kaydeder
     */
    suspend fun saveUserFoodPreference(preference: UserFoodPreference): Result<UserFoodPreference> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Saving food preference: $preference")
            
            // Aynı türde ve aynı öğe ID'sine sahip bir tercih var mı kontrol et
            val existingPreferences = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("user_id", preference.userId)
                .whereEqualTo("type", preference.type)
                .whereEqualTo("item_id", preference.itemId)
                .get()
                .await()
            
            if (!existingPreferences.isEmpty) {
                Log.d(TAG, "Preference already exists, skipping")
                val existingPreference = existingPreferences.documents.first().toObject(UserFoodPreference::class.java)
                return@withContext Result.success(existingPreference!!)
            }
            
            // Yeni bir belge ID'si oluştur
            val docRef = firestore.collection(COLLECTION_NAME).document()
            
            // ID'yi ayarla
            val preferenceWithId = preference.copy(id = docRef.id)
            
            // Firestore'a kaydet
            docRef.set(preferenceWithId).await()
            
            Log.d(TAG, "Preference saved successfully with ID: ${preferenceWithId.id}")
            
            // Tercihleri yeniden yükle
            getUserFoodPreferences(preference.userId)
            
            Result.success(preferenceWithId)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving food preference", e)
            Result.failure(e)
        }
    }

    /**
     * Kullanıcının gıda tercihini siler
     */
    suspend fun deleteUserFoodPreference(preferenceId: String, userId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting food preference: $preferenceId")
            
            firestore.collection(COLLECTION_NAME)
                .document(preferenceId)
                .delete()
                .await()
            
            Log.d(TAG, "Preference deleted successfully")
            
            // Tercihleri yeniden yükle
            getUserFoodPreferences(userId)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting food preference", e)
            Result.failure(e)
        }
    }

    /**
     * Kullanıcının özel gıda tercihini ekler
     */
    suspend fun addCustomFoodPreference(
        userId: String,
        type: FoodPreferenceType,
        itemName: String
    ): Result<UserFoodPreference> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding custom ${type.value} preference: $itemName for user: $userId")
            
            // Özel bir ID oluştur
            val customItemId = "custom_${System.currentTimeMillis()}"
            
            val preference = UserFoodPreference(
                userId = userId,
                type = type.value,
                itemId = customItemId,
                itemName = itemName,
                isCustom = true
            )
            
            saveUserFoodPreference(preference)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding custom food preference", e)
            Result.failure(e)
        }
    }
}
