package com.healthyproducts.app.di;

import com.healthyproducts.app.data.api.AiService;
import com.healthyproducts.app.data.api.DeepSeekApiService;
import com.healthyproducts.app.data.api.GeminiApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideAiServiceFactory implements Factory<AiService> {
  private final Provider<DeepSeekApiService> deepSeekApiServiceProvider;

  private final Provider<GeminiApiService> geminiApiServiceProvider;

  public NetworkModule_ProvideAiServiceFactory(
      Provider<DeepSeekApiService> deepSeekApiServiceProvider,
      Provider<GeminiApiService> geminiApiServiceProvider) {
    this.deepSeekApiServiceProvider = deepSeekApiServiceProvider;
    this.geminiApiServiceProvider = geminiApiServiceProvider;
  }

  @Override
  public AiService get() {
    return provideAiService(deepSeekApiServiceProvider.get(), geminiApiServiceProvider.get());
  }

  public static NetworkModule_ProvideAiServiceFactory create(
      Provider<DeepSeekApiService> deepSeekApiServiceProvider,
      Provider<GeminiApiService> geminiApiServiceProvider) {
    return new NetworkModule_ProvideAiServiceFactory(deepSeekApiServiceProvider, geminiApiServiceProvider);
  }

  public static AiService provideAiService(DeepSeekApiService deepSeekApiService,
      GeminiApiService geminiApiService) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideAiService(deepSeekApiService, geminiApiService));
  }
}
