package com.healthyproducts.app;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.storage.FirebaseStorage;
import com.healthyproducts.app.data.api.AiService;
import com.healthyproducts.app.data.api.DeepSeekApiService;
import com.healthyproducts.app.data.api.GeminiApiService;
import com.healthyproducts.app.data.repository.AdditiveRepository;
import com.healthyproducts.app.data.repository.AllergenRepository;
import com.healthyproducts.app.data.repository.FatRepository;
import com.healthyproducts.app.data.repository.FirebaseAuthRepository;
import com.healthyproducts.app.data.repository.FirestoreRepository;
import com.healthyproducts.app.data.repository.FoodAnalysisRepository;
import com.healthyproducts.app.data.repository.FoodCertificateRepository;
import com.healthyproducts.app.data.repository.ImageUploadRepository;
import com.healthyproducts.app.data.repository.IntoleranceRepository;
import com.healthyproducts.app.data.repository.OcrCorrectionRepository;
import com.healthyproducts.app.data.repository.PreservativeRepository;
import com.healthyproducts.app.data.repository.ProductRepository;
import com.healthyproducts.app.data.repository.ScanHistoryRepository;
import com.healthyproducts.app.data.repository.SugarRepository;
import com.healthyproducts.app.data.repository.UserFoodPreferenceRepository;
import com.healthyproducts.app.data.repository.UserRepository;
import com.healthyproducts.app.data.service.FoodAnalysisService;
import com.healthyproducts.app.di.AppModule;
import com.healthyproducts.app.di.AppModule_ProvideContextFactory;
import com.healthyproducts.app.di.FirebaseModule;
import com.healthyproducts.app.di.FirebaseModule_ProvideFirebaseAuthFactory;
import com.healthyproducts.app.di.FirebaseModule_ProvideFirebaseFirestoreFactory;
import com.healthyproducts.app.di.FirebaseModule_ProvideFirebaseStorageFactory;
import com.healthyproducts.app.di.NetworkModule;
import com.healthyproducts.app.di.NetworkModule_ProvideAiServiceFactory;
import com.healthyproducts.app.di.NetworkModule_ProvideAuthInterceptorFactory;
import com.healthyproducts.app.di.NetworkModule_ProvideDeepSeekApiServiceFactory;
import com.healthyproducts.app.di.NetworkModule_ProvideGeminiApiServiceFactory;
import com.healthyproducts.app.di.NetworkModule_ProvideLoggingInterceptorFactory;
import com.healthyproducts.app.di.NetworkModule_ProvideOkHttpClientFactory;
import com.healthyproducts.app.di.RepositoryModule;
import com.healthyproducts.app.di.RepositoryModule_ProvideAdditiveRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideAllergenRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideFatRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideFirebaseAuthRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideFirestoreRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideFoodAnalysisServiceFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideFoodCertificateRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideImageUploadRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideIntoleranceRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvidePreservativeRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideProductRepositoryFactory;
import com.healthyproducts.app.di.RepositoryModule_ProvideSugarRepositoryFactory;
import com.healthyproducts.app.ui.auth.LoginViewModel;
import com.healthyproducts.app.ui.auth.LoginViewModel_HiltModules_KeyModule_ProvideFactory;
import com.healthyproducts.app.ui.auth.RegisterViewModel;
import com.healthyproducts.app.ui.auth.RegisterViewModel_HiltModules_KeyModule_ProvideFactory;
import com.healthyproducts.app.ui.viewmodel.AdditiveViewModel;
import com.healthyproducts.app.ui.viewmodel.AdditiveViewModel_HiltModules_KeyModule_ProvideFactory;
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel;
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel_HiltModules_KeyModule_ProvideFactory;
import com.healthyproducts.app.ui.viewmodel.LanguageViewModel;
import com.healthyproducts.app.ui.viewmodel.LanguageViewModel_HiltModules_KeyModule_ProvideFactory;
import com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel;
import com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel_HiltModules_KeyModule_ProvideFactory;
import com.healthyproducts.app.ui.viewmodel.ScanViewModel;
import com.healthyproducts.app.ui.viewmodel.ScanViewModel_HiltModules_KeyModule_ProvideFactory;
import com.healthyproducts.app.ui.viewmodel.UserViewModel;
import com.healthyproducts.app.ui.viewmodel.UserViewModel_HiltModules_KeyModule_ProvideFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideApplicationFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerHealthyProductsApp_HiltComponents_SingletonC {
  private DaggerHealthyProductsApp_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder appModule(AppModule appModule) {
      Preconditions.checkNotNull(appModule);
      return this;
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder firebaseModule(FirebaseModule firebaseModule) {
      Preconditions.checkNotNull(firebaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder networkModule(NetworkModule networkModule) {
      Preconditions.checkNotNull(networkModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder repositoryModule(RepositoryModule repositoryModule) {
      Preconditions.checkNotNull(repositoryModule);
      return this;
    }

    public HealthyProductsApp_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements HealthyProductsApp_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public HealthyProductsApp_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements HealthyProductsApp_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public HealthyProductsApp_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements HealthyProductsApp_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public HealthyProductsApp_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements HealthyProductsApp_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public HealthyProductsApp_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements HealthyProductsApp_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public HealthyProductsApp_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements HealthyProductsApp_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public HealthyProductsApp_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements HealthyProductsApp_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public HealthyProductsApp_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends HealthyProductsApp_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends HealthyProductsApp_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends HealthyProductsApp_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends HealthyProductsApp_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity arg0) {
      injectMainActivity2(arg0);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return ImmutableSet.<String>of(AdditiveViewModel_HiltModules_KeyModule_ProvideFactory.provide(), FoodAnalysisViewModel_HiltModules_KeyModule_ProvideFactory.provide(), LanguageViewModel_HiltModules_KeyModule_ProvideFactory.provide(), LoginViewModel_HiltModules_KeyModule_ProvideFactory.provide(), RegisterViewModel_HiltModules_KeyModule_ProvideFactory.provide(), ScanHistoryViewModel_HiltModules_KeyModule_ProvideFactory.provide(), ScanViewModel_HiltModules_KeyModule_ProvideFactory.provide(), UserViewModel_HiltModules_KeyModule_ProvideFactory.provide());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectFirebaseAuth(instance, singletonCImpl.provideFirebaseAuthProvider.get());
      return instance;
    }
  }

  private static final class ViewModelCImpl extends HealthyProductsApp_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<AdditiveViewModel> additiveViewModelProvider;

    private Provider<FoodAnalysisViewModel> foodAnalysisViewModelProvider;

    private Provider<LanguageViewModel> languageViewModelProvider;

    private Provider<LoginViewModel> loginViewModelProvider;

    private Provider<RegisterViewModel> registerViewModelProvider;

    private Provider<ScanHistoryViewModel> scanHistoryViewModelProvider;

    private Provider<ScanViewModel> scanViewModelProvider;

    private Provider<UserViewModel> userViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.additiveViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.foodAnalysisViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.languageViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.loginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.registerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.scanHistoryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.scanViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.userViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return ImmutableMap.<String, Provider<ViewModel>>builderWithExpectedSize(8).put("com.healthyproducts.app.ui.viewmodel.AdditiveViewModel", ((Provider) additiveViewModelProvider)).put("com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel", ((Provider) foodAnalysisViewModelProvider)).put("com.healthyproducts.app.ui.viewmodel.LanguageViewModel", ((Provider) languageViewModelProvider)).put("com.healthyproducts.app.ui.auth.LoginViewModel", ((Provider) loginViewModelProvider)).put("com.healthyproducts.app.ui.auth.RegisterViewModel", ((Provider) registerViewModelProvider)).put("com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel", ((Provider) scanHistoryViewModelProvider)).put("com.healthyproducts.app.ui.viewmodel.ScanViewModel", ((Provider) scanViewModelProvider)).put("com.healthyproducts.app.ui.viewmodel.UserViewModel", ((Provider) userViewModelProvider)).build();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.healthyproducts.app.ui.viewmodel.AdditiveViewModel 
          return (T) new AdditiveViewModel(singletonCImpl.provideAdditiveRepositoryProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 1: // com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel 
          return (T) new FoodAnalysisViewModel(singletonCImpl.foodAnalysisRepositoryProvider.get(), singletonCImpl.provideAllergenRepositoryProvider.get(), singletonCImpl.provideFatRepositoryProvider.get(), singletonCImpl.provideSugarRepositoryProvider.get(), singletonCImpl.providePreservativeRepositoryProvider.get(), singletonCImpl.provideFoodCertificateRepositoryProvider.get(), singletonCImpl.userFoodPreferenceRepositoryProvider.get(), singletonCImpl.provideFoodAnalysisServiceProvider.get(), singletonCImpl.provideAiServiceProvider.get(), singletonCImpl.provideFirestoreRepositoryProvider.get());

          case 2: // com.healthyproducts.app.ui.viewmodel.LanguageViewModel 
          return (T) new LanguageViewModel(ApplicationContextModule_ProvideApplicationFactory.provideApplication(singletonCImpl.applicationContextModule), singletonCImpl.provideFirestoreRepositoryProvider.get());

          case 3: // com.healthyproducts.app.ui.auth.LoginViewModel 
          return (T) new LoginViewModel(singletonCImpl.provideFirebaseAuthRepositoryProvider.get());

          case 4: // com.healthyproducts.app.ui.auth.RegisterViewModel 
          return (T) new RegisterViewModel(singletonCImpl.provideFirebaseAuthRepositoryProvider.get());

          case 5: // com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel 
          return (T) new ScanHistoryViewModel(singletonCImpl.scanHistoryRepositoryProvider.get());

          case 6: // com.healthyproducts.app.ui.viewmodel.ScanViewModel 
          return (T) new ScanViewModel(ApplicationContextModule_ProvideApplicationFactory.provideApplication(singletonCImpl.applicationContextModule), singletonCImpl.scanHistoryRepositoryProvider.get(), singletonCImpl.provideFirestoreRepositoryProvider.get(), singletonCImpl.ocrCorrectionRepositoryProvider.get());

          case 7: // com.healthyproducts.app.ui.viewmodel.UserViewModel 
          return (T) new UserViewModel(singletonCImpl.userRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends HealthyProductsApp_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends HealthyProductsApp_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends HealthyProductsApp_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<FirebaseFirestore> provideFirebaseFirestoreProvider;

    private Provider<FirebaseAuth> provideFirebaseAuthProvider;

    private Provider<FirebaseAuthRepository> provideFirebaseAuthRepositoryProvider;

    private Provider<FirestoreRepository> provideFirestoreRepositoryProvider;

    private Provider<ProductRepository> provideProductRepositoryProvider;

    private Provider<FirebaseStorage> provideFirebaseStorageProvider;

    private Provider<Context> provideContextProvider;

    private Provider<ImageUploadRepository> provideImageUploadRepositoryProvider;

    private Provider<AdditiveRepository> provideAdditiveRepositoryProvider;

    private Provider<FoodAnalysisRepository> foodAnalysisRepositoryProvider;

    private Provider<AllergenRepository> provideAllergenRepositoryProvider;

    private Provider<FatRepository> provideFatRepositoryProvider;

    private Provider<SugarRepository> provideSugarRepositoryProvider;

    private Provider<PreservativeRepository> providePreservativeRepositoryProvider;

    private Provider<FoodCertificateRepository> provideFoodCertificateRepositoryProvider;

    private Provider<UserFoodPreferenceRepository> userFoodPreferenceRepositoryProvider;

    private Provider<IntoleranceRepository> provideIntoleranceRepositoryProvider;

    private Provider<FoodAnalysisService> provideFoodAnalysisServiceProvider;

    private Provider<Interceptor> provideAuthInterceptorProvider;

    private Provider<HttpLoggingInterceptor> provideLoggingInterceptorProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<DeepSeekApiService> provideDeepSeekApiServiceProvider;

    private Provider<GeminiApiService> provideGeminiApiServiceProvider;

    private Provider<AiService> provideAiServiceProvider;

    private Provider<ScanHistoryRepository> scanHistoryRepositoryProvider;

    private Provider<OcrCorrectionRepository> ocrCorrectionRepositoryProvider;

    private Provider<UserRepository> userRepositoryProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideFirebaseFirestoreProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseFirestore>(singletonCImpl, 1));
      this.provideFirebaseAuthProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAuth>(singletonCImpl, 3));
      this.provideFirebaseAuthRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAuthRepository>(singletonCImpl, 2));
      this.provideFirestoreRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<FirestoreRepository>(singletonCImpl, 0));
      this.provideProductRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ProductRepository>(singletonCImpl, 4));
      this.provideFirebaseStorageProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseStorage>(singletonCImpl, 6));
      this.provideContextProvider = DoubleCheck.provider(new SwitchingProvider<Context>(singletonCImpl, 7));
      this.provideImageUploadRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ImageUploadRepository>(singletonCImpl, 5));
      this.provideAdditiveRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AdditiveRepository>(singletonCImpl, 8));
      this.foodAnalysisRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<FoodAnalysisRepository>(singletonCImpl, 9));
      this.provideAllergenRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AllergenRepository>(singletonCImpl, 10));
      this.provideFatRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<FatRepository>(singletonCImpl, 11));
      this.provideSugarRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<SugarRepository>(singletonCImpl, 12));
      this.providePreservativeRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<PreservativeRepository>(singletonCImpl, 13));
      this.provideFoodCertificateRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<FoodCertificateRepository>(singletonCImpl, 14));
      this.userFoodPreferenceRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<UserFoodPreferenceRepository>(singletonCImpl, 15));
      this.provideIntoleranceRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<IntoleranceRepository>(singletonCImpl, 17));
      this.provideFoodAnalysisServiceProvider = DoubleCheck.provider(new SwitchingProvider<FoodAnalysisService>(singletonCImpl, 16));
      this.provideAuthInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<Interceptor>(singletonCImpl, 21));
      this.provideLoggingInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<HttpLoggingInterceptor>(singletonCImpl, 22));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 20));
      this.provideDeepSeekApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<DeepSeekApiService>(singletonCImpl, 19));
      this.provideGeminiApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<GeminiApiService>(singletonCImpl, 23));
      this.provideAiServiceProvider = DoubleCheck.provider(new SwitchingProvider<AiService>(singletonCImpl, 18));
      this.scanHistoryRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ScanHistoryRepository>(singletonCImpl, 24));
      this.ocrCorrectionRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<OcrCorrectionRepository>(singletonCImpl, 25));
      this.userRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<UserRepository>(singletonCImpl, 26));
    }

    @Override
    public void injectHealthyProductsApp(HealthyProductsApp healthyProductsApp) {
      injectHealthyProductsApp2(healthyProductsApp);
    }

    @Override
    public ProductRepository productRepository() {
      return provideProductRepositoryProvider.get();
    }

    @Override
    public ImageUploadRepository imageUploadRepository() {
      return provideImageUploadRepositoryProvider.get();
    }

    @Override
    public FirebaseAuth firebaseAuth() {
      return provideFirebaseAuthProvider.get();
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private HealthyProductsApp injectHealthyProductsApp2(HealthyProductsApp instance) {
      HealthyProductsApp_MembersInjector.injectFirestoreRepository(instance, provideFirestoreRepositoryProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.healthyproducts.app.data.repository.FirestoreRepository 
          return (T) RepositoryModule_ProvideFirestoreRepositoryFactory.provideFirestoreRepository(singletonCImpl.provideFirebaseFirestoreProvider.get(), singletonCImpl.provideFirebaseAuthRepositoryProvider.get());

          case 1: // com.google.firebase.firestore.FirebaseFirestore 
          return (T) FirebaseModule_ProvideFirebaseFirestoreFactory.provideFirebaseFirestore();

          case 2: // com.healthyproducts.app.data.repository.FirebaseAuthRepository 
          return (T) RepositoryModule_ProvideFirebaseAuthRepositoryFactory.provideFirebaseAuthRepository(singletonCImpl.provideFirebaseAuthProvider.get());

          case 3: // com.google.firebase.auth.FirebaseAuth 
          return (T) FirebaseModule_ProvideFirebaseAuthFactory.provideFirebaseAuth();

          case 4: // com.healthyproducts.app.data.repository.ProductRepository 
          return (T) RepositoryModule_ProvideProductRepositoryFactory.provideProductRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 5: // com.healthyproducts.app.data.repository.ImageUploadRepository 
          return (T) RepositoryModule_ProvideImageUploadRepositoryFactory.provideImageUploadRepository(singletonCImpl.provideFirebaseStorageProvider.get(), singletonCImpl.provideContextProvider.get());

          case 6: // com.google.firebase.storage.FirebaseStorage 
          return (T) FirebaseModule_ProvideFirebaseStorageFactory.provideFirebaseStorage();

          case 7: // android.content.Context 
          return (T) AppModule_ProvideContextFactory.provideContext(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.healthyproducts.app.data.repository.AdditiveRepository 
          return (T) RepositoryModule_ProvideAdditiveRepositoryFactory.provideAdditiveRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 9: // com.healthyproducts.app.data.repository.FoodAnalysisRepository 
          return (T) new FoodAnalysisRepository();

          case 10: // com.healthyproducts.app.data.repository.AllergenRepository 
          return (T) RepositoryModule_ProvideAllergenRepositoryFactory.provideAllergenRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 11: // com.healthyproducts.app.data.repository.FatRepository 
          return (T) RepositoryModule_ProvideFatRepositoryFactory.provideFatRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 12: // com.healthyproducts.app.data.repository.SugarRepository 
          return (T) RepositoryModule_ProvideSugarRepositoryFactory.provideSugarRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 13: // com.healthyproducts.app.data.repository.PreservativeRepository 
          return (T) RepositoryModule_ProvidePreservativeRepositoryFactory.providePreservativeRepository(singletonCImpl.provideFirebaseFirestoreProvider.get(), singletonCImpl.provideContextProvider.get());

          case 14: // com.healthyproducts.app.data.repository.FoodCertificateRepository 
          return (T) RepositoryModule_ProvideFoodCertificateRepositoryFactory.provideFoodCertificateRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 15: // com.healthyproducts.app.data.repository.UserFoodPreferenceRepository 
          return (T) new UserFoodPreferenceRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 16: // com.healthyproducts.app.data.service.FoodAnalysisService 
          return (T) RepositoryModule_ProvideFoodAnalysisServiceFactory.provideFoodAnalysisService(singletonCImpl.provideAdditiveRepositoryProvider.get(), singletonCImpl.provideAllergenRepositoryProvider.get(), singletonCImpl.provideSugarRepositoryProvider.get(), singletonCImpl.provideFatRepositoryProvider.get(), singletonCImpl.provideIntoleranceRepositoryProvider.get(), singletonCImpl.providePreservativeRepositoryProvider.get(), singletonCImpl.provideFoodCertificateRepositoryProvider.get());

          case 17: // com.healthyproducts.app.data.repository.IntoleranceRepository 
          return (T) RepositoryModule_ProvideIntoleranceRepositoryFactory.provideIntoleranceRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 18: // com.healthyproducts.app.data.api.AiService 
          return (T) NetworkModule_ProvideAiServiceFactory.provideAiService(singletonCImpl.provideDeepSeekApiServiceProvider.get(), singletonCImpl.provideGeminiApiServiceProvider.get());

          case 19: // com.healthyproducts.app.data.api.DeepSeekApiService 
          return (T) NetworkModule_ProvideDeepSeekApiServiceFactory.provideDeepSeekApiService(singletonCImpl.provideOkHttpClientProvider.get());

          case 20: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient(singletonCImpl.provideAuthInterceptorProvider.get(), singletonCImpl.provideLoggingInterceptorProvider.get());

          case 21: // okhttp3.Interceptor 
          return (T) NetworkModule_ProvideAuthInterceptorFactory.provideAuthInterceptor();

          case 22: // okhttp3.logging.HttpLoggingInterceptor 
          return (T) NetworkModule_ProvideLoggingInterceptorFactory.provideLoggingInterceptor();

          case 23: // com.healthyproducts.app.data.api.GeminiApiService 
          return (T) NetworkModule_ProvideGeminiApiServiceFactory.provideGeminiApiService();

          case 24: // com.healthyproducts.app.data.repository.ScanHistoryRepository 
          return (T) new ScanHistoryRepository();

          case 25: // com.healthyproducts.app.data.repository.OcrCorrectionRepository 
          return (T) new OcrCorrectionRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideAiServiceProvider.get(), singletonCImpl.provideFirestoreRepositoryProvider.get());

          case 26: // com.healthyproducts.app.data.repository.UserRepository 
          return (T) new UserRepository(singletonCImpl.provideFirebaseAuthRepositoryProvider.get(), singletonCImpl.provideFirebaseFirestoreProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
