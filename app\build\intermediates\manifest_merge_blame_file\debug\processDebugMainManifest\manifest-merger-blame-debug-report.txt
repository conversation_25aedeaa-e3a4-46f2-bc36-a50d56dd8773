1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.healthyproducts.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Kamera izni -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:5-65
12-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- İnternet izni -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:5-67
15-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:9:22-64
16
17    <!-- Depolama izinleri -->
18    <uses-permission
18-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:12:22-77
20        android:maxSdkVersion="32" />
20-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:5-76
21-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:14:22-73
22
23    <!-- Kamera özelliği gereksinimi -->
24    <uses-feature
24-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:17:5-19:36
25        android:name="android.hardware.camera"
25-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:18:9-47
26        android:required="false" />
26-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:19:9-33
27
28    <queries>
28-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:17:5-19:15
29        <package android:name="com.facebook.katana" />
29-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:9-55
29-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:18:18-52
30    </queries>
31
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
32-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
32-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
33    <uses-permission android:name="android.permission.WAKE_LOCK" />
33-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:5-68
33-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:22-65
34    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
34-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
34-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
35    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
35-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
35-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
36-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
36-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
37    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
37-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
37-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
38    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
38-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
38-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c09478d1204a95b539c6d975ccae0c56\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.healthyproducts.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
46-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:21:5-44:19
47        android:name="com.healthyproducts.app.HealthyProductsApp"
47-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:22:9-43
48        android:allowBackup="true"
48-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:23:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94669d66a91321d208dab1f867a16831\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:24:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:25:9-54
54        android:icon="@mipmap/ic_launcher"
54-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:26:9-43
55        android:label="@string/app_name"
55-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:27:9-41
56        android:roundIcon="@mipmap/ic_launcher_round"
56-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:28:9-54
57        android:supportsRtl="true"
57-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:29:9-35
58        android:testOnly="true"
59        android:theme="@style/Theme.HealthyProducts" >
59-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:30:9-53
60        <activity
60-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:32:9-43:20
61            android:name="com.healthyproducts.app.MainActivity"
61-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:33:13-41
62            android:configChanges="orientation|screenSize"
62-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:38:13-59
63            android:exported="true"
63-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:34:13-36
64            android:label="@string/app_name"
64-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:35:13-45
65            android:screenOrientation="portrait"
65-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:37:13-49
66            android:theme="@style/Theme.HealthyProducts" >
66-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:36:13-57
67            <intent-filter>
67-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:39:13-42:29
68                <action android:name="android.intent.action.MAIN" />
68-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:40:17-69
68-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:40:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:41:17-77
70-->F:\Software Development\augment-projects\Healthy Product\app\src\main\AndroidManifest.xml:41:27-74
71            </intent-filter>
72        </activity>
73
74        <service
74-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
75            android:name="androidx.camera.core.impl.MetadataHolderService"
75-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
76            android:enabled="false"
76-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
77            android:exported="false" >
77-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
78            <meta-data
78-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
79                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
79-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
80                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
80-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e079ec07e58a46ce8b514ec6ede7628f\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
81        </service>
82
83        <activity
83-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:22:9-25:66
84            android:name="com.facebook.FacebookActivity"
84-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:23:13-57
85            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
85-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:24:13-96
86            android:theme="@style/com_facebook_activity_theme" />
86-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:25:13-63
87        <activity android:name="com.facebook.CustomTabMainActivity" />
87-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:9-71
87-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:26:19-68
88        <activity
88-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:27:9-41:20
89            android:name="com.facebook.CustomTabActivity"
89-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:28:13-58
90            android:exported="true" >
90-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:29:13-36
91            <intent-filter>
91-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
92                <action android:name="android.intent.action.VIEW" />
92-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
92-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
93
94                <category android:name="android.intent.category.DEFAULT" />
94-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
94-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
95                <category android:name="android.intent.category.BROWSABLE" />
95-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
95-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
96
97                <data
97-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
98                    android:host="cct.com.healthyproducts.app"
98-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
99                    android:scheme="fbconnect" />
99-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
100            </intent-filter>
101        </activity>
102        <activity
102-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
103            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
103-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
104            android:excludeFromRecents="true"
104-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
105            android:exported="true"
105-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
106            android:launchMode="singleTask"
106-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
107            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
107-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
108            <intent-filter>
108-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
109                <action android:name="android.intent.action.VIEW" />
109-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
109-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
111-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
112                <category android:name="android.intent.category.BROWSABLE" />
112-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
112-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
113
114                <data
114-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
115                    android:host="firebase.auth"
115-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
116                    android:path="/"
117                    android:scheme="genericidp" />
117-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
118            </intent-filter>
119        </activity>
120        <activity
120-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
121            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
121-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
122            android:excludeFromRecents="true"
122-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
123            android:exported="true"
123-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
124            android:launchMode="singleTask"
124-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
125            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
125-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
126            <intent-filter>
126-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
127                <action android:name="android.intent.action.VIEW" />
127-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:17-69
127-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:32:25-66
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:17-76
129-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:34:27-73
130                <category android:name="android.intent.category.BROWSABLE" />
130-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:17-78
130-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:35:27-75
131
132                <data
132-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:37:17-39:50
133                    android:host="firebase.auth"
133-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:38:21-56
134                    android:path="/"
135                    android:scheme="recaptcha" />
135-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b89f0ee9be1e0a15cf2905800b0f25\transformed\facebook-common-16.3.0\AndroidManifest.xml:39:21-47
136            </intent-filter>
137        </activity>
138
139        <service
139-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
140            android:name="com.google.firebase.components.ComponentDiscoveryService"
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
141            android:directBootAware="true"
141-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
142            android:exported="false" >
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
143            <meta-data
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
144                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
144-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa1b418c2ed8789bfe4ebdbbff887cca\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
146            <meta-data
146-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:17:13-19:85
147                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
147-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:18:17-122
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:19:17-82
149            <meta-data
149-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:20:13-22:85
150                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
150-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:21:17-111
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-firestore:24.10.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48e6c3c794416b4304eb6ed48079048\transformed\firebase-firestore-24.10.3\AndroidManifest.xml:22:17-82
152            <meta-data
152-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
153                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
153-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
155            <meta-data
155-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
156                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
156-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454c7ca489c746e07c23d1c00f98619b\transformed\firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
158            <meta-data
158-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
159                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
159-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
161            <meta-data
161-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
162                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
162-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
164            <meta-data
164-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
165                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
165-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ccad733fe7f08f5c28b70908fd6145a\transformed\firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
167            <meta-data
167-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
168                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
168-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
170            <meta-data
170-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
171                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
171-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3648dc43eb94b48734da8e3956743ed4\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
173            <meta-data
173-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
174                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
174-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad0fa371e133020766f91f0262dcf9d0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
176            <meta-data
176-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
177                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
177-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
179        </service>
180
181        <property
181-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
182            android:name="android.adservices.AD_SERVICES_CONFIG"
182-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
183            android:resource="@xml/ga_ad_services_config" />
183-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fd67470faa2ecb9a156ee74901bb348\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
184
185        <provider
185-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
186            android:name="com.google.firebase.provider.FirebaseInitProvider"
186-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
187            android:authorities="com.healthyproducts.app.firebaseinitprovider"
187-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
188            android:directBootAware="true"
188-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
189            android:exported="false"
189-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
190            android:initOrder="100" />
190-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf560896ee247489e7db286a85473a29\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
191
192        <activity
192-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
193            android:name="androidx.compose.ui.tooling.PreviewActivity"
193-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
194            android:exported="true" />
194-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f86f51a7543cf9633768c73945f46e1\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
195        <activity
195-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
196            android:name="androidx.activity.ComponentActivity"
196-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
197            android:exported="true" />
197-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3dd9615db85ed2c7db76707ce82907\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
198        <activity
198-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
199            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
199-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
200            android:excludeFromRecents="true"
200-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
201            android:exported="false"
201-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
202            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
202-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
203        <!--
204            Service handling Google Sign-In user revocation. For apps that do not integrate with
205            Google Sign-In, this service will never be started.
206        -->
207        <service
207-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
208            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
208-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
209            android:exported="true"
209-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
210            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
210-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
211            android:visibleToInstantApps="true" />
211-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f6c93d77bcd620b3c11da61466a65c3\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
212        <service
212-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
213            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
213-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
214            android:directBootAware="true"
214-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
215            android:exported="false" >
215-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
216            <meta-data
216-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
217                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
217-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0debc741bac288917687b36e7ad6a55f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
219            <meta-data
219-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
220                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
220-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2074efd148e5eecdb89b2bf94a534f5\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
222            <meta-data
222-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
223                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
223-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2a1339d8925ea4b449fd54378339293\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
225            <meta-data
225-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
226                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
226-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
228        </service>
229
230        <provider
230-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
231            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
231-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
232            android:authorities="com.healthyproducts.app.mlkitinitprovider"
232-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
233            android:exported="false"
233-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
234            android:initOrder="99" />
234-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7409a3cfbad4b922e91cb5d5ed3f85f\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
235
236        <activity
236-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
237            android:name="com.google.android.gms.common.api.GoogleApiActivity"
237-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
238            android:exported="false"
238-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
239            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
239-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea199b5bc61986cf0429ca4ca01f9df\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
240
241        <receiver
241-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
242            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
242-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
243            android:enabled="true"
243-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
244            android:exported="false" >
244-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
245        </receiver>
246
247        <service
247-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
248            android:name="com.google.android.gms.measurement.AppMeasurementService"
248-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
249            android:enabled="true"
249-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
250            android:exported="false" />
250-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
251        <service
251-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
252            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
252-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
253            android:enabled="true"
253-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
254            android:exported="false"
254-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
255            android:permission="android.permission.BIND_JOB_SERVICE" />
255-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f034022ef932fa896990baf0d4051310\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
256
257        <meta-data
257-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
258            android:name="com.google.android.gms.version"
258-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
259            android:value="@integer/google_play_services_version" />
259-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21ddb6e5847bd1a05683bb0c448269e9\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
260        <!--
261         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
262         with the application context. This config is merged in with the host app's manifest,
263         but there can only be one provider with the same authority activated at any given
264         point; so if the end user has two or more different apps that use Facebook SDK, only the
265         first one will be able to use the provider. To work around this problem, we use the
266         following placeholder in the authority to identify each host application as if it was
267         a completely different provider.
268        -->
269        <provider
269-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:29:9-32:40
270            android:name="com.facebook.internal.FacebookInitProvider"
270-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:30:13-70
271            android:authorities="com.healthyproducts.app.FacebookInitProvider"
271-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:31:13-72
272            android:exported="false" />
272-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:32:13-37
273
274        <receiver
274-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:34:9-40:20
275            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
275-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:35:13-86
276            android:exported="false" >
276-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:36:13-37
277            <intent-filter>
277-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:37:13-39:29
278                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
278-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:17-95
278-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:38:25-92
279            </intent-filter>
280        </receiver>
281        <receiver
281-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:41:9-47:20
282            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
282-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:42:13-118
283            android:exported="false" >
283-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:43:13-37
284            <intent-filter>
284-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:44:13-46:29
285                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
285-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:17-103
285-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63f2763bb65d2983548f8f570f6430e\transformed\facebook-core-16.3.0\AndroidManifest.xml:45:25-100
286            </intent-filter>
287        </receiver>
288
289        <provider
289-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
290            android:name="androidx.startup.InitializationProvider"
290-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
291            android:authorities="com.healthyproducts.app.androidx-startup"
291-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
292            android:exported="false" >
292-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
293            <meta-data
293-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
294                android:name="androidx.emoji2.text.EmojiCompatInitializer"
294-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
295                android:value="androidx.startup" />
295-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd0c694edcb64a0889ba2aac8961be66\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
296            <meta-data
296-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
297                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
297-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
298                android:value="androidx.startup" />
298-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdafa8fff50e547b79bcbe352c03c200\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
299            <meta-data
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
300                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
301                android:value="androidx.startup" />
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
302        </provider>
303
304        <uses-library
304-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
305            android:name="android.ext.adservices"
305-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
306            android:required="false" />
306-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af30761024d0ebe07e6a77965d13ff11\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
307
308        <receiver
308-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
309            android:name="androidx.profileinstaller.ProfileInstallReceiver"
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
310            android:directBootAware="false"
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
311            android:enabled="true"
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
312            android:exported="true"
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
313            android:permission="android.permission.DUMP" >
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
314            <intent-filter>
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
315                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
316            </intent-filter>
317            <intent-filter>
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
318                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
319            </intent-filter>
320            <intent-filter>
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
321                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
322            </intent-filter>
323            <intent-filter>
323-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
324                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\615a5209d542a9a19ed4228fec2c68d6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
325            </intent-filter>
326        </receiver>
327
328        <service
328-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
329            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
329-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
330            android:exported="false" >
330-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
331            <meta-data
331-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
332                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
332-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
333                android:value="cct" />
333-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5375281050c220e274ac1b5f7a9ef4\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
334        </service>
335        <service
335-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
336            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
336-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
337            android:exported="false"
337-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
338            android:permission="android.permission.BIND_JOB_SERVICE" >
338-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
339        </service>
340
341        <receiver
341-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
342            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
342-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
343            android:exported="false" />
343-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd1b4731b6c9d62c02fdc755f918e8a\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
344    </application>
345
346</manifest>
