{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values/values.xml", "map": [{"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "67,126,246,230,234,231,235,233,232,45,131,128,130,125,132,135,134,129,137,136,133,124,117,65,66,160,156,118,114,113,104,112,211,1,224,102,253,252,20,108,22,94,208,163,40,121,187,188,189,186,180,84,103,216,81,110,106,243,236,59,147,151,146,150,95,165,170,96,127,28,79,209,99,161,157,6,17,221,220,159,191,190,31,215,164,184,183,185,23,41,43,182,116,168,4,194,226,199,195,197,198,196,200,212,39,30,35,111,29,27,173,58,143,140,142,141,98,76,238,85,78,214,14,82,109,242,217,16,179,178,26,21,101,166,80,213,240,239,205,204,203,120,68,249,38,254,248,255,251,250,7,257,105,11,77,86,171,46,210,100,225,169,93,237,247,5,12,83,13,25,24,97,227,107,8,73,71,72,60,63,62,64,61,34,33,32,49,50,54,176,177,53,175,51,55,52,174,162,158,119,256,44,206,69,207,153,181,152,148,149,167,241,115,42,172,70,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3370,6275,12739,11775,12039,11843,12113,11963,11901,2267,6558,6385,6497,6215,6613,6804,6739,6441,6938,6869,6676,6167,5855,3261,3307,8047,7855,5911,5676,5613,4990,5538,10832,16,11509,4912,13177,13092,777,5206,873,4587,10670,8211,1963,6081,9572,9646,9718,9512,9153,4170,4950,11104,4016,5417,5084,12657,12173,2859,7332,7644,7267,7569,4629,8339,8581,4671,6329,1310,3930,10720,4798,8107,7903,178,695,11336,11273,7983,9856,9792,1552,11044,8277,9398,9335,9456,955,2017,2125,9279,5791,8473,102,9941,11619,10257,10003,10111,10181,10055,10325,10882,1911,1480,1740,5479,1407,1236,8773,2813,7179,7010,7116,7063,4751,3802,12297,4226,3888,10996,512,4078,5347,12593,11164,638,9097,9057,1147,825,4878,8391,3970,10948,12452,12375,10528,10460,10404,6017,3410,12899,1851,13245,12845,13313,13024,12955,226,13454,5034,342,3842,4290,8643,2329,10788,4838,11559,8531,4549,12235,12791,140,396,4116,450,1066,1017,4709,11671,5144,270,3718,3574,3650,2907,3113,3043,3185,2975,1695,1648,1603,2431,2478,2684,8951,9000,2629,8886,2525,2735,2582,8829,8157,7941,5967,13402,2193,10582,3468,10630,7772,9219,7711,7449,7508,8433,12523,5735,2071,8709,3530,566", "endColumns": "39,53,51,67,73,57,59,75,61,61,54,55,60,59,62,64,64,55,48,68,62,47,55,45,62,59,47,55,58,62,43,74,49,53,49,37,67,84,47,140,81,41,49,65,53,61,73,71,73,59,65,55,39,59,61,61,59,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,63,57,37,61,51,67,51,69,75,55,50,65,51,71,81,58,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,69,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,63,57,55,59,67,53,88,67,68,43,67,49,53,45,69,65,71,43,39,59,49,37,61,53,37,53,53,61,80,48,41,67,61,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,49,51,73,47,61,39,54,59,60,58,60,39,69,55,53,63,43,71", "endOffsets": "3405,6324,12786,11838,12108,11896,12168,12034,11958,2324,6608,6436,6553,6270,6671,6864,6799,6492,6982,6933,6734,6210,5906,3302,3365,8102,7898,5962,5730,5671,5029,5608,10877,65,11554,4945,13240,13172,820,5342,950,4624,10715,8272,2012,6138,9641,9713,9787,9567,9214,4221,4985,11159,4073,5474,5139,12704,12230,2902,7444,7706,7327,7639,4666,8386,8638,4704,6380,1402,3965,10783,4833,8152,7936,221,746,11472,11331,8042,9915,9851,1598,11099,8334,9451,9393,9507,1012,2066,2188,9330,5850,8526,135,9998,11666,10320,10050,10176,10252,10106,10371,10943,1958,1547,1817,5533,1475,1305,8824,2854,7236,7058,7174,7111,4793,3837,12370,4285,3925,11039,561,4111,5412,12652,11233,690,9148,9092,1231,868,4907,8428,4011,10991,12518,12447,10577,10523,10455,6076,3463,12950,1906,13308,12894,13397,13087,13019,265,13517,5079,391,3883,4355,8704,2396,10827,4873,11614,8576,4582,12292,12840,173,445,4165,507,1142,1061,4746,11734,5201,311,3775,3645,3713,2970,3180,3108,3256,3038,1735,1690,1643,2473,2520,2730,8995,9052,2679,8946,2577,2785,2624,8881,8206,7978,6012,13449,2262,10625,3525,10665,7822,9274,7767,7503,7564,8468,12588,5786,2120,8768,3569,633"}, "to": {"startLines": "386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,419,420,421,422,423,424,425,433,434,435,436,437,438,439,440,441,442,443,444,445,446,488,491,492,494,495,496,497,498,499,500,501,505,506,507,509,510,511,512,513,514,518,519,520,521,522,523,524,525,526,527,528,529,531,532,533,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,634,635,636,637,639,640,641,642,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,693,694,695,696,697,698,699,700,701,702,703,704,705,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,727,730,733,734,735,736,737,738,739,740,741,742,743,744,745", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24876,24916,24970,25022,25090,25164,25222,25282,25358,25420,25482,25537,25593,25654,25714,25777,25842,25907,25963,26012,26081,26144,26192,26248,26294,26357,26417,26465,26521,26580,26643,26687,26845,26895,26949,26999,27037,27105,27190,27749,27890,27972,28014,28064,28130,28184,28246,28320,28392,28466,28526,28592,28648,33291,33492,33554,33684,33744,33796,33858,33906,34023,34090,34155,34541,34583,34635,34755,34793,34849,34946,34986,35054,35309,35359,35397,35445,35501,35642,35705,35769,35833,35897,35948,36008,36152,36210,36273,36775,36837,36891,36959,37015,37079,37137,37175,37237,37289,37357,37409,37479,37555,37611,37774,37840,37892,37964,38046,38105,38178,38252,38308,38354,38416,38469,38532,38585,38632,38672,38750,38814,43297,43345,43399,43437,43569,43633,43707,43764,43874,43914,44003,44051,44085,44127,44173,44221,44292,44369,44423,44491,44547,44611,44669,44725,44785,44853,44907,44996,45064,45133,45363,45431,45481,45535,45581,45651,45717,45789,45833,45873,45933,45983,46021,46083,46137,46175,46229,46283,46345,46426,46475,46517,46684,46746,46792,46854,46930,46998,47066,47138,47208,47284,47352,47397,47444,47688,47735,47782,47833,47882,47939,47994,48059,48116,48171,48218,48275,48329,48371,48421,48473,48594,48768,48941,48981,49036,49096,49157,49216,49277,49317,49387,49443,49497,49561,49605", "endColumns": "39,53,51,67,73,57,59,75,61,61,54,55,60,59,62,64,64,55,48,68,62,47,55,45,62,59,47,55,58,62,43,74,49,53,49,37,67,84,47,140,81,41,49,65,53,61,73,71,73,59,65,55,39,59,61,61,59,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,63,57,37,61,51,67,51,69,75,55,50,65,51,71,81,58,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,69,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,63,57,55,59,67,53,88,67,68,43,67,49,53,45,69,65,71,43,39,59,49,37,61,53,37,53,53,61,80,48,41,67,61,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,49,51,73,47,61,39,54,59,60,58,60,39,69,55,53,63,43,71", "endOffsets": "24911,24965,25017,25085,25159,25217,25277,25353,25415,25477,25532,25588,25649,25709,25772,25837,25902,25958,26007,26076,26139,26187,26243,26289,26352,26412,26460,26516,26575,26638,26682,26757,26890,26944,26994,27032,27100,27185,27233,27885,27967,28009,28059,28125,28179,28241,28315,28387,28461,28521,28587,28643,28683,33346,33549,33611,33739,33791,33853,33901,34018,34085,34150,34225,34578,34630,34692,34788,34844,34941,34981,35049,35089,35354,35392,35440,35496,35637,35700,35764,35828,35892,35943,36003,36065,36205,36268,36324,36832,36886,36954,37010,37074,37132,37170,37232,37284,37352,37404,37474,37550,37606,37657,37835,37887,37959,38041,38100,38173,38247,38303,38349,38411,38464,38527,38580,38627,38667,38745,38809,38851,43340,43394,43432,43502,43628,43702,43759,43815,43909,43998,44046,44080,44122,44168,44216,44287,44364,44418,44486,44542,44606,44664,44720,44780,44848,44902,44991,45059,45128,45172,45426,45476,45530,45576,45646,45712,45784,45828,45868,45928,45978,46016,46078,46132,46170,46224,46278,46340,46421,46470,46512,46580,46741,46787,46849,46925,46993,47061,47133,47203,47279,47347,47392,47439,47484,47730,47777,47828,47877,47934,47989,48054,48111,48166,48213,48270,48324,48366,48416,48468,48542,48637,48825,48976,49031,49091,49152,49211,49272,49312,49382,49438,49492,49556,49600,49672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\981fd9293439b18a98e73358fb60a309\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2305,3105,3111", "startColumns": "4,4,4,4", "startOffsets": "202,154687,182959,183170", "endLines": "3,2307,3110,3194", "endColumns": "60,12,24,24", "endOffsets": "258,154827,183165,187681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,426,427,428,429,430,431,432,709,2143,2144,2149,2152,2157,2303,2304,3005,3067,3213,3246,3276,3309", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "564,1590,1662,3001,3066,6278,6347,15105,15175,15243,15315,15385,15446,15520,16377,16438,16499,16561,16625,16687,16748,16816,16916,16976,17042,17115,17184,17241,17293,18020,18092,18168,18233,18292,18351,18411,18471,18531,18591,18651,18711,18771,18831,18891,18951,19010,19070,19130,19190,19250,19310,19370,19430,19490,19550,19610,19669,19729,19789,19848,19907,19966,20025,20084,20902,20937,21341,21396,21459,21514,21572,21630,21691,21754,21811,21862,21912,21973,22030,22096,22130,22165,22940,27238,27305,27377,27446,27515,27589,27661,47617,142021,142138,142405,142698,142965,154548,154620,178804,181301,188258,189989,190989,191671", "endLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,426,427,428,429,430,431,432,709,2143,2147,2149,2155,2157,2303,2304,3010,3076,3245,3266,3308,3314", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "619,1657,1745,3061,3127,6342,6405,15170,15238,15310,15380,15441,15515,15588,16433,16494,16556,16620,16682,16743,16811,16911,16971,17037,17110,17179,17236,17288,17350,18087,18163,18228,18287,18346,18406,18466,18526,18586,18646,18706,18766,18826,18886,18946,19005,19065,19125,19185,19245,19305,19365,19425,19485,19545,19605,19664,19724,19784,19843,19902,19961,20020,20079,20138,20932,20967,21391,21454,21509,21567,21625,21686,21749,21806,21857,21907,21968,22025,22091,22125,22160,22195,23005,27300,27372,27441,27510,27584,27656,27744,47683,142133,142334,142510,142894,143089,154615,154682,179002,181597,189984,190665,191666,191833"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "36,108,109,110,121,122,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "2069,7087,7134,7181,7925,7970,8136", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2106,7129,7176,7223,7965,8010,8173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ccfdf8fe6da9c4c34a4246aecbf96939\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "322,2457,3557,3560", "startColumns": "4,4,4,4", "startOffsets": "20972,160894,199892,200007", "endLines": "322,2463,3559,3562", "endColumns": "52,24,24,24", "endOffsets": "21020,161193,200002,200117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "310,312,313,316,318,351,447,448,502,503,508,553,554,638,643,667,668,692,706,707,708,726,728,729,1849,1865,1868", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20348,20472,20530,20708,20793,22503,28688,28753,34230,34296,34697,37662,37714,43507,43820,45259,45309,46638,47489,47535,47577,48547,48642,48678,119849,120829,120940", "endLines": "310,312,313,316,318,351,447,448,502,503,508,553,554,638,643,667,668,692,706,707,708,726,728,729,1851,1867,1871", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "20417,20525,20580,20754,20843,22551,28748,28802,34291,34392,34750,37709,37769,43564,43869,45304,45358,46679,47530,47572,47612,48589,48673,48763,119956,120935,121130"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "92", "endOffsets": "144"}, "to": {"startLines": "2195", "startColumns": "4", "startOffsets": "145797", "endColumns": "91", "endOffsets": "145884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c36e1f051d57c59ccd6930c68000c21a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "346", "startColumns": "4", "startOffsets": "22232", "endColumns": "42", "endOffsets": "22270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25ad31b9d5ee235be384e44c485c1169\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2474,2490,2496,3654,3670", "startColumns": "4,4,4,4,4", "startOffsets": "161715,162140,162318,202854,203265", "endLines": "2489,2495,2505,3669,3673", "endColumns": "24,24,24,24,24", "endOffsets": "162135,162313,162597,203260,203387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\167b5a10bb8d261a1d1c21747e97c5f2\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "99,259,260,261,262,2148,2150,2151,2156,2158", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6410,17355,17408,17461,17514,142339,142515,142637,142899,143094", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "6494,17403,17456,17509,17562,142400,142632,142693,142960,143156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\14d03a95cc07898beae69a352dd60254\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "349", "startColumns": "4", "startOffsets": "22389", "endColumns": "49", "endOffsets": "22434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,691,746,747,748,749,750,751,759,760,764,768,779,784,790,797,801,805,810,814,818,822,826,830,834,840,844,850,854,860,864,869,873,876,880,886,890,896,900,906,909,913,917,921,925,929,930,931,932,935,938,941,944,948,949,950,951,952,955,957,959,961,966,967,971,977,981,982,984,996,997,1001,1007,1011,1012,1013,1017,1044,1048,1049,1053,1081,1253,1279,1450,1476,1507,1515,1521,1537,1559,1564,1569,1579,1588,1597,1601,1608,1627,1634,1635,1644,1647,1650,1654,1658,1662,1665,1666,1671,1676,1686,1691,1698,1704,1705,1708,1712,1717,1719,1721,1724,1727,1729,1733,1736,1743,1746,1749,1753,1755,1759,1761,1763,1765,1769,1777,1785,1797,1803,1812,1815,1826,1829,1830,1835,1836,1872,1941,2011,2012,2022,2031,2032,2034,2038,2041,2044,2047,2050,2053,2056,2059,2063,2066,2069,2072,2076,2079,2083,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2109,2111,2112,2113,2114,2115,2116,2117,2118,2120,2121,2123,2124,2126,2128,2129,2131,2132,2133,2134,2135,2136,2138,2139,2140,2141,2142,2159,2161,2163,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2179,2180,2181,2182,2183,2184,2185,2187,2191,2196,2197,2198,2199,2200,2201,2205,2206,2207,2208,2210,2212,2214,2216,2218,2219,2220,2221,2223,2225,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2241,2242,2243,2244,2246,2248,2249,2251,2252,2254,2256,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2271,2272,2273,2274,2276,2277,2278,2279,2280,2282,2284,2286,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2353,2428,2431,2434,2437,2451,2464,2506,2509,2538,2565,2574,2638,3001,3039,3077,3195,3315,3339,3345,3364,3385,3509,3568,3574,3582,3588,3642,3674,3740,3760,3815,3827,3853", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "263,318,515,931,972,1027,1089,1153,1223,1284,1359,1435,1512,1750,1835,1917,1993,2111,2188,2266,2372,2478,2557,2886,2943,4925,4999,5074,5139,5205,5265,5326,5398,5471,5538,5606,5665,5724,5783,5842,5901,5955,6009,6062,6116,6170,6224,6499,6573,6652,6725,6799,6870,6942,7014,7228,7285,7343,7416,7490,7564,7639,7711,7784,7854,8015,8075,8178,8247,8316,8386,8460,8536,8600,8677,8753,8830,8895,8964,9041,9116,9185,9253,9330,9396,9457,9554,9619,9688,9787,9858,9917,9975,10032,10091,10155,10226,10298,10370,10442,10514,10581,10649,10717,10776,10839,10903,10993,11084,11144,11210,11277,11343,11413,11477,11530,11597,11658,11725,11838,11896,11959,12024,12089,12164,12237,12309,12353,12400,12446,12495,12556,12617,12678,12740,12804,12868,12932,12997,13060,13120,13181,13247,13306,13366,13428,13499,13559,15593,15679,15766,15856,15943,16031,16113,16196,16286,17567,17619,17677,17722,17788,17852,17909,17966,20143,20200,20248,20297,20759,21092,21139,21295,22200,22556,22620,22682,22742,23010,23084,23154,23232,23286,23356,23441,23489,23535,23596,23659,23725,23789,23860,23923,23988,24052,24113,24174,24226,24299,24373,24442,24517,24591,24665,24806,46585,49677,49755,49845,49933,50029,50119,50701,50790,51037,51318,51984,52269,52662,53139,53361,53583,53859,54086,54316,54546,54776,55006,55233,55652,55878,56303,56533,56961,57180,57463,57671,57802,58029,58455,58680,59107,59328,59753,59873,60149,60450,60774,61065,61379,61516,61647,61752,61994,62161,62365,62573,62844,62956,63068,63173,63290,63504,63650,63790,63876,64224,64312,64558,64976,65225,65307,65405,66062,66162,66414,66838,67093,67187,67276,67513,69537,69779,69881,70134,72290,82971,84487,95182,96710,98467,99093,99513,100774,102039,102295,102531,103078,103572,104177,104375,104955,106323,106698,106816,107354,107511,107707,107980,108236,108406,108547,108611,108976,109343,110019,110283,110621,110974,111068,111254,111560,111822,111947,112074,112313,112524,112643,112836,113013,113468,113649,113771,114030,114143,114330,114432,114539,114668,114943,115451,115947,116824,117118,117688,117837,118569,118741,118825,119161,119253,121135,126366,131737,131799,132377,132961,133052,133165,133394,133554,133706,133877,134043,134212,134379,134542,134785,134955,135128,135299,135573,135772,135977,136307,136391,136487,136583,136681,136781,136883,136985,137087,137189,137291,137391,137487,137599,137728,137851,137982,138113,138211,138325,138419,138559,138693,138789,138901,139001,139117,139213,139325,139425,139565,139701,139865,139995,140153,140303,140444,140588,140723,140835,140985,141113,141241,141377,141509,141639,141769,141881,143161,143307,143451,143589,143655,143745,143821,143925,144015,144117,144225,144333,144433,144513,144605,144703,144813,144865,144943,145049,145141,145245,145355,145477,145640,145889,145969,146069,146159,146269,146359,146600,146694,146800,146892,146992,147104,147218,147334,147450,147544,147658,147770,147872,147992,148114,148196,148300,148420,148546,148644,148738,148826,148938,149054,149176,149288,149463,149579,149665,149757,149869,149993,150060,150186,150254,150382,150526,150654,150723,150818,150933,151046,151145,151254,151365,151476,151577,151682,151782,151912,152003,152126,152220,152332,152418,152522,152618,152706,152824,152928,153032,153158,153246,153354,153454,153544,153654,153738,153840,153924,153978,154042,154148,154234,154344,154428,157371,159987,160105,160220,160300,160661,161198,162602,162680,164024,165385,165773,168616,178669,179931,181602,187686,191838,192589,192851,193366,193745,198023,200304,200533,200827,201042,202542,203392,206418,207162,209293,209633,210944", "endLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,691,746,747,748,749,750,758,759,763,767,771,783,789,796,800,804,809,813,817,821,825,829,833,839,843,849,853,859,863,868,872,875,879,885,889,895,899,905,908,912,916,920,924,928,929,930,931,934,937,940,943,947,948,949,950,951,954,956,958,960,965,966,970,976,980,981,983,995,996,1000,1006,1010,1011,1012,1016,1043,1047,1048,1052,1080,1252,1278,1449,1475,1506,1514,1520,1536,1558,1563,1568,1578,1587,1596,1600,1607,1626,1633,1634,1643,1646,1649,1653,1657,1661,1664,1665,1670,1675,1685,1690,1697,1703,1704,1707,1711,1716,1718,1720,1723,1726,1728,1732,1735,1742,1745,1748,1752,1754,1758,1760,1762,1764,1768,1776,1784,1796,1802,1811,1814,1825,1828,1829,1834,1835,1840,1940,2010,2011,2021,2030,2031,2033,2037,2040,2043,2046,2049,2052,2055,2058,2062,2065,2068,2071,2075,2078,2082,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2108,2110,2111,2112,2113,2114,2115,2116,2117,2119,2120,2122,2123,2125,2127,2128,2130,2131,2132,2133,2134,2135,2137,2138,2139,2140,2141,2142,2160,2162,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2178,2179,2180,2181,2182,2183,2184,2186,2190,2194,2196,2197,2198,2199,2200,2204,2205,2206,2207,2209,2211,2213,2215,2217,2218,2219,2220,2222,2224,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2240,2241,2242,2243,2245,2247,2248,2250,2251,2253,2255,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2270,2271,2272,2273,2275,2276,2277,2278,2279,2281,2283,2285,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2427,2430,2433,2436,2450,2456,2473,2508,2537,2564,2573,2637,3000,3004,3066,3104,3212,3338,3344,3350,3384,3508,3528,3573,3577,3587,3622,3653,3739,3759,3814,3826,3852,3859", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "313,358,559,967,1022,1084,1148,1218,1279,1354,1430,1507,1585,1830,1912,1988,2064,2183,2261,2367,2473,2552,2632,2938,2996,4994,5069,5134,5200,5260,5321,5393,5466,5533,5601,5660,5719,5778,5837,5896,5950,6004,6057,6111,6165,6219,6273,6568,6647,6720,6794,6865,6937,7009,7082,7280,7338,7411,7485,7559,7634,7706,7779,7849,7920,8070,8131,8242,8311,8381,8455,8531,8595,8672,8748,8825,8890,8959,9036,9111,9180,9248,9325,9391,9452,9549,9614,9683,9782,9853,9912,9970,10027,10086,10150,10221,10293,10365,10437,10509,10576,10644,10712,10771,10834,10898,10988,11079,11139,11205,11272,11338,11408,11472,11525,11592,11653,11720,11833,11891,11954,12019,12084,12159,12232,12304,12348,12395,12441,12490,12551,12612,12673,12735,12799,12863,12927,12992,13055,13115,13176,13242,13301,13361,13423,13494,13554,13622,15674,15761,15851,15938,16026,16108,16191,16281,16372,17614,17672,17717,17783,17847,17904,17961,18015,20195,20243,20292,20343,20788,21134,21183,21336,22227,22615,22677,22737,22794,23079,23149,23227,23281,23351,23436,23484,23530,23591,23654,23720,23784,23855,23918,23983,24047,24108,24169,24221,24294,24368,24437,24512,24586,24660,24801,24871,46633,49750,49840,49928,50024,50114,50696,50785,51032,51313,51565,52264,52657,53134,53356,53578,53854,54081,54311,54541,54771,55001,55228,55647,55873,56298,56528,56956,57175,57458,57666,57797,58024,58450,58675,59102,59323,59748,59868,60144,60445,60769,61060,61374,61511,61642,61747,61989,62156,62360,62568,62839,62951,63063,63168,63285,63499,63645,63785,63871,64219,64307,64553,64971,65220,65302,65400,66057,66157,66409,66833,67088,67182,67271,67508,69532,69774,69876,70129,72285,82966,84482,95177,96705,98462,99088,99508,100769,102034,102290,102526,103073,103567,104172,104370,104950,106318,106693,106811,107349,107506,107702,107975,108231,108401,108542,108606,108971,109338,110014,110278,110616,110969,111063,111249,111555,111817,111942,112069,112308,112519,112638,112831,113008,113463,113644,113766,114025,114138,114325,114427,114534,114663,114938,115446,115942,116819,117113,117683,117832,118564,118736,118820,119156,119248,119526,126361,131732,131794,132372,132956,133047,133160,133389,133549,133701,133872,134038,134207,134374,134537,134780,134950,135123,135294,135568,135767,135972,136302,136386,136482,136578,136676,136776,136878,136980,137082,137184,137286,137386,137482,137594,137723,137846,137977,138108,138206,138320,138414,138554,138688,138784,138896,138996,139112,139208,139320,139420,139560,139696,139860,139990,140148,140298,140439,140583,140718,140830,140980,141108,141236,141372,141504,141634,141764,141876,142016,143302,143446,143584,143650,143740,143816,143920,144010,144112,144220,144328,144428,144508,144600,144698,144808,144860,144938,145044,145136,145240,145350,145472,145635,145792,145964,146064,146154,146264,146354,146595,146689,146795,146887,146987,147099,147213,147329,147445,147539,147653,147765,147867,147987,148109,148191,148295,148415,148541,148639,148733,148821,148933,149049,149171,149283,149458,149574,149660,149752,149864,149988,150055,150181,150249,150377,150521,150649,150718,150813,150928,151041,151140,151249,151360,151471,151572,151677,151777,151907,151998,152121,152215,152327,152413,152517,152613,152701,152819,152923,153027,153153,153241,153349,153449,153539,153649,153733,153835,153919,153973,154037,154143,154229,154339,154423,154543,159982,160100,160215,160295,160656,160889,161710,162675,164019,165380,165768,168611,178664,178799,181296,182954,188253,192584,192846,193046,193740,198018,198624,200528,200679,201037,202120,202849,206413,207157,209288,209628,210939,211142"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,727", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "194,276,380,489,609,722,804"}, "to": {"startLines": "504,530,534,535,536,537,666", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "34397,36070,36329,36433,36542,36662,45177", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "34536,36147,36428,36537,36657,36770,45254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\02b89f0ee9be1e0a15cf2905800b0f25\\transformed\\facebook-common-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,49,54,60,71,82,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,202,290,370,449,513,590,665,732,814,895,968,1023,1092,1171,1235,1309,1382,1455,1527,1600,1673,1738,1807,4164,4458,4808,5429,6060,6239", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,53,59,70,81,84,112", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "197,285,365,444,508,585,660,727,809,890,963,1018,1087,1166,1230,1304,1377,1450,1522,1595,1668,1733,1802,1867,4453,4803,5424,6055,6234,7468"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2308,2313,2319,2330,2341,3860", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3381,3433,3521,3601,3680,3744,3821,3896,3963,4045,4126,4199,13940,14009,14088,14222,14296,14369,14442,14514,14587,14660,14725,14794,154832,155126,155476,156097,156728,211147", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2312,2318,2329,2340,2343,3887", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "3428,3516,3596,3675,3739,3816,3891,3958,4040,4121,4194,4249,14004,14083,14147,14291,14364,14437,14509,14582,14655,14720,14789,14854,155121,155471,156092,156723,156902,212376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1bf99d2dddf6b3f731ce95e33fee49d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "319,323", "startColumns": "4,4", "startOffsets": "20848,21025", "endColumns": "53,66", "endOffsets": "20897,21087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\856e9279244c37324ec12dc8b216b897\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "326,347", "startColumns": "4,4", "startOffsets": "21188,22275", "endColumns": "41,59", "endOffsets": "21225,22330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "43,44,45,46,207,208,493,515,516,517", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2637,2695,2761,2824,13627,13698,33616,35094,35161,35240", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2690,2756,2819,2881,13693,13765,33679,35156,35235,35304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b616a44a83a35cc1ff94b844e1245adb\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "20422", "endColumns": "49", "endOffsets": "20467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,38,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,175,257,340,421,558,673,791,886,951,1019,1077,1149,1221,1318,1409,1483,1557,1670,1771,1834,2054,2219,2296,2378,2503,2589,2713,2799,3177,3880", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,37,50,59", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "170,252,335,416,553,668,786,881,946,1014,1072,1144,1216,1313,1404,1478,1552,1665,1766,1829,2049,2214,2291,2373,2498,2584,2708,2794,3172,3875,4296"}, "to": {"startLines": "215,225,226,227,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,2344,2345,3888,3901", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14152,14859,14941,15024,28807,28944,29059,29177,29272,29337,29405,29463,29535,29607,29704,29795,29869,29943,30056,30157,30220,30440,30605,30682,30764,30889,30975,156907,156993,212381,213084", "endLines": "215,225,226,227,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,2344,2352,3900,3909", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "14217,14936,15019,15100,28939,29054,29172,29267,29332,29400,29458,29530,29602,29699,29790,29864,29938,30051,30152,30215,30435,30600,30677,30759,30884,30970,31094,156988,157366,213079,213500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e520bbb7f153fcbb4e273d864eff355d\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "22335", "endColumns": "53", "endOffsets": "22384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21c308ad6a5cb3ffbae3557cea5538ff\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "6,12,3578", "startColumns": "4,4,4", "startOffsets": "363,624,200684", "endLines": "9,19,3581", "endColumns": "11,11,24", "endOffsets": "510,926,200822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "731,732", "startColumns": "4,4", "startOffsets": "48830,48886", "endColumns": "55,54", "endOffsets": "48881,48936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b341f2bf9e26a9b9b6d9ac1cb5423684\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3529,3542,3548,3554,3563", "startColumns": "4,4,4,4,4", "startOffsets": "198629,199268,199512,199759,200122", "endLines": "3541,3547,3553,3556,3567", "endColumns": "24,24,24,24,24", "endOffsets": "199263,199507,199754,199887,200299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5ccc5722ca8455978d22b35e0684d747\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "314,327,350,3267,3272", "startColumns": "4,4,4,4,4", "startOffsets": "20585,21230,22439,190670,190840", "endLines": "314,327,350,3271,3275", "endColumns": "56,64,63,24,24", "endOffsets": "20637,21290,22498,190835,190984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "357,573,574,575,576,577,578,579,580,581,582,585,586,587,588,589,590,591,592,593,594,595,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,1852,1862", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22867,38856,38944,39030,39111,39195,39264,39329,39412,39518,39604,39724,39778,39847,39908,39977,40066,40161,40235,40332,40425,40523,40672,40763,40851,40947,41045,41109,41177,41264,41358,41425,41497,41569,41670,41779,41855,41924,41972,42038,42102,42176,42233,42290,42362,42412,42466,42537,42608,42678,42747,42805,42881,42952,43026,43112,43162,43232,119961,120676", "endLines": "357,573,574,575,576,577,578,579,580,581,584,585,586,587,588,589,590,591,592,593,594,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,1861,1864", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "22935,38939,39025,39106,39190,39259,39324,39407,39513,39599,39719,39773,39842,39903,39972,40061,40156,40230,40327,40420,40518,40667,40758,40846,40942,41040,41104,41172,41259,41353,41420,41492,41564,41665,41774,41850,41919,41967,42033,42097,42171,42228,42285,42357,42407,42461,42532,42603,42673,42742,42800,42876,42947,43021,43107,43157,43227,43292,120671,120824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "67,68,69,70,71,72,73,74,472,473,474,475,476,477,478,479,481,482,483,484,485,486,487,489,490,3351,3623", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4254,4344,4424,4514,4604,4684,4765,4845,31099,31204,31385,31510,31617,31797,31920,32036,32306,32494,32599,32780,32905,33080,33228,33351,33413,193051,202125", "endLines": "67,68,69,70,71,72,73,74,472,473,474,475,476,477,478,479,481,482,483,484,485,486,487,489,490,3363,3641", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4339,4419,4509,4599,4679,4760,4840,4920,31199,31380,31505,31612,31792,31915,32031,32134,32489,32594,32775,32900,33075,33223,33286,33408,33487,193361,202537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5092f4b0fc3229c87d6dc9ffd8fb80cd\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "20642", "endColumns": "65", "endOffsets": "20703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2cda5a1f9d8c110805098b8b6936f0e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "418", "startColumns": "4", "startOffsets": "26762", "endColumns": "82", "endOffsets": "26840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0632335579b593b6b56131d5dd003758\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "2,51,52,53,54,209,210,211,772,1841,1843,1846,3011", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3132,3193,3255,3317,13770,13829,13886,51570,119531,119595,119721,179007", "endLines": "2,51,52,53,54,209,210,211,778,1842,1845,1848,3038", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "197,3188,3250,3312,3376,13824,13881,13935,51979,119590,119716,119844,179926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "356,480", "startColumns": "4,4", "startOffsets": "22799,32139", "endColumns": "67,166", "endOffsets": "22862,32301"}}]}]}