package com.healthyproducts.app.data.api;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GeminiApiService_Factory implements Factory<GeminiApiService> {
  @Override
  public GeminiApiService get() {
    return newInstance();
  }

  public static GeminiApiService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static GeminiApiService newInstance() {
    return new GeminiApiService();
  }

  private static final class InstanceHolder {
    private static final GeminiApiService_Factory INSTANCE = new GeminiApiService_Factory();
  }
}
