package com.healthyproducts.app.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FoodAnalysisRepository_Factory implements Factory<FoodAnalysisRepository> {
  @Override
  public FoodAnalysisRepository get() {
    return newInstance();
  }

  public static FoodAnalysisRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static FoodAnalysisRepository newInstance() {
    return new FoodAnalysisRepository();
  }

  private static final class InstanceHolder {
    private static final FoodAnalysisRepository_Factory INSTANCE = new FoodAnalysisRepository_Factory();
  }
}
