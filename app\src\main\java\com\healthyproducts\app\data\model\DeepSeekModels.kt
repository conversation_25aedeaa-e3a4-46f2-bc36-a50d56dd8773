package com.healthyproducts.app.data.model

import com.google.gson.annotations.SerializedName

/**
 * DeepSeek API istek modeli
 */
data class DeepSeekRequest(
    val model: String = "deepseek-chat",
    val messages: List<Message>,
    val temperature: Double = 0.3,
    @SerializedName("max_tokens") val maxTokens: Int = 1000
)

/**
 * DeepSeek API mesaj modeli
 */
data class Message(
    val role: String,
    val content: String
)

/**
 * DeepSeek API yanıt modeli
 */
data class DeepSeekResponse(
    val id: String,
    @SerializedName("object") val objectType: String,
    val created: Long,
    val model: String,
    val choices: List<Choice>,
    val usage: Usage
)

/**
 * DeepSeek API seçenek modeli
 */
data class Choice(
    val index: Int,
    val message: Message,
    @SerializedName("finish_reason") val finishReason: String
)

/**
 * DeepSeek API kullanım modeli
 */
data class Usage(
    @SerializedName("prompt_tokens") val promptTokens: Int,
    @SerializedName("completion_tokens") val completionTokens: Int,
    @SerializedName("total_tokens") val totalTokens: Int
)
