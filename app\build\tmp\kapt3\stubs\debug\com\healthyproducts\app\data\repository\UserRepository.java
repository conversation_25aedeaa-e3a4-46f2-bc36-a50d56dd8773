package com.healthyproducts.app.data.repository;

/**
 * <PERSON>llanıcı işlemlerini yöneten repository sınıfı
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0015\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0002J\u0010\u0010\u000b\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\r\u001a\u0004\u0018\u00010\b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u0010\u0010J$\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0010J\u0016\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\bH\u0082@\u00a2\u0006\u0002\u0010\u0018J,\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\b0\u00122\u0006\u0010\u001a\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ$\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\b0\u00122\u0006\u0010\u001f\u001a\u00020\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b \u0010\u0010J\u001c\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00160\u0012H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010\fJ8\u0010#\u001a\b\u0012\u0004\u0012\u00020\b0\u00122\u0006\u0010\u001a\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\u000f2\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b%\u0010&J,\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010(\u001a\u00020\u0013H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b)\u0010*R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006+"}, d2 = {"Lcom/healthyproducts/app/data/repository/UserRepository;", "", "firebaseAuthRepository", "Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "(Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;Lcom/google/firebase/firestore/FirebaseFirestore;)V", "createUserFromFirebaseUser", "Lcom/healthyproducts/app/model/User;", "firebaseUser", "Lcom/google/firebase/auth/FirebaseUser;", "getCurrentUser", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserFromFirestore", "userId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserPreferences", "Lkotlin/Result;", "Lcom/healthyproducts/app/model/UserPreferences;", "getUserPreferences-gIAlu-s", "saveUserToFirestore", "", "user", "(Lcom/healthyproducts/app/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signIn", "email", "password", "signIn-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signInWithGoogle", "token", "signInWithGoogle-gIAlu-s", "signOut", "signOut-IoAF18A", "signUp", "name", "signUp-BWLJW6A", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserPreferences", "preferences", "updateUserPreferences-0E7RQCE", "(Ljava/lang/String;Lcom/healthyproducts/app/model/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class UserRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FirebaseAuthRepository firebaseAuthRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.firestore.FirebaseFirestore firestore = null;
    
    @javax.inject.Inject()
    public UserRepository(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirebaseAuthRepository firebaseAuthRepository, @org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        super();
    }
    
    /**
     * Mevcut kullanıcıyı getirme
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentUser(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.healthyproducts.app.model.User> $completion) {
        return null;
    }
    
    /**
     * Firebase kullanıcısından User nesnesi oluşturur
     */
    private final com.healthyproducts.app.model.User createUserFromFirebaseUser(com.google.firebase.auth.FirebaseUser firebaseUser) {
        return null;
    }
    
    /**
     * Firestore'a kullanıcı bilgilerini kaydeder
     */
    private final java.lang.Object saveUserToFirestore(com.healthyproducts.app.model.User user, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Firestore'dan kullanıcı bilgilerini getirir
     */
    private final java.lang.Object getUserFromFirestore(java.lang.String userId, kotlin.coroutines.Continuation<? super com.healthyproducts.app.model.User> $completion) {
        return null;
    }
}