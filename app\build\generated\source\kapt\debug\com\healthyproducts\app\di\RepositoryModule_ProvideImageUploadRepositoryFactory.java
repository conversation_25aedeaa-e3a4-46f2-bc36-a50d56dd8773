package com.healthyproducts.app.di;

import android.content.Context;
import com.google.firebase.storage.FirebaseStorage;
import com.healthyproducts.app.data.repository.ImageUploadRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideImageUploadRepositoryFactory implements Factory<ImageUploadRepository> {
  private final Provider<FirebaseStorage> storageProvider;

  private final Provider<Context> contextProvider;

  public RepositoryModule_ProvideImageUploadRepositoryFactory(
      Provider<FirebaseStorage> storageProvider, Provider<Context> contextProvider) {
    this.storageProvider = storageProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public ImageUploadRepository get() {
    return provideImageUploadRepository(storageProvider.get(), contextProvider.get());
  }

  public static RepositoryModule_ProvideImageUploadRepositoryFactory create(
      Provider<FirebaseStorage> storageProvider, Provider<Context> contextProvider) {
    return new RepositoryModule_ProvideImageUploadRepositoryFactory(storageProvider, contextProvider);
  }

  public static ImageUploadRepository provideImageUploadRepository(FirebaseStorage storage,
      Context context) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideImageUploadRepository(storage, context));
  }
}
