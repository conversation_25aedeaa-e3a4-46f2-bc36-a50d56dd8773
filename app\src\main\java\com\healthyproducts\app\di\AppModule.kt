package com.healthyproducts.app.di

import android.content.Context
import com.healthyproducts.app.HealthyProductsApp
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Uygulama seviyesindeki bağımlılıkları sağlayan Hilt modülü
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    /**
     * Uygulama Context'ini sağlar
     */
    @Provides
    @Singleton
    fun provideContext(@ApplicationContext appContext: Context): Context {
        return appContext
    }
}
