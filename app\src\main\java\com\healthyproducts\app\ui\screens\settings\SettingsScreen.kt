package com.healthyproducts.app.ui.screens.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.healthyproducts.app.ui.components.clickable
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.AiModel
import com.healthyproducts.app.model.UserPreferences
import com.healthyproducts.app.navigation.Screen
import com.healthyproducts.app.ui.viewmodel.UserViewModel
import androidx.compose.runtime.collectAsState

/**
 * Ayarlar ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    userViewModel: UserViewModel = hiltViewModel()
) {
    // Kullanıcı durumu ve tercihleri
    val userState by userViewModel.userState.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()

    // Ekranın yeniden oluşturulmasını sağlamak için key kullan
    val recomposeKey = userPreferences.hashCode()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.settings)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Geri"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        // Key kullanarak ekranın yeniden oluşturulmasını sağla
        key(recomposeKey) {
            SettingsContent(
                paddingValues = innerPadding,
                userPreferences = userPreferences,
                navController = navController,
                onPreferencesChanged = { updatedPreferences ->
                    // Kullanıcı giriş yapmışsa tercihleri güncelle
                    if (userState is UserViewModel.UserState.LoggedIn) {
                        val userId = (userState as UserViewModel.UserState.LoggedIn).user.id
                        userViewModel.updateUserPreferences(updatedPreferences)
                    }
                }
            )
        }
    }
}

/**
 * Ayarlar ekranı içeriği
 */
@Composable
fun SettingsContent(
    paddingValues: PaddingValues,
    userPreferences: UserPreferences,
    navController: NavController,
    onPreferencesChanged: (UserPreferences) -> Unit
) {
    // Kullanıcı tercihlerini state olarak tut
    var showHalalAnalysis by remember(userPreferences.showHalalAnalysis) { mutableStateOf(userPreferences.showHalalAnalysis) }
    var showVeganAnalysis by remember(userPreferences.showVeganAnalysis) { mutableStateOf(userPreferences.showVeganAnalysis) }
    var showKosherAnalysis by remember(userPreferences.showKosherAnalysis) { mutableStateOf(userPreferences.showKosherAnalysis) }
    var showHarmfulAnalysis by remember(userPreferences.showHarmfulAnalysis) { mutableStateOf(userPreferences.showHarmfulAnalysis) }
    var showUnhealthyAnalysis by remember(userPreferences.showUnhealthyAnalysis) { mutableStateOf(userPreferences.showUnhealthyAnalysis) }
    var darkMode by remember(userPreferences.darkMode) { mutableStateOf(userPreferences.darkMode) }
    var aiModel by remember(userPreferences.aiModel) { mutableStateOf(userPreferences.aiModel) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
            .verticalScroll(rememberScrollState())
    ) {
        SettingsCategory(title = stringResource(R.string.settings_category_analysis))

        SettingsSwitch(
            title = stringResource(R.string.show_halal_analysis),
            checked = showHalalAnalysis,
            onCheckedChange = {
                showHalalAnalysis = it
                updatePreferences(
                    showHalalAnalysis = it,
                    showVeganAnalysis = showVeganAnalysis,
                    showKosherAnalysis = showKosherAnalysis,
                    showHarmfulAnalysis = showHarmfulAnalysis,
                    showUnhealthyAnalysis = showUnhealthyAnalysis,
                    darkMode = darkMode,
                    onPreferencesChanged = onPreferencesChanged,
                    aiModel = aiModel
                )
            }
        )

        SettingsSwitch(
            title = stringResource(R.string.show_vegan_analysis),
            checked = showVeganAnalysis,
            onCheckedChange = {
                showVeganAnalysis = it
                updatePreferences(
                    showHalalAnalysis = showHalalAnalysis,
                    showVeganAnalysis = it,
                    showKosherAnalysis = showKosherAnalysis,
                    showHarmfulAnalysis = showHarmfulAnalysis,
                    showUnhealthyAnalysis = showUnhealthyAnalysis,
                    darkMode = darkMode,
                    onPreferencesChanged = onPreferencesChanged,
                    aiModel = aiModel
                )
            }
        )

        SettingsSwitch(
            title = stringResource(R.string.show_kosher_analysis),
            checked = showKosherAnalysis,
            onCheckedChange = {
                showKosherAnalysis = it
                updatePreferences(
                    showHalalAnalysis = showHalalAnalysis,
                    showVeganAnalysis = showVeganAnalysis,
                    showKosherAnalysis = it,
                    showHarmfulAnalysis = showHarmfulAnalysis,
                    showUnhealthyAnalysis = showUnhealthyAnalysis,
                    darkMode = darkMode,
                    onPreferencesChanged = onPreferencesChanged,
                    aiModel = aiModel
                )
            }
        )

        SettingsSwitch(
            title = stringResource(R.string.show_harmful_analysis),
            checked = showHarmfulAnalysis,
            onCheckedChange = {
                showHarmfulAnalysis = it
                updatePreferences(
                    showHalalAnalysis = showHalalAnalysis,
                    showVeganAnalysis = showVeganAnalysis,
                    showKosherAnalysis = showKosherAnalysis,
                    showHarmfulAnalysis = it,
                    showUnhealthyAnalysis = showUnhealthyAnalysis,
                    darkMode = darkMode,
                    onPreferencesChanged = onPreferencesChanged,
                    aiModel = aiModel
                )
            }
        )

        SettingsSwitch(
            title = stringResource(R.string.show_unhealthy_analysis),
            checked = showUnhealthyAnalysis,
            onCheckedChange = {
                showUnhealthyAnalysis = it
                updatePreferences(
                    showHalalAnalysis = showHalalAnalysis,
                    showVeganAnalysis = showVeganAnalysis,
                    showKosherAnalysis = showKosherAnalysis,
                    showHarmfulAnalysis = showHarmfulAnalysis,
                    showUnhealthyAnalysis = it,
                    darkMode = darkMode,
                    onPreferencesChanged = onPreferencesChanged,
                    aiModel = aiModel
                )
            }
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        SettingsCategory(title = stringResource(R.string.settings_category_appearance))

        SettingsSwitch(
            title = stringResource(R.string.dark_mode),
            checked = darkMode,
            onCheckedChange = {
                darkMode = it
                updatePreferences(
                    showHalalAnalysis = showHalalAnalysis,
                    showVeganAnalysis = showVeganAnalysis,
                    showKosherAnalysis = showKosherAnalysis,
                    showHarmfulAnalysis = showHarmfulAnalysis,
                    showUnhealthyAnalysis = showUnhealthyAnalysis,
                    darkMode = it,
                    onPreferencesChanged = onPreferencesChanged,
                    aiModel = aiModel
                )
            }
        )

        SettingsItem(
            title = stringResource(R.string.language),
            onClick = { navController.navigate("language") }
        )

        // AI model değerini logla
        android.util.Log.d("SettingsScreen", "Current AI model: ${userPreferences.aiModel}")

        // Güvenli bir şekilde enum değerini al
        val aiModelDisplayName = try {
            // Null veya boş değilse enum değerini al
            if (!userPreferences.aiModel.isNullOrBlank()) {
                AiModel.valueOf(userPreferences.aiModel).displayName
            } else {
                android.util.Log.w("SettingsScreen", "AI model is null or blank, using default")
                AiModel.GEMINI_LITE.displayName
            }
        } catch (e: IllegalArgumentException) {
            android.util.Log.e("SettingsScreen", "Invalid AI model: ${userPreferences.aiModel}", e)
            AiModel.GEMINI_LITE.displayName
        }

        SettingsItem(
            title = stringResource(R.string.ai_model),
            value = aiModelDisplayName,
            onClick = { navController.navigate("ai_model_selection") }
        )

        SettingsItem(
            title = stringResource(R.string.additives),
            onClick = { navController.navigate(Screen.Additives.route) }
        )

        SettingsItem(
            title = stringResource(R.string.user_food_preferences),
            onClick = { navController.navigate(Screen.UserFoodPreferences.route) }
        )

        Divider(modifier = Modifier.padding(vertical = 8.dp))

        SettingsCategory(title = stringResource(R.string.settings_category_about))

        SettingsItem(title = stringResource(R.string.version), value = "1.0.0")
        SettingsItem(title = stringResource(R.string.privacy_policy))
        SettingsItem(title = stringResource(R.string.terms_of_service))
        SettingsItem(title = stringResource(R.string.about))

        // Yönetici seçenekleri
        Divider(modifier = Modifier.padding(vertical = 8.dp))
        SettingsCategory(title = "Admin")

        // Veri yükleme ekranına git
        SettingsItem(
            title = stringResource(R.string.data_upload_screen_title),
            onClick = { navController.navigate(Screen.DataUpload.route) }
        )
    }
}

/**
 * Ayarlar kategorisi başlığı
 */
@Composable
fun SettingsCategory(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        color = MaterialTheme.colorScheme.primary,
        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
    )
}

/**
 * Ayarlar öğesi (başlık ve değer)
 */
@Composable
fun SettingsItem(
    title: String,
    value: String? = null,
    onClick: (() -> Unit)? = null
) {
    androidx.compose.material3.ListItem(
        headlineContent = { Text(text = title) },
        supportingContent = value?.let { { Text(text = it) } },
        modifier = Modifier.clickable(enabled = onClick != null, onClick = onClick ?: {})
    )
}

/**
 * Ayarlar switch öğesi
 */
@Composable
fun SettingsSwitch(
    title: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    androidx.compose.material3.ListItem(
        headlineContent = { Text(text = title) },
        trailingContent = {
            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange
            )
        }
    )
}

/**
 * Kullanıcı tercihlerini günceller
 */
private fun updatePreferences(
    showHalalAnalysis: Boolean,
    showVeganAnalysis: Boolean,
    showKosherAnalysis: Boolean,
    showHarmfulAnalysis: Boolean,
    showUnhealthyAnalysis: Boolean,
    darkMode: Boolean,
    onPreferencesChanged: (UserPreferences) -> Unit,
    aiModel: String = AiModel.GEMINI_LITE.name // Varsayılan olarak GEMINI_LITE kullan
) {
    val updatedPreferences = UserPreferences(
        showHalalAnalysis = showHalalAnalysis,
        showVeganAnalysis = showVeganAnalysis,
        showKosherAnalysis = showKosherAnalysis,
        showHarmfulAnalysis = showHarmfulAnalysis,
        showUnhealthyAnalysis = showUnhealthyAnalysis,
        darkMode = darkMode,
        aiModel = aiModel
    )

    onPreferencesChanged(updatedPreferences)
}
