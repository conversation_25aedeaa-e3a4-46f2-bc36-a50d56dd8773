{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,24570", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,24645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "334,335", "startColumns": "4,4", "startOffsets": "26918,27002", "endColumns": "83,86", "endOffsets": "26997,27084"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values-tr\\strings.xml", "from": {"startLines": "68,161,211,215,212,216,214,213,46,166,163,165,160,167,170,169,164,172,171,168,159,66,67,124,120,101,192,2,205,99,21,23,91,189,127,41,151,152,153,150,144,85,100,197,82,224,217,60,111,115,110,114,92,129,134,93,162,29,80,190,96,125,121,7,18,202,201,123,155,154,32,196,128,148,147,149,24,42,44,146,132,5,175,207,180,176,178,179,177,181,193,40,31,36,30,28,137,59,107,104,106,105,95,77,219,86,79,195,15,83,223,198,17,143,142,27,22,98,130,81,194,221,220,186,185,184,69,39,8,12,78,87,135,47,191,97,206,133,90,218,6,13,84,14,26,25,94,208,9,74,72,73,61,64,63,65,62,35,34,33,50,51,55,140,141,54,139,52,56,53,138,126,122,45,187,70,188,117,145,116,112,113,131,222,43,136,71,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3372,7991,10628,10883,10694,10954,10812,10751,2273,8294,8115,8236,7926,8349,8545,8481,8176,8691,8616,8412,7872,3264,3311,5974,5776,4853,9667,55,10350,4775,828,918,4443,9509,6137,1976,7507,7579,7646,7441,7078,4188,4813,9939,4036,11508,11012,2855,5241,5564,5175,5489,4484,6265,6510,4523,8051,1314,3951,9559,4653,6033,5825,222,747,10166,10104,5909,7783,7719,1557,9879,6201,7327,7267,7386,993,2030,2138,7205,6398,141,8762,10471,9097,8829,8949,9021,8887,9168,9718,1924,1485,1747,1410,1245,6696,2814,5087,4922,5029,4977,4603,3816,11133,4246,3906,9832,549,4096,11447,9998,680,7022,6981,1167,875,4738,6314,3993,9785,11298,11221,9371,9307,9253,3415,1865,270,384,3860,4313,6571,2334,9623,4692,10412,6457,4403,11070,184,439,4132,492,1097,1047,4564,10526,313,3724,3586,3659,2906,3114,3044,3187,2975,1702,1657,1612,2433,2480,2685,6875,6923,2628,6808,2527,2736,2581,6751,6083,5865,2204,9421,3478,9468,5691,7139,5630,5362,5424,6355,11380,2084,6632,3541,603", "endColumns": "42,59,65,70,56,57,70,60,60,54,60,57,64,62,70,63,59,49,74,68,53,46,60,58,48,45,50,53,61,37,46,74,40,49,63,53,71,66,72,65,60,57,39,58,59,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,58,42,66,54,70,57,71,75,61,56,66,51,71,88,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,35,60,70,66,55,40,77,42,36,40,42,46,81,76,49,63,53,62,58,42,54,45,68,60,68,43,45,58,52,39,62,37,52,55,56,69,49,38,65,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,68,46,62,40,56,65,60,61,64,42,66,53,63,44,76", "endOffsets": "3410,8046,10689,10949,10746,11007,10878,10807,2329,8344,8171,8289,7986,8407,8611,8540,8231,8736,8686,8476,7921,3306,3367,6028,5820,4894,9713,104,10407,4808,870,988,4479,9554,6196,2025,7574,7641,7714,7502,7134,4241,4848,9993,4091,11552,11065,2901,5357,5625,5236,5559,4518,6309,6566,4559,8110,1405,3988,9618,4687,6078,5860,265,797,10313,10161,5969,7842,7778,1607,9934,6260,7381,7322,7436,1042,2079,2199,7262,6452,179,8824,10521,9163,8882,9016,9092,8944,9220,9780,1971,1552,1831,1480,1309,6746,2850,5144,4972,5082,5024,4648,3855,11216,4308,3946,9874,598,4127,11503,10064,742,7073,7017,1240,913,4770,6350,4031,9827,11375,11293,9416,9366,9302,3473,1919,308,434,3901,4377,6627,2398,9662,4733,10466,6505,4438,11128,217,487,4183,544,1162,1092,4598,10587,353,3789,3654,3719,2970,3182,3109,3259,3039,1742,1697,1652,2475,2522,2731,6918,6976,2680,6870,2576,2786,2623,6803,6132,5904,2268,9463,3536,9504,5743,7200,5686,5419,5484,6393,11442,2133,6691,3581,675"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,67,68,69,70,71,72,73,74,75,76,77,78,120,123,125,126,127,128,129,130,131,134,135,136,138,139,140,141,142,143,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,252,253,254,256,257,258,259,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,299,300,301,302,303,304,305,306,307,308,309,310,315,316,317,318,319,320,321,322,323,324,325,326,327,328,330,333,336,337,338,339,340,341,342,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,2840,2900,2966,3037,3094,3152,3223,3284,3345,3400,3461,3519,3584,3647,3718,3782,3842,3892,3967,4036,4090,4137,4198,4257,4306,4352,4403,4457,4519,4557,5317,5392,5433,5483,5547,5601,5673,5740,5813,5879,5940,5998,10847,11050,11210,11259,11317,11368,11489,11555,11621,11891,11930,11979,12124,12165,12229,12325,12367,12431,12789,12839,12879,12927,12982,13134,13196,13261,13325,13389,13444,13504,13568,13627,13687,13742,13796,13850,13916,13978,14037,14080,14147,14202,14273,14331,14403,14479,14541,14781,14848,14900,14972,15061,15136,15205,15260,15301,15363,15418,15476,15528,15578,15622,15710,15777,21982,22029,22083,22207,22268,22339,22406,22546,22587,22665,22708,22745,22786,22829,22876,22958,23035,23085,23149,23203,23266,23325,23536,23591,23637,23706,23767,23836,23880,23926,23985,24038,24078,24141,24179,24232,24288,24345,24415,24465,24504,24721,24766,24836,24909,24974,25043,25116,25186,25263,25332,25377,25422,25789,25836,25883,25934,25982,26040,26097,26164,26218,26273,26320,26377,26431,26475,26622,26855,27089,27130,27187,27253,27314,27376,27441,27484,27551,27605,27669,27714", "endColumns": "42,59,65,70,56,57,70,60,60,54,60,57,64,62,70,63,59,49,74,68,53,46,60,58,48,45,50,53,61,37,46,74,40,49,63,53,71,66,72,65,60,57,39,58,59,48,57,50,120,65,65,74,38,48,60,40,63,95,41,63,38,49,39,47,54,151,61,64,63,63,54,59,63,58,59,54,53,53,65,61,58,42,66,54,70,57,71,75,61,56,66,51,71,88,74,68,54,40,61,54,57,51,49,43,87,66,44,46,53,35,60,70,66,55,40,77,42,36,40,42,46,81,76,49,63,53,62,58,42,54,45,68,60,68,43,45,58,52,39,62,37,52,55,56,69,49,38,65,44,69,72,64,68,72,69,76,68,44,44,44,46,46,50,47,57,56,66,53,54,46,56,53,43,68,46,62,40,56,65,60,61,64,42,66,53,63,44,76", "endOffsets": "2835,2895,2961,3032,3089,3147,3218,3279,3340,3395,3456,3514,3579,3642,3713,3777,3837,3887,3962,4031,4085,4132,4193,4252,4301,4347,4398,4452,4514,4552,4599,5387,5428,5478,5542,5596,5668,5735,5808,5874,5935,5993,6033,10901,11105,11254,11312,11363,11484,11550,11616,11691,11925,11974,12035,12160,12224,12320,12362,12426,12465,12834,12874,12922,12977,13129,13191,13256,13320,13384,13439,13499,13563,13622,13682,13737,13791,13845,13911,13973,14032,14075,14142,14197,14268,14326,14398,14474,14536,14593,14843,14895,14967,15056,15131,15200,15255,15296,15358,15413,15471,15523,15573,15617,15705,15772,15817,22024,22078,22114,22263,22334,22401,22457,22582,22660,22703,22740,22781,22824,22871,22953,23030,23080,23144,23198,23261,23320,23363,23586,23632,23701,23762,23831,23875,23921,23980,24033,24073,24136,24174,24227,24283,24340,24410,24460,24499,24565,24761,24831,24904,24969,25038,25111,25181,25258,25327,25372,25417,25462,25831,25878,25929,25977,26035,26092,26159,26213,26268,26315,26372,26426,26470,26539,26664,26913,27125,27182,27248,27309,27371,27436,27479,27546,27600,27664,27709,27786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "124,144,145,146", "startColumns": "4,4,4,4", "startOffsets": "11110,12470,12576,12683", "endColumns": "99,105,106,105", "endOffsets": "11205,12571,12678,12784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "60,61,62,63,64,65,66,314", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4604,4701,4803,4901,4998,5100,5206,25688", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "4696,4798,4896,4993,5095,5201,5312,25784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,996,1084,1155,1229,1304,1376,1454,1522", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,991,1079,1150,1224,1299,1371,1449,1517,1635"}, "to": {"startLines": "79,80,132,133,137,176,177,255,260,276,277,298,311,312,313,329,331,332", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6038,6131,11696,11791,12040,14598,14681,22119,22462,23368,23448,24650,25467,25541,25616,26544,26669,26737", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,73,74,71,77,67,117", "endOffsets": "6126,6210,11786,11886,12119,14676,14776,22202,22541,23443,23531,24716,25536,25611,25683,26617,26732,26850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "104,105,106,107,108,109,110,111,113,114,115,116,117,118,119,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8740,8850,9005,9141,9246,9393,9523,9650,9903,10075,10182,10339,10473,10618,10785,10906,10970", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "8845,9000,9136,9241,9388,9518,9645,9751,10070,10177,10334,10468,10613,10780,10842,10965,11045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15822,15937,16050,16168,16283,16379,16475,16588,16721,16843,16983,17068,17166,17255,17352,17467,17588,17691,17828,17964,18086,18257,18375,18491,18609,18724,18814,18912,19036,19165,19266,19368,19474,19610,19750,19862,19964,20040,20137,20235,20345,20431,20516,20633,20713,20797,20897,20997,21093,21188,21276,21382,21482,21581,21702,21782,21889", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "15932,16045,16163,16278,16374,16470,16583,16716,16838,16978,17063,17161,17250,17347,17462,17583,17686,17823,17959,18081,18252,18370,18486,18604,18719,18809,18907,19031,19160,19261,19363,19469,19605,19745,19857,19959,20035,20132,20230,20340,20426,20511,20628,20708,20792,20892,20992,21088,21183,21271,21377,21477,21576,21697,21777,21884,21977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,361,497,610,695,781,859,947,1039,1152,1262,1355,1448,1555,1675,1757,1840,2017,2113,2222,2338,2441", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "219,356,492,605,690,776,854,942,1034,1147,1257,1350,1443,1550,1670,1752,1835,2012,2108,2217,2333,2436,2575"}, "to": {"startLines": "81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6215,6384,6521,6657,6770,6855,6941,7019,7107,7199,7312,7422,7515,7608,7715,7835,7917,8000,8177,8273,8382,8498,8601", "endColumns": "168,136,135,112,84,85,77,87,91,112,109,92,92,106,119,81,82,176,95,108,115,102,138", "endOffsets": "6379,6516,6652,6765,6850,6936,7014,7102,7194,7307,7417,7510,7603,7710,7830,7912,7995,8172,8268,8377,8493,8596,8735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "9756", "endColumns": "146", "endOffsets": "9898"}}]}]}