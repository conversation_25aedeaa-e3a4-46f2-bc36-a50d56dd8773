package com.healthyproducts.app.navigation;

/**
 * Uygulama içi navigasyon rotalarını tanımlayan sealed class
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u001b\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u001f !B\u000f\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u0082\u0001\u001b\"#$%&\'()*+,-./**********:;<\u00a8\u0006="}, d2 = {"Lcom/healthyproducts/app/navigation/Screen;", "", "route", "", "(Ljava/lang/String;)V", "getRoute", "()Ljava/lang/String;", "AdditiveDetail", "AdditiveEdit", "Additives", "AiModelSelection", "AllergenDetail", "Allergens", "DataUpload", "FatDetail", "Fats", "Favorites", "FoodAnalysis", "FoodCertificateDetail", "FoodCertificates", "Home", "Language", "Login", "PreservativeDetail", "Preservatives", "ProductDetail", "Profile", "Register", "Scan", "ScanHistory", "Settings", "SugarDetail", "Sugars", "UserFoodPreferences", "Lcom/healthyproducts/app/navigation/Screen$AdditiveDetail;", "Lcom/healthyproducts/app/navigation/Screen$AdditiveEdit;", "Lcom/healthyproducts/app/navigation/Screen$Additives;", "Lcom/healthyproducts/app/navigation/Screen$AiModelSelection;", "Lcom/healthyproducts/app/navigation/Screen$AllergenDetail;", "Lcom/healthyproducts/app/navigation/Screen$Allergens;", "Lcom/healthyproducts/app/navigation/Screen$DataUpload;", "Lcom/healthyproducts/app/navigation/Screen$FatDetail;", "Lcom/healthyproducts/app/navigation/Screen$Fats;", "Lcom/healthyproducts/app/navigation/Screen$Favorites;", "Lcom/healthyproducts/app/navigation/Screen$FoodAnalysis;", "Lcom/healthyproducts/app/navigation/Screen$FoodCertificateDetail;", "Lcom/healthyproducts/app/navigation/Screen$FoodCertificates;", "Lcom/healthyproducts/app/navigation/Screen$Home;", "Lcom/healthyproducts/app/navigation/Screen$Language;", "Lcom/healthyproducts/app/navigation/Screen$Login;", "Lcom/healthyproducts/app/navigation/Screen$PreservativeDetail;", "Lcom/healthyproducts/app/navigation/Screen$Preservatives;", "Lcom/healthyproducts/app/navigation/Screen$ProductDetail;", "Lcom/healthyproducts/app/navigation/Screen$Profile;", "Lcom/healthyproducts/app/navigation/Screen$Register;", "Lcom/healthyproducts/app/navigation/Screen$Scan;", "Lcom/healthyproducts/app/navigation/Screen$ScanHistory;", "Lcom/healthyproducts/app/navigation/Screen$Settings;", "Lcom/healthyproducts/app/navigation/Screen$SugarDetail;", "Lcom/healthyproducts/app/navigation/Screen$Sugars;", "Lcom/healthyproducts/app/navigation/Screen$UserFoodPreferences;", "app_debug"})
public abstract class Screen {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String route = null;
    
    private Screen(java.lang.String route) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRoute() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$AdditiveDetail;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "additiveCode", "app_debug"})
    public static final class AdditiveDetail extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.AdditiveDetail INSTANCE = null;
        
        private AdditiveDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String additiveCode) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$AdditiveEdit;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "additiveCode", "app_debug"})
    public static final class AdditiveEdit extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.AdditiveEdit INSTANCE = null;
        
        private AdditiveEdit() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.Nullable()
        java.lang.String additiveCode) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Additives;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Additives extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Additives INSTANCE = null;
        
        private Additives() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$AiModelSelection;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class AiModelSelection extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.AiModelSelection INSTANCE = null;
        
        private AiModelSelection() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$AllergenDetail;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "allergenCode", "app_debug"})
    public static final class AllergenDetail extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.AllergenDetail INSTANCE = null;
        
        private AllergenDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String allergenCode) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Allergens;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Allergens extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Allergens INSTANCE = null;
        
        private Allergens() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$DataUpload;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class DataUpload extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.DataUpload INSTANCE = null;
        
        private DataUpload() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$FatDetail;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "fatCode", "app_debug"})
    public static final class FatDetail extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.FatDetail INSTANCE = null;
        
        private FatDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String fatCode) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Fats;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Fats extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Fats INSTANCE = null;
        
        private Fats() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Favorites;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Favorites extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Favorites INSTANCE = null;
        
        private Favorites() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$FoodAnalysis;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "ingredients", "app_debug"})
    public static final class FoodAnalysis extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.FoodAnalysis INSTANCE = null;
        
        private FoodAnalysis() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String ingredients) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$FoodCertificateDetail;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "certificateCode", "app_debug"})
    public static final class FoodCertificateDetail extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.FoodCertificateDetail INSTANCE = null;
        
        private FoodCertificateDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String certificateCode) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$FoodCertificates;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class FoodCertificates extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.FoodCertificates INSTANCE = null;
        
        private FoodCertificates() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Home;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Home extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Home INSTANCE = null;
        
        private Home() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Language;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Language extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Language INSTANCE = null;
        
        private Language() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Login;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Login extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Login INSTANCE = null;
        
        private Login() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$PreservativeDetail;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "preservativeCode", "app_debug"})
    public static final class PreservativeDetail extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.PreservativeDetail INSTANCE = null;
        
        private PreservativeDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String preservativeCode) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Preservatives;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Preservatives extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Preservatives INSTANCE = null;
        
        private Preservatives() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$ProductDetail;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "productId", "app_debug"})
    public static final class ProductDetail extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.ProductDetail INSTANCE = null;
        
        private ProductDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String productId) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Profile;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Profile extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Profile INSTANCE = null;
        
        private Profile() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Register;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Register extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Register INSTANCE = null;
        
        private Register() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Scan;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Scan extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Scan INSTANCE = null;
        
        private Scan() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$ScanHistory;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class ScanHistory extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.ScanHistory INSTANCE = null;
        
        private ScanHistory() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Settings;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Settings extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Settings INSTANCE = null;
        
        private Settings() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$SugarDetail;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "createRoute", "", "sugarCode", "app_debug"})
    public static final class SugarDetail extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.SugarDetail INSTANCE = null;
        
        private SugarDetail() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String createRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String sugarCode) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$Sugars;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class Sugars extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.Sugars INSTANCE = null;
        
        private Sugars() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/navigation/Screen$UserFoodPreferences;", "Lcom/healthyproducts/app/navigation/Screen;", "()V", "app_debug"})
    public static final class UserFoodPreferences extends com.healthyproducts.app.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.healthyproducts.app.navigation.Screen.UserFoodPreferences INSTANCE = null;
        
        private UserFoodPreferences() {
        }
    }
}