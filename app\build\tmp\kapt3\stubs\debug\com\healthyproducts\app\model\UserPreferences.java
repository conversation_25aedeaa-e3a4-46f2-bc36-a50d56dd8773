package com.healthyproducts.app.model;

/**
 * Kullanıcı tercihlerini temsil eden veri sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b&\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B_\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0004\u0012\b\b\u0002\u0010\b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\f\u001a\u00020\n\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u000fJ\t\u0010(\u001a\u00020\u0004H\u00c6\u0003J\t\u0010)\u001a\u00020\u0004H\u00c6\u0003J\t\u0010*\u001a\u00020\u0004H\u00c6\u0003J\t\u0010+\u001a\u00020\u0004H\u00c6\u0003J\t\u0010,\u001a\u00020\u0004H\u00c6\u0003J\t\u0010-\u001a\u00020\nH\u00c6\u0003J\t\u0010.\u001a\u00020\u0004H\u00c6\u0003J\t\u0010/\u001a\u00020\nH\u00c6\u0003J\t\u00100\u001a\u00020\u000eH\u00c6\u0003Jc\u00101\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\u00042\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00042\b\b\u0002\u0010\f\u001a\u00020\n2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u00c6\u0001J\u0013\u00102\u001a\u00020\u00042\b\u00103\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00104\u001a\u000205H\u00d6\u0001J\t\u00106\u001a\u00020\nH\u00d6\u0001R\u001e\u0010\f\u001a\u00020\n8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013R\u001e\u0010\u000b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R\u001e\u0010\t\u001a\u00020\n8\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0018\u0010\u0011\"\u0004\b\u0019\u0010\u0013R\u001e\u0010\u0003\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u0015\"\u0004\b\u001b\u0010\u0017R\u001e\u0010\u0007\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u0010\u0015\"\u0004\b\u001d\u0010\u0017R\u001e\u0010\u0006\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u0015\"\u0004\b\u001f\u0010\u0017R\u001e\u0010\b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010\u0015\"\u0004\b!\u0010\u0017R\u001e\u0010\u0005\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010\u0015\"\u0004\b#\u0010\u0017R\u001a\u0010\r\u001a\u00020\u000eX\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010%\"\u0004\b&\u0010\'\u00a8\u00067"}, d2 = {"Lcom/healthyproducts/app/model/UserPreferences;", "", "()V", "showHalalAnalysis", "", "showVeganAnalysis", "showKosherAnalysis", "showHarmfulAnalysis", "showUnhealthyAnalysis", "language", "", "darkMode", "aiModel", "updatedAt", "Ljava/util/Date;", "(ZZZZZLjava/lang/String;ZLjava/lang/String;Ljava/util/Date;)V", "getAiModel", "()Ljava/lang/String;", "setAiModel", "(Ljava/lang/String;)V", "getDarkMode", "()Z", "setDarkMode", "(Z)V", "getLanguage", "setLanguage", "getShowHalalAnalysis", "setShowHalalAnalysis", "getShowHarmfulAnalysis", "setShowHarmfulAnalysis", "getShowKosherAnalysis", "setShowKosherAnalysis", "getShowUnhealthyAnalysis", "setShowUnhealthyAnalysis", "getShowVeganAnalysis", "setShowVeganAnalysis", "getUpdatedAt", "()Ljava/util/Date;", "setUpdatedAt", "(Ljava/util/Date;)V", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class UserPreferences {
    private boolean showHalalAnalysis;
    private boolean showVeganAnalysis;
    private boolean showKosherAnalysis;
    private boolean showHarmfulAnalysis;
    private boolean showUnhealthyAnalysis;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String language;
    private boolean darkMode;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String aiModel;
    @com.google.firebase.firestore.ServerTimestamp()
    @org.jetbrains.annotations.NotNull()
    private java.util.Date updatedAt;
    
    public UserPreferences(boolean showHalalAnalysis, boolean showVeganAnalysis, boolean showKosherAnalysis, boolean showHarmfulAnalysis, boolean showUnhealthyAnalysis, @org.jetbrains.annotations.NotNull()
    java.lang.String language, boolean darkMode, @org.jetbrains.annotations.NotNull()
    java.lang.String aiModel, @org.jetbrains.annotations.NotNull()
    java.util.Date updatedAt) {
        super();
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_halal_analysis")
    public final boolean getShowHalalAnalysis() {
        return false;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_halal_analysis")
    public final void setShowHalalAnalysis(boolean p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_vegan_analysis")
    public final boolean getShowVeganAnalysis() {
        return false;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_vegan_analysis")
    public final void setShowVeganAnalysis(boolean p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_kosher_analysis")
    public final boolean getShowKosherAnalysis() {
        return false;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_kosher_analysis")
    public final void setShowKosherAnalysis(boolean p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_harmful_analysis")
    public final boolean getShowHarmfulAnalysis() {
        return false;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_harmful_analysis")
    public final void setShowHarmfulAnalysis(boolean p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_unhealthy_analysis")
    public final boolean getShowUnhealthyAnalysis() {
        return false;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "show_unhealthy_analysis")
    public final void setShowUnhealthyAnalysis(boolean p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "language")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLanguage() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "language")
    public final void setLanguage(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "dark_mode")
    public final boolean getDarkMode() {
        return false;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "dark_mode")
    public final void setDarkMode(boolean p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "ai_model")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAiModel() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "ai_model")
    public final void setAiModel(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "updated_at")
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date getUpdatedAt() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "updated_at")
    public final void setUpdatedAt(@org.jetbrains.annotations.NotNull()
    java.util.Date p0) {
    }
    
    public UserPreferences() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Date component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.model.UserPreferences copy(boolean showHalalAnalysis, boolean showVeganAnalysis, boolean showKosherAnalysis, boolean showHarmfulAnalysis, boolean showUnhealthyAnalysis, @org.jetbrains.annotations.NotNull()
    java.lang.String language, boolean darkMode, @org.jetbrains.annotations.NotNull()
    java.lang.String aiModel, @org.jetbrains.annotations.NotNull()
    java.util.Date updatedAt) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}