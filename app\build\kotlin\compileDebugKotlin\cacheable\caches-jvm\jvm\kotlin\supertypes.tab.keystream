*com.healthyproducts.app.HealthyProductsApp$com.healthyproducts.app.MainActivity.com.healthyproducts.app.data.api.AiServiceImpl.com.healthyproducts.app.data.model.HalalStatus.com.healthyproducts.app.data.model.VeganStatus/com.healthyproducts.app.data.model.KosherStatus3com.healthyproducts.app.data.model.AdditiveCategory*com.healthyproducts.app.data.model.AiModel3com.healthyproducts.app.data.model.ProductMatchType3com.healthyproducts.app.data.model.User.$serializer;com.healthyproducts.app.data.model.UserSettings.$serializer4com.healthyproducts.app.data.model.SupportedLanguage5com.healthyproducts.app.data.model.FoodPreferenceType.com.healthyproducts.app.model.IngredientStatus-com.healthyproducts.app.model.CertificateType&com.healthyproducts.app.model.ScanType.com.healthyproducts.app.navigation.Screen.Home.com.healthyproducts.app.navigation.Screen.Scan3com.healthyproducts.app.navigation.Screen.Favorites1com.healthyproducts.app.navigation.Screen.Profile2com.healthyproducts.app.navigation.Screen.Settings/com.healthyproducts.app.navigation.Screen.Login2com.healthyproducts.app.navigation.Screen.Register5com.healthyproducts.app.navigation.Screen.ScanHistory2com.healthyproducts.app.navigation.Screen.Language:com.healthyproducts.app.navigation.Screen.AiModelSelection=com.healthyproducts.app.navigation.Screen.UserFoodPreferences3com.healthyproducts.app.navigation.Screen.Additives8com.healthyproducts.app.navigation.Screen.AdditiveDetail6com.healthyproducts.app.navigation.Screen.AdditiveEdit7com.healthyproducts.app.navigation.Screen.ProductDetail4com.healthyproducts.app.navigation.Screen.DataUpload3com.healthyproducts.app.navigation.Screen.Allergens8com.healthyproducts.app.navigation.Screen.AllergenDetail.com.healthyproducts.app.navigation.Screen.Fats3com.healthyproducts.app.navigation.Screen.FatDetail0com.healthyproducts.app.navigation.Screen.Sugars5com.healthyproducts.app.navigation.Screen.SugarDetail:com.healthyproducts.app.navigation.Screen.FoodCertificates?com.healthyproducts.app.navigation.Screen.FoodCertificateDetail7com.healthyproducts.app.navigation.Screen.Preservatives<com.healthyproducts.app.navigation.Screen.PreservativeDetail6com.healthyproducts.app.navigation.Screen.FoodAnalysis.com.healthyproducts.app.ui.auth.LoginViewModel1com.healthyproducts.app.ui.auth.RegisterViewModel/com.healthyproducts.app.ui.components.FrameSize=com.healthyproducts.app.ui.screens.admin.UploadStatus.Initial=com.healthyproducts.app.ui.screens.admin.UploadStatus.Success;com.healthyproducts.app.ui.screens.admin.UploadStatus.Error6com.healthyproducts.app.ui.viewmodel.AdditiveViewModelHcom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.IdleKcom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.LoadingKcom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.SuccessIcom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatus.Error7com.healthyproducts.app.ui.viewmodel.FavoritesViewModelNcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.LoadingLcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.EmptyNcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.SuccessLcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState.Error:com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel;com.healthyproducts.app.ui.viewmodel.AllergensState.Loading;com.healthyproducts.app.ui.viewmodel.AllergensState.Success9com.healthyproducts.app.ui.viewmodel.AllergensState.Error6com.healthyproducts.app.ui.viewmodel.FatsState.Loading6com.healthyproducts.app.ui.viewmodel.FatsState.Success4com.healthyproducts.app.ui.viewmodel.FatsState.Error8com.healthyproducts.app.ui.viewmodel.SugarsState.Loading8com.healthyproducts.app.ui.viewmodel.SugarsState.Success6com.healthyproducts.app.ui.viewmodel.SugarsState.Error>com.healthyproducts.app.ui.viewmodel.CertificatesState.Loading>com.healthyproducts.app.ui.viewmodel.CertificatesState.Success<com.healthyproducts.app.ui.viewmodel.CertificatesState.Error9com.healthyproducts.app.ui.viewmodel.ImportStatus.Initial9com.healthyproducts.app.ui.viewmodel.ImportStatus.Loading9com.healthyproducts.app.ui.viewmodel.ImportStatus.Success7com.healthyproducts.app.ui.viewmodel.ImportStatus.Error:com.healthyproducts.app.ui.viewmodel.AnalysisState.Initial:com.healthyproducts.app.ui.viewmodel.AnalysisState.Loading:com.healthyproducts.app.ui.viewmodel.AnalysisState.Success8com.healthyproducts.app.ui.viewmodel.AnalysisState.Error?com.healthyproducts.app.ui.viewmodel.PreservativesState.Loading?com.healthyproducts.app.ui.viewmodel.PreservativesState.Success=com.healthyproducts.app.ui.viewmodel.PreservativesState.Error>com.healthyproducts.app.ui.viewmodel.PreservativeState.Initial>com.healthyproducts.app.ui.viewmodel.PreservativeState.Loading>com.healthyproducts.app.ui.viewmodel.PreservativeState.Success<com.healthyproducts.app.ui.viewmodel.PreservativeState.ErrorEcom.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState.InitialEcom.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState.LoadingEcom.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState.SuccessCcom.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState.Error6com.healthyproducts.app.ui.viewmodel.LanguageViewModel9com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModelRcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.LoadingPcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.EmptyRcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.SuccessPcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Error2com.healthyproducts.app.ui.viewmodel.ScanViewModelAcom.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.IdleEcom.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.ScanningDcom.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.SuccessBcom.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.ErrorDcom.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.IdleHcom.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.ScanningGcom.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.SuccessEcom.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.ErrorGcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.IdleMcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.CorrectingJcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.SuccessHcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.Error2com.healthyproducts.app.ui.viewmodel.UserViewModelDcom.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.LoadingEcom.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.LoggedInFcom.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.LoggedOutBcom.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.Error,com.healthyproducts.app.util.BarcodeAnalyzer2com.healthyproducts.app.util.CameraPermissionState2com.healthyproducts.app.util.LocaleHelper.Language4com.healthyproducts.app.util.TextRecognitionAnalyzer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       