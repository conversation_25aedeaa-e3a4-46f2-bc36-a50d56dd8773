package com.healthyproducts.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a\u0089\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2K\u0010\n\u001aG\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\u000e\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\u000f\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\u0010\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\u0011\u001a\u00020\u0003H\u0007\u00a8\u0006\u0012"}, d2 = {"SaveProductDialog", "", "isVisible", "", "barcode", "", "productImage", "Landroid/graphics/Bitmap;", "onDismiss", "Lkotlin/Function0;", "onSave", "Lkotlin/Function3;", "Lkotlin/ParameterName;", "name", "productName", "productBrand", "ingredients", "isLoading", "app_debug"})
public final class SaveProductDialogKt {
    
    /**
     * Ürün kaydetme dialog'u
     */
    @androidx.compose.runtime.Composable()
    public static final void SaveProductDialog(boolean isVisible, @org.jetbrains.annotations.NotNull()
    java.lang.String barcode, @org.jetbrains.annotations.Nullable()
    android.graphics.Bitmap productImage, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave, boolean isLoading) {
    }
}