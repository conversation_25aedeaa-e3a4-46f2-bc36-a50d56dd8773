package com.healthyproducts.app.ui.screens.settings

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.FoodPreferenceType
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.model.UserFoodPreference
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState
import com.healthyproducts.app.ui.viewmodel.UserViewModel
import kotlinx.coroutines.launch

/**
 * Kullanıcı gıda tercihleri ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserFoodPreferencesScreen(
    navController: NavController,
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Kullanıcı durumu
    val userState by userViewModel.userState.collectAsState()
    val userId = if (userState is UserViewModel.UserState.LoggedIn) {
        (userState as UserViewModel.UserState.LoggedIn).user.id
    } else {
        ""
    }
    
    // Kullanıcı tercihleri
    val userPreferences by userViewModel.userPreferences.collectAsState()
    val userFoodPreferencesState by foodAnalysisViewModel.userFoodPreferencesState.collectAsState()
    
    // Dil tercihi
    val language = SupportedLanguage.fromCode(userPreferences.language)
    
    // Seçili sekme
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val tabs = listOf(
        FoodPreferenceType.ALLERGEN,
        FoodPreferenceType.FAT,
        FoodPreferenceType.SUGAR,
        FoodPreferenceType.PRESERVATIVE,
        FoodPreferenceType.CERTIFICATE
    )
    
    // Kullanıcı tercihlerini yükle
    LaunchedEffect(selectedTabIndex) {
        if (userId.isNotEmpty()) {
            foodAnalysisViewModel.loadUserFoodPreferencesByType(userId, tabs[selectedTabIndex])
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.user_food_preferences)) },
                navigationIcon = { BackButton(navController) }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // Sekme çubuğu
            TabRow(selectedTabIndex = selectedTabIndex) {
                tabs.forEachIndexed { index, type ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { selectedTabIndex = index },
                        text = {
                            Text(
                                text = when (type) {
                                    FoodPreferenceType.ALLERGEN -> stringResource(R.string.allergens)
                                    FoodPreferenceType.FAT -> stringResource(R.string.fats)
                                    FoodPreferenceType.SUGAR -> stringResource(R.string.sugars)
                                    FoodPreferenceType.PRESERVATIVE -> stringResource(R.string.preservatives)
                                    FoodPreferenceType.CERTIFICATE -> stringResource(R.string.certificates)
                                }
                            )
                        }
                    )
                }
            }
            
            // Tercih listesi
            when (val state = userFoodPreferencesState) {
                is UserFoodPreferencesState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                is UserFoodPreferencesState.Success -> {
                    val preferences = state.preferences
                    
                    if (preferences.isEmpty()) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.no_preferences),
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    } else {
                        LazyColumn(
                            contentPadding = PaddingValues(16.dp)
                        ) {
                            items(preferences) { preference ->
                                UserFoodPreferenceItem(
                                    preference = preference,
                                    onDelete = {
                                        scope.launch {
                                            foodAnalysisViewModel.deleteUserFoodPreference(
                                                preferenceId = preference.id,
                                                userId = userId,
                                                type = tabs[selectedTabIndex]
                                            )
                                            snackbarHostState.showSnackbar("Tercih silindi: ${preference.itemName}")
                                        }
                                    }
                                )
                            }
                        }
                    }
                }
                
                is UserFoodPreferencesState.Error -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = state.message,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
                
                else -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.no_preferences),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
        }
    }
}

/**
 * Kullanıcı gıda tercihi öğesi
 */
@Composable
fun UserFoodPreferenceItem(
    preference: UserFoodPreference,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = preference.itemName,
                    style = MaterialTheme.typography.titleMedium
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = preference.itemId,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                if (preference.isCustom) {
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = stringResource(R.string.custom_item),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Silme butonu
            IconButton(onClick = onDelete) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = stringResource(R.string.delete),
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}
