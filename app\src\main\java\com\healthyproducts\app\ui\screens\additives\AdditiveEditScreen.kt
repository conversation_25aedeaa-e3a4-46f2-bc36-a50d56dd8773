package com.healthyproducts.app.ui.screens.additives

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.data.model.Additive
import com.healthyproducts.app.data.model.AdditiveCategory
import com.healthyproducts.app.data.model.HalalStatus
import com.healthyproducts.app.data.model.KosherStatus
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.model.VeganStatus
import com.healthyproducts.app.ui.viewmodel.AdditiveViewModel
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Katkı maddesi ekleme/düzenleme ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdditiveEditScreen(
    navController: NavController,
    additiveCode: String? = null,
    viewModel: AdditiveViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val selectedAdditive by viewModel.selectedAdditive.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()
    val userLanguage = SupportedLanguage.fromCode(userPreferences.language)

    // Katkı maddesini yükle (düzenleme modu)
    LaunchedEffect(additiveCode) {
        if (additiveCode != null) {
            viewModel.getAdditiveByCode(additiveCode)
        } else {
            viewModel.selectAdditive(null)
        }
    }

    // Form durumu
    var code by remember { mutableStateOf("") }
    var nameTr by remember { mutableStateOf("") }
    var nameEn by remember { mutableStateOf("") }
    var descriptionTr by remember { mutableStateOf("") }
    var descriptionEn by remember { mutableStateOf("") }
    var category by remember { mutableStateOf(AdditiveCategory.OTHER.name) }
    var categoryTr by remember { mutableStateOf("") }
    var categoryEn by remember { mutableStateOf("") }
    var originTr by remember { mutableStateOf("") }
    var originEn by remember { mutableStateOf("") }
    var usageTr by remember { mutableStateOf("") }
    var usageEn by remember { mutableStateOf("") }
    var healthEffectTr by remember { mutableStateOf("") }
    var healthEffectEn by remember { mutableStateOf("") }
    var halalStatus by remember { mutableStateOf(HalalStatus.MUSHBOOH.name) }
    var veganStatus by remember { mutableStateOf(VeganStatus.NON_VEGAN.name) }
    var vegetarianStatus by remember { mutableStateOf("") }
    var kosherStatus by remember { mutableStateOf(KosherStatus.NON_KOSHER.name) }
    var harmfulLevel by remember { mutableFloatStateOf(0f) }
    var unhealthyLevel by remember { mutableFloatStateOf(0f) }
    var risky by remember { mutableStateOf(false) }
    var notesTr by remember { mutableStateOf("") }
    var notesEn by remember { mutableStateOf("") }

    // Form değerlerini seçili katkı maddesinden doldur
    LaunchedEffect(selectedAdditive) {
        selectedAdditive?.let { additive ->
            code = additive.code
            nameTr = additive.nameTr
            nameEn = additive.nameEn
            descriptionTr = additive.descriptionTr
            descriptionEn = additive.descriptionEn
            category = additive.category
            categoryTr = additive.categoryTr
            categoryEn = additive.categoryEn
            originTr = additive.originTr
            originEn = additive.originEn
            usageTr = additive.usageTr
            usageEn = additive.usageEn
            healthEffectTr = additive.healthEffectTr
            healthEffectEn = additive.healthEffectEn
            halalStatus = additive.halalStatus
            veganStatus = additive.veganStatus
            vegetarianStatus = additive.vegetarianStatus
            kosherStatus = additive.kosherStatus
            harmfulLevel = additive.harmfulLevel.toFloat()
            unhealthyLevel = additive.unhealthyLevel.toFloat()
            risky = additive.risky
            notesTr = additive.notesTr
            notesEn = additive.notesEn
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = if (additiveCode != null) "Katkı Maddesi Düzenle" else "Katkı Maddesi Ekle"
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Geri"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = {
                            val additive = Additive(
                                id = code,
                                code = code,
                                nameTr = nameTr,
                                nameEn = nameEn,
                                descriptionTr = descriptionTr,
                                descriptionEn = descriptionEn,
                                category = category,
                                categoryTr = categoryTr,
                                categoryEn = categoryEn,
                                originTr = originTr,
                                originEn = originEn,
                                usageTr = usageTr,
                                usageEn = usageEn,
                                healthEffectTr = healthEffectTr,
                                healthEffectEn = healthEffectEn,
                                halalStatus = halalStatus,
                                veganStatus = veganStatus,
                                vegetarianStatus = vegetarianStatus,
                                kosherStatus = kosherStatus,
                                harmfulLevel = harmfulLevel.toInt(),
                                unhealthyLevel = unhealthyLevel.toInt(),
                                risky = risky,
                                notesTr = notesTr,
                                notesEn = notesEn
                            )

                            if (additiveCode != null) {
                                viewModel.updateAdditive(additive)
                            } else {
                                viewModel.addAdditive(additive)
                            }

                            navController.popBackStack()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = "Kaydet"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (error != null) {
                Text(
                    text = error ?: "Bir hata oluştu",
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    // Kod
                    OutlinedTextField(
                        value = code,
                        onValueChange = { code = it },
                        label = { Text("Kod (E-kodu)") },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = additiveCode == null // Düzenleme modunda kod değiştirilemez
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // İsim (Türkçe)
                    OutlinedTextField(
                        value = nameTr,
                        onValueChange = { nameTr = it },
                        label = { Text("İsim (Türkçe)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // İsim (İngilizce)
                    OutlinedTextField(
                        value = nameEn,
                        onValueChange = { nameEn = it },
                        label = { Text("İsim (İngilizce)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Açıklama (Türkçe)
                    OutlinedTextField(
                        value = descriptionTr,
                        onValueChange = { descriptionTr = it },
                        label = { Text("Açıklama (Türkçe)") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 3
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Açıklama (İngilizce)
                    OutlinedTextField(
                        value = descriptionEn,
                        onValueChange = { descriptionEn = it },
                        label = { Text("Açıklama (İngilizce)") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 3
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kategori
                    CategoryDropdown(
                        selectedCategory = category,
                        onCategorySelected = { category = it }
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kategori (Türkçe)
                    OutlinedTextField(
                        value = categoryTr,
                        onValueChange = { categoryTr = it },
                        label = { Text("Kategori (Türkçe)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kategori (İngilizce)
                    OutlinedTextField(
                        value = categoryEn,
                        onValueChange = { categoryEn = it },
                        label = { Text("Kategori (İngilizce)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kaynak (Türkçe)
                    OutlinedTextField(
                        value = originTr,
                        onValueChange = { originTr = it },
                        label = { Text("Kaynak (Türkçe)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kaynak (İngilizce)
                    OutlinedTextField(
                        value = originEn,
                        onValueChange = { originEn = it },
                        label = { Text("Kaynak (İngilizce)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kullanım Amacı (Türkçe)
                    OutlinedTextField(
                        value = usageTr,
                        onValueChange = { usageTr = it },
                        label = { Text("Kullanım Amacı (Türkçe)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kullanım Amacı (İngilizce)
                    OutlinedTextField(
                        value = usageEn,
                        onValueChange = { usageEn = it },
                        label = { Text("Kullanım Amacı (İngilizce)") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Sağlık Etkileri (Türkçe)
                    OutlinedTextField(
                        value = healthEffectTr,
                        onValueChange = { healthEffectTr = it },
                        label = { Text("Sağlık Etkileri (Türkçe)") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 2
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Sağlık Etkileri (İngilizce)
                    OutlinedTextField(
                        value = healthEffectEn,
                        onValueChange = { healthEffectEn = it },
                        label = { Text("Sağlık Etkileri (İngilizce)") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 2
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Helal durumu
                    HalalStatusDropdown(
                        selectedStatus = halalStatus,
                        onStatusSelected = { halalStatus = it }
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Vegan durumu
                    VeganStatusDropdown(
                        selectedStatus = veganStatus,
                        onStatusSelected = { veganStatus = it }
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Vejetaryen durumu
                    OutlinedTextField(
                        value = vegetarianStatus,
                        onValueChange = { vegetarianStatus = it },
                        label = { Text("Vejetaryen Durumu") },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Koşer durumu
                    KosherStatusDropdown(
                        selectedStatus = kosherStatus,
                        onStatusSelected = { kosherStatus = it }
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Zararlılık seviyesi
                    Text(
                        text = "Zararlılık Seviyesi: ${harmfulLevel.toInt()}/5",
                        style = MaterialTheme.typography.titleMedium
                    )

                    Slider(
                        value = harmfulLevel,
                        onValueChange = { harmfulLevel = it },
                        valueRange = 0f..5f,
                        steps = 4,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Sağlıksızlık seviyesi
                    Text(
                        text = "Sağlıksızlık Seviyesi: ${unhealthyLevel.toInt()}/5",
                        style = MaterialTheme.typography.titleMedium
                    )

                    Slider(
                        value = unhealthyLevel,
                        onValueChange = { unhealthyLevel = it },
                        valueRange = 0f..5f,
                        steps = 4,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Riskli mi?
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Riskli mi?",
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.weight(1f)
                        )

                        Switch(
                            checked = risky,
                            onCheckedChange = { risky = it }
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Notlar (Türkçe)
                    OutlinedTextField(
                        value = notesTr,
                        onValueChange = { notesTr = it },
                        label = { Text("Notlar (Türkçe)") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 3
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Notlar (İngilizce)
                    OutlinedTextField(
                        value = notesEn,
                        onValueChange = { notesEn = it },
                        label = { Text("Notlar (İngilizce)") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 3
                    )

                    Spacer(modifier = Modifier.height(32.dp))

                    // Kaydet butonu
                    Button(
                        onClick = {
                            val additive = Additive(
                                id = code,
                                code = code,
                                nameTr = nameTr,
                                nameEn = nameEn,
                                descriptionTr = descriptionTr,
                                descriptionEn = descriptionEn,
                                category = category,
                                categoryTr = categoryTr,
                                categoryEn = categoryEn,
                                originTr = originTr,
                                originEn = originEn,
                                usageTr = usageTr,
                                usageEn = usageEn,
                                healthEffectTr = healthEffectTr,
                                healthEffectEn = healthEffectEn,
                                halalStatus = halalStatus,
                                veganStatus = veganStatus,
                                vegetarianStatus = vegetarianStatus,
                                kosherStatus = kosherStatus,
                                harmfulLevel = harmfulLevel.toInt(),
                                unhealthyLevel = unhealthyLevel.toInt(),
                                risky = risky,
                                notesTr = notesTr,
                                notesEn = notesEn
                            )

                            if (additiveCode != null) {
                                viewModel.updateAdditive(additive)
                            } else {
                                viewModel.addAdditive(additive)
                            }

                            navController.popBackStack()
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = if (additiveCode != null) "Güncelle" else "Ekle"
                        )
                    }
                }
            }
        }
    }
}

/**
 * Kategori dropdown
 */
@Composable
fun CategoryDropdown(
    selectedCategory: String,
    onCategorySelected: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "Kategori",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(4.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(
                onClick = { expanded = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = AdditiveCategory.values().find { it.name == selectedCategory }?.displayName ?: selectedCategory
                )
            }

            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                AdditiveCategory.values().forEach { category ->
                    DropdownMenuItem(
                        text = { Text(category.displayName) },
                        onClick = {
                            onCategorySelected(category.name)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * Helal durumu dropdown
 */
@Composable
fun HalalStatusDropdown(
    selectedStatus: String,
    onStatusSelected: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "Helal Durumu",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(4.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(
                onClick = { expanded = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = HalalStatus.values().find { it.name == selectedStatus }?.displayName ?: selectedStatus
                )
            }

            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                HalalStatus.values().forEach { status ->
                    DropdownMenuItem(
                        text = { Text(status.displayName) },
                        onClick = {
                            onStatusSelected(status.name)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * Vegan durumu dropdown
 */
@Composable
fun VeganStatusDropdown(
    selectedStatus: String,
    onStatusSelected: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "Vegan Durumu",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(4.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(
                onClick = { expanded = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = VeganStatus.values().find { it.name == selectedStatus }?.displayName ?: selectedStatus
                )
            }

            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                VeganStatus.values().forEach { status ->
                    DropdownMenuItem(
                        text = { Text(status.displayName) },
                        onClick = {
                            onStatusSelected(status.name)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * Koşer durumu dropdown
 */
@Composable
fun KosherStatusDropdown(
    selectedStatus: String,
    onStatusSelected: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "Koşer Durumu",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(4.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(
                onClick = { expanded = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = KosherStatus.values().find { it.name == selectedStatus }?.displayName ?: selectedStatus
                )
            }

            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                KosherStatus.values().forEach { status ->
                    DropdownMenuItem(
                        text = { Text(status.displayName) },
                        onClick = {
                            onStatusSelected(status.name)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}
