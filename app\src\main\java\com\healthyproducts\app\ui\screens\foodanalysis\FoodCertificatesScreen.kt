package com.healthyproducts.app.ui.screens.foodanalysis

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.FoodCertificate
import com.healthyproducts.app.data.model.FoodPreferenceType
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.ui.components.AddCustomItemDialog
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.viewmodel.CertificatesState
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.ImportStatus
import com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Gıda sertifikaları ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FoodCertificatesScreen(
    navController: NavController,
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }

    val certificatesState by foodAnalysisViewModel.certificatesState.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()
    val userFoodPreferencesState by foodAnalysisViewModel.userFoodPreferencesState.collectAsState()
    val selectedFoodPreferences by foodAnalysisViewModel.selectedFoodPreferences.collectAsState()

    // Kullanıcı ID'si
    val userState by userViewModel.userState.collectAsState()
    val userId = if (userState is UserViewModel.UserState.LoggedIn) {
        (userState as UserViewModel.UserState.LoggedIn).user.id
    } else {
        ""
    }

    // Dil tercihi
    val language = SupportedLanguage.fromCode(userPreferences.language)

    // Arama metni
    var searchText by remember { mutableStateOf("") }

    // Özel sertifika ekleme diyaloğu
    var showAddCustomDialog by remember { mutableStateOf(false) }

    // Sertifikaları yükle
    LaunchedEffect(Unit) {
        foodAnalysisViewModel.loadFoodCertificates()
    }

    // Kullanıcı tercihlerini yükle (kullanıcı ID'si değiştiğinde)
    LaunchedEffect(userId) {
        if (userId.isNotEmpty()) {
            foodAnalysisViewModel.loadUserFoodPreferencesByType(userId, FoodPreferenceType.CERTIFICATE)
        }
    }

    // İçeri aktarma durumunu izle
    val importStatus by foodAnalysisViewModel.importStatus.collectAsState()

    // Admin kullanıcısı mı?
    val isAdmin = true // TODO: Gerçek admin kontrolü eklenecek

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.food_certificates)) },
                navigationIcon = { BackButton(navController) },
                actions = {
                    // Admin için içe aktarma butonu
                    if (isAdmin) {
                        val importDataText = stringResource(R.string.import_data)
                        val context = LocalContext.current
                        IconButton(
                            onClick = {
                                foodAnalysisViewModel.importCertificatesFromJson(context)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = importDataText
                            )
                        }
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        floatingActionButton = {
            // Kullanıcı giriş yapmışsa özel sertifika ekleme butonu göster
            if (userId.isNotEmpty()) {
                FloatingActionButton(
                    onClick = {
                        // Özel sertifika ekleme diyaloğunu göster
                        showAddCustomDialog = true
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(R.string.add_custom_certificate)
                    )
                }
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
        ) {
            // İçeri aktarma durumu
            when (importStatus) {
                is ImportStatus.Loading -> {
                    LinearProgressIndicator(modifier = Modifier.fillMaxWidth())
                    Spacer(modifier = Modifier.height(8.dp))
                }
                is ImportStatus.Success -> {
                    val count = (importStatus as ImportStatus.Success).count
                    Text(
                        text = stringResource(R.string.import_success, count),
                        color = MaterialTheme.colorScheme.primary,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    // Başarılı içe aktarma durumunu 3 saniye sonra sıfırla
                    LaunchedEffect(importStatus) {
                        delay(3000)
                        foodAnalysisViewModel.resetImportStatus()
                    }
                }
                is ImportStatus.Error -> {
                    Text(
                        text = stringResource(R.string.import_error, (importStatus as ImportStatus.Error).message),
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                else -> { /* Initial durumunda bir şey gösterme */ }
            }

            // Arama alanı
            OutlinedTextField(
                value = searchText,
                onValueChange = { searchText = it },
                modifier = Modifier.fillMaxWidth(),
                label = { Text(stringResource(R.string.search)) },
                leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Sertifikalar listesi
            when (val state = certificatesState) {
                is CertificatesState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }

                is CertificatesState.Success -> {
                    val certificates = state.certificates

                    // Arama filtresine göre sertifikaları filtrele
                    val filteredCertificates = if (searchText.isBlank()) {
                        certificates
                    } else {
                        certificates.filter { certificate ->
                            certificate.getName(language).contains(searchText, ignoreCase = true) ||
                            certificate.certificateId.contains(searchText, ignoreCase = true)
                        }
                    }

                    if (filteredCertificates.isEmpty()) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.no_results),
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    } else {
                        LazyColumn(
                            contentPadding = PaddingValues(vertical = 8.dp)
                        ) {
                            items(filteredCertificates) { certificate ->
                                FoodCertificateItem(
                                    certificate = certificate,
                                    language = language,
                                    isSelected = selectedFoodPreferences[certificate.certificateId] ?: false,
                                    onCheckedChange = { isChecked ->
                                        if (userId.isNotEmpty()) {
                                            if (isChecked) {
                                                // Sertifika tercihini kaydet
                                                foodAnalysisViewModel.saveUserFoodPreference(
                                                    userId = userId,
                                                    type = FoodPreferenceType.CERTIFICATE,
                                                    itemId = certificate.certificateId,
                                                    itemName = certificate.getName(language)
                                                )
                                            } else {
                                                // Sertifika tercihini bul ve sil
                                                val preference = (userFoodPreferencesState as? UserFoodPreferencesState.Success)?.preferences?.find {
                                                    it.itemId == certificate.certificateId && it.type == FoodPreferenceType.CERTIFICATE.value
                                                }

                                                if (preference != null) {
                                                    foodAnalysisViewModel.deleteUserFoodPreference(
                                                        preferenceId = preference.id,
                                                        userId = userId,
                                                        type = FoodPreferenceType.CERTIFICATE
                                                    )
                                                }
                                            }
                                        } else {
                                            // Kullanıcı giriş yapmamışsa uyarı göster
                                            scope.launch {
                                                snackbarHostState.showSnackbar("Tercih eklemek için giriş yapmalısınız")
                                            }
                                        }
                                    },
                                    onClick = {
                                        // Sertifika detay sayfasına git
                                        navController.navigate("food_certificate_detail/${certificate.certificateId}")
                                    }
                                )
                            }
                        }
                    }
                }

                is CertificatesState.Error -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = state.message,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }

        // Özel sertifika ekleme diyaloğu
        if (showAddCustomDialog) {
            AddCustomItemDialog(
                title = stringResource(R.string.add_custom_certificate),
                onDismiss = { showAddCustomDialog = false },
                onConfirm = { certificateName ->
                    scope.launch {
                        foodAnalysisViewModel.addCustomFoodPreference(userId, FoodPreferenceType.CERTIFICATE, certificateName)
                        snackbarHostState.showSnackbar("Özel sertifika eklendi: $certificateName")
                    }
                }
            )
        }
    }
}

/**
 * Gıda sertifikası öğesi
 */
@Composable
fun FoodCertificateItem(
    certificate: FoodCertificate,
    language: SupportedLanguage,
    isSelected: Boolean = false,
    onCheckedChange: (Boolean) -> Unit = {},
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable(onClick = onClick)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Checkbox
            Checkbox(
                checked = isSelected,
                onCheckedChange = onCheckedChange,
                modifier = Modifier.padding(end = 8.dp)
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                // Emoji veya ikon
                if (certificate.symbol.isNotEmpty()) {
                    Text(
                        text = certificate.symbol,
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                }

                Column {
                    Text(
                        text = certificate.getName(language),
                        style = MaterialTheme.typography.titleMedium
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    // Sertifika türü
                    val certificateType = if (certificate.functionalType.isNotEmpty()) {
                        certificate.functionalType.joinToString(", ") { it.replaceFirstChar { c -> c.uppercase() } }
                    } else {
                        ""
                    }

                    Text(
                        text = certificateType,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = certificate.getDescription(language),
                        style = MaterialTheme.typography.bodySmall,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Sertifika türü göstergesi (ilk fonksiyonel tür)
            if (certificate.functionalType.isNotEmpty()) {
                CertificateTypeIndicator(certificateType = certificate.functionalType.first())
            }
        }
    }
}

/**
 * Sertifika türü göstergesi
 */
@Composable
fun CertificateTypeIndicator(certificateType: String) {
    val color = when (certificateType) {
        "vegan" -> MaterialTheme.colorScheme.primary
        "halal" -> MaterialTheme.colorScheme.secondary
        "kosher" -> MaterialTheme.colorScheme.tertiary
        "organic" -> MaterialTheme.colorScheme.primary
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }

    Box(
        modifier = Modifier
            .width(24.dp)
            .height(24.dp)
            .padding(2.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = certificateType.first().uppercase(),
            style = MaterialTheme.typography.labelMedium,
            color = color
        )
    }
}
