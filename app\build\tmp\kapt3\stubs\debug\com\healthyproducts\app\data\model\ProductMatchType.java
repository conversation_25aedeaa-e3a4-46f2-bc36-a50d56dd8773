package com.healthyproducts.app.data.model;

/**
 * <PERSON><PERSON><PERSON><PERSON>
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/healthyproducts/app/data/model/ProductMatchType;", "", "(Ljava/lang/String;I)V", "EXACT_BARCODE", "SIMILAR_INGREDIENTS", "SAME_BRAND", "app_debug"})
public enum ProductMatchType {
    /*public static final*/ EXACT_BARCODE /* = new EXACT_BARCODE() */,
    /*public static final*/ SIMILAR_INGREDIENTS /* = new SIMILAR_INGREDIENTS() */,
    /*public static final*/ SAME_BRAND /* = new SAME_BRAND() */;
    
    ProductMatchType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.healthyproducts.app.data.model.ProductMatchType> getEntries() {
        return null;
    }
}