package com.healthyproducts.app.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName

/**
 * Gıda intoleransını temsil eden veri sınıfı (Gluten/Laktoz Hassasiyeti)
 */
data class Intolerance(
    @DocumentId
    val id: String = "",

    @get:PropertyName("id")
    @set:PropertyName("id")
    var intoleranceId: String = "", // Benzersiz tanımlayıcı (örn. "gluten", "lactose")

    @get:PropertyName("name_tr")
    @set:PropertyName("name_tr")
    var nameTr: String = "", // İntoleransın Türkçe adı

    @get:PropertyName("name_en")
    @set:PropertyName("name_en")
    var nameEn: String = "", // İntoleransın İngilizce adı

    @get:PropertyName("symbol")
    @set:PropertyName("symbol")
    var symbol: String = "", // Emoji veya ikon referansı

    @get:PropertyName("description_tr")
    @set:PropertyName("description_tr")
    var descriptionTr: String = "", // İntoleransın Türkçe açıklaması

    @get:PropertyName("description_en")
    @set:PropertyName("description_en")
    var descriptionEn: String = "", // İntoleransın İngilizce açıklaması

    @get:PropertyName("related_ingredients_tr")
    @set:PropertyName("related_ingredients_tr")
    var relatedIngredientsTr: List<String> = listOf(), // Türkçe kaçınılması gereken içerikler

    @get:PropertyName("related_ingredients_en")
    @set:PropertyName("related_ingredients_en")
    var relatedIngredientsEn: List<String> = listOf(), // İngilizce kaçınılması gereken içerikler

    @get:PropertyName("risk_level")
    @set:PropertyName("risk_level")
    var riskLevel: Int = 0, // Risk seviyesi (1-3)

    @get:PropertyName("labels_to_look_for_tr")
    @set:PropertyName("labels_to_look_for_tr")
    var labelsToLookForTr: List<String> = listOf(), // Türkçe yardımcı etiketler

    @get:PropertyName("labels_to_look_for_en")
    @set:PropertyName("labels_to_look_for_en")
    var labelsToLookForEn: List<String> = listOf(), // İngilizce yardımcı etiketler

    @get:PropertyName("notes_tr")
    @set:PropertyName("notes_tr")
    var notesTr: String = "", // Türkçe ek notlar

    @get:PropertyName("notes_en")
    @set:PropertyName("notes_en")
    var notesEn: String = "" // İngilizce ek notlar
) {
    // Firestore için boş constructor
    constructor() : this("", "", "", "", "", "", "", listOf(), listOf(), 0, listOf(), listOf(), "", "")

    // Kullanıcının dil tercihine göre adı döndürür
    fun getName(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> nameTr.ifEmpty { nameEn }
            SupportedLanguage.ENGLISH -> nameEn.ifEmpty { nameTr }
        }
    }

    // Kullanıcının dil tercihine göre açıklamayı döndürür
    fun getDescription(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> descriptionTr.ifEmpty { descriptionEn }
            SupportedLanguage.ENGLISH -> descriptionEn.ifEmpty { descriptionTr }
        }
    }

    // Kullanıcının dil tercihine göre ilgili içerikleri döndürür
    fun getRelatedIngredients(language: SupportedLanguage): List<String> {
        return when (language) {
            SupportedLanguage.TURKISH -> relatedIngredientsTr.ifEmpty { relatedIngredientsEn }
            SupportedLanguage.ENGLISH -> relatedIngredientsEn.ifEmpty { relatedIngredientsTr }
        }
    }

    // Kullanıcının dil tercihine göre bakılacak etiketleri döndürür
    fun getLabelsToLookFor(language: SupportedLanguage): List<String> {
        return when (language) {
            SupportedLanguage.TURKISH -> labelsToLookForTr.ifEmpty { labelsToLookForEn }
            SupportedLanguage.ENGLISH -> labelsToLookForEn.ifEmpty { labelsToLookForTr }
        }
    }

    // Kullanıcının dil tercihine göre notları döndürür
    fun getNotes(language: SupportedLanguage): String {
        return when (language) {
            SupportedLanguage.TURKISH -> notesTr.ifEmpty { notesEn }
            SupportedLanguage.ENGLISH -> notesEn.ifEmpty { notesTr }
        }
    }
}
