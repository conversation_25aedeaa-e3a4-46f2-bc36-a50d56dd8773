package com.healthyproducts.app.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.google.firebase.auth.FirebaseAuth
import com.healthyproducts.app.ui.auth.LoginScreen
import com.healthyproducts.app.ui.auth.RegisterScreen
import com.healthyproducts.app.ui.screens.favorites.FavoritesScreen
import com.healthyproducts.app.ui.screens.home.HomeScreen
import com.healthyproducts.app.ui.screens.product.ProductDetailScreen
import com.healthyproducts.app.ui.screens.profile.ProfileScreen
import com.healthyproducts.app.ui.screens.scan.ScanScreen
import com.healthyproducts.app.ui.screens.additives.AdditiveDetailScreen
import com.healthyproducts.app.ui.screens.additives.AdditiveEditScreen
import com.healthyproducts.app.ui.screens.additives.AdditivesScreen
import com.healthyproducts.app.ui.screens.admin.DataUploadScreen
import com.healthyproducts.app.ui.screens.foodanalysis.FoodAnalysisScreen
import com.healthyproducts.app.ui.screens.foodanalysis.PreservativeDetailScreen
import com.healthyproducts.app.ui.screens.foodanalysis.PreservativesScreen
import com.healthyproducts.app.ui.screens.settings.AiModelSelectionScreen
import com.healthyproducts.app.ui.screens.settings.LanguageScreen
import com.healthyproducts.app.ui.screens.settings.SettingsScreen
import com.healthyproducts.app.ui.screens.settings.UserFoodPreferencesScreen

/**
 * Uygulama içi navigasyon rotalarını tanımlayan sealed class
 */
sealed class Screen(val route: String) {
    object Home : Screen("home")
    object Scan : Screen("scan")
    object Favorites : Screen("favorites")
    object Profile : Screen("profile")
    object Settings : Screen("settings")
    object Login : Screen("login")
    object Register : Screen("register")
    object ScanHistory : Screen("scan_history")
    object Language : Screen("language")
    object AiModelSelection : Screen("ai_model_selection")
    object UserFoodPreferences : Screen("user_food_preferences")
    object Additives : Screen("additives")
    object AdditiveDetail : Screen("additive_detail/{additiveCode}") {
        fun createRoute(additiveCode: String) = "additive_detail/$additiveCode"
    }
    object AdditiveEdit : Screen("additive_edit?additiveCode={additiveCode}") {
        fun createRoute(additiveCode: String? = null) = if (additiveCode != null) {
            "additive_edit?additiveCode=$additiveCode"
        } else {
            "additive_edit"
        }
    }
    object ProductDetail : Screen("product_detail/{productId}") {
        fun createRoute(productId: String) = "product_detail/$productId"
    }
    object DataUpload : Screen("data_upload")

    // Gıda analizi ekranları
    object Allergens : Screen("allergens")
    object AllergenDetail : Screen("allergen_detail/{allergenCode}") {
        fun createRoute(allergenCode: String) = "allergen_detail/$allergenCode"
    }
    object Fats : Screen("fats")
    object FatDetail : Screen("fat_detail/{fatCode}") {
        fun createRoute(fatCode: String) = "fat_detail/$fatCode"
    }
    object Sugars : Screen("sugars")
    object SugarDetail : Screen("sugar_detail/{sugarCode}") {
        fun createRoute(sugarCode: String) = "sugar_detail/$sugarCode"
    }
    object FoodCertificates : Screen("food_certificates")
    object FoodCertificateDetail : Screen("food_certificate_detail/{certificateCode}") {
        fun createRoute(certificateCode: String) = "food_certificate_detail/$certificateCode"
    }
    object Preservatives : Screen("preservatives")
    object PreservativeDetail : Screen("preservative_detail/{preservativeCode}") {
        fun createRoute(preservativeCode: String) = "preservative_detail/$preservativeCode"
    }
    object FoodAnalysis : Screen("food_analysis?ingredients={ingredients}") {
        fun createRoute(ingredients: String = "") = "food_analysis?ingredients=$ingredients"
    }
}

/**
 * Uygulama navigasyonunu yöneten NavHost
 */
@Composable
fun AppNavigation(navController: NavHostController) {
    // Kullanıcının giriş durumunu kontrol et
    val auth = FirebaseAuth.getInstance()
    val startDestination = if (auth.currentUser != null) {
        Screen.Home.route
    } else {
        Screen.Login.route
    }

    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Ana ekranlar
        composable(Screen.Home.route) {
            HomeScreen(navController = navController)
        }

        composable(Screen.Scan.route) {
            ScanScreen(navController = navController)
        }

        composable(Screen.Favorites.route) {
            FavoritesScreen(navController = navController)
        }

        composable(Screen.Profile.route) {
            ProfileScreen(navController = navController)
        }

        composable(Screen.Settings.route) {
            SettingsScreen(navController = navController)
        }

        // Kimlik doğrulama ekranları
        composable(Screen.Login.route) {
            LoginScreen(
                onNavigateToRegister = { navController.navigate(Screen.Register.route) },
                onLoginSuccess = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.Login.route) { inclusive = true }
                }}
            )
        }

        composable(Screen.Register.route) {
            RegisterScreen(
                onNavigateBack = { navController.popBackStack() },
                onRegisterSuccess = { navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.Register.route) { inclusive = true }
                }}
            )
        }

        // Dil seçimi ekranı
        composable(Screen.Language.route) {
            LanguageScreen(navController = navController)
        }

        // AI model seçimi ekranı
        composable(Screen.AiModelSelection.route) {
            AiModelSelectionScreen(navController = navController)
        }

        // Kullanıcı gıda tercihleri ekranı
        composable(Screen.UserFoodPreferences.route) {
            UserFoodPreferencesScreen(navController = navController)
        }

        // Katkı maddeleri ekranı
        composable(Screen.Additives.route) {
            AdditivesScreen(navController = navController)
        }

        // Katkı maddesi detay ekranı
        composable(
            route = Screen.AdditiveDetail.route,
            arguments = listOf(
                navArgument("additiveCode") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val additiveCode = backStackEntry.arguments?.getString("additiveCode") ?: ""
            AdditiveDetailScreen(
                navController = navController,
                additiveCode = additiveCode
            )
        }

        // Katkı maddesi ekleme/düzenleme ekranı
        composable(
            route = Screen.AdditiveEdit.route,
            arguments = listOf(
                navArgument("additiveCode") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val additiveCode = backStackEntry.arguments?.getString("additiveCode")
            AdditiveEditScreen(
                navController = navController,
                additiveCode = additiveCode
            )
        }

        // Tarama geçmişi ekranı
        composable(Screen.ScanHistory.route) {
            // ScanHistoryScreen(navController = navController)
            // Not: ScanHistoryScreen henüz oluşturulmadı
        }

        // Ürün detay sayfası için parametre alan rota
        composable(Screen.ProductDetail.route) { backStackEntry ->
            val productId = backStackEntry.arguments?.getString("productId") ?: ""
            ProductDetailScreen(productId = productId, navController = navController)
        }

        // Veri yükleme ekranı
        composable(Screen.DataUpload.route) {
            DataUploadScreen(navController = navController)
        }

        // Alerjenler ekranı
        composable(Screen.Allergens.route) {
            com.healthyproducts.app.ui.screens.foodanalysis.AllergensScreen(navController = navController)
        }

        // Alerjen detay ekranı
        composable(
            route = Screen.AllergenDetail.route,
            arguments = listOf(
                navArgument("allergenCode") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val allergenCode = backStackEntry.arguments?.getString("allergenCode") ?: ""
            com.healthyproducts.app.ui.screens.foodanalysis.AllergenDetailScreen(
                navController = navController,
                allergenCode = allergenCode
            )
        }

        // Yağlar ekranı
        composable(Screen.Fats.route) {
            com.healthyproducts.app.ui.screens.foodanalysis.FatsScreen(navController = navController)
        }

        // Yağ detay ekranı
        composable(
            route = Screen.FatDetail.route,
            arguments = listOf(
                navArgument("fatCode") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val fatCode = backStackEntry.arguments?.getString("fatCode") ?: ""
            com.healthyproducts.app.ui.screens.foodanalysis.FatDetailScreen(
                navController = navController,
                fatCode = fatCode
            )
        }

        // Şekerler ekranı
        composable(Screen.Sugars.route) {
            com.healthyproducts.app.ui.screens.foodanalysis.SugarsScreen(navController = navController)
        }

        // Şeker detay ekranı
        composable(
            route = Screen.SugarDetail.route,
            arguments = listOf(
                navArgument("sugarCode") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val sugarCode = backStackEntry.arguments?.getString("sugarCode") ?: ""
            com.healthyproducts.app.ui.screens.foodanalysis.SugarDetailScreen(
                navController = navController,
                sugarCode = sugarCode
            )
        }

        // Gıda sertifikaları ekranı
        composable(Screen.FoodCertificates.route) {
            com.healthyproducts.app.ui.screens.foodanalysis.FoodCertificatesScreen(navController = navController)
        }

        // Gıda sertifikası detay ekranı
        composable(
            route = Screen.FoodCertificateDetail.route,
            arguments = listOf(
                navArgument("certificateCode") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val certificateCode = backStackEntry.arguments?.getString("certificateCode") ?: ""
            com.healthyproducts.app.ui.screens.foodanalysis.FoodCertificateDetailScreen(
                navController = navController,
                certificateCode = certificateCode
            )
        }

        // Koruyucular ekranı
        composable(Screen.Preservatives.route) {
            PreservativesScreen(navController = navController)
        }

        // Koruyucu detay ekranı
        composable(
            route = Screen.PreservativeDetail.route,
            arguments = listOf(
                navArgument("preservativeCode") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val preservativeCode = backStackEntry.arguments?.getString("preservativeCode") ?: ""
            PreservativeDetailScreen(
                navController = navController,
                preservativeId = preservativeCode
            )
        }

        // Gıda analizi ekranı
        composable(
            route = Screen.FoodAnalysis.route,
            arguments = listOf(
                navArgument("ingredients") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = ""
                }
            )
        ) { backStackEntry ->
            val ingredients = backStackEntry.arguments?.getString("ingredients") ?: ""
            FoodAnalysisScreen(
                navController = navController,
                ingredients = ingredients
            )
        }
    }
}
