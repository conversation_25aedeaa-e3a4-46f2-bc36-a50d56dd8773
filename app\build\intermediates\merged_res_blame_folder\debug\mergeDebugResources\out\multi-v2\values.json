{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values/values.xml", "map": [{"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "68,110,249,214,218,215,219,217,216,46,115,112,114,109,116,119,118,113,121,120,117,108,242,66,67,144,140,243,239,238,105,237,195,1,208,103,256,255,21,233,23,95,192,147,41,246,171,172,173,170,164,85,104,200,82,235,231,227,220,60,131,135,130,134,96,149,154,97,111,29,80,193,100,145,141,6,18,205,204,143,175,174,32,199,148,168,167,169,24,42,44,166,241,152,4,178,210,183,179,181,182,180,184,196,40,31,36,236,30,28,157,59,127,124,126,125,99,77,222,86,79,198,15,83,234,226,201,17,163,162,27,22,102,150,81,197,224,223,189,188,187,245,69,252,39,257,251,258,254,253,7,248,230,11,78,87,155,47,194,101,209,153,94,221,250,5,14,12,84,13,26,25,98,211,232,8,74,72,73,61,64,63,65,62,35,34,33,50,51,55,160,161,54,159,52,56,53,158,146,142,244,247,45,190,70,191,137,165,136,132,133,151,225,240,43,156,71,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3420,5216,12925,10716,10980,10784,11054,10904,10842,2317,5499,5326,5438,5156,5554,5745,5680,5382,5879,5810,5617,5108,12517,3311,3357,6988,6796,12573,12338,12275,5040,12200,9773,16,10450,4962,13363,13278,827,11868,923,4637,9611,7152,2013,12743,8513,8587,8659,8453,8094,4220,5000,10045,4066,12079,11746,11598,11114,2909,6273,6585,6208,6510,4679,7280,7522,4721,5270,1360,3980,9661,4848,7048,6844,178,745,10277,10214,6924,8797,8733,1602,9985,7218,8339,8276,8397,1005,2067,2175,8220,12453,7414,102,8882,10560,9198,8944,9052,9122,8996,9266,9823,1961,1530,1790,12141,1457,1286,7714,2863,6120,5951,6057,6004,4801,3852,11238,4276,3938,9937,562,4128,12009,11534,10105,688,8038,7998,1197,875,4928,7332,4020,9889,11393,11316,9469,9401,9345,12679,3460,13085,1901,13431,13031,13499,13210,13141,226,12857,11696,342,3892,4340,7584,2379,9729,4888,10500,7472,4599,11176,12977,140,512,396,4166,450,1116,1067,4759,10612,11806,270,3768,3624,3700,2957,3163,3093,3235,3025,1745,1698,1653,2481,2528,2734,7892,7941,2679,7827,2575,2785,2632,7770,7098,6882,12629,12805,2243,9523,3518,9571,6713,8160,6652,6390,6449,7374,11464,12397,2121,7650,3580,616", "endColumns": "39,53,51,67,73,57,59,75,61,61,54,55,60,59,62,64,64,55,48,68,62,47,55,45,62,59,47,55,58,62,43,74,49,53,49,37,67,84,47,140,81,41,49,65,53,61,73,71,73,59,65,55,39,59,61,61,59,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,63,57,37,61,51,67,51,69,75,55,50,65,51,71,81,58,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,69,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,63,57,55,59,67,53,88,67,68,43,67,49,53,45,69,65,71,43,39,59,49,37,61,53,37,49,53,53,61,80,48,41,67,61,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,49,51,73,47,61,39,54,59,60,58,60,39,69,55,53,63,43,71", "endOffsets": "3455,5265,12972,10779,11049,10837,11109,10975,10899,2374,5549,5377,5494,5211,5612,5805,5740,5433,5923,5874,5675,5151,12568,3352,3415,7043,6839,12624,12392,12333,5079,12270,9818,65,10495,4995,13426,13358,870,12004,1000,4674,9656,7213,2062,12800,8582,8654,8728,8508,8155,4271,5035,10100,4123,12136,11801,11645,11171,2952,6385,6647,6268,6580,4716,7327,7579,4754,5321,1452,4015,9724,4883,7093,6877,221,796,10413,10272,6983,8856,8792,1648,10040,7275,8392,8334,8448,1062,2116,2238,8271,12512,7467,135,8939,10607,9261,8991,9117,9193,9047,9312,9884,2008,1597,1867,12195,1525,1355,7765,2904,6177,5999,6115,6052,4843,3887,11311,4335,3975,9980,611,4161,12074,11593,10174,740,8089,8033,1281,918,4957,7369,4061,9932,11459,11388,9518,9464,9396,12738,3513,13136,1956,13494,13080,13583,13273,13205,265,12920,11741,391,3933,4405,7645,2446,9768,4923,10555,7517,4632,11233,13026,173,557,445,4215,507,1192,1111,4796,10675,11863,311,3825,3695,3763,3020,3230,3158,3306,3088,1785,1740,1693,2523,2570,2780,7936,7993,2729,7887,2627,2835,2674,7822,7147,6919,12674,12852,2312,9566,3575,9606,6763,8215,6708,6444,6505,7409,11529,12448,2170,7709,3619,683"}, "to": {"startLines": "386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,419,420,421,422,423,424,425,433,434,435,436,437,438,439,440,441,442,443,444,445,446,488,491,492,494,495,496,497,498,499,500,501,505,506,507,509,510,511,512,513,514,518,519,520,521,522,523,524,525,526,527,528,529,531,532,533,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,634,635,636,637,639,640,641,642,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,694,695,696,697,698,699,700,701,702,703,704,705,706,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,728,731,734,735,736,737,738,739,740,741,742,743,744,745,746", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24876,24916,24970,25022,25090,25164,25222,25282,25358,25420,25482,25537,25593,25654,25714,25777,25842,25907,25963,26012,26081,26144,26192,26248,26294,26357,26417,26465,26521,26580,26643,26687,26845,26895,26949,26999,27037,27105,27190,27749,27890,27972,28014,28064,28130,28184,28246,28320,28392,28466,28526,28592,28648,33291,33492,33554,33684,33744,33796,33858,33906,34023,34090,34155,34541,34583,34635,34755,34793,34849,34946,34986,35054,35309,35359,35397,35445,35501,35642,35705,35769,35833,35897,35948,36008,36152,36210,36273,36775,36837,36891,36959,37015,37079,37137,37175,37237,37289,37357,37409,37479,37555,37611,37774,37840,37892,37964,38046,38105,38178,38252,38308,38354,38416,38469,38532,38585,38632,38672,38750,38814,43297,43345,43399,43437,43569,43633,43707,43764,43874,43914,44003,44051,44085,44127,44173,44221,44292,44369,44423,44491,44547,44611,44669,44725,44785,44853,44907,44996,45064,45133,45363,45431,45481,45535,45581,45651,45717,45789,45833,45873,45933,45983,46021,46083,46137,46175,46225,46279,46333,46395,46476,46525,46567,46734,46796,46842,46904,46980,47048,47116,47188,47258,47334,47402,47447,47494,47738,47785,47832,47883,47932,47989,48044,48109,48166,48221,48268,48325,48379,48421,48471,48523,48644,48818,48991,49031,49086,49146,49207,49266,49327,49367,49437,49493,49547,49611,49655", "endColumns": "39,53,51,67,73,57,59,75,61,61,54,55,60,59,62,64,64,55,48,68,62,47,55,45,62,59,47,55,58,62,43,74,49,53,49,37,67,84,47,140,81,41,49,65,53,61,73,71,73,59,65,55,39,59,61,61,59,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,63,57,37,61,51,67,51,69,75,55,50,65,51,71,81,58,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,69,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,63,57,55,59,67,53,88,67,68,43,67,49,53,45,69,65,71,43,39,59,49,37,61,53,37,49,53,53,61,80,48,41,67,61,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,49,51,73,47,61,39,54,59,60,58,60,39,69,55,53,63,43,71", "endOffsets": "24911,24965,25017,25085,25159,25217,25277,25353,25415,25477,25532,25588,25649,25709,25772,25837,25902,25958,26007,26076,26139,26187,26243,26289,26352,26412,26460,26516,26575,26638,26682,26757,26890,26944,26994,27032,27100,27185,27233,27885,27967,28009,28059,28125,28179,28241,28315,28387,28461,28521,28587,28643,28683,33346,33549,33611,33739,33791,33853,33901,34018,34085,34150,34225,34578,34630,34692,34788,34844,34941,34981,35049,35089,35354,35392,35440,35496,35637,35700,35764,35828,35892,35943,36003,36065,36205,36268,36324,36832,36886,36954,37010,37074,37132,37170,37232,37284,37352,37404,37474,37550,37606,37657,37835,37887,37959,38041,38100,38173,38247,38303,38349,38411,38464,38527,38580,38627,38667,38745,38809,38851,43340,43394,43432,43502,43628,43702,43759,43815,43909,43998,44046,44080,44122,44168,44216,44287,44364,44418,44486,44542,44606,44664,44720,44780,44848,44902,44991,45059,45128,45172,45426,45476,45530,45576,45646,45712,45784,45828,45868,45928,45978,46016,46078,46132,46170,46220,46274,46328,46390,46471,46520,46562,46630,46791,46837,46899,46975,47043,47111,47183,47253,47329,47397,47442,47489,47534,47780,47827,47878,47927,47984,48039,48104,48161,48216,48263,48320,48374,48416,48466,48518,48592,48687,48875,49026,49081,49141,49202,49261,49322,49362,49432,49488,49542,49606,49650,49722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\981fd9293439b18a98e73358fb60a309\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2306,3106,3112", "startColumns": "4,4,4,4", "startOffsets": "202,154737,183009,183220", "endLines": "3,2308,3111,3195", "endColumns": "60,12,24,24", "endOffsets": "258,154877,183215,187731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,426,427,428,429,430,431,432,710,2144,2145,2150,2153,2158,2304,2305,3006,3068,3214,3247,3277,3310", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "564,1590,1662,3001,3066,6278,6347,15105,15175,15243,15315,15385,15446,15520,16377,16438,16499,16561,16625,16687,16748,16816,16916,16976,17042,17115,17184,17241,17293,18020,18092,18168,18233,18292,18351,18411,18471,18531,18591,18651,18711,18771,18831,18891,18951,19010,19070,19130,19190,19250,19310,19370,19430,19490,19550,19610,19669,19729,19789,19848,19907,19966,20025,20084,20902,20937,21341,21396,21459,21514,21572,21630,21691,21754,21811,21862,21912,21973,22030,22096,22130,22165,22940,27238,27305,27377,27446,27515,27589,27661,47667,142071,142188,142455,142748,143015,154598,154670,178854,181351,188308,190039,191039,191721", "endLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,426,427,428,429,430,431,432,710,2144,2148,2150,2156,2158,2304,2305,3011,3077,3246,3267,3309,3315", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "619,1657,1745,3061,3127,6342,6405,15170,15238,15310,15380,15441,15515,15588,16433,16494,16556,16620,16682,16743,16811,16911,16971,17037,17110,17179,17236,17288,17350,18087,18163,18228,18287,18346,18406,18466,18526,18586,18646,18706,18766,18826,18886,18946,19005,19065,19125,19185,19245,19305,19365,19425,19485,19545,19605,19664,19724,19784,19843,19902,19961,20020,20079,20138,20932,20967,21391,21454,21509,21567,21625,21686,21749,21806,21857,21907,21968,22025,22091,22125,22160,22195,23005,27300,27372,27441,27510,27584,27656,27744,47733,142183,142384,142560,142944,143139,154665,154732,179052,181647,190034,190715,191716,191883"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "36,108,109,110,121,122,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "2069,7087,7134,7181,7925,7970,8136", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2106,7129,7176,7223,7965,8010,8173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ccfdf8fe6da9c4c34a4246aecbf96939\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "322,2458,3558,3561", "startColumns": "4,4,4,4", "startOffsets": "20972,160944,199942,200057", "endLines": "322,2464,3560,3563", "endColumns": "52,24,24,24", "endOffsets": "21020,161243,200052,200167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "310,312,313,316,318,351,447,448,502,503,508,553,554,638,643,667,668,693,707,708,709,727,729,730,1850,1866,1869", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20348,20472,20530,20708,20793,22503,28688,28753,34230,34296,34697,37662,37714,43507,43820,45259,45309,46688,47539,47585,47627,48597,48692,48728,119899,120879,120990", "endLines": "310,312,313,316,318,351,447,448,502,503,508,553,554,638,643,667,668,693,707,708,709,727,729,730,1852,1868,1872", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "20417,20525,20580,20754,20843,22551,28748,28802,34291,34392,34750,37709,37769,43564,43869,45304,45358,46729,47580,47622,47662,48639,48723,48813,120006,120985,121180"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "92", "endOffsets": "144"}, "to": {"startLines": "2196", "startColumns": "4", "startOffsets": "145847", "endColumns": "91", "endOffsets": "145934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c36e1f051d57c59ccd6930c68000c21a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "346", "startColumns": "4", "startOffsets": "22232", "endColumns": "42", "endOffsets": "22270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25ad31b9d5ee235be384e44c485c1169\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2475,2491,2497,3655,3671", "startColumns": "4,4,4,4,4", "startOffsets": "161765,162190,162368,202904,203315", "endLines": "2490,2496,2506,3670,3674", "endColumns": "24,24,24,24,24", "endOffsets": "162185,162363,162647,203310,203437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\167b5a10bb8d261a1d1c21747e97c5f2\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "99,259,260,261,262,2149,2151,2152,2157,2159", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6410,17355,17408,17461,17514,142389,142565,142687,142949,143144", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "6494,17403,17456,17509,17562,142450,142682,142743,143010,143206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\14d03a95cc07898beae69a352dd60254\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "349", "startColumns": "4", "startOffsets": "22389", "endColumns": "49", "endOffsets": "22434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,692,747,748,749,750,751,752,760,761,765,769,780,785,791,798,802,806,811,815,819,823,827,831,835,841,845,851,855,861,865,870,874,877,881,887,891,897,901,907,910,914,918,922,926,930,931,932,933,936,939,942,945,949,950,951,952,953,956,958,960,962,967,968,972,978,982,983,985,997,998,1002,1008,1012,1013,1014,1018,1045,1049,1050,1054,1082,1254,1280,1451,1477,1508,1516,1522,1538,1560,1565,1570,1580,1589,1598,1602,1609,1628,1635,1636,1645,1648,1651,1655,1659,1663,1666,1667,1672,1677,1687,1692,1699,1705,1706,1709,1713,1718,1720,1722,1725,1728,1730,1734,1737,1744,1747,1750,1754,1756,1760,1762,1764,1766,1770,1778,1786,1798,1804,1813,1816,1827,1830,1831,1836,1837,1873,1942,2012,2013,2023,2032,2033,2035,2039,2042,2045,2048,2051,2054,2057,2060,2064,2067,2070,2073,2077,2080,2084,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2110,2112,2113,2114,2115,2116,2117,2118,2119,2121,2122,2124,2125,2127,2129,2130,2132,2133,2134,2135,2136,2137,2139,2140,2141,2142,2143,2160,2162,2164,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2180,2181,2182,2183,2184,2185,2186,2188,2192,2197,2198,2199,2200,2201,2202,2206,2207,2208,2209,2211,2213,2215,2217,2219,2220,2221,2222,2224,2226,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2242,2243,2244,2245,2247,2249,2250,2252,2253,2255,2257,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2272,2273,2274,2275,2277,2278,2279,2280,2281,2283,2285,2287,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2354,2429,2432,2435,2438,2452,2465,2507,2510,2539,2566,2575,2639,3002,3040,3078,3196,3316,3340,3346,3365,3386,3510,3569,3575,3583,3589,3643,3675,3741,3761,3816,3828,3854", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "263,318,515,931,972,1027,1089,1153,1223,1284,1359,1435,1512,1750,1835,1917,1993,2111,2188,2266,2372,2478,2557,2886,2943,4925,4999,5074,5139,5205,5265,5326,5398,5471,5538,5606,5665,5724,5783,5842,5901,5955,6009,6062,6116,6170,6224,6499,6573,6652,6725,6799,6870,6942,7014,7228,7285,7343,7416,7490,7564,7639,7711,7784,7854,8015,8075,8178,8247,8316,8386,8460,8536,8600,8677,8753,8830,8895,8964,9041,9116,9185,9253,9330,9396,9457,9554,9619,9688,9787,9858,9917,9975,10032,10091,10155,10226,10298,10370,10442,10514,10581,10649,10717,10776,10839,10903,10993,11084,11144,11210,11277,11343,11413,11477,11530,11597,11658,11725,11838,11896,11959,12024,12089,12164,12237,12309,12353,12400,12446,12495,12556,12617,12678,12740,12804,12868,12932,12997,13060,13120,13181,13247,13306,13366,13428,13499,13559,15593,15679,15766,15856,15943,16031,16113,16196,16286,17567,17619,17677,17722,17788,17852,17909,17966,20143,20200,20248,20297,20759,21092,21139,21295,22200,22556,22620,22682,22742,23010,23084,23154,23232,23286,23356,23441,23489,23535,23596,23659,23725,23789,23860,23923,23988,24052,24113,24174,24226,24299,24373,24442,24517,24591,24665,24806,46635,49727,49805,49895,49983,50079,50169,50751,50840,51087,51368,52034,52319,52712,53189,53411,53633,53909,54136,54366,54596,54826,55056,55283,55702,55928,56353,56583,57011,57230,57513,57721,57852,58079,58505,58730,59157,59378,59803,59923,60199,60500,60824,61115,61429,61566,61697,61802,62044,62211,62415,62623,62894,63006,63118,63223,63340,63554,63700,63840,63926,64274,64362,64608,65026,65275,65357,65455,66112,66212,66464,66888,67143,67237,67326,67563,69587,69829,69931,70184,72340,83021,84537,95232,96760,98517,99143,99563,100824,102089,102345,102581,103128,103622,104227,104425,105005,106373,106748,106866,107404,107561,107757,108030,108286,108456,108597,108661,109026,109393,110069,110333,110671,111024,111118,111304,111610,111872,111997,112124,112363,112574,112693,112886,113063,113518,113699,113821,114080,114193,114380,114482,114589,114718,114993,115501,115997,116874,117168,117738,117887,118619,118791,118875,119211,119303,121185,126416,131787,131849,132427,133011,133102,133215,133444,133604,133756,133927,134093,134262,134429,134592,134835,135005,135178,135349,135623,135822,136027,136357,136441,136537,136633,136731,136831,136933,137035,137137,137239,137341,137441,137537,137649,137778,137901,138032,138163,138261,138375,138469,138609,138743,138839,138951,139051,139167,139263,139375,139475,139615,139751,139915,140045,140203,140353,140494,140638,140773,140885,141035,141163,141291,141427,141559,141689,141819,141931,143211,143357,143501,143639,143705,143795,143871,143975,144065,144167,144275,144383,144483,144563,144655,144753,144863,144915,144993,145099,145191,145295,145405,145527,145690,145939,146019,146119,146209,146319,146409,146650,146744,146850,146942,147042,147154,147268,147384,147500,147594,147708,147820,147922,148042,148164,148246,148350,148470,148596,148694,148788,148876,148988,149104,149226,149338,149513,149629,149715,149807,149919,150043,150110,150236,150304,150432,150576,150704,150773,150868,150983,151096,151195,151304,151415,151526,151627,151732,151832,151962,152053,152176,152270,152382,152468,152572,152668,152756,152874,152978,153082,153208,153296,153404,153504,153594,153704,153788,153890,153974,154028,154092,154198,154284,154394,154478,157421,160037,160155,160270,160350,160711,161248,162652,162730,164074,165435,165823,168666,178719,179981,181652,187736,191888,192639,192901,193416,193795,198073,200354,200583,200877,201092,202592,203442,206468,207212,209343,209683,210994", "endLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,692,747,748,749,750,751,759,760,764,768,772,784,790,797,801,805,810,814,818,822,826,830,834,840,844,850,854,860,864,869,873,876,880,886,890,896,900,906,909,913,917,921,925,929,930,931,932,935,938,941,944,948,949,950,951,952,955,957,959,961,966,967,971,977,981,982,984,996,997,1001,1007,1011,1012,1013,1017,1044,1048,1049,1053,1081,1253,1279,1450,1476,1507,1515,1521,1537,1559,1564,1569,1579,1588,1597,1601,1608,1627,1634,1635,1644,1647,1650,1654,1658,1662,1665,1666,1671,1676,1686,1691,1698,1704,1705,1708,1712,1717,1719,1721,1724,1727,1729,1733,1736,1743,1746,1749,1753,1755,1759,1761,1763,1765,1769,1777,1785,1797,1803,1812,1815,1826,1829,1830,1835,1836,1841,1941,2011,2012,2022,2031,2032,2034,2038,2041,2044,2047,2050,2053,2056,2059,2063,2066,2069,2072,2076,2079,2083,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2109,2111,2112,2113,2114,2115,2116,2117,2118,2120,2121,2123,2124,2126,2128,2129,2131,2132,2133,2134,2135,2136,2138,2139,2140,2141,2142,2143,2161,2163,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2179,2180,2181,2182,2183,2184,2185,2187,2191,2195,2197,2198,2199,2200,2201,2205,2206,2207,2208,2210,2212,2214,2216,2218,2219,2220,2221,2223,2225,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2241,2242,2243,2244,2246,2248,2249,2251,2252,2254,2256,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2271,2272,2273,2274,2276,2277,2278,2279,2280,2282,2284,2286,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2428,2431,2434,2437,2451,2457,2474,2509,2538,2565,2574,2638,3001,3005,3067,3105,3213,3339,3345,3351,3385,3509,3529,3574,3578,3588,3623,3654,3740,3760,3815,3827,3853,3860", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "313,358,559,967,1022,1084,1148,1218,1279,1354,1430,1507,1585,1830,1912,1988,2064,2183,2261,2367,2473,2552,2632,2938,2996,4994,5069,5134,5200,5260,5321,5393,5466,5533,5601,5660,5719,5778,5837,5896,5950,6004,6057,6111,6165,6219,6273,6568,6647,6720,6794,6865,6937,7009,7082,7280,7338,7411,7485,7559,7634,7706,7779,7849,7920,8070,8131,8242,8311,8381,8455,8531,8595,8672,8748,8825,8890,8959,9036,9111,9180,9248,9325,9391,9452,9549,9614,9683,9782,9853,9912,9970,10027,10086,10150,10221,10293,10365,10437,10509,10576,10644,10712,10771,10834,10898,10988,11079,11139,11205,11272,11338,11408,11472,11525,11592,11653,11720,11833,11891,11954,12019,12084,12159,12232,12304,12348,12395,12441,12490,12551,12612,12673,12735,12799,12863,12927,12992,13055,13115,13176,13242,13301,13361,13423,13494,13554,13622,15674,15761,15851,15938,16026,16108,16191,16281,16372,17614,17672,17717,17783,17847,17904,17961,18015,20195,20243,20292,20343,20788,21134,21183,21336,22227,22615,22677,22737,22794,23079,23149,23227,23281,23351,23436,23484,23530,23591,23654,23720,23784,23855,23918,23983,24047,24108,24169,24221,24294,24368,24437,24512,24586,24660,24801,24871,46683,49800,49890,49978,50074,50164,50746,50835,51082,51363,51615,52314,52707,53184,53406,53628,53904,54131,54361,54591,54821,55051,55278,55697,55923,56348,56578,57006,57225,57508,57716,57847,58074,58500,58725,59152,59373,59798,59918,60194,60495,60819,61110,61424,61561,61692,61797,62039,62206,62410,62618,62889,63001,63113,63218,63335,63549,63695,63835,63921,64269,64357,64603,65021,65270,65352,65450,66107,66207,66459,66883,67138,67232,67321,67558,69582,69824,69926,70179,72335,83016,84532,95227,96755,98512,99138,99558,100819,102084,102340,102576,103123,103617,104222,104420,105000,106368,106743,106861,107399,107556,107752,108025,108281,108451,108592,108656,109021,109388,110064,110328,110666,111019,111113,111299,111605,111867,111992,112119,112358,112569,112688,112881,113058,113513,113694,113816,114075,114188,114375,114477,114584,114713,114988,115496,115992,116869,117163,117733,117882,118614,118786,118870,119206,119298,119576,126411,131782,131844,132422,133006,133097,133210,133439,133599,133751,133922,134088,134257,134424,134587,134830,135000,135173,135344,135618,135817,136022,136352,136436,136532,136628,136726,136826,136928,137030,137132,137234,137336,137436,137532,137644,137773,137896,138027,138158,138256,138370,138464,138604,138738,138834,138946,139046,139162,139258,139370,139470,139610,139746,139910,140040,140198,140348,140489,140633,140768,140880,141030,141158,141286,141422,141554,141684,141814,141926,142066,143352,143496,143634,143700,143790,143866,143970,144060,144162,144270,144378,144478,144558,144650,144748,144858,144910,144988,145094,145186,145290,145400,145522,145685,145842,146014,146114,146204,146314,146404,146645,146739,146845,146937,147037,147149,147263,147379,147495,147589,147703,147815,147917,148037,148159,148241,148345,148465,148591,148689,148783,148871,148983,149099,149221,149333,149508,149624,149710,149802,149914,150038,150105,150231,150299,150427,150571,150699,150768,150863,150978,151091,151190,151299,151410,151521,151622,151727,151827,151957,152048,152171,152265,152377,152463,152567,152663,152751,152869,152973,153077,153203,153291,153399,153499,153589,153699,153783,153885,153969,154023,154087,154193,154279,154389,154473,154593,160032,160150,160265,160345,160706,160939,161760,162725,164069,165430,165818,168661,178714,178849,181346,183004,188303,192634,192896,193096,193790,198068,198674,200578,200729,201087,202170,202899,206463,207207,209338,209678,210989,211192"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,727", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "194,276,380,489,609,722,804"}, "to": {"startLines": "504,530,534,535,536,537,666", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "34397,36070,36329,36433,36542,36662,45177", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "34536,36147,36428,36537,36657,36770,45254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\02b89f0ee9be1e0a15cf2905800b0f25\\transformed\\facebook-common-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,49,54,60,71,82,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,202,290,370,449,513,590,665,732,814,895,968,1023,1092,1171,1235,1309,1382,1455,1527,1600,1673,1738,1807,4164,4458,4808,5429,6060,6239", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,53,59,70,81,84,112", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "197,285,365,444,508,585,660,727,809,890,963,1018,1087,1166,1230,1304,1377,1450,1522,1595,1668,1733,1802,1867,4453,4803,5424,6055,6234,7468"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2309,2314,2320,2331,2342,3861", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3381,3433,3521,3601,3680,3744,3821,3896,3963,4045,4126,4199,13940,14009,14088,14222,14296,14369,14442,14514,14587,14660,14725,14794,154882,155176,155526,156147,156778,211197", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2313,2319,2330,2341,2344,3888", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "3428,3516,3596,3675,3739,3816,3891,3958,4040,4121,4194,4249,14004,14083,14147,14291,14364,14437,14509,14582,14655,14720,14789,14854,155171,155521,156142,156773,156952,212426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1bf99d2dddf6b3f731ce95e33fee49d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "319,323", "startColumns": "4,4", "startOffsets": "20848,21025", "endColumns": "53,66", "endOffsets": "20897,21087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\856e9279244c37324ec12dc8b216b897\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "326,347", "startColumns": "4,4", "startOffsets": "21188,22275", "endColumns": "41,59", "endOffsets": "21225,22330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "43,44,45,46,207,208,493,515,516,517", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2637,2695,2761,2824,13627,13698,33616,35094,35161,35240", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2690,2756,2819,2881,13693,13765,33679,35156,35235,35304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b616a44a83a35cc1ff94b844e1245adb\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "20422", "endColumns": "49", "endOffsets": "20467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,38,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,175,257,340,421,558,673,791,886,951,1019,1077,1149,1221,1318,1409,1483,1557,1670,1771,1834,2054,2219,2296,2378,2503,2589,2713,2799,3177,3880", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,37,50,59", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "170,252,335,416,553,668,786,881,946,1014,1072,1144,1216,1313,1404,1478,1552,1665,1766,1829,2049,2214,2291,2373,2498,2584,2708,2794,3172,3875,4296"}, "to": {"startLines": "215,225,226,227,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,2345,2346,3889,3902", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14152,14859,14941,15024,28807,28944,29059,29177,29272,29337,29405,29463,29535,29607,29704,29795,29869,29943,30056,30157,30220,30440,30605,30682,30764,30889,30975,156957,157043,212431,213134", "endLines": "215,225,226,227,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,2345,2353,3901,3910", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "14217,14936,15019,15100,28939,29054,29172,29267,29332,29400,29458,29530,29602,29699,29790,29864,29938,30051,30152,30215,30435,30600,30677,30759,30884,30970,31094,157038,157416,213129,213550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e520bbb7f153fcbb4e273d864eff355d\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "22335", "endColumns": "53", "endOffsets": "22384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21c308ad6a5cb3ffbae3557cea5538ff\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "6,12,3579", "startColumns": "4,4,4", "startOffsets": "363,624,200734", "endLines": "9,19,3582", "endColumns": "11,11,24", "endOffsets": "510,926,200872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "732,733", "startColumns": "4,4", "startOffsets": "48880,48936", "endColumns": "55,54", "endOffsets": "48931,48986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b341f2bf9e26a9b9b6d9ac1cb5423684\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3530,3543,3549,3555,3564", "startColumns": "4,4,4,4,4", "startOffsets": "198679,199318,199562,199809,200172", "endLines": "3542,3548,3554,3557,3568", "endColumns": "24,24,24,24,24", "endOffsets": "199313,199557,199804,199937,200349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5ccc5722ca8455978d22b35e0684d747\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "314,327,350,3268,3273", "startColumns": "4,4,4,4,4", "startOffsets": "20585,21230,22439,190720,190890", "endLines": "314,327,350,3272,3276", "endColumns": "56,64,63,24,24", "endOffsets": "20637,21290,22498,190885,191034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "357,573,574,575,576,577,578,579,580,581,582,585,586,587,588,589,590,591,592,593,594,595,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,1853,1863", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22867,38856,38944,39030,39111,39195,39264,39329,39412,39518,39604,39724,39778,39847,39908,39977,40066,40161,40235,40332,40425,40523,40672,40763,40851,40947,41045,41109,41177,41264,41358,41425,41497,41569,41670,41779,41855,41924,41972,42038,42102,42176,42233,42290,42362,42412,42466,42537,42608,42678,42747,42805,42881,42952,43026,43112,43162,43232,120011,120726", "endLines": "357,573,574,575,576,577,578,579,580,581,584,585,586,587,588,589,590,591,592,593,594,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,1862,1865", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "22935,38939,39025,39106,39190,39259,39324,39407,39513,39599,39719,39773,39842,39903,39972,40061,40156,40230,40327,40420,40518,40667,40758,40846,40942,41040,41104,41172,41259,41353,41420,41492,41564,41665,41774,41850,41919,41967,42033,42097,42171,42228,42285,42357,42407,42461,42532,42603,42673,42742,42800,42876,42947,43021,43107,43157,43227,43292,120721,120874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "67,68,69,70,71,72,73,74,472,473,474,475,476,477,478,479,481,482,483,484,485,486,487,489,490,3352,3624", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4254,4344,4424,4514,4604,4684,4765,4845,31099,31204,31385,31510,31617,31797,31920,32036,32306,32494,32599,32780,32905,33080,33228,33351,33413,193101,202175", "endLines": "67,68,69,70,71,72,73,74,472,473,474,475,476,477,478,479,481,482,483,484,485,486,487,489,490,3364,3642", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4339,4419,4509,4599,4679,4760,4840,4920,31199,31380,31505,31612,31792,31915,32031,32134,32489,32594,32775,32900,33075,33223,33286,33408,33487,193411,202587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5092f4b0fc3229c87d6dc9ffd8fb80cd\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "20642", "endColumns": "65", "endOffsets": "20703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2cda5a1f9d8c110805098b8b6936f0e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "418", "startColumns": "4", "startOffsets": "26762", "endColumns": "82", "endOffsets": "26840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0632335579b593b6b56131d5dd003758\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "2,51,52,53,54,209,210,211,773,1842,1844,1847,3012", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3132,3193,3255,3317,13770,13829,13886,51620,119581,119645,119771,179057", "endLines": "2,51,52,53,54,209,210,211,779,1843,1846,1849,3039", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "197,3188,3250,3312,3376,13824,13881,13935,52029,119640,119766,119894,179976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "356,480", "startColumns": "4,4", "startOffsets": "22799,32139", "endColumns": "67,166", "endOffsets": "22862,32301"}}]}]}