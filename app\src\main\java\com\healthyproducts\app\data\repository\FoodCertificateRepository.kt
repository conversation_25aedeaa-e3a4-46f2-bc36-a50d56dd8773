package com.healthyproducts.app.data.repository

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.FieldNamingPolicy
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.healthyproducts.app.data.model.FoodCertificate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gıda sertifikaları işlemlerini yöneten repository sınıfı
 */
@Singleton
class FoodCertificateRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val context: Context? = null
) {
    companion object {
        private const val TAG = "FoodCertificateRepository"
        private const val COLLECTION_NAME = "certificates"
        private const val JSON_FILE = "json/foodCertificateList.json"
    }

    /**
     * Firestore instance'ını döndürür
     */
    fun getFirestore(): FirebaseFirestore {
        return firestore
    }

    private val _certificates = MutableStateFlow<List<FoodCertificate>>(emptyList())
    val certificates: StateFlow<List<FoodCertificate>> = _certificates.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    /**
     * Tüm sertifikaları getirir
     */
    suspend fun getAllCertificates(): Result<List<FoodCertificate>> = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null

            Log.d(TAG, "Getting all certificates")

            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val certificatesList = snapshot.documents.mapNotNull { document ->
                document.toObject(FoodCertificate::class.java)
            }

            Log.d(TAG, "Retrieved ${certificatesList.size} certificates")

            _certificates.value = certificatesList
            _isLoading.value = false

            Result.success(certificatesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting certificates", e)
            _error.value = e.message
            _isLoading.value = false
            Result.failure(e)
        }
    }

    /**
     * Sertifika ekler
     */
    suspend fun addCertificate(certificate: FoodCertificate): Result<FoodCertificate> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding certificate: ${certificate.certificateId} - ${certificate.nameTr}")

            // Sertifikayı Firestore'a ekle
            val documentRef = firestore.collection(COLLECTION_NAME).document(certificate.certificateId)
            documentRef.set(certificate).await()

            // Sertifikayı yerel listeye ekle
            val currentList = _certificates.value.toMutableList()
            currentList.add(certificate)
            _certificates.value = currentList

            Log.d(TAG, "Certificate added successfully")

            Result.success(certificate)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding certificate", e)
            Result.failure(e)
        }
    }

    /**
     * Sertifikayı günceller
     */
    suspend fun updateCertificate(certificate: FoodCertificate): Result<FoodCertificate> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating certificate: ${certificate.certificateId} - ${certificate.nameTr}")

            // Sertifikayı Firestore'da güncelle
            val documentRef = firestore.collection(COLLECTION_NAME).document(certificate.certificateId)
            documentRef.set(certificate).await()

            // Sertifikayı yerel listede güncelle
            val currentList = _certificates.value.toMutableList()
            val index = currentList.indexOfFirst { it.certificateId == certificate.certificateId }
            if (index != -1) {
                currentList[index] = certificate
                _certificates.value = currentList
            }

            Log.d(TAG, "Certificate updated successfully")

            Result.success(certificate)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating certificate", e)
            Result.failure(e)
        }
    }

    /**
     * Sertifikayı siler
     */
    suspend fun deleteCertificate(certificateId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting certificate: $certificateId")

            // Sertifikayı Firestore'dan sil
            val documentRef = firestore.collection(COLLECTION_NAME).document(certificateId)
            documentRef.delete().await()

            // Sertifikayı yerel listeden sil
            val currentList = _certificates.value.toMutableList()
            val index = currentList.indexOfFirst { it.certificateId == certificateId }
            if (index != -1) {
                currentList.removeAt(index)
                _certificates.value = currentList
            }

            Log.d(TAG, "Certificate deleted successfully")

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting certificate", e)
            Result.failure(e)
        }
    }

    /**
     * Sertifikayı ID ile getirir
     */
    suspend fun getCertificateById(certificateId: String): Result<FoodCertificate?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting certificate by ID: $certificateId")

            // Önce yerel listede ara
            val localCertificate = _certificates.value.find { it.certificateId == certificateId }
            if (localCertificate != null) {
                Log.d(TAG, "Certificate found in local cache")
                return@withContext Result.success(localCertificate)
            }

            // Yerel listede yoksa Firestore'dan getir
            val documentRef = firestore.collection(COLLECTION_NAME).document(certificateId)
            val document = documentRef.get().await()

            val certificate = document.toObject(FoodCertificate::class.java)

            if (certificate != null) {
                Log.d(TAG, "Certificate found in Firestore")
            } else {
                Log.d(TAG, "Certificate not found")
            }

            Result.success(certificate)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting certificate by ID", e)
            Result.failure(e)
        }
    }

    /**
     * Sertifikaları adına göre arar
     */
    suspend fun searchCertificatesByName(query: String): Result<List<FoodCertificate>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching certificates by name: $query")

            // Firestore'da tam metin araması yapılamadığı için tüm sertifikaları getirip filtreliyoruz
            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val certificatesList = snapshot.documents.mapNotNull { document ->
                document.toObject(FoodCertificate::class.java)
            }.filter { certificate ->
                certificate.nameTr.contains(query, ignoreCase = true) ||
                certificate.nameEn.contains(query, ignoreCase = true) ||
                certificate.certificateId.contains(query, ignoreCase = true)
            }

            Log.d(TAG, "Found ${certificatesList.size} certificates matching query")

            Result.success(certificatesList)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching certificates", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dosyasından sertifikaları içe aktarır ve Firestore'a kaydeder
     */
    suspend fun importCertificatesFromJson(): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Importing certificates from JSON")

            if (context == null) {
                Log.e(TAG, "Context is null, cannot import certificates from JSON")
                return@withContext Result.failure(IllegalStateException("Context is null"))
            }

            // JSON dosyasını oku
            val jsonString = context.assets.open(JSON_FILE).bufferedReader().use { it.readText() }

            // JSON'ı parse et
            val gson = GsonBuilder()
                .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                .create()
            val listType = object : TypeToken<List<FoodCertificate>>() {}.type
            val certificates: List<FoodCertificate> = gson.fromJson(jsonString, listType)

            Log.d(TAG, "Parsed ${certificates.size} certificates from JSON")

            // İlk sertifikanın içeriğini kontrol et
            if (certificates.isNotEmpty()) {
                val firstCert = certificates[0]
                Log.d(TAG, "First certificate: id=${firstCert.id}, name_tr=${firstCert.nameTr}, name_en=${firstCert.nameEn}, " +
                        "functional_type=${firstCert.functionalType}")
            }

            // Firestore'a kaydet
            var successCount = 0
            certificates.forEach { certificate ->
                try {
                    // certificateId'yi ayarla
                    certificate.certificateId = certificate.id

                    // Firestore'a kaydedilecek veri haritasını oluştur
                    val certificateMap = hashMapOf(
                        "certificateId" to certificate.certificateId,
                        "name_tr" to certificate.nameTr,
                        "name_en" to certificate.nameEn,
                        "symbol" to certificate.symbol,
                        "description_tr" to certificate.descriptionTr,
                        "description_en" to certificate.descriptionEn,
                        "issuer_tr" to certificate.issuerTr,
                        "issuer_en" to certificate.issuerEn,
                        "valid_for_tr" to certificate.validForTr,
                        "valid_for_en" to certificate.validForEn,
                        "region" to certificate.region,
                        "risk_if_missing_tr" to certificate.riskIfMissingTr,
                        "risk_if_missing_en" to certificate.riskIfMissingEn,
                        "notes_tr" to certificate.notesTr,
                        "notes_en" to certificate.notesEn,
                        "functional_type" to certificate.functionalType
                    )

                    val documentRef = firestore.collection(COLLECTION_NAME).document(certificate.certificateId)

                    // Sertifikayı harita olarak kaydet
                    documentRef.set(certificateMap).await()

                    // Kaydedilen veriyi kontrol et
                    val savedDoc = documentRef.get().await()
                    Log.d(TAG, "  Saved document: ${savedDoc.data?.entries?.joinToString { "${it.key}='${it.value}'" }}")

                    successCount++
                } catch (e: Exception) {
                    Log.e(TAG, "Error importing certificate ${certificate.certificateId}", e)
                }
            }

            Log.d(TAG, "Successfully imported $successCount out of ${certificates.size} certificates")

            // Tüm sertifikaları yeniden yükle
            getAllCertificates()

            Result.success(successCount)
        } catch (e: Exception) {
            Log.e(TAG, "Error importing certificates from JSON", e)
            Result.failure(e)
        }
    }
}
