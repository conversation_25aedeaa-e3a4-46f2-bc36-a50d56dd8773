package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AdditiveRepository_Factory implements Factory<AdditiveRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public AdditiveRepository_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public AdditiveRepository get() {
    return newInstance(firestoreProvider.get());
  }

  public static AdditiveRepository_Factory create(Provider<FirebaseFirestore> firestoreProvider) {
    return new AdditiveRepository_Factory(firestoreProvider);
  }

  public static AdditiveRepository newInstance(FirebaseFirestore firestore) {
    return new AdditiveRepository(firestore);
  }
}
