package com.healthyproducts.app.data.model;

/**
 * Yağ türünü temsil eden veri sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u000e\n\u0002\u0010 \n\u0002\bP\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\f\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u00e9\u0001\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\u0004\u0012\b\b\u0002\u0010\n\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\f\u001a\u00020\u0004\u0012\b\b\u0002\u0010\r\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0007\u0012\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016\u0012\u000e\b\u0002\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016\u00a2\u0006\u0002\u0010\u001bJ\t\u0010P\u001a\u00020\u0004H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0004H\u00c6\u0003J\t\u0010R\u001a\u00020\u0004H\u00c6\u0003J\t\u0010S\u001a\u00020\u0004H\u00c6\u0003J\t\u0010T\u001a\u00020\u0004H\u00c6\u0003J\t\u0010U\u001a\u00020\u0004H\u00c6\u0003J\t\u0010V\u001a\u00020\u0004H\u00c6\u0003J\t\u0010W\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010X\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016H\u00c6\u0003J\u000f\u0010Y\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0004H\u00c6\u0003J\t\u0010[\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0004H\u00c6\u0003J\u000f\u0010]\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016H\u00c6\u0003J\t\u0010^\u001a\u00020\u0007H\u00c6\u0003J\t\u0010_\u001a\u00020\u0007H\u00c6\u0003J\t\u0010`\u001a\u00020\u0004H\u00c6\u0003J\t\u0010a\u001a\u00020\u0004H\u00c6\u0003J\t\u0010b\u001a\u00020\u0004H\u00c6\u0003J\t\u0010c\u001a\u00020\u0004H\u00c6\u0003J\t\u0010d\u001a\u00020\u0004H\u00c6\u0003J\u00ed\u0001\u0010e\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00042\b\b\u0002\u0010\n\u001a\u00020\u00042\b\b\u0002\u0010\u000b\u001a\u00020\u00042\b\b\u0002\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\r\u001a\u00020\u00042\b\b\u0002\u0010\u000e\u001a\u00020\u00042\b\b\u0002\u0010\u000f\u001a\u00020\u00042\b\b\u0002\u0010\u0010\u001a\u00020\u00042\b\b\u0002\u0010\u0011\u001a\u00020\u00042\b\b\u0002\u0010\u0012\u001a\u00020\u00042\b\b\u0002\u0010\u0013\u001a\u00020\u00042\b\b\u0002\u0010\u0014\u001a\u00020\u00072\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u00162\u000e\b\u0002\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u00162\b\b\u0002\u0010\u0018\u001a\u00020\u00042\b\b\u0002\u0010\u0019\u001a\u00020\u00042\u000e\b\u0002\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016H\u00c6\u0001J\u0013\u0010f\u001a\u00020g2\b\u0010h\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010i\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u000e\u0010l\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u000e\u0010m\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u000e\u0010n\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u0014\u0010o\u001a\b\u0012\u0004\u0012\u00020\u00040\u00162\u0006\u0010j\u001a\u00020kJ\u000e\u0010p\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u000e\u0010q\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u000e\u0010r\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u000e\u0010s\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\u000e\u0010t\u001a\u00020\u00042\u0006\u0010j\u001a\u00020kJ\t\u0010u\u001a\u00020\u0007H\u00d6\u0001J\t\u0010v\u001a\u00020\u0004H\u00d6\u0001R\u001e\u0010\r\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u0010\u001d\"\u0004\b\u001e\u0010\u001fR\u001e\u0010\f\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010\u001d\"\u0004\b!\u0010\u001fR\u001e\u0010\u0005\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010\u001d\"\u0004\b#\u0010\u001fR$\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00040\u00168\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010%\"\u0004\b&\u0010\'R\u001e\u0010\u0006\u001a\u00020\u00078\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010)\"\u0004\b*\u0010+R\u001e\u0010\u0013\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010\u001d\"\u0004\b-\u0010\u001fR\u001e\u0010\u0012\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010\u001d\"\u0004\b/\u0010\u001fR\u001e\u0010\u0003\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u0010\u001d\"\u0004\b1\u0010\u001fR$\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u00168\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b2\u0010%\"\u0004\b3\u0010\'R$\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u00168\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u0010%\"\u0004\b5\u0010\'R\u001e\u0010\n\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b6\u0010\u001d\"\u0004\b7\u0010\u001fR\u001e\u0010\t\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b8\u0010\u001d\"\u0004\b9\u0010\u001fR\u001e\u0010\u0019\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b:\u0010\u001d\"\u0004\b;\u0010\u001fR\u001e\u0010\u0018\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b<\u0010\u001d\"\u0004\b=\u0010\u001fR\u001e\u0010\u000f\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b>\u0010\u001d\"\u0004\b?\u0010\u001fR\u001e\u0010\u000e\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b@\u0010\u001d\"\u0004\bA\u0010\u001fR\u0017\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00040\u00168F\u00a2\u0006\u0006\u001a\u0004\bC\u0010%R\u001e\u0010\u0014\u001a\u00020\u00078\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bD\u0010)\"\u0004\bE\u0010+R\u001e\u0010\u000b\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bF\u0010\u001d\"\u0004\bG\u0010\u001fR\u001e\u0010\u0011\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bH\u0010\u001d\"\u0004\bI\u0010\u001fR\u001e\u0010\u0010\u001a\u00020\u00048\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bJ\u0010\u001d\"\u0004\bK\u0010\u001fR\u001e\u0010\b\u001a\u00020\u00078\u0007@\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bL\u0010)\"\u0004\bM\u0010+R\u0017\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00040\u00168F\u00a2\u0006\u0006\u001a\u0004\bO\u0010%\u00a8\u0006w"}, d2 = {"Lcom/healthyproducts/app/data/model/Fat;", "", "()V", "id", "", "fatId", "harmfulLevel", "", "unhealthyLevel", "nameTr", "nameEn", "symbol", "descriptionTr", "descriptionEn", "originTr", "originEn", "typeTr", "typeEn", "healthEffectTr", "healthEffectEn", "riskLevel", "labelsTr", "", "labelsEn", "notesTr", "notesEn", "functionalType", "(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V", "getDescriptionEn", "()Ljava/lang/String;", "setDescriptionEn", "(Ljava/lang/String;)V", "getDescriptionTr", "setDescriptionTr", "getFatId", "setFatId", "getFunctionalType", "()Ljava/util/List;", "setFunctionalType", "(Ljava/util/List;)V", "getHarmfulLevel", "()I", "setHarmfulLevel", "(I)V", "getHealthEffectEn", "setHealthEffectEn", "getHealthEffectTr", "setHealthEffectTr", "getId", "setId", "getLabelsEn", "setLabelsEn", "getLabelsTr", "setLabelsTr", "getNameEn", "setNameEn", "getNameTr", "setNameTr", "getNotesEn", "setNotesEn", "getNotesTr", "setNotesTr", "getOriginEn", "setOriginEn", "getOriginTr", "setOriginTr", "religiousStatuses", "getReligiousStatuses", "getRiskLevel", "setRiskLevel", "getSymbol", "setSymbol", "getTypeEn", "setTypeEn", "getTypeTr", "setTypeTr", "getUnhealthyLevel", "setUnhealthyLevel", "veganStatuses", "getVeganStatuses", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "getChemicalStructure", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "getDescription", "getHealthEffect", "getHealthEffects", "getLabels", "getName", "getNotes", "getOrigin", "getType", "getUsage", "hashCode", "toString", "app_debug"})
public final class Fat {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String id;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String fatId;
    private int harmfulLevel;
    private int unhealthyLevel;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String symbol;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String originTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String originEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String typeTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String typeEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String healthEffectTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String healthEffectEn;
    private int riskLevel;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> labelsTr;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> labelsEn;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesTr;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesEn;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> functionalType;
    
    public Fat(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String fatId, int harmfulLevel, int unhealthyLevel, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.lang.String originTr, @org.jetbrains.annotations.NotNull()
    java.lang.String originEn, @org.jetbrains.annotations.NotNull()
    java.lang.String typeTr, @org.jetbrains.annotations.NotNull()
    java.lang.String typeEn, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectTr, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectEn, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType) {
        super();
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "id")
    public final void setId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "fatId")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFatId() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "fatId")
    public final void setFatId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "harmful_level")
    public final int getHarmfulLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "harmful_level")
    public final void setHarmfulLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "unhealthy_level")
    public final int getUnhealthyLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "unhealthy_level")
    public final void setUnhealthyLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    public final void setNameTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    public final void setNameEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSymbol() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    public final void setSymbol(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    public final void setDescriptionTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    public final void setDescriptionEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOriginTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_tr")
    public final void setOriginTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOriginEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "origin_en")
    public final void setOriginEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "type_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTypeTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "type_tr")
    public final void setTypeTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "type_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTypeEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "type_en")
    public final void setTypeEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffectTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_tr")
    public final void setHealthEffectTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffectEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "health_effect_en")
    public final void setHealthEffectEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final int getRiskLevel() {
        return 0;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_level")
    public final void setRiskLevel(int p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_tr")
    public final void setLabelsTr(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_en")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "labels_en")
    public final void setLabelsEn(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    public final void setNotesTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    public final void setNotesEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getFunctionalType() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    public final void setFunctionalType(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    public Fat() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrigin(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getType(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffect(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabels(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotes(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getHealthEffects(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getChemicalStructure(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUsage(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getVeganStatuses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getReligiousStatuses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component15() {
        return null;
    }
    
    public final int component16() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component17() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component18() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component20() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component21() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.model.Fat copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String fatId, int harmfulLevel, int unhealthyLevel, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.lang.String originTr, @org.jetbrains.annotations.NotNull()
    java.lang.String originEn, @org.jetbrains.annotations.NotNull()
    java.lang.String typeTr, @org.jetbrains.annotations.NotNull()
    java.lang.String typeEn, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectTr, @org.jetbrains.annotations.NotNull()
    java.lang.String healthEffectEn, int riskLevel, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> labelsEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}