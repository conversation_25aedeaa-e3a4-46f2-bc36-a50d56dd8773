package com.healthyproducts.app.ui.viewmodel;

/**
 * Tarama geçmişini yöneten ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001:\u0001\u0019B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J:\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u000f2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u000f2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010\u0015\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ\u0018\u0010\u0016\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0017\u001a\u00020\u0018R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u001a"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel;", "Landroidx/lifecycle/ViewModel;", "scanHistoryRepository", "Lcom/healthyproducts/app/data/repository/ScanHistoryRepository;", "(Lcom/healthyproducts/app/data/repository/ScanHistoryRepository;)V", "_scanHistoryState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState;", "scanHistoryState", "Lkotlinx/coroutines/flow/StateFlow;", "getScanHistoryState", "()Lkotlinx/coroutines/flow/StateFlow;", "addToScanHistory", "", "userId", "", "productId", "barcode", "imageUrl", "scanType", "Lcom/healthyproducts/app/model/ScanType;", "clearScanHistory", "getScanHistory", "limit", "", "ScanHistoryState", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ScanHistoryViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.ScanHistoryRepository scanHistoryRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState> _scanHistoryState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState> scanHistoryState = null;
    
    @javax.inject.Inject()
    public ScanHistoryViewModel(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.ScanHistoryRepository scanHistoryRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState> getScanHistoryState() {
        return null;
    }
    
    /**
     * Kullanıcının tarama geçmişini getirme
     */
    public final void getScanHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, int limit) {
    }
    
    /**
     * Tarama geçmişine ekleme
     */
    public final void addToScanHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String productId, @org.jetbrains.annotations.Nullable()
    java.lang.String barcode, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.model.ScanType scanType) {
    }
    
    /**
     * Tarama geçmişini temizleme
     */
    public final void clearScanHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
    
    /**
     * Tarama geçmişi durumunu temsil eden sealed class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState;", "", "()V", "Empty", "Error", "Loading", "Success", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Empty;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Success;", "app_debug"})
    public static abstract class ScanHistoryState {
        
        private ScanHistoryState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Empty;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState;", "()V", "app_debug"})
        public static final class Empty extends com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Empty INSTANCE = null;
            
            private Empty() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Error extends com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState;", "()V", "app_debug"})
        public static final class Loading extends com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Loading INSTANCE = null;
            
            private Loading() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u0013\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0002\u0010\u0005J\u000f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0019\u0010\t\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\rH\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState$Success;", "Lcom/healthyproducts/app/ui/viewmodel/ScanHistoryViewModel$ScanHistoryState;", "scanHistory", "", "Lcom/healthyproducts/app/data/repository/ScanHistoryRepository$ScanHistoryWithProduct;", "(Ljava/util/List;)V", "getScanHistory", "()Ljava/util/List;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
        public static final class Success extends com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState {
            @org.jetbrains.annotations.NotNull()
            private final java.util.List<com.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct> scanHistory = null;
            
            public Success(@org.jetbrains.annotations.NotNull()
            java.util.List<com.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct> scanHistory) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.util.List<com.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct> getScanHistory() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.util.List<com.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct> component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState.Success copy(@org.jetbrains.annotations.NotNull()
            java.util.List<com.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct> scanHistory) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
}