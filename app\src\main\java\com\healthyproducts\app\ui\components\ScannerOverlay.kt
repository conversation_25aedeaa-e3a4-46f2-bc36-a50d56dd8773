package com.healthyproducts.app.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthyproducts.app.R

/**
 * Tarayıcı overlay bileşeni
 * Kamera önizleme üzerine hedef dikdörtgen çizer
 */
@Composable
fun ScannerOverlay(
    modifier: Modifier = Modifier,
    frameColor: Color = Color.White,
    frameRatio: Float = 0.75f, // Ekran genişliğinin yüzde kaçı kadar genişlik
    frameHeightRatio: Float = 0.3f, // Genişliğin yüzde kaçı kadar yükseklik (aspect ratio)
    frameSize: FrameSize = FrameSize.MEDIUM, // Çerçeve boyutu
    showIngredientsGuide: Boolean = true // İçerik kılavuzu gösterilsin mi
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp

    // Boyut faktörü
    val sizeFactor = when (frameSize) {
        FrameSize.SMALL -> 0.8f
        FrameSize.MEDIUM -> 1.0f
        FrameSize.LARGE -> 1.2f
    }

    // Hedef dikdörtgenin genişliği
    val frameWidth = screenWidth * frameRatio * sizeFactor

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // Hedef dikdörtgen
        Box(
            modifier = Modifier
                .width(frameWidth)
                .aspectRatio(1f / (frameHeightRatio * sizeFactor)) // Genişlik/Yükseklik oranı
        ) {
            // Kesikli çizgili dikdörtgen çerçeve
            Canvas(modifier = Modifier.fillMaxSize()) {
                val strokeWidth = 4f
                val dashPathEffect = PathEffect.dashPathEffect(floatArrayOf(20f, 10f), 0f)

                drawRect(
                    color = frameColor,
                    style = Stroke(
                        width = strokeWidth,
                        pathEffect = dashPathEffect
                    ),
                    size = size
                )

                // Köşe işaretleri
                val cornerLength = 30f

                // Sol üst köşe
                drawLine(
                    color = Color.Green,
                    start = Offset(0f, 0f),
                    end = Offset(cornerLength, 0f),
                    strokeWidth = strokeWidth
                )
                drawLine(
                    color = Color.Green,
                    start = Offset(0f, 0f),
                    end = Offset(0f, cornerLength),
                    strokeWidth = strokeWidth
                )

                // Sağ üst köşe
                drawLine(
                    color = Color.Green,
                    start = Offset(size.width, 0f),
                    end = Offset(size.width - cornerLength, 0f),
                    strokeWidth = strokeWidth
                )
                drawLine(
                    color = Color.Green,
                    start = Offset(size.width, 0f),
                    end = Offset(size.width, cornerLength),
                    strokeWidth = strokeWidth
                )

                // Sol alt köşe
                drawLine(
                    color = Color.Green,
                    start = Offset(0f, size.height),
                    end = Offset(cornerLength, size.height),
                    strokeWidth = strokeWidth
                )
                drawLine(
                    color = Color.Green,
                    start = Offset(0f, size.height),
                    end = Offset(0f, size.height - cornerLength),
                    strokeWidth = strokeWidth
                )

                // Sağ alt köşe
                drawLine(
                    color = Color.Green,
                    start = Offset(size.width, size.height),
                    end = Offset(size.width - cornerLength, size.height),
                    strokeWidth = strokeWidth
                )
                drawLine(
                    color = Color.Green,
                    start = Offset(size.width, size.height),
                    end = Offset(size.width, size.height - cornerLength),
                    strokeWidth = strokeWidth
                )
            }
        }

        // İçerik kılavuzu metni
        if (showIngredientsGuide) {
            Text(
                text = stringResource(R.string.ingredients_guide_text),
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 16.dp)
                    .background(Color(0x80000000))
                    .padding(horizontal = 12.dp, vertical = 4.dp)
            )
        }
    }
}

/**
 * Çerçeve boyutu enum sınıfı
 */
enum class FrameSize {
    SMALL,
    MEDIUM,
    LARGE
}
