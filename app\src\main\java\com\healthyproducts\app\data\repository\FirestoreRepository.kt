package com.healthyproducts.app.data.repository

import com.google.firebase.firestore.FirebaseFirestore
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.model.UserSettings
import com.healthyproducts.app.model.UserPreferences
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firestore veritabanı işlemlerini yöneten repository sınıfı
 */
@Singleton
class FirestoreRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    val authRepository: FirebaseAuthRepository
) {

    init {
        // Kullanıcı oturum açtığında tercihleri yükle
        android.util.Log.d("FirestoreRepository", "Initializing FirestoreRepository")

        // Mevcut kullanıcıyı kontrol et
        val currentUser = authRepository.getCurrentUser()
        if (currentUser != null) {
            android.util.Log.d("FirestoreRepository", "Current user found: ${currentUser.uid}")
            kotlinx.coroutines.GlobalScope.launch {
                try {
                    getUserPreferences(currentUser.uid)
                } catch (e: Exception) {
                    android.util.Log.e("FirestoreRepository", "Error loading user preferences", e)
                }
            }
        } else {
            android.util.Log.d("FirestoreRepository", "No current user")
        }
    }
    private val _userSettings = MutableStateFlow<UserSettings?>(null)
    val userSettings: StateFlow<UserSettings?> = _userSettings.asStateFlow()

    private val _userLanguage = MutableStateFlow<SupportedLanguage>(SupportedLanguage.TURKISH)
    val userLanguage: StateFlow<SupportedLanguage> = _userLanguage.asStateFlow()

    private val _userPreferences = MutableStateFlow<UserPreferences>(UserPreferences())
    val userPreferences: StateFlow<UserPreferences> = _userPreferences.asStateFlow()

    /**
     * Kullanıcı ayarlarını getirir
     */
    suspend fun getUserSettings(): UserSettings {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        val document = firestore.collection("user_settings")
            .document(userId)
            .get()
            .await()

        return if (document.exists()) {
            val settings = document.toObject(UserSettings::class.java)
                ?: throw Exception("Kullanıcı ayarları alınamadı")

            _userSettings.value = settings
            _userLanguage.value = SupportedLanguage.fromCode(settings.language)

            // Kullanıcı tercihlerini de getir
            getUserPreferences(userId)

            settings
        } else {
            // Kullanıcı ayarları yoksa oluştur
            createUserSettings(userId)
        }
    }

    /**
     * Kullanıcı tercihlerini getirir
     */
    suspend fun getUserPreferences(userId: String) {
        android.util.Log.d("FirestoreRepository", "Getting user preferences for user: $userId")

        try {
            val document = firestore.collection("user_preferences")
                .document(userId)
                .get()
                .await()

            if (document.exists()) {
                val preferences = document.toObject(UserPreferences::class.java)
                if (preferences != null) {
                    android.util.Log.d("FirestoreRepository", "Loaded user preferences: aiModel=${preferences.aiModel}, language=${preferences.language}")
                    _userPreferences.value = preferences
                    android.util.Log.d("FirestoreRepository", "Updated userPreferences state: aiModel=${_userPreferences.value.aiModel}, language=${_userPreferences.value.language}")
                } else {
                    android.util.Log.w("FirestoreRepository", "Failed to parse user preferences from document")
                }
            } else {
                // Kullanıcı tercihleri yoksa varsayılan değerleri kullan
                val defaultPreferences = UserPreferences()
                android.util.Log.d("FirestoreRepository", "Created default preferences: aiModel=${defaultPreferences.aiModel}")
                _userPreferences.value = defaultPreferences

                // Varsayılan tercihleri kaydet
                firestore.collection("user_preferences")
                    .document(userId)
                    .set(defaultPreferences)
                    .await()

                android.util.Log.d("FirestoreRepository", "Saved default preferences to Firestore")
            }
        } catch (e: Exception) {
            android.util.Log.e("FirestoreRepository", "Error getting user preferences", e)
            throw e
        }
    }

    /**
     * Kullanıcı ayarlarını oluşturur
     */
    private suspend fun createUserSettings(userId: String): UserSettings {
        val settings = UserSettings(userId = userId)

        firestore.collection("user_settings")
            .document(userId)
            .set(settings)
            .await()

        _userSettings.value = settings
        _userLanguage.value = SupportedLanguage.fromCode(settings.language)

        // Kullanıcı tercihlerini de oluştur
        val preferences = UserPreferences()
        _userPreferences.value = preferences

        firestore.collection("user_preferences")
            .document(userId)
            .set(preferences)
            .await()

        return settings
    }

    /**
     * Kullanıcı tercihlerini günceller
     */
    suspend fun updateUserPreferences(preferences: UserPreferences) {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        android.util.Log.d("FirestoreRepository", "Updating user preferences: aiModel=${preferences.aiModel}")

        firestore.collection("user_preferences")
            .document(userId)
            .set(preferences)
            .await()

        _userPreferences.value = preferences

        // Tercihlerin güncellendiğini doğrula
        val updatedDocument = firestore.collection("user_preferences")
            .document(userId)
            .get()
            .await()

        if (updatedDocument.exists()) {
            val updatedPreferences = updatedDocument.toObject(UserPreferences::class.java)
            if (updatedPreferences != null) {
                android.util.Log.d("FirestoreRepository", "Verified updated preferences: aiModel=${updatedPreferences.aiModel}")
            }
        }
    }

    /**
     * Kullanıcı dilini günceller
     */
    suspend fun updateUserLanguage(language: SupportedLanguage) {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        val settings = _userSettings.value?.copy(language = language.code)
            ?: UserSettings(userId = userId, language = language.code)

        firestore.collection("user_settings")
            .document(userId)
            .set(settings)
            .await()

        _userSettings.value = settings
        _userLanguage.value = language
    }

    /**
     * Kullanıcının favorilerini getirir
     */
    suspend fun getFavorites(): List<String> {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        val document = firestore.collection("favorites")
            .document(userId)
            .get()
            .await()

        return if (document.exists()) {
            @Suppress("UNCHECKED_CAST")
            document.get("products") as? List<String> ?: emptyList()
        } else {
            emptyList()
        }
    }

    /**
     * Ürünü favorilere ekler
     */
    suspend fun addToFavorites(productId: String) {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        val favorites = getFavorites().toMutableList()
        if (!favorites.contains(productId)) {
            favorites.add(productId)

            firestore.collection("favorites")
                .document(userId)
                .set(mapOf("products" to favorites))
                .await()
        }
    }

    /**
     * Ürünü favorilerden çıkarır
     */
    suspend fun removeFromFavorites(productId: String) {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        val favorites = getFavorites().toMutableList()
        if (favorites.contains(productId)) {
            favorites.remove(productId)

            firestore.collection("favorites")
                .document(userId)
                .set(mapOf("products" to favorites))
                .await()
        }
    }

    /**
     * Kullanıcının arama geçmişini getirir
     */
    suspend fun getSearchHistory(): List<String> {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        val document = firestore.collection("search_history")
            .document(userId)
            .get()
            .await()

        return if (document.exists()) {
            @Suppress("UNCHECKED_CAST")
            document.get("searches") as? List<String> ?: emptyList()
        } else {
            emptyList()
        }
    }

    /**
     * Aramayı geçmişe ekler
     */
    suspend fun addToSearchHistory(search: String) {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        val history = getSearchHistory().toMutableList()

        // Aynı arama zaten varsa, onu listeden çıkar (sonra başa ekleyeceğiz)
        if (history.contains(search)) {
            history.remove(search)
        }

        // Aramayı listenin başına ekle (en son aramalar en üstte)
        history.add(0, search)

        // Listeyi maksimum 20 öğeyle sınırla
        val trimmedHistory = if (history.size > 20) history.subList(0, 20) else history

        firestore.collection("search_history")
            .document(userId)
            .set(mapOf("searches" to trimmedHistory))
            .await()
    }

    /**
     * Arama geçmişini temizler
     */
    suspend fun clearSearchHistory() {
        val userId = authRepository.getCurrentUser()?.uid
            ?: throw Exception("Kullanıcı oturum açmamış")

        firestore.collection("search_history")
            .document(userId)
            .set(mapOf("searches" to emptyList<String>()))
            .await()
    }
}
