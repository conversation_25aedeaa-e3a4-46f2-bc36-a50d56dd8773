package com.healthyproducts.app.ui.screens.settings;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a&\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u001a.\u0010\b\u001a\u00020\u00012\b\b\u0002\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a\u001a\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0007\u00a8\u0006\u0014"}, d2 = {"AiModelOption", "", "model", "Lcom/healthyproducts/app/data/model/AiModel;", "isSelected", "", "onSelected", "Lkotlin/Function0;", "AiModelSelectionContent", "modifier", "Landroidx/compose/ui/Modifier;", "selectedModel", "", "onModelSelected", "Lkotlin/Function1;", "AiModelSelectionScreen", "navController", "Landroidx/navigation/NavController;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "app_debug"})
public final class AiModelSelectionScreenKt {
    
    /**
     * AI model seçim ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AiModelSelectionScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * AI model seçim içeriği
     */
    @androidx.compose.runtime.Composable()
    public static final void AiModelSelectionContent(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.healthyproducts.app.data.model.AiModel, kotlin.Unit> onModelSelected) {
    }
    
    /**
     * AI model seçeneği
     */
    @androidx.compose.runtime.Composable()
    public static final void AiModelOption(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.AiModel model, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSelected) {
    }
}