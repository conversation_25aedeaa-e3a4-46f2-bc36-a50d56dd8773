<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2" xmlns:ns2="http://schemas.android.com/tools">
    <attr format="reference" name="cardViewStyle"/>
    <attr format="reference" name="coordinatorLayoutStyle"/>
    <attr format="reference" name="drawerArrowStyle"/>
    <attr format="dimension" name="height"/>
    <attr format="enum" name="implementationMode">
        <enum name="performance" value="0"/>
        <enum name="compatible" value="1"/>
    </attr>
    <attr format="boolean" name="isLightTheme"/>
    <attr format="reference" name="nestedScrollViewStyle"/>
    <attr format="enum" name="scaleType">
        <enum name="fillStart" value="0"/>
        <enum name="fillCenter" value="1"/>
        <enum name="fillEnd" value="2"/>
        <enum name="fitStart" value="3"/>
        <enum name="fitCenter" value="4"/>
        <enum name="fitEnd" value="5"/>
    </attr>
    <attr format="string" name="title"/>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <color name="abc_decor_view_status_guard">#ff000000</color>
    <color name="abc_decor_view_status_guard_light">#ffffffff</color>
    <color name="abc_search_url_text_normal">#7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="androidx_core_ripple_material_light">#1f000000</color>
    <color name="androidx_core_secondary_text_default_material_light">#8a000000</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="black">#FF000000</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="browser_actions_bg_grey">#F5F5F5</color>
    <color name="browser_actions_divider_color">#1E000000</color>
    <color name="browser_actions_text_color">#DE000000</color>
    <color name="browser_actions_title_color">#646464</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="call_notification_answer_color">#1d873b</color>
    <color name="call_notification_decline_color">#d93025</color>
    <color name="cardview_dark_background">#FF424242</color>
    <color name="cardview_light_background">#FFFFFFFF</color>
    <color name="cardview_shadow_end_color">#03000000</color>
    <color name="cardview_shadow_start_color">#37000000</color>
    <color name="com_facebook_blue">#1877F2</color>
    <color name="com_facebook_button_background_color">@color/com_facebook_blue</color>
    <color name="com_facebook_button_background_color_disabled">#EBEDF0</color>
    <color name="com_facebook_button_background_color_pressed">#186BDA</color>
    <color name="com_facebook_device_auth_text">#90949C</color>
    <color name="com_facebook_likeboxcountview_border_color">#6a7180</color>
    <color name="com_facebook_likeboxcountview_text_color">#6a7180</color>
    <color name="com_facebook_likeview_text_color">#6a7180</color>
    <color name="com_facebook_primary_button_disabled_text_color">#BCC3C9</color>
    <color name="com_facebook_primary_button_pressed_text_color">#DADDE2</color>
    <color name="com_facebook_primary_button_text_color">#FFFFFF</color>
    <color name="com_smart_login_code">#3C6499</color>
    <color name="common_google_signin_btn_text_dark_default">@android:color/white</color>
    <color name="common_google_signin_btn_text_dark_disabled">#1F000000</color>
    <color name="common_google_signin_btn_text_dark_focused">@android:color/black</color>
    <color name="common_google_signin_btn_text_dark_pressed">@android:color/white</color>
    <color name="common_google_signin_btn_text_light_default">#90000000</color>
    <color name="common_google_signin_btn_text_light_disabled">#1F000000</color>
    <color name="common_google_signin_btn_text_light_focused">#90000000</color>
    <color name="common_google_signin_btn_text_light_pressed">#DE000000</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="error_color_material_dark">#ff7043</color>
    <color name="error_color_material_light">#ff5722</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff008577</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_300">#ffe0e0e0</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="notification_material_background_media_default_color">#ff424242</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4Dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6FFFFFF</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <item name="abc_dialog_fixed_height_major" type="dimen">80%</item>
    <item name="abc_dialog_fixed_height_minor" type="dimen">100%</item>
    <item name="abc_dialog_fixed_width_major" type="dimen">320dp</item>
    <item name="abc_dialog_fixed_width_minor" type="dimen">320dp</item>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <item name="abc_dialog_min_width_major" type="dimen">65%</item>
    <item name="abc_dialog_min_width_minor" type="dimen">95%</item>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <item format="float" name="abc_disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="abc_disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="abc_dropdownitem_icon_width">32dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dip</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_height_large_material">80dp</dimen>
    <dimen name="abc_list_item_height_material">64dp</dimen>
    <dimen name="abc_list_item_height_small_material">48dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dip</dimen>
    <dimen name="abc_search_view_preferred_width">320dip</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_star_big">48dp</dimen>
    <dimen name="abc_star_medium">36dp</dimen>
    <dimen name="abc_star_small">16dp</dimen>
    <dimen name="abc_switch_padding">3dp</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="browser_actions_context_menu_max_width">500dp</dimen>
    <dimen name="browser_actions_context_menu_min_padding">20dp</dimen>
    <dimen name="cardview_compat_inset_shadow">1dp</dimen>
    <dimen name="cardview_default_elevation">2dp</dimen>
    <dimen name="cardview_default_radius">2dp</dimen>
    <dimen name="com_facebook_auth_dialog_corner_radius">2dp</dimen>
    <dimen name="com_facebook_auth_dialog_corner_radius_oversized">4dp</dimen>
    <dimen name="com_facebook_button_corner_radius">4dp</dimen>
    <dimen name="com_facebook_button_login_corner_radius">3dp</dimen>
    <dimen name="com_facebook_likeboxcountview_border_radius">3dp</dimen>
    <dimen name="com_facebook_likeboxcountview_border_width">1dp</dimen>
    <dimen name="com_facebook_likeboxcountview_caret_height">3dp</dimen>
    <dimen name="com_facebook_likeboxcountview_caret_width">6dp</dimen>
    <dimen name="com_facebook_likeboxcountview_text_padding">6dp</dimen>
    <dimen name="com_facebook_likeboxcountview_text_size">11.0sp</dimen>
    <dimen name="com_facebook_likeview_edge_padding">2dp</dimen>
    <dimen name="com_facebook_likeview_internal_padding">6dp</dimen>
    <dimen name="com_facebook_likeview_text_size">11.0sp</dimen>
    <dimen name="com_facebook_profilepictureview_preset_size_large">180dp</dimen>
    <dimen name="com_facebook_profilepictureview_preset_size_normal">100dp</dimen>
    <dimen name="com_facebook_profilepictureview_preset_size_small">50dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <item format="float" name="disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="disabled_alpha_material_light" type="dimen">0.26</item>
    <item format="float" name="highlight_alpha_material_colored" type="dimen">0.26</item>
    <item format="float" name="highlight_alpha_material_dark" type="dimen">0.20</item>
    <item format="float" name="highlight_alpha_material_light" type="dimen">0.12</item>
    <item format="float" name="hint_alpha_material_dark" type="dimen">0.50</item>
    <item format="float" name="hint_alpha_material_light" type="dimen">0.38</item>
    <item format="float" name="hint_pressed_alpha_material_dark" type="dimen">0.70</item>
    <item format="float" name="hint_pressed_alpha_material_light" type="dimen">0.54</item>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">4dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="subtitle_corner_radius">2dp</dimen>
    <dimen name="subtitle_outline_width">2dp</dimen>
    <dimen name="subtitle_shadow_offset">2dp</dimen>
    <dimen name="subtitle_shadow_radius">2dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="accessibility_action_clickable_span" type="id"/>
    <item name="accessibility_custom_action_0" type="id"/>
    <item name="accessibility_custom_action_1" type="id"/>
    <item name="accessibility_custom_action_10" type="id"/>
    <item name="accessibility_custom_action_11" type="id"/>
    <item name="accessibility_custom_action_12" type="id"/>
    <item name="accessibility_custom_action_13" type="id"/>
    <item name="accessibility_custom_action_14" type="id"/>
    <item name="accessibility_custom_action_15" type="id"/>
    <item name="accessibility_custom_action_16" type="id"/>
    <item name="accessibility_custom_action_17" type="id"/>
    <item name="accessibility_custom_action_18" type="id"/>
    <item name="accessibility_custom_action_19" type="id"/>
    <item name="accessibility_custom_action_2" type="id"/>
    <item name="accessibility_custom_action_20" type="id"/>
    <item name="accessibility_custom_action_21" type="id"/>
    <item name="accessibility_custom_action_22" type="id"/>
    <item name="accessibility_custom_action_23" type="id"/>
    <item name="accessibility_custom_action_24" type="id"/>
    <item name="accessibility_custom_action_25" type="id"/>
    <item name="accessibility_custom_action_26" type="id"/>
    <item name="accessibility_custom_action_27" type="id"/>
    <item name="accessibility_custom_action_28" type="id"/>
    <item name="accessibility_custom_action_29" type="id"/>
    <item name="accessibility_custom_action_3" type="id"/>
    <item name="accessibility_custom_action_30" type="id"/>
    <item name="accessibility_custom_action_31" type="id"/>
    <item name="accessibility_custom_action_4" type="id"/>
    <item name="accessibility_custom_action_5" type="id"/>
    <item name="accessibility_custom_action_6" type="id"/>
    <item name="accessibility_custom_action_7" type="id"/>
    <item name="accessibility_custom_action_8" type="id"/>
    <item name="accessibility_custom_action_9" type="id"/>
    <item name="action_bar_activity_content" type="id"/>
    <item name="action_bar_spinner" type="id"/>
    <item name="action_menu_divider" type="id"/>
    <item name="action_menu_presenter" type="id"/>
    <item name="androidx_compose_ui_view_composition_context" type="id"/>
    <item name="coil_request_manager" type="id"/>
    <item name="compose_view_saveable_id_tag" type="id"/>
    <item name="consume_window_insets_tag" type="id"/>
    <item name="fragment_container_view_tag" type="id"/>
    <item name="hide_graphics_layer_in_inspector_tag" type="id"/>
    <item name="hide_in_inspector_tag" type="id"/>
    <item name="home" type="id"/>
    <item name="inspection_slot_table_set" type="id"/>
    <item name="is_pooling_container_tag" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="nav_controller_view_tag" type="id"/>
    <item name="pooling_container_listener_holder_tag" type="id"/>
    <item name="progress_circular" type="id"/>
    <item name="progress_horizontal" type="id"/>
    <item name="report_drawn" type="id"/>
    <item name="special_effects_controller_view_tag" type="id"/>
    <item name="split_action_bar" type="id"/>
    <item name="tag_accessibility_actions" type="id"/>
    <item name="tag_accessibility_clickable_spans" type="id"/>
    <item name="tag_accessibility_heading" type="id"/>
    <item name="tag_accessibility_pane_title" type="id"/>
    <item name="tag_on_apply_window_listener" type="id"/>
    <item name="tag_on_receive_content_listener" type="id"/>
    <item name="tag_on_receive_content_mime_types" type="id"/>
    <item name="tag_screen_reader_focusable" type="id"/>
    <item name="tag_state_description" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="tag_window_insets_animation_callback" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="title" type="id"/>
    <item name="up" type="id"/>
    <id name="view_tree_lifecycle_owner"/>
    <id name="view_tree_on_back_pressed_dispatcher_owner"/>
    <id name="view_tree_saved_state_registry_owner"/>
    <id name="view_tree_view_model_store_owner"/>
    <item name="visible_removing_fragment_view_tag" type="id"/>
    <item name="wrapped_composition_tag" type="id"/>
    <integer name="abc_config_activityDefaultDur">220</integer>
    <integer name="abc_config_activityShortDur">150</integer>
    <integer name="cancel_button_image_alpha">127</integer>
    <integer name="config_tooltipAnimTime">150</integer>
    <integer name="google_play_services_version">12451000</integer>
    <integer name="m3c_window_layout_in_display_cutout_mode">1</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with <ns1:g example="Mail" id="application_name">%s</ns1:g></string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="about">About</string>
    <string name="add_additive">Add Additive</string>
    <string name="add_barcode">Add Barcode</string>
    <string name="add_custom_allergen">Add Custom Allergen</string>
    <string name="add_custom_certificate">Add Custom Certificate</string>
    <string name="add_custom_fat">Add Custom Fat</string>
    <string name="add_custom_item">Add Custom Item</string>
    <string name="add_custom_preservative">Add Custom Preservative</string>
    <string name="add_custom_sugar">Add Custom Sugar</string>
    <string name="add_image">Add Image</string>
    <string name="add_product_image">Add Product Image</string>
    <string name="add_to_favorites">Add to Favorites</string>
    <string name="additive_category">Category</string>
    <string name="additive_code">Additive Code</string>
    <string name="additive_description">Description</string>
    <string name="additive_detail">Additive Detail</string>
    <string name="additive_halal_status">Halal Status</string>
    <string name="additive_harmful_level">Harmful Level</string>
    <string name="additive_kosher_status">Kosher Status</string>
    <string name="additive_name">Additive Name</string>
    <string name="additive_notes">Notes</string>
    <string name="additive_unhealthy_level">Unhealthy Level</string>
    <string name="additive_vegan_status">Vegan Status</string>
    <string name="additives">Additives</string>
    <string name="additives_section">Additives</string>
    <string name="ai_model">AI Model</string>
    <string name="ai_model_selection">Select AI Model</string>
    <string name="allergen_detail">Allergen Detail</string>
    <string name="allergens">Allergens</string>
    <string name="allergens_section">Allergens</string>
    <string name="analysis_error">Analysis Error:</string>
    <string name="analysis_results">Analysis Results:</string>
    <string name="analyze">Analyze</string>
    <string name="analyze_product">Analyze Product</string>
    <string name="analyzing_ingredients">Analyzing ingredients...</string>
    <string name="androidx_startup" translatable="false">androidx.startup</string>
    <string name="antibiotic">Antibiotic</string>
    <string name="app_name">Healthy Products</string>
    <string name="applies_to">Applies To</string>
    <string name="back">Back</string>
    <string name="barcode_detected">Barcode detected: %1$s</string>
    <string name="barcode_scan_instruction">Point camera at barcode to scan</string>
    <string name="barcode_tab">Barcode</string>
    <string name="call_notification_answer_action">Answer</string>
    <string name="call_notification_answer_video_action">Video</string>
    <string name="call_notification_decline_action">Decline</string>
    <string name="call_notification_hang_up_action">Hang Up</string>
    <string name="call_notification_incoming_text">Incoming call</string>
    <string name="call_notification_ongoing_text">Ongoing call</string>
    <string name="call_notification_screening_text">Screening an incoming call</string>
    <string name="camera_permission_denied">Camera permission denied. You need to grant permission from app settings to use camera.</string>
    <string name="camera_permission_required">Camera permission required</string>
    <string name="cancel">Cancel</string>
    <string name="carcinogen">Carcinogen</string>
    <string name="certificate_detail">Certificate Detail</string>
    <string name="certificates">Certificates</string>
    <string name="certificates_section">Certificates</string>
    <string name="certification_criteria">Certification Criteria</string>
    <string name="certification_process">Certification Process</string>
    <string name="certification_validity">Certification Validity</string>
    <string name="certifying_body">Certifying Body</string>
    <string name="change_image">Change Image</string>
    <string name="checking_barcode">Checking barcode...</string>
    <string name="chemical_structure">Chemical Structure</string>
    <string name="choose_from_gallery">Choose from Gallery</string>
    <string name="clear_history">Clear History</string>
    <string name="close">Close</string>
    <string name="close_drawer">"Close navigation menu"</string>
    <string name="close_sheet">"Close sheet"</string>
    <string name="com_facebook_device_auth_instructions">Visit &lt;b>facebook.com/device&lt;/b> and enter the code shown above.</string>
    <string name="com_facebook_image_download_unknown_error">Unexpected error while downloading an image.</string>
    <string name="com_facebook_internet_permission_error_message">WebView login requires INTERNET permission</string>
    <string name="com_facebook_internet_permission_error_title">AndroidManifest Error</string>
    <string name="com_facebook_like_button_liked">Liked</string>
    <string name="com_facebook_like_button_not_liked">Like</string>
    <string name="com_facebook_loading">Loading…</string>
    <string name="com_facebook_loginview_cancel_action">Cancel</string>
    <string name="com_facebook_loginview_log_in_button">Log in</string>
    <string name="com_facebook_loginview_log_in_button_continue">Continue with Facebook</string>
    <string name="com_facebook_loginview_log_in_button_long">Log in with Facebook</string>
    <string name="com_facebook_loginview_log_out_action">Log out</string>
    <string name="com_facebook_loginview_log_out_button">Log out</string>
    <string name="com_facebook_loginview_logged_in_as">Logged in as: <ns1:g id="user name">%1$s</ns1:g></string>
    <string name="com_facebook_loginview_logged_in_using_facebook">Logged in using Facebook</string>
    <string name="com_facebook_send_button_text">Send</string>
    <string description="This is a button. It is located on the home page for all games. Tapping this allows the user to share this game as a post on their timeline." name="com_facebook_share_button_text">Share</string>
    <string name="com_facebook_smart_device_instructions">To connect your account, open the Facebook app on your mobile device and check for notifications.</string>
    <string name="com_facebook_smart_device_instructions_or">- OR -</string>
    <string name="com_facebook_smart_login_confirmation_cancel">Not you?</string>
    <string name="com_facebook_smart_login_confirmation_continue_as">Continue as <ns1:g id="user name">%1$s</ns1:g></string>
    <string name="com_facebook_smart_login_confirmation_title">Confirm Login</string>
    <string name="com_facebook_tooltip_default">You\'re in control - choose what info you want to share with apps.</string>
    <string msgid="2523291102206661146" name="common_google_play_services_enable_button">Enable</string>
    <string msgid="227660514972886228" name="common_google_play_services_enable_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t work unless you enable Google Play services.</string>
    <string msgid="5122002158466380389" name="common_google_play_services_enable_title">Enable Google Play services</string>
    <string msgid="7153882981874058840" name="common_google_play_services_install_button">Install</string>
    <string name="common_google_play_services_install_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run without Google Play services, which are missing from your device.</string>
    <string msgid="7215213145546190223" name="common_google_play_services_install_title">Get Google Play services</string>
    <string name="common_google_play_services_notification_channel_name">Google Play services availability</string>
    <string name="common_google_play_services_notification_ticker">Google Play services error</string>
    <string name="common_google_play_services_unknown_issue"><ns1:g id="app_name">%1$s</ns1:g> is having trouble with Google Play services. Please try again.</string>
    <string name="common_google_play_services_unsupported_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run without Google Play services, which are not supported by your device.</string>
    <string msgid="6556509956452265614" name="common_google_play_services_update_button">Update</string>
    <string msgid="9053896323427875356" name="common_google_play_services_update_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run unless you update Google Play services.</string>
    <string msgid="6006316683626838685" name="common_google_play_services_update_title">Update Google Play services</string>
    <string name="common_google_play_services_updating_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run without Google Play services, which are currently updating.</string>
    <string name="common_google_play_services_wear_update_text">New version of Google Play services needed. It will update itself shortly.</string>
    <string name="common_open_on_phone">Open on phone</string>
    <string name="common_products">Common Products</string>
    <string name="common_signin_button_text">Sign in</string>
    <string name="common_signin_button_text_long">Sign in with Google</string>
    <string name="confirm_password">Confirm Password</string>
    <string name="content_analysis">Content Analysis</string>
    <string name="copy_toast_msg">Link copied to clipboard</string>
    <string name="correct_content">Correct Content</string>
    <string name="custom_item">Custom Item</string>
    <string name="custom_item_name">Custom Item Name</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="data_upload_description">Upload food data to Firebase. This operation may take some time.</string>
    <string name="data_upload_error">Error uploading data</string>
    <string name="data_upload_screen_title">Data Upload</string>
    <string name="data_upload_success">Data uploaded successfully</string>
    <string name="default_error_message">"Invalid input"</string>
    <string name="default_popup_window_title" ns2:ignore="ExtraTranslation">"Pop-Up Window"</string>
    <string name="default_web_client_id" translatable="false">860618467975-sjnn72ruqqp99g3sm4j992qm0on8su7q.apps.googleusercontent.com</string>
    <string name="delete">Delete</string>
    <string name="description">Description</string>
    <string name="dietary_statuses">Dietary Statuses</string>
    <string name="dropdown_menu">"Dropdown menu"</string>
    <string name="edit">Edit</string>
    <string name="edit_additive">Edit Additive</string>
    <string name="edit_recognized_ingredients">You can edit the recognized ingredients:</string>
    <string name="email">Email</string>
    <string name="endocrine_disruptor">Endocrine Disruptor</string>
    <string name="error">Error</string>
    <string name="fallback_menu_item_copy_link">Copy link</string>
    <string name="fallback_menu_item_open_in_browser">Open in browser</string>
    <string name="fallback_menu_item_share_link">Share link</string>
    <string name="fat_detail">Fat Detail</string>
    <string name="fats">Fats</string>
    <string name="favorites">Favorites</string>
    <string name="food_analysis">Food Analysis</string>
    <string name="food_analysis_description">Analyze food ingredients for allergens, fats, sugars, preservatives and certifications</string>
    <string name="food_analysis_screen">Food Analysis</string>
    <string name="food_certificates">Food Certificates</string>
    <string name="food_halal_status">Food Halal Status</string>
    <string name="food_vegan_status">Food Vegan Status</string>
    <string name="frame_size">Frame Size:</string>
    <string name="functional_type">Functional Type</string>
    <string name="functional_types">Functional Types</string>
    <string name="gcm_defaultSenderId" translatable="false">860618467975</string>
    <string name="glycemic_index">Glycemic Index</string>
    <string name="glycemic_info">Glycemic Information</string>
    <string name="glycemic_load">Glycemic Load</string>
    <string name="google_api_key" translatable="false">AIzaSyA-4p5g1x9VRe-EqPTTTgWFiM757tFm0I8</string>
    <string name="google_app_id" translatable="false">1:860618467975:android:74e1755969ea43d2106622</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyA-4p5g1x9VRe-EqPTTTgWFiM757tFm0I8</string>
    <string name="google_storage_bucket" translatable="false">healthyproducts-d7ce9.firebasestorage.app</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="halal_status">Halal Status</string>
    <string name="harmful_ingredients">Harmful Ingredients</string>
    <string name="harmful_level">Harmful Level</string>
    <string name="health_assessment">Health Assessment</string>
    <string name="health_effects">Health Effects</string>
    <string name="home">Home</string>
    <string name="image_added">Image added</string>
    <string name="image_upload_error">Error uploading image</string>
    <string name="image_upload_success">Image uploaded successfully</string>
    <string name="import_allergens">Import Allergens</string>
    <string name="import_data">Import Data</string>
    <string name="import_error">Error importing data: %1$s</string>
    <string name="import_fats">Import Fats</string>
    <string name="import_preservatives">Import Preservatives</string>
    <string name="import_success">Successfully imported %1$d items</string>
    <string name="import_sugars">Import Sugars</string>
    <string name="importing">Importing...</string>
    <string name="in_progress">In progress</string>
    <string name="indeterminate">Partially checked</string>
    <string name="industrial_residue">Industrial Residue</string>
    <string name="ingredients">Ingredients</string>
    <string name="ingredients_corrected">Corrected Ingredients</string>
    <string name="ingredients_guide_text">Align INGREDIENTS section here</string>
    <string name="ingredients_label">Ingredients:</string>
    <string name="ingredients_raw">Raw OCR Result (Uncorrected)</string>
    <string name="ingredients_recognized">Ingredients recognized</string>
    <string name="kosher_status">Kosher Status</string>
    <string name="language">Language</string>
    <string name="language_changed">Language changed</string>
    <string name="language_english">English</string>
    <string name="language_selection">Select Language</string>
    <string name="language_turkish">Turkish</string>
    <string name="loading">Loading...</string>
    <string name="login">Login</string>
    <string name="login_required">Login required to save preferences</string>
    <string name="login_with_google">Login with Google</string>
    <string name="logout">Logout</string>
    <string name="m3c_bottom_sheet_collapse_description">Collapse bottom sheet</string>
    <string name="m3c_bottom_sheet_dismiss_description">Dismiss bottom sheet</string>
    <string name="m3c_bottom_sheet_drag_handle_description">Drag handle</string>
    <string name="m3c_bottom_sheet_expand_description">Expand bottom sheet</string>
    <string name="m3c_bottom_sheet_pane_title">Bottom Sheet</string>
    <string name="m3c_date_input_headline">Entered date</string>
    <string name="m3c_date_input_headline_description">Entered date: %1$s</string>
    <string name="m3c_date_input_invalid_for_pattern">Date does not match expected pattern: %1$s</string>
    <string name="m3c_date_input_invalid_not_allowed">Date not allowed: %1$s</string>
    <string name="m3c_date_input_invalid_year_range">
        Date out of expected year range %1$s - %2$s
    </string>
    <string name="m3c_date_input_label">Date</string>
    <string name="m3c_date_input_no_input_description">None</string>
    <string name="m3c_date_input_title">Select date</string>
    <string name="m3c_date_picker_headline">"Selected date"</string>
    <string name="m3c_date_picker_headline_description">Current selection: %1$s</string>
    <string name="m3c_date_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="m3c_date_picker_no_selection_description">None</string>
    <string name="m3c_date_picker_scroll_to_earlier_years">Scroll to show earlier years</string>
    <string name="m3c_date_picker_scroll_to_later_years">Scroll to show later years</string>
    <string name="m3c_date_picker_switch_to_calendar_mode">Switch to calendar input mode</string>
    <string name="m3c_date_picker_switch_to_day_selection">
        "Swipe to select a year, or tap to switch back to selecting a day"
    </string>
    <string name="m3c_date_picker_switch_to_input_mode">Switch to text input mode</string>
    <string name="m3c_date_picker_switch_to_next_month">"Change to next month"</string>
    <string name="m3c_date_picker_switch_to_previous_month">"Change to previous month"</string>
    <string name="m3c_date_picker_switch_to_year_selection">"Switch to selecting a year"</string>
    <string name="m3c_date_picker_title">"Select date"</string>
    <string name="m3c_date_picker_today_description">Today</string>
    <string name="m3c_date_picker_year_picker_pane_title">Year picker visible</string>
    <string name="m3c_date_range_input_invalid_range_input">Invalid date range input</string>
    <string name="m3c_date_range_input_title">Enter dates</string>
    <string name="m3c_date_range_picker_day_in_range">In range</string>
    <string name="m3c_date_range_picker_end_headline">End date</string>
    <string name="m3c_date_range_picker_scroll_to_next_month">Scroll to show the next month</string>
    <string name="m3c_date_range_picker_scroll_to_previous_month">Scroll to show the previous month</string>
    <string name="m3c_date_range_picker_start_headline">Start date</string>
    <string name="m3c_date_range_picker_title">Select dates</string>
    <string name="m3c_dialog">"Dialog"</string>
    <string name="m3c_dropdown_menu_collapsed">Collapsed</string>
    <string name="m3c_dropdown_menu_expanded">Expanded</string>
    <string name="m3c_dropdown_menu_toggle">Toggle dropdown menu</string>
    <string name="m3c_search_bar_search">Search</string>
    <string name="m3c_snackbar_dismiss">Dismiss</string>
    <string name="m3c_suggestions_available">Suggestions below</string>
    <string name="m3c_time_picker_am">AM</string>
    <string name="m3c_time_picker_hour">Hour</string>
    <string name="m3c_time_picker_hour_24h_suffix">%1$d hours</string>
    <string name="m3c_time_picker_hour_selection">Select hour</string>
    <string name="m3c_time_picker_hour_suffix">%1$d o\'clock</string>
    <string name="m3c_time_picker_hour_text_field">for hour</string>
    <string name="m3c_time_picker_minute">Minute</string>
    <string name="m3c_time_picker_minute_selection">Select minutes</string>
    <string name="m3c_time_picker_minute_suffix">%1$d minutes</string>
    <string name="m3c_time_picker_minute_text_field">for minutes</string>
    <string name="m3c_time_picker_period_toggle_description">Select AM or PM</string>
    <string name="m3c_time_picker_pm">PM</string>
    <string name="m3c_tooltip_long_press_label">Show tooltip</string>
    <string name="m3c_tooltip_pane_description">Tooltip</string>
    <string name="main_risk">Main Risk</string>
    <string name="manual_entry">Manual Entry</string>
    <string name="name">Name</string>
    <string name="name_cannot_be_empty">Name cannot be empty</string>
    <string name="navigation_menu">"Navigation menu"</string>
    <string name="no_preferences">No preferences found</string>
    <string name="no_preservatives_found">No preservatives found</string>
    <string name="no_recent_scans">No scans yet</string>
    <string name="no_results">No results found</string>
    <string name="not_selected">Not selected</string>
    <string name="notes">Notes</string>
    <string name="ocr_instructions">Place the ingredients list inside the frame</string>
    <string name="ocr_tab">Ingredients</string>
    <string name="ok">OK</string>
    <string name="origin">Origin</string>
    <string name="password">Password</string>
    <string name="pesticide">Pesticide</string>
    <string name="preferences_error">Error saving preferences</string>
    <string name="preferences_saved">Preferences saved successfully</string>
    <string name="preservative">Preservative</string>
    <string name="preservative_detail">Preservative Detail</string>
    <string name="preservatives">Preservatives</string>
    <string name="preservatives_section">Preservatives</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="product_brand">Product Brand</string>
    <string name="product_details">Product Details</string>
    <string name="product_found">Product found in database</string>
    <string name="product_name">Product Name</string>
    <string name="product_not_found">Product not found, performing OCR analysis</string>
    <string name="product_save_error">Error saving product</string>
    <string name="product_saved">Product saved successfully</string>
    <string name="profile">Profile</string>
    <string name="project_id" translatable="false">healthyproducts-d7ce9</string>
    <string name="range_end">"Range end"</string>
    <string name="range_start">"Range start"</string>
    <string name="raw_analysis_result">Raw Analysis Result</string>
    <string name="re_analyze">Re-analyze</string>
    <string name="recent_scans">Recent Scans</string>
    <string name="register">Register</string>
    <string name="register_with_google">Register with Google</string>
    <string name="religious_statuses">Religious Statuses</string>
    <string name="remove_from_favorites">Remove from Favorites</string>
    <string name="residue">Residue</string>
    <string name="retry">Retry</string>
    <string name="risk_if_missing">Risk If Missing</string>
    <string name="risk_level">Risk Level</string>
    <string name="save">Save</string>
    <string name="save_preferences">Save Preferences</string>
    <string name="save_product">Save Product</string>
    <string name="saving">Saving...</string>
    <string name="scan">Scan</string>
    <string name="scan_again">Scan Again</string>
    <string name="scan_barcode">Scan Barcode</string>
    <string name="scan_history">Scan History</string>
    <string name="scan_ingredients">Scan Ingredients</string>
    <string name="scan_instructions">Place the barcode inside the frame</string>
    <string name="scanning">Scanning...</string>
    <string name="search">Search</string>
    <string name="search_certificates">Search Certificates</string>
    <string name="search_menu_title">Search</string>
    <string name="searching_product">Searching product...</string>
    <string name="select_image_source">Select Image Source</string>
    <string name="selected">Selected</string>
    <string name="send_to_analysis">Send to Analysis</string>
    <string name="settings">Settings</string>
    <string name="settings_category_about">About App</string>
    <string name="settings_category_analysis">Analysis Preferences</string>
    <string name="settings_category_appearance">Appearance</string>
    <string name="show_halal_analysis">Show Halal Analysis</string>
    <string name="show_harmful_analysis">Show Harmful Analysis</string>
    <string name="show_kosher_analysis">Show Kosher Analysis</string>
    <string name="show_unhealthy_analysis">Show Unhealthy Analysis</string>
    <string name="show_vegan_analysis">Show Vegan Analysis</string>
    <string name="size_large">Large</string>
    <string name="size_medium">Medium</string>
    <string name="size_small">Small</string>
    <string name="state_empty">Empty</string>
    <string name="state_off">Off</string>
    <string name="state_on">On</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="status_halal">Halal</string>
    <string name="status_haram">Haram</string>
    <string name="status_harmful">Harmful</string>
    <string name="status_kosher">Kosher</string>
    <string name="status_not_kosher">Not Kosher</string>
    <string name="status_not_vegan">Not Vegan</string>
    <string name="status_not_vegetarian">Not Vegetarian</string>
    <string name="status_suspicious">Suspicious</string>
    <string name="status_unhealthy">Unhealthy</string>
    <string name="status_vegan">Vegan</string>
    <string name="status_vegetarian">Vegetarian</string>
    <string name="sugar_detail">Sugar Detail</string>
    <string name="sugars">Sugars</string>
    <string name="sugars_section">Sugars</string>
    <string name="summary_section">Summary</string>
    <string name="suspicious_ingredients">Suspicious Ingredients</string>
    <string name="switch_role">Switch</string>
    <string name="synthetic">Synthetic</string>
    <string name="tab">Tab</string>
    <string name="take_photo">Take Photo</string>
    <string name="template_percent"><ns1:g id="percentage">%1$d</ns1:g> percent.</string>
    <string name="terms_of_service">Terms of Service</string>
    <string name="tooltip_description">tooltip</string>
    <string name="tooltip_label">show tooltip</string>
    <string name="toxic">Toxic</string>
    <string name="try_again_button">Try Again</string>
    <string name="unhealthy_level">Unhealthy Level</string>
    <string name="upload_again_button">Upload Again</string>
    <string name="upload_data_button">Upload Data</string>
    <string name="uploading_data">Uploading data...</string>
    <string name="uploading_image">Uploading image...</string>
    <string name="usage">Usage</string>
    <string name="use_flash">Use Flash</string>
    <string name="user_food_preferences">My Food Preferences</string>
    <string name="user_warnings">User Warnings</string>
    <string name="vegan_status">Vegan Status</string>
    <string name="vegetarian_status">Vegetarian Status</string>
    <string name="version">Version</string>
    <string name="welcome_message">Welcome to Healthy Products</string>
    <style name="AlertDialog.AppCompat" parent="Base.AlertDialog.AppCompat"/>
    <style name="AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat.Light"/>
    <style name="Animation.AppCompat.Dialog" parent="Base.Animation.AppCompat.Dialog"/>
    <style name="Animation.AppCompat.DropDownUp" parent="Base.Animation.AppCompat.DropDownUp"/>
    <style name="Animation.AppCompat.Tooltip" parent="Base.Animation.AppCompat.Tooltip"/>
    <style name="Base.AlertDialog.AppCompat" parent="android:Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat"/>
    <style name="Base.Animation.AppCompat.Dialog" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="android:Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardUseCompatPadding">false</item>
        <item name="cardPreventCornerOverlap">true</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="android:Widget">
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="android:Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorLink">?android:textColorLink</item>
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title">
        <item name="android:textSize">18sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="android:TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="android:TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionModeTheme">?attr/actionBarTheme</item>

        
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Theme.AppCompat.Light"/>
    <style name="Base.ThemeOverlay.AppCompat" parent="Platform.ThemeOverlay.AppCompat"/>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>

        
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog"/>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>

        
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="Platform.AppCompat">
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        
        <item name="isLightTheme">false</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>

        
        <item name="actionModeTheme">?attr/actionBarTheme</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseContentDescription">@string/abc_action_mode_done</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorAccent">@color/accent_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>

        <item name="colorError">@color/error_color_material_dark</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="Platform.AppCompat.Light">
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        
        <item name="isLightTheme">true</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionModeTheme">?attr/actionBarTheme</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseContentDescription">@string/abc_action_mode_done</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorAccent">@color/accent_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>

        <item name="colorError">@color/error_color_material_light</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.AutoCompleteTextView">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="android:Widget.EditText">
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="android:Widget">
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleMargin">4dp</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="buttonGravity">top</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingStart">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_default_padding_end_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="height">?attr/actionBarSize</item>

        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>

        <item name="background">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="backgroundSplit">@null</item>

        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>

        <item name="android:gravity">center_vertical</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">8dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxLines">2</item>
        <item name="android:maxWidth">180dp</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
        <item name="android:layout_width">0dip</item>
        <item name="android:layout_weight">1</item>
        <item name="android:minWidth">80dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:scaleType">center</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>

        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleMargin">4dp</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="buttonGravity">top</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingStart">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_default_padding_end_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">6dip</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="Base.V7.Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Base.Widget.AppCompat.Button" parent="android:Widget">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">88dip</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored">
        <item name="android:background">@drawable/abc_btn_colored_material</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small">
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">48dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
        <item name="buttonCompat">?attr/listChoiceIndicatorMultipleAnimated</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.CompoundButton.RadioButton">
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
        <item name="buttonCompat">?attr/listChoiceIndicatorSingleAnimated</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="android:Widget.CompoundButton">
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="android:background">?attr/controlBackground</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="gapBetweenBars">3dp</item>
        <item name="drawableSize">24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="arrowHeadLength">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="Base.V7.Widget.AppCompat.EditText"/>
    <style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="android:Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="android:Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="android:Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="android:Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Holo.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Holo.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:maxHeight">36dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:maxHeight">16dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="android:Widget">
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar">
        <item name="queryBackground">@null</item>
        <item name="submitBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="defaultQueryHint">@string/abc_search_hint</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
        <item name="android:focusable">true</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="Platform.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">left|start|center_vertical</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView" parent="android:Widget.TextView"/>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="Base.V7.Widget.AppCompat.Toolbar"/>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="CardView" parent="Base.CardView">
    </style>
    <style name="CardView.Dark">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="DialogWindowTheme">
        <item name="android:windowClipToOutline">false</item>
    </style>
    <style name="EdgeToEdgeFloatingDialogTheme" parent="android:Theme.DeviceDefault.Dialog">
        <item name="android:windowLayoutInDisplayCutoutMode" ns2:targetApi="27">@integer/m3c_window_layout_in_display_cutout_mode</item>
        <item name="android:windowClipToOutline">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowElevation">0dp</item>
    </style>
    <style name="EdgeToEdgeFloatingDialogWindowTheme">
        <item name="android:dialogTheme">@style/EdgeToEdgeFloatingDialogTheme</item>
    </style>
    <style name="FloatingDialogTheme">
        <item name="android:windowIsFloating">false</item>
    </style>
    <style name="FloatingDialogWindowTheme">
        <item name="android:windowClipToOutline">false</item>
        <item name="android:dialogTheme">@style/FloatingDialogTheme</item>
    </style>
    <style name="Platform.AppCompat" parent="android:Theme.Holo">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_dark</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="listChoiceIndicatorSingleAnimated">@drawable/abc_btn_radio_material_anim</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="listChoiceIndicatorMultipleAnimated">@drawable/abc_btn_check_material_anim</item>

        
        <item name="android:textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="android:textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        
        <item name="android:listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="android:listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="android:listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="android:Theme.Holo.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_light</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="listChoiceIndicatorSingleAnimated">@drawable/abc_btn_radio_material_anim</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="listChoiceIndicatorMultipleAnimated">@drawable/abc_btn_check_material_anim</item>

        
        <item name="android:textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="android:textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        
        <item name="android:listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="android:listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="android:listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat" parent=""/>
    <style name="Platform.ThemeOverlay.AppCompat.Dark">
        
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light">
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="android:Widget.Holo.Spinner"/>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|left</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingRight">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="android:Widget">
        <item name="android:layout_marginLeft">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="Base.TextAppearance.AppCompat"/>
    <style name="TextAppearance.AppCompat.Body1" parent="Base.TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.AppCompat.Body2" parent="Base.TextAppearance.AppCompat.Body2"/>
    <style name="TextAppearance.AppCompat.Button" parent="Base.TextAppearance.AppCompat.Button"/>
    <style name="TextAppearance.AppCompat.Caption" parent="Base.TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.AppCompat.Display1" parent="Base.TextAppearance.AppCompat.Display1"/>
    <style name="TextAppearance.AppCompat.Display2" parent="Base.TextAppearance.AppCompat.Display2"/>
    <style name="TextAppearance.AppCompat.Display3" parent="Base.TextAppearance.AppCompat.Display3"/>
    <style name="TextAppearance.AppCompat.Display4" parent="Base.TextAppearance.AppCompat.Display4"/>
    <style name="TextAppearance.AppCompat.Headline" parent="Base.TextAppearance.AppCompat.Headline"/>
    <style name="TextAppearance.AppCompat.Inverse" parent="Base.TextAppearance.AppCompat.Inverse"/>
    <style name="TextAppearance.AppCompat.Large" parent="Base.TextAppearance.AppCompat.Large"/>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="Base.TextAppearance.AppCompat.Large.Inverse"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="TextAppearance.AppCompat.SearchResult.Subtitle"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="TextAppearance.AppCompat.SearchResult.Title"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Medium" parent="Base.TextAppearance.AppCompat.Medium"/>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="Base.TextAppearance.AppCompat.Medium.Inverse"/>
    <style name="TextAppearance.AppCompat.Menu" parent="Base.TextAppearance.AppCompat.Menu"/>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="Base.TextAppearance.AppCompat.Small"/>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="Base.TextAppearance.AppCompat.Small.Inverse"/>
    <style name="TextAppearance.AppCompat.Subhead" parent="Base.TextAppearance.AppCompat.Subhead"/>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="Base.TextAppearance.AppCompat.Subhead.Inverse"/>
    <style name="TextAppearance.AppCompat.Title" parent="Base.TextAppearance.AppCompat.Title"/>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="Base.TextAppearance.AppCompat.Title.Inverse"/>
    <style name="TextAppearance.AppCompat.Tooltip" parent="Base.TextAppearance.AppCompat.Tooltip"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Title"/>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="Base.TextAppearance.AppCompat.Widget.Button"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="Base.TextAppearance.AppCompat.Widget.Button.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="Base.TextAppearance.AppCompat.Widget.Switch"/>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Media"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media"/>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="TextAppearance.Compat.Notification.Title.Media"/>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AppCompat" parent="Base.Theme.AppCompat"/>
    <style name="Theme.AppCompat.CompactMenu" parent="Base.Theme.AppCompat.CompactMenu"/>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.Light.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/>
    <style name="Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.Dialog.Alert" parent="Base.Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="Base.Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Empty" parent=""/>
    <style name="Theme.AppCompat.Light" parent="Base.Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="Base.Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="Base.Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.HealthyProducts" parent="android:Theme.Material.Light.NoActionBar"/>
    <style name="ThemeOverlay.AppCompat" parent="Base.ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="Base.ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dark" parent="Base.ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="Base.ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="ThemeOverlay.AppCompat.DayNight.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat.Dialog"/>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="Base.ThemeOverlay.AppCompat.Dialog.Alert"/>
    <style name="ThemeOverlay.AppCompat.Light" parent="Base.ThemeOverlay.AppCompat.Light"/>
    <style name="Widget.AppCompat.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="Base.Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="Base.Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.ActionMode" parent="Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="Base.Widget.AppCompat.Button"/>
    <style name="Widget.AppCompat.Button.Borderless" parent="Base.Widget.AppCompat.Button.Borderless"/>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="Base.Widget.AppCompat.Button.Borderless.Colored"/>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.Button.Colored" parent="Base.Widget.AppCompat.Button.Colored"/>
    <style name="Widget.AppCompat.Button.Small" parent="Base.Widget.AppCompat.Button.Small"/>
    <style name="Widget.AppCompat.ButtonBar" parent="Base.Widget.AppCompat.ButtonBar"/>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="Base.Widget.AppCompat.CompoundButton.CheckBox"/>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="Base.Widget.AppCompat.CompoundButton.RadioButton"/>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="Base.Widget.AppCompat.CompoundButton.Switch"/>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/>
    <style name="Widget.AppCompat.EditText" parent="Base.Widget.AppCompat.EditText"/>
    <style name="Widget.AppCompat.ImageButton" parent="Base.Widget.AppCompat.ImageButton"/>
    <style name="Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionButton" parent="Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="Widget.AppCompat.ActionMode"/>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="Widget.AppCompat.ActivityChooserView"/>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="Widget.AppCompat.DropDownItem.Spinner"/>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="Base.Widget.AppCompat.Light.PopupMenu"/>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.ListMenuView" parent="Base.Widget.AppCompat.ListMenuView"/>
    <style name="Widget.AppCompat.ListPopupWindow" parent="Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="Base.Widget.AppCompat.ListView"/>
    <style name="Widget.AppCompat.ListView.DropDown" parent="Base.Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.ListView.Menu" parent="Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Widget.AppCompat.PopupMenu" parent="Base.Widget.AppCompat.PopupMenu"/>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="Base.Widget.AppCompat.RatingBar"/>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="Base.Widget.AppCompat.RatingBar.Indicator"/>
    <style name="Widget.AppCompat.RatingBar.Small" parent="Base.Widget.AppCompat.RatingBar.Small"/>
    <style name="Widget.AppCompat.SearchView" parent="Base.Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="Base.Widget.AppCompat.SearchView.ActionBar"/>
    <style name="Widget.AppCompat.SeekBar" parent="Base.Widget.AppCompat.SeekBar"/>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="Base.Widget.AppCompat.SeekBar.Discrete"/>
    <style name="Widget.AppCompat.Spinner" parent="Base.Widget.AppCompat.Spinner"/>
    <style name="Widget.AppCompat.Spinner.DropDown"/>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="Base.Widget.AppCompat.Spinner.Underlined"/>
    <style name="Widget.AppCompat.TextView" parent="Base.Widget.AppCompat.TextView"/>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="Base.Widget.AppCompat.TextView.SpinnerItem"/>
    <style name="Widget.AppCompat.Toolbar" parent="Base.Widget.AppCompat.Toolbar"/>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="Base.Widget.AppCompat.Toolbar.Button.Navigation"/>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <style name="Widget.Support.CoordinatorLayout" parent="android:Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <style name="com_facebook_activity_theme" parent="@style/Theme.AppCompat.NoActionBar">
      <item name="android:windowIsTranslucent">true</item>
      <item name="android:windowBackground">@android:color/transparent</item>
      <item name="android:windowNoTitle">true</item>
    </style>
    <style name="com_facebook_auth_dialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>
    <style name="com_facebook_auth_dialog_instructions_textview" ns2:targetApi="17">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">roboto</item>
        <item name="android:gravity">center</item>
        <item name="android:drawablePadding">12dp</item>
        <item name="android:singleLine">false</item>
        <item name="android:textColor">@color/com_facebook_device_auth_text</item>
        <item name="android:textSize">16sp</item>
        <item name="android:typeface">sans</item>
    </style>
    <style name="com_facebook_button" parent="@android:style/Widget.Button">
        <item name="android:background">@drawable/com_facebook_button_background</item>
        <item name="android:drawablePadding">4dp</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingBottom">5dp</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:textColor">@color/com_facebook_button_text_color</item>
        <item name="android:textSize">14.0dp</item>
    </style>
    <style name="com_facebook_button_like" parent="com_facebook_button">
        <item name="android:background">@drawable/com_facebook_button_like_background</item>
    </style>
    <style name="com_facebook_loginview_default_style" parent="com_facebook_button"/>
    <style name="tooltip_bubble_text">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">left</item>
        <item name="android:textSize">12sp</item>
        <item name="android:shadowDy">-1</item>
        <item name="android:shadowRadius">0.25</item>
        <item name="android:shadowColor">#40000000</item>
    </style>
    <declare-styleable name="ActionBar">
        
        <attr name="navigationMode">
            
            <enum name="normal" value="0"/>
            
            <enum name="listMode" value="1"/>
            
            <enum name="tabMode" value="2"/>
        </attr>
        
        <attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr>
        
        <attr name="title"/>
        
        <attr format="string" name="subtitle"/>
        
        <attr format="reference" name="titleTextStyle"/>
        
        <attr format="reference" name="subtitleTextStyle"/>
        
        <attr format="reference" name="icon"/>
        
        <attr format="reference" name="logo"/>
        
        <attr format="reference" name="divider"/>
        
        <attr format="reference" name="background"/>
        
        <attr format="reference|color" name="backgroundStacked"/>
        
        <attr format="reference|color" name="backgroundSplit"/>
        
        <attr format="reference" name="customNavigationLayout"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="homeLayout"/>
        
        <attr format="reference" name="progressBarStyle"/>
        
        <attr format="reference" name="indeterminateProgressStyle"/>
        
        <attr format="dimension" name="progressBarPadding"/>
        
        <attr name="homeAsUpIndicator"/>
        
        <attr format="dimension" name="itemPadding"/>
        
        <attr format="boolean" name="hideOnContentScroll"/>
        
        <attr format="dimension" name="contentInsetStart"/>
        
        <attr format="dimension" name="contentInsetEnd"/>
        
        <attr format="dimension" name="contentInsetLeft"/>
        
        <attr format="dimension" name="contentInsetRight"/>
        
        <attr format="dimension" name="contentInsetStartWithNavigation"/>
        
        <attr format="dimension" name="contentInsetEndWithActions"/>
        
        <attr format="dimension" name="elevation"/>
        
        <attr format="reference" name="popupTheme"/>
    </declare-styleable>
    <declare-styleable name="ActionBarLayout">
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuItemView">
        <attr name="android:minWidth"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuView">
        
    </declare-styleable>
    <declare-styleable name="ActionMode">
        
        <attr name="titleTextStyle"/>
        
        <attr name="subtitleTextStyle"/>
        
        <attr name="background"/>
        
        <attr name="backgroundSplit"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="closeItemLayout"/>
    </declare-styleable>
    <declare-styleable name="ActivityChooserView">
        
        <attr format="string" name="initialActivityCount"/>
        
        <attr format="reference" name="expandActivityOverflowButtonDrawable"/>
    </declare-styleable>
    <declare-styleable name="ActivityNavigator">
        <attr name="android:name"/>
        <attr format="string" name="action"/>
        <attr format="string" name="data"/>
        <attr format="string" name="dataPattern"/>
        <attr format="string" name="targetPackage"/>
    </declare-styleable>
    <declare-styleable name="AlertDialog">
        <attr name="android:layout"/>
        <attr format="reference" name="buttonPanelSideLayout"/>
        <attr format="reference" name="listLayout"/>
        <attr format="reference" name="multiChoiceItemLayout"/>
        <attr format="reference" name="singleChoiceItemLayout"/>
        <attr format="reference" name="listItemLayout"/>
        <attr format="boolean" name="showTitle"/>
        <attr format="dimension" name="buttonIconDimen"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableCompat">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableItem">
        
        <attr name="android:drawable"/>
        
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableTransition">
        
        <attr name="android:fromId"/>
        
        <attr name="android:toId"/>
        
        <attr name="android:drawable"/>
        
        <attr name="android:reversible"/>
    </declare-styleable>
    <declare-styleable name="AppCompatEmojiHelper">

    </declare-styleable>
    <declare-styleable name="AppCompatImageView">
        <attr name="android:src"/>
        
        <attr format="reference" name="srcCompat"/>

        
        <attr format="color" name="tint"/>

        
        <attr name="tintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatSeekBar">
        <attr name="android:thumb"/>
        
        <attr format="reference" name="tickMark"/>
        
        <attr format="color" name="tickMarkTint"/>
        
        <attr name="tickMarkTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTextHelper">
        <attr name="android:drawableLeft"/>
        <attr name="android:drawableTop"/>
        <attr name="android:drawableRight"/>
        <attr name="android:drawableBottom"/>
        <attr name="android:drawableStart"/>
        <attr name="android:drawableEnd"/>
        <attr name="android:textAppearance"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTextView">
        
        <attr format="reference|boolean" name="textAllCaps"/>
        
        <attr format="string" name="textLocale"/>
        <attr name="android:textAppearance"/>
        
        <attr format="enum" name="autoSizeTextType">
            
            <enum name="none" value="0"/>
            
            <enum name="uniform" value="1"/>
        </attr>
        
        <attr format="dimension" name="autoSizeStepGranularity"/>
        
        <attr format="reference" name="autoSizePresetSizes"/>
        
        <attr format="dimension" name="autoSizeMinTextSize"/>
        
        <attr format="dimension" name="autoSizeMaxTextSize"/>
        
        <attr format="string" name="fontFamily"/>
        
        <attr format="dimension" name="lineHeight"/>
        
        <attr format="dimension" name="firstBaselineToTopHeight"/>
        
        <attr format="dimension" name="lastBaselineToBottomHeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="reference" name="drawableLeftCompat"/>
        <attr format="reference" name="drawableTopCompat"/>
        <attr format="reference" name="drawableRightCompat"/>
        <attr format="reference" name="drawableBottomCompat"/>
        <attr format="reference" name="drawableStartCompat"/>
        <attr format="reference" name="drawableEndCompat"/>
        
        <attr format="color" name="drawableTint"/>
        
        <attr name="drawableTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
        
        <attr format="boolean" name="emojiCompatEnabled"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTheme">

        
        
        
        <eat-comment/>

        
        <attr format="boolean" name="windowActionBar"/>

        
        <attr format="boolean" name="windowNoTitle"/>

        
        <attr format="boolean" name="windowActionBarOverlay"/>

        
        <attr format="boolean" name="windowActionModeOverlay"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMinor"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMinor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMajor"/>

        
        <attr format="dimension|fraction" name="windowMinWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowMinWidthMinor"/>

        <attr name="android:windowIsFloating"/>
        <attr name="android:windowAnimationStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="actionBarTabStyle"/>
        <attr format="reference" name="actionBarTabBarStyle"/>
        <attr format="reference" name="actionBarTabTextStyle"/>
        <attr format="reference" name="actionOverflowButtonStyle"/>
        <attr format="reference" name="actionOverflowMenuStyle"/>
        
        <attr format="reference" name="actionBarPopupTheme"/>
        
        <attr format="reference" name="actionBarStyle"/>
        
        <attr format="reference" name="actionBarSplitStyle"/>
        
        <attr format="reference" name="actionBarTheme"/>
        
        <attr format="reference" name="actionModeTheme"/>
        
        <attr format="reference" name="actionBarWidgetTheme"/>
        
        <attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr>
        
        <attr format="reference" name="actionBarDivider"/>
        
        <attr format="reference" name="actionBarItemBackground"/>
        
        <attr format="reference" name="actionMenuTextAppearance"/>
        
        
        <attr format="color|reference" name="actionMenuTextColor"/>


        
        
        
        <eat-comment/>
        <attr format="reference" name="actionModeStyle"/>
        <attr format="reference" name="actionModeCloseButtonStyle"/>
        
        <attr format="reference" name="actionModeBackground"/>
        
        <attr format="reference" name="actionModeSplitBackground"/>
        
        <attr format="reference" name="actionModeCloseDrawable"/>
        
        <attr format="reference" name="actionModeCutDrawable"/>
        
        <attr format="reference" name="actionModeCopyDrawable"/>
        
        <attr format="reference" name="actionModePasteDrawable"/>
        
        <attr format="reference" name="actionModeSelectAllDrawable"/>
        
        <attr format="reference" name="actionModeShareDrawable"/>
        
        <attr format="reference" name="actionModeFindDrawable"/>
        
        <attr format="reference" name="actionModeWebSearchDrawable"/>

        
        <attr format="string" name="actionModeCloseContentDescription"/>

        
        <attr format="reference" name="actionModePopupWindowStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceLargePopupMenu"/>
        
        <attr format="reference" name="textAppearanceSmallPopupMenu"/>
        
        <attr format="reference" name="textAppearancePopupMenuHeader"/>


        
        
        
        <eat-comment/>

        
        <attr format="reference" name="dialogTheme"/>
        
        <attr format="dimension" name="dialogPreferredPadding"/>
        
        <attr format="reference" name="listDividerAlertDialog"/>
        
        <attr format="dimension" name="dialogCornerRadius"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="actionDropDownStyle"/>
        
        <attr format="dimension" name="dropdownListPreferredItemHeight"/>
        
        <attr format="reference" name="spinnerDropDownItemStyle"/>
        
        <attr format="reference" name="homeAsUpIndicator"/>

        
        <attr format="reference" name="actionButtonStyle"/>

        
        <attr format="reference" name="buttonBarStyle"/>
        
        <attr format="reference" name="buttonBarButtonStyle"/>
        
        <attr format="reference" name="selectableItemBackground"/>
        
        <attr format="reference" name="selectableItemBackgroundBorderless"/>
        
        <attr format="reference" name="borderlessButtonStyle"/>
        
        <attr format="reference" name="dividerVertical"/>
        
        <attr format="reference" name="dividerHorizontal"/>
        
        <attr format="reference" name="activityChooserViewStyle"/>

        
        <attr format="reference" name="toolbarStyle"/>
        
        <attr format="reference" name="toolbarNavigationButtonStyle"/>

        
        <attr format="reference" name="popupMenuStyle"/>
        
        <attr format="reference" name="popupWindowStyle"/>

        
        <attr format="reference|color" name="editTextColor"/>
        
        <attr format="reference" name="editTextBackground"/>

        
        <attr format="reference" name="imageButtonStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceSearchResultTitle"/>
        
        <attr format="reference" name="textAppearanceSearchResultSubtitle"/>
        
        <attr format="reference|color" name="textColorSearchUrl"/>
        
        <attr format="reference" name="searchViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="dimension" name="listPreferredItemHeight"/>
        
        <attr format="dimension" name="listPreferredItemHeightSmall"/>
        
        <attr format="dimension" name="listPreferredItemHeightLarge"/>

        
        <attr format="dimension" name="listPreferredItemPaddingLeft"/>
        
        <attr format="dimension" name="listPreferredItemPaddingRight"/>

        
        <attr format="dimension" name="listPreferredItemPaddingStart"/>
        
        <attr format="dimension" name="listPreferredItemPaddingEnd"/>

        
        <attr format="reference" name="dropDownListViewStyle"/>
        <attr format="reference" name="listPopupWindowStyle"/>

        
        <attr format="reference" name="textAppearanceListItem"/>
        
        <attr format="reference" name="textAppearanceListItemSecondary"/>
        
        <attr format="reference" name="textAppearanceListItemSmall"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="panelBackground"/>
        
        <attr format="dimension" name="panelMenuListWidth"/>
        
        <attr format="reference" name="panelMenuListTheme"/>
        
        <attr format="reference" name="listChoiceBackgroundIndicator"/>

        
        
        
        <eat-comment/>

        
        <attr format="color" name="colorPrimary"/>

        
        <attr format="color" name="colorPrimaryDark"/>

        
        <attr format="color" name="colorAccent"/>

        
        <attr format="color" name="colorControlNormal"/>

        
        <attr format="color" name="colorControlActivated"/>

        
        <attr format="color" name="colorControlHighlight"/>

        
        <attr format="color" name="colorButtonNormal"/>

        
        <attr format="color" name="colorSwitchThumbNormal"/>

        
        <attr format="reference" name="controlBackground"/>

        
        <attr format="color" name="colorBackgroundFloating"/>

        
        
        
        <eat-comment/>
        <attr format="reference" name="alertDialogStyle"/>
        <attr format="reference" name="alertDialogButtonGroupStyle"/>
        <attr format="boolean" name="alertDialogCenterButtons"/>
        
        <attr format="reference" name="alertDialogTheme"/>

        
        <attr format="reference|color" name="textColorAlertDialogListItem"/>

        
        <attr format="reference" name="buttonBarPositiveButtonStyle"/>

        
        <attr format="reference" name="buttonBarNegativeButtonStyle"/>

        
        <attr format="reference" name="buttonBarNeutralButtonStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="autoCompleteTextViewStyle"/>
        
        <attr format="reference" name="buttonStyle"/>
        
        <attr format="reference" name="buttonStyleSmall"/>
        
        <attr format="reference" name="checkboxStyle"/>
        
        <attr format="reference" name="checkedTextViewStyle"/>
        
        <attr format="reference" name="editTextStyle"/>
        
        <attr format="reference" name="radioButtonStyle"/>
        
        <attr format="reference" name="ratingBarStyle"/>
        
        <attr format="reference" name="ratingBarStyleIndicator"/>
        
        <attr format="reference" name="ratingBarStyleSmall"/>
        
        <attr format="reference" name="seekBarStyle"/>
        
        <attr format="reference" name="spinnerStyle"/>
        
        <attr format="reference" name="switchStyle"/>

        
        <attr format="reference" name="listMenuViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="tooltipFrameBackground"/>
        
        <attr format="reference|color" name="tooltipForegroundColor"/>

        
        <attr format="reference|color" name="colorError"/>

        <attr format="string" name="viewInflaterClass"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="listChoiceIndicatorSingleAnimated"/>
        
        <attr format="reference" name="listChoiceIndicatorMultipleAnimated"/>

    </declare-styleable>
    <declare-styleable name="ButtonBarLayout">
        
        <attr format="boolean" name="allowStacking"/>
    </declare-styleable>
    <declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable>
    <declare-styleable name="CardView">
        
        <attr format="color" name="cardBackgroundColor"/>
        
        <attr format="dimension" name="cardCornerRadius"/>
        
        <attr format="dimension" name="cardElevation"/>
        
        <attr format="dimension" name="cardMaxElevation"/>
        
        <attr format="boolean" name="cardUseCompatPadding"/>
        
        <attr format="boolean" name="cardPreventCornerOverlap"/>
        
        <attr format="dimension" name="contentPadding"/>
        
        <attr format="dimension" name="contentPaddingLeft"/>
        
        <attr format="dimension" name="contentPaddingRight"/>
        
        <attr format="dimension" name="contentPaddingTop"/>
        
        <attr format="dimension" name="contentPaddingBottom"/>
        
        <attr name="android:minWidth"/>
        
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="CheckedTextView">
        <attr name="android:checkMark"/>
        
        <attr format="reference" name="checkMarkCompat"/>
        
        <attr format="color" name="checkMarkTint"/>

        
        <attr name="checkMarkTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable>
    <declare-styleable name="CompoundButton">
        <attr name="android:button"/>
        
        <attr format="reference" name="buttonCompat"/>
        
        <attr format="color" name="buttonTint"/>

        
        <attr name="buttonTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout">
        
        <attr format="reference" name="keylines"/>
        
        <attr format="color|reference" name="statusBarBackground"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout_Layout">
        <attr name="android:layout_gravity"/>
        
        <attr format="string" name="layout_behavior"/>
        
        <attr format="reference" name="layout_anchor"/>
        
        <attr format="integer" name="layout_keyline"/>

        
        <attr name="layout_anchorGravity">
            
            <flag name="top" value="0x30"/>
            
            <flag name="bottom" value="0x50"/>
            
            <flag name="left" value="0x03"/>
            
            <flag name="right" value="0x05"/>
            
            <flag name="center_vertical" value="0x10"/>
            
            <flag name="fill_vertical" value="0x70"/>
            
            <flag name="center_horizontal" value="0x01"/>
            
            <flag name="fill_horizontal" value="0x07"/>
            
            <flag name="center" value="0x11"/>
            
            <flag name="fill" value="0x77"/>
            
            <flag name="clip_vertical" value="0x80"/>
            
            <flag name="clip_horizontal" value="0x08"/>
            
            <flag name="start" value="0x00800003"/>
            
            <flag name="end" value="0x00800005"/>
        </attr>

        
        <attr format="enum" name="layout_insetEdge">
            
            <enum name="none" value="0x0"/>
            
            <enum name="top" value="0x30"/>
            
            <enum name="bottom" value="0x50"/>
            
            <enum name="left" value="0x03"/>
            
            <enum name="right" value="0x05"/>
            
            <enum name="start" value="0x00800003"/>
            
            <enum name="end" value="0x00800005"/>
        </attr>
        
        <attr name="layout_dodgeInsetEdges">
            
            <flag name="none" value="0x0"/>
            
            <flag name="top" value="0x30"/>
            
            <flag name="bottom" value="0x50"/>
            
            <flag name="left" value="0x03"/>
            
            <flag name="right" value="0x05"/>
            
            <flag name="start" value="0x00800003"/>
            
            <flag name="end" value="0x00800005"/>
            
            <flag name="all" value="0x77"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="DrawerArrowToggle">
        
        <attr format="color" name="color"/>
        
        <attr format="boolean" name="spinBars"/>
        
        <attr format="dimension" name="drawableSize"/>
        
        <attr format="dimension" name="gapBetweenBars"/>
        
        <attr format="dimension" name="arrowHeadLength"/>
        
        <attr format="dimension" name="arrowShaftLength"/>
        
        <attr format="dimension" name="barLength"/>
        
        <attr format="dimension" name="thickness"/>
    </declare-styleable>
    <declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            
            <enum name="blocking" value="0"/>
            
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat">
        
        <attr name="android:orientation"/>
        <attr name="android:gravity"/>
        
        <attr name="android:baselineAligned"/>
        
        <attr name="android:baselineAlignedChildIndex"/>
        
        <attr name="android:weightSum"/>
        
        <attr format="boolean" name="measureWithLargestChild"/>
        
        <attr name="divider"/>
        
        <attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr>
        
        <attr format="dimension" name="dividerPadding"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat_Layout">
        <attr name="android:layout_width"/>
        <attr name="android:layout_height"/>
        <attr name="android:layout_weight"/>
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ListPopupWindow">
        
        <attr name="android:dropDownVerticalOffset"/>
        
        <attr name="android:dropDownHorizontalOffset"/>
    </declare-styleable>
    <declare-styleable name="LoadingImageView"><attr name="imageAspectRatioAdjust">
<enum name="none" value="0"/>

<enum name="adjust_width" value="1"/>

<enum name="adjust_height" value="2"/>

</attr>

<attr format="float" name="imageAspectRatio"/>

<attr format="boolean" name="circleCrop"/>
</declare-styleable>
    <declare-styleable name="MenuGroup">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:checkableBehavior"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

    </declare-styleable>
    <declare-styleable name="MenuItem">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:title"/>

        
        <attr name="android:titleCondensed"/>

        
        <attr name="android:icon"/>

        
        <attr name="android:alphabeticShortcut"/>

        
        <attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:numericShortcut"/>

        
        <attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:checkable"/>

        
        <attr name="android:checked"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

        
        <attr name="android:onClick"/>

        
        <attr name="showAsAction">
            
            <flag name="never" value="0"/>
            
            <flag name="ifRoom" value="1"/>
            
            <flag name="always" value="2"/>
            
            <flag name="withText" value="4"/>
            
            <flag name="collapseActionView" value="8"/>
        </attr>

        
        <attr format="reference" name="actionLayout"/>

        
        <attr format="string" name="actionViewClass"/>

        
        <attr format="string" name="actionProviderClass"/>

        
        <attr format="string" name="contentDescription"/>

        
        <attr format="string" name="tooltipText"/>

        
        <attr format="color" name="iconTint"/>

        
        <attr name="iconTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>

    </declare-styleable>
    <declare-styleable name="MenuView">
        
        <attr name="android:itemTextAppearance"/>
        
        <attr name="android:horizontalDivider"/>
        
        <attr name="android:verticalDivider"/>
        
        <attr name="android:headerBackground"/>
        
        <attr name="android:itemBackground"/>
        
        <attr name="android:windowAnimationStyle"/>
        
        <attr name="android:itemIconDisabledAlpha"/>
        
        <attr format="boolean" name="preserveIconSpacing"/>
        
        <attr format="reference" name="subMenuArrow"/>
    </declare-styleable>
    <declare-styleable name="NavAction">
        <attr name="android:id"/>
        <attr format="reference" name="destination"/>
        <attr format="boolean" name="launchSingleTop"/>
        <attr format="boolean" name="restoreState"/>
        <attr format="reference" name="popUpTo"/>
        <attr format="boolean" name="popUpToInclusive"/>
        <attr format="boolean" name="popUpToSaveState"/>
        <attr format="reference" name="enterAnim"/>
        <attr format="reference" name="exitAnim"/>
        <attr format="reference" name="popEnterAnim"/>
        <attr format="reference" name="popExitAnim"/>
    </declare-styleable>
    <declare-styleable name="NavArgument">
        <attr name="android:name"/>
        <attr name="android:defaultValue"/>
        <attr format="boolean" name="nullable"/>
        <attr format="string" name="argType"/>
    </declare-styleable>
    <declare-styleable name="NavDeepLink">
        <attr format="string" name="uri"/>
        <attr format="string" name="action"/>
        <attr format="string" name="mimeType"/>
        <attr name="android:autoVerify"/>
    </declare-styleable>
    <declare-styleable name="NavGraphNavigator">
        <attr format="reference" name="startDestination"/>
    </declare-styleable>
    <declare-styleable name="NavHost">
        <attr format="reference" name="navGraph"/>
    </declare-styleable>
    <declare-styleable name="NavInclude">
        <attr format="reference" name="graph"/>
    </declare-styleable>
    <declare-styleable name="Navigator">
        <attr name="android:id"/>
        <attr format="string" name="route"/>
        <attr name="android:label"/>
    </declare-styleable>
    <declare-styleable name="PopupWindow">
        
        <attr format="boolean" name="overlapAnchor"/>
        <attr name="android:popupBackground"/>
        <attr name="android:popupAnimationStyle"/>
    </declare-styleable>
    <declare-styleable name="PopupWindowBackgroundState">
        
        <attr format="boolean" name="state_above_anchor"/>
    </declare-styleable>
    <declare-styleable name="PreviewView">
        <attr name="scaleType"/>
        <attr name="implementationMode"/>
    </declare-styleable>
    <declare-styleable name="RecycleListView">
        
        <attr format="dimension" name="paddingBottomNoButtons"/>
        
        <attr format="dimension" name="paddingTopNoTitle"/>
    </declare-styleable>
    <declare-styleable name="SearchView">
        
        <attr format="reference" name="layout"/>
        
        <attr format="boolean" name="iconifiedByDefault"/>
        
        <attr name="android:maxWidth"/>
        
        <attr format="string" name="queryHint"/>
        
        <attr format="string" name="defaultQueryHint"/>
        
        <attr name="android:imeOptions"/>
        
        <attr name="android:inputType"/>
        
        <attr format="reference" name="closeIcon"/>
        
        <attr format="reference" name="goIcon"/>
        
        <attr format="reference" name="searchIcon"/>
        
        <attr format="reference" name="searchHintIcon"/>
        
        <attr format="reference" name="voiceIcon"/>
        
        <attr format="reference" name="commitIcon"/>
        
        <attr format="reference" name="suggestionRowLayout"/>
        
        <attr format="reference" name="queryBackground"/>
        
        <attr format="reference" name="submitBackground"/>
        <attr name="android:focusable"/>
    </declare-styleable>
    <declare-styleable name="SignInButton"><attr format="reference" name="buttonSize">
<enum name="standard" value="0"/>

<enum name="wide" value="1"/>

<enum name="icon_only" value="2"/>

</attr>
<attr format="reference" name="colorScheme">
<enum name="dark" value="0"/>

<enum name="light" value="1"/>

<enum name="auto" value="2"/>

</attr>

<attr format="reference|string" name="scopeUris"/>
</declare-styleable>
    <declare-styleable name="Spinner">
        
        <attr name="android:prompt"/>
        
        <attr name="popupTheme"/>
        
        <attr name="android:popupBackground"/>
        
        <attr name="android:dropDownWidth"/>
        
        <attr name="android:entries"/>
    </declare-styleable>
    <declare-styleable name="StateListDrawable">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="StateListDrawableItem">
        
        <attr name="android:drawable"/>
    </declare-styleable>
    <declare-styleable name="SwitchCompat">
        
        <attr name="android:thumb"/>
        
        <attr format="color" name="thumbTint"/>
        
        <attr name="thumbTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
        
        <attr format="reference" name="track"/>
        
        <attr format="color" name="trackTint"/>
        
        <attr name="trackTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
        
        <attr name="android:textOn"/>
        
        <attr name="android:textOff"/>
        
        <attr format="dimension" name="thumbTextPadding"/>
        
        <attr format="reference" name="switchTextAppearance"/>
        
        <attr format="dimension" name="switchMinWidth"/>
        
        <attr format="dimension" name="switchPadding"/>
        
        <attr format="boolean" name="splitTrack"/>
        
        <attr format="boolean" name="showText"/>
    </declare-styleable>
    <declare-styleable name="TextAppearance">
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
        <attr name="android:textColorHint"/>
        <attr name="android:textColorLink"/>
        <attr name="android:textStyle"/>
        <attr name="android:textFontWeight"/>
        <attr name="android:typeface"/>
        <attr name="android:fontFamily"/>
        <attr name="fontFamily"/>
        <attr name="textAllCaps"/>
        
        <attr name="textLocale"/>
        <attr name="android:shadowColor"/>
        <attr name="android:shadowDy"/>
        <attr name="android:shadowDx"/>
        <attr name="android:shadowRadius"/>
        
        <attr name="fontVariationSettings"/>
    </declare-styleable>
    <declare-styleable name="Toolbar">
        <attr format="reference" name="titleTextAppearance"/>
        <attr format="reference" name="subtitleTextAppearance"/>
        <attr name="title"/>
        <attr name="subtitle"/>
        <attr name="android:gravity"/>
        
        <attr format="dimension" name="titleMargin"/>
        
        <attr format="dimension" name="titleMarginStart"/>
        
        <attr format="dimension" name="titleMarginEnd"/>
        
        <attr format="dimension" name="titleMarginTop"/>
        
        <attr format="dimension" name="titleMarginBottom"/>
        
        <attr format="dimension" name="titleMargins"/>
        <attr name="contentInsetStart"/>
        <attr name="contentInsetEnd"/>
        <attr name="contentInsetLeft"/>
        <attr name="contentInsetRight"/>
        <attr name="contentInsetStartWithNavigation"/>
        <attr name="contentInsetEndWithActions"/>
        <attr format="dimension" name="maxButtonHeight"/>
        <attr name="buttonGravity">
            
            <flag name="center_vertical" value="0x10"/>
            
            <flag name="top" value="0x30"/>
            
            <flag name="bottom" value="0x50"/>
        </attr>
        
        <attr format="reference" name="collapseIcon"/>
        
        <attr format="string" name="collapseContentDescription"/>
        
        <attr name="popupTheme"/>
        
        <attr format="reference" name="navigationIcon"/>
        
        <attr format="string" name="navigationContentDescription"/>
        
        <attr name="logo"/>
        
        <attr format="string" name="logoDescription"/>
        
        <attr format="color" name="titleTextColor"/>
        
        <attr format="color" name="subtitleTextColor"/>
        <attr name="android:minHeight"/>
        
        <attr format="reference" name="menu"/>
    </declare-styleable>
    <declare-styleable name="View">
        
        <attr format="dimension" name="paddingStart"/>
        
        <attr format="dimension" name="paddingEnd"/>
        
        <attr name="android:focusable"/>
        
        <attr format="reference" name="theme"/>
        
        <attr name="android:theme"/>
    </declare-styleable>
    <declare-styleable name="ViewBackgroundHelper">
        <attr name="android:background"/>
        
        <attr format="color" name="backgroundTint"/>

        
        <attr name="backgroundTintMode">
            
            <enum name="src_over" value="3"/>
            
            <enum name="src_in" value="5"/>
            
            <enum name="src_atop" value="9"/>
            
            <enum name="multiply" value="14"/>
            
            <enum name="screen" value="15"/>
            
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ViewStubCompat">
        
        <attr name="android:layout"/>
        
        <attr name="android:inflatedId"/>
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="com_facebook_like_view">
        <attr format="color" name="com_facebook_foreground_color"/>
        <attr format="string" name="com_facebook_object_id"/>
        <attr name="com_facebook_object_type">
            
            <enum name="unknown" value="0"/>
            <enum name="open_graph" value="1"/>
            <enum name="page" value="2"/>
        </attr>
        <attr name="com_facebook_style">
            
            <enum name="standard" value="0"/>
            <enum name="button" value="1"/>
            <enum name="box_count" value="2"/>
        </attr>
        <attr name="com_facebook_auxiliary_view_position">
            
            <enum name="bottom" value="0"/>
            <enum name="inline" value="1"/>
            <enum name="top" value="2"/>
        </attr>
        <attr name="com_facebook_horizontal_alignment">
            
            <enum name="center" value="0"/>
            <enum name="left" value="1"/>
            <enum name="right" value="2"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="com_facebook_login_view">
        <attr format="boolean" name="com_facebook_confirm_logout"/>
        <attr format="string" name="com_facebook_login_text"/>
        <attr format="string" name="com_facebook_logout_text"/>
        <attr name="com_facebook_tooltip_mode">
            
            <enum name="automatic" value="0"/>
            <enum name="display_always" value="1"/>
            <enum name="never_display" value="2"/>
        </attr>
        <attr format="dimension" name="com_facebook_login_button_radius"/>
        <attr format="integer" name="com_facebook_login_button_transparency"/>
    </declare-styleable>
    <declare-styleable name="com_facebook_profile_picture_view">
        <attr name="com_facebook_preset_size">
            
            <enum name="small" value="-2"/>
            <enum name="normal" value="-3"/>
            <enum name="large" value="-4"/>
        </attr>
        <attr format="boolean" name="com_facebook_is_cropped"/>
    </declare-styleable>
</resources>