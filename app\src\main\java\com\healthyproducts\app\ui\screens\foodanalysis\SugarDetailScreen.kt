package com.healthyproducts.app.ui.screens.foodanalysis

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.components.ChipGroup
import com.healthyproducts.app.ui.components.RiskLevelIndicator
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.UserViewModel

/**
 * Şeker detay ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SugarDetailScreen(
    navController: NavController,
    sugarCode: String,
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val selectedSugar by foodAnalysisViewModel.selectedSugar.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()

    // Dil tercihi
    val language = SupportedLanguage.fromCode(userPreferences.language)

    // Şekeri yükle
    LaunchedEffect(sugarCode) {
        foodAnalysisViewModel.loadSugarByCode(sugarCode)
    }

    // Ekrandan çıkıldığında seçili şekeri temizle
    DisposableEffect(Unit) {
        onDispose {
            foodAnalysisViewModel.clearSelectedSugar()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.sugar_detail)) },
                navigationIcon = { BackButton(navController) }
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
        ) {
            if (selectedSugar == null) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                val sugar = selectedSugar!!

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                ) {
                    // Başlık, sembol ve kod
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Emoji veya ikon
                        if (sugar.symbol.isNotEmpty()) {
                            Text(
                                text = sugar.symbol,
                                style = MaterialTheme.typography.headlineLarge,
                                modifier = Modifier.padding(end = 16.dp)
                            )
                        }

                        Column {
                            Text(
                                text = sugar.getName(language),
                                style = MaterialTheme.typography.headlineMedium
                            )

                            Text(
                                text = sugar.sugarId,
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Fonksiyonel türler
                    if (sugar.functionalType.isNotEmpty()) {
                        Text(
                            text = stringResource(R.string.functional_types),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        ChipGroup(items = sugar.functionalType)

                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // Açıklama
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.description),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = sugar.getDescription(language),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Köken
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.origin),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = sugar.getOrigin(language),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kimyasal yapı
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.chemical_structure),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = sugar.getChemicalStructure(language),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Kullanım
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.usage),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = sugar.getUsage(language),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Glisemik indeks ve yük
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.glycemic_info),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.glycemic_index),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = sugar.glycemicIndex.toString(),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }

                            Divider(modifier = Modifier.padding(vertical = 8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.glycemic_load),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = sugar.glycemicLoad.toString(),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Sağlık etkileri
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.health_effects),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            // Risk seviyesi
                            RiskLevelIndicator(
                                title = stringResource(R.string.risk_level),
                                level = sugar.riskLevel,
                                maxLevel = 3
                            )

                            Divider(modifier = Modifier.padding(vertical = 8.dp))

                            Text(
                                text = sugar.getHealthEffects(language),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Diyet durumları
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.dietary_statuses),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            // Vegan durumu
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.food_vegan_status),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = if (sugar.veganStatuses.contains("vegan")) {
                                        stringResource(R.string.status_vegan)
                                    } else {
                                        stringResource(R.string.status_not_vegan)
                                    },
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }

                            Divider(modifier = Modifier.padding(vertical = 8.dp))

                            // Vejetaryen durumu
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.vegetarian_status),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = if (sugar.veganStatuses.contains("vegetarian")) {
                                        stringResource(R.string.status_vegetarian)
                                    } else {
                                        stringResource(R.string.status_not_vegetarian)
                                    },
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Dini durumlar
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.religious_statuses),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            // Helal durumu
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.food_halal_status),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = if (sugar.religiousStatuses.contains("halal")) {
                                        stringResource(R.string.status_halal)
                                    } else if (sugar.religiousStatuses.contains("haram")) {
                                        stringResource(R.string.status_haram)
                                    } else {
                                        stringResource(R.string.status_suspicious)
                                    },
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }

                            Divider(modifier = Modifier.padding(vertical = 8.dp))

                            // Koşer durumu
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.kosher_status),
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = if (sugar.religiousStatuses.contains("kosher")) {
                                        stringResource(R.string.status_kosher)
                                    } else {
                                        stringResource(R.string.status_not_kosher)
                                    },
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Notlar
                    if (sugar.getNotes(language).isNotBlank()) {
                        Card(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.notes),
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Text(
                                    text = sugar.getNotes(language),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }
        }
    }
}
