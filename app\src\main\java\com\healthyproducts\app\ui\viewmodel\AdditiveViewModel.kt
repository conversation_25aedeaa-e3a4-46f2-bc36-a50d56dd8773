package com.healthyproducts.app.ui.viewmodel

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthyproducts.app.data.model.Additive
import com.healthyproducts.app.data.repository.AdditiveRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Katkı maddeleri işlemlerini yöneten ViewModel
 */
@HiltViewModel
class AdditiveViewModel @Inject constructor(
    private val additiveRepository: AdditiveRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {
    private val TAG = "AdditiveViewModel"

    // Katk<PERSON> maddeleri listesi
    private val _additives = MutableStateFlow<List<Additive>>(emptyList())
    val additives: StateFlow<List<Additive>> = _additives.asStateFlow()

    // Seçili katkı maddesi
    private val _selectedAdditive = MutableStateFlow<Additive?>(null)
    val selectedAdditive: StateFlow<Additive?> = _selectedAdditive.asStateFlow()

    // Yükleniyor durumu
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // Hata durumu
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Arama sorgusu
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // İçe aktarma durumu
    private val _importStatus = MutableStateFlow<ImportStatus>(ImportStatus.Idle)
    val importStatus: StateFlow<ImportStatus> = _importStatus.asStateFlow()

    // Filtreleme kategorisi
    private val _filterCategory = MutableStateFlow<String?>(null)
    val filterCategory: StateFlow<String?> = _filterCategory.asStateFlow()

    init {
        loadAdditives()
    }

    /**
     * Tüm katkı maddelerini yükler
     */
    fun loadAdditives() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "Loading additives")

                additiveRepository.getAllAdditives()
                    .onSuccess { additivesList ->
                        _additives.value = additivesList
                        Log.d(TAG, "Loaded ${additivesList.size} additives")
                    }
                    .onFailure { exception ->
                        _error.value = exception.message
                        Log.e(TAG, "Error loading additives", exception)
                    }
            } catch (e: Exception) {
                _error.value = e.message
                Log.e(TAG, "Error loading additives", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Katkı maddesi ekler
     */
    fun addAdditive(additive: Additive) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "Adding additive: ${additive.code} - ${additive.nameTr}")

                additiveRepository.addAdditive(additive)
                    .onSuccess {
                        Log.d(TAG, "Additive added successfully")
                        loadAdditives() // Listeyi yenile
                    }
                    .onFailure { exception ->
                        _error.value = exception.message
                        Log.e(TAG, "Error adding additive", exception)
                    }
            } catch (e: Exception) {
                _error.value = e.message
                Log.e(TAG, "Error adding additive", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Katkı maddesini günceller
     */
    fun updateAdditive(additive: Additive) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "Updating additive: ${additive.code} - ${additive.nameTr}")

                additiveRepository.updateAdditive(additive)
                    .onSuccess {
                        Log.d(TAG, "Additive updated successfully")
                        loadAdditives() // Listeyi yenile
                    }
                    .onFailure { exception ->
                        _error.value = exception.message
                        Log.e(TAG, "Error updating additive", exception)
                    }
            } catch (e: Exception) {
                _error.value = e.message
                Log.e(TAG, "Error updating additive", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Katkı maddesini siler
     */
    fun deleteAdditive(code: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "Deleting additive: $code")

                additiveRepository.deleteAdditive(code)
                    .onSuccess {
                        Log.d(TAG, "Additive deleted successfully")
                        loadAdditives() // Listeyi yenile
                    }
                    .onFailure { exception ->
                        _error.value = exception.message
                        Log.e(TAG, "Error deleting additive", exception)
                    }
            } catch (e: Exception) {
                _error.value = e.message
                Log.e(TAG, "Error deleting additive", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Katkı maddesini seçer
     */
    fun selectAdditive(additive: Additive?) {
        _selectedAdditive.value = additive
    }

    /**
     * Arama sorgusunu günceller
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
        searchAdditives()
    }

    /**
     * Filtreleme kategorisini günceller
     */
    fun updateFilterCategory(category: String?) {
        _filterCategory.value = category
        filterAdditives()
    }

    /**
     * Katkı maddelerini arar
     */
    private fun searchAdditives() {
        viewModelScope.launch {
            if (_searchQuery.value.isBlank()) {
                loadAdditives()
                return@launch
            }

            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "Searching additives with query: ${_searchQuery.value}")

                additiveRepository.searchAdditivesByName(_searchQuery.value)
                    .onSuccess { additivesList ->
                        _additives.value = additivesList
                        Log.d(TAG, "Found ${additivesList.size} additives matching query")
                    }
                    .onFailure { exception ->
                        _error.value = exception.message
                        Log.e(TAG, "Error searching additives", exception)
                    }
            } catch (e: Exception) {
                _error.value = e.message
                Log.e(TAG, "Error searching additives", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Katkı maddelerini filtreler
     */
    private fun filterAdditives() {
        viewModelScope.launch {
            if (_filterCategory.value == null) {
                loadAdditives()
                return@launch
            }

            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "Filtering additives by category: ${_filterCategory.value}")

                additiveRepository.getAdditivesByCategory(_filterCategory.value!!)
                    .onSuccess { additivesList ->
                        _additives.value = additivesList
                        Log.d(TAG, "Found ${additivesList.size} additives in category")
                    }
                    .onFailure { exception ->
                        _error.value = exception.message
                        Log.e(TAG, "Error filtering additives", exception)
                    }
            } catch (e: Exception) {
                _error.value = e.message
                Log.e(TAG, "Error filtering additives", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Katkı maddesini kodu ile getirir
     */
    fun getAdditiveByCode(code: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "Getting additive by code: $code")

                additiveRepository.getAdditiveByCode(code)
                    .onSuccess { additive ->
                        if (additive != null) {
                            _selectedAdditive.value = additive
                            Log.d(TAG, "Found additive: ${additive.nameTr}")
                        } else {
                            _error.value = "Katkı maddesi bulunamadı"
                            Log.d(TAG, "Additive not found")
                        }
                    }
                    .onFailure { exception ->
                        _error.value = exception.message
                        Log.e(TAG, "Error getting additive by code", exception)
                    }
            } catch (e: Exception) {
                _error.value = e.message
                Log.e(TAG, "Error getting additive by code", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * JSON dosyasından katkı maddelerini içe aktarır
     */
    fun importAdditivesFromJson() {
        viewModelScope.launch {
            _importStatus.value = ImportStatus.Loading
            _error.value = null

            try {
                Log.d(TAG, "Importing additives from JSON")

                additiveRepository.importAdditivesFromJson(context)
                    .onSuccess { count ->
                        _importStatus.value = ImportStatus.Success(count)
                        Log.d(TAG, "Successfully imported $count additives")
                        loadAdditives() // Listeyi yenile
                    }
                    .onFailure { exception ->
                        _importStatus.value = ImportStatus.Error(exception.message ?: "Bilinmeyen hata")
                        _error.value = exception.message
                        Log.e(TAG, "Error importing additives", exception)
                    }
            } catch (e: Exception) {
                _importStatus.value = ImportStatus.Error(e.message ?: "Bilinmeyen hata")
                _error.value = e.message
                Log.e(TAG, "Error importing additives", e)
            }
        }
    }

    /**
     * İçe aktarma durumunu temsil eden sealed class
     */
    sealed class ImportStatus {
        object Idle : ImportStatus()
        object Loading : ImportStatus()
        data class Success(val count: Int) : ImportStatus()
        data class Error(val message: String) : ImportStatus()
    }
}
