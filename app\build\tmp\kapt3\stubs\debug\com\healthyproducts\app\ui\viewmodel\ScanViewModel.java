package com.healthyproducts.app.ui.viewmodel;

/**
 * Tarama ekranı için ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001:\u0003OPQB\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u00104\u001a\u000205J\u000e\u00106\u001a\u0002052\u0006\u00107\u001a\u000208J\u0006\u00109\u001a\u000205J\u0016\u0010:\u001a\u0002052\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00150\u001aH\u0002J\n\u0010<\u001a\u0004\u0018\u00010\u0015H\u0002J\u0014\u0010=\u001a\u0002052\f\u0010>\u001a\b\u0012\u0004\u0012\u00020?0\u001aJ\u000e\u0010@\u001a\u0002052\u0006\u0010A\u001a\u00020BJ\u0016\u0010C\u001a\b\u0012\u0004\u0012\u00020\u00150\u001a2\u0006\u0010A\u001a\u00020\u0015H\u0002J\u0006\u0010D\u001a\u000205J\u0006\u0010E\u001a\u000205J\u0006\u0010F\u001a\u000205J\u000e\u0010G\u001a\u0002052\u0006\u0010H\u001a\u00020\u000fJ\u000e\u0010I\u001a\u0002052\u0006\u0010;\u001a\u00020\u0015J\u000e\u0010J\u001a\u0002052\u0006\u0010K\u001a\u00020\rJ\u000e\u0010L\u001a\u0002052\u0006\u0010M\u001a\u00020\u0013J\u0014\u0010N\u001a\u0002052\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00150\u001aR\u0016\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00150\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u001a0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001d\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\u000f0\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010 R\u0017\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00110\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010 R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00130\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010 R\u0017\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00150\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010 R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00170\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010 R\u0017\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00150\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010 R\u001d\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u001a0\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010 R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010/\u001a\b\u0012\u0004\u0012\u00020\u001c0\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010 R\u0017\u00101\u001a\b\u0012\u0004\u0012\u0002020\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010 \u00a8\u0006R"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "scanHistoryRepository", "Lcom/healthyproducts/app/data/repository/ScanHistoryRepository;", "firestoreRepository", "Lcom/healthyproducts/app/data/repository/FirestoreRepository;", "ocrCorrectionRepository", "Lcom/healthyproducts/app/data/repository/OcrCorrectionRepository;", "(Landroid/app/Application;Lcom/healthyproducts/app/data/repository/ScanHistoryRepository;Lcom/healthyproducts/app/data/repository/FirestoreRepository;Lcom/healthyproducts/app/data/repository/OcrCorrectionRepository;)V", "_cameraPermissionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/healthyproducts/app/util/CameraPermissionState;", "_correctionMode", "", "_correctionState", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState;", "_frameSize", "Lcom/healthyproducts/app/ui/components/FrameSize;", "_ingredientsForAnalysis", "", "_ocrScanState", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState;", "_rawOcrText", "_recognizedIngredients", "", "_scanState", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState;", "cameraPermissionState", "Lkotlinx/coroutines/flow/StateFlow;", "getCameraPermissionState", "()Lkotlinx/coroutines/flow/StateFlow;", "correctionMode", "getCorrectionMode", "correctionState", "getCorrectionState", "frameSize", "getFrameSize", "ingredientsForAnalysis", "getIngredientsForAnalysis", "ocrScanState", "getOcrScanState", "rawOcrText", "getRawOcrText", "recognizedIngredients", "getRecognizedIngredients", "scanState", "getScanState", "userLanguage", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "getUserLanguage", "captureAndAnalyzeImage", "", "checkCameraPermission", "context", "Landroid/content/Context;", "clearIngredientsForAnalysis", "correctIngredients", "ingredients", "getCurrentUserId", "onBarcodeDetected", "barcodes", "Lcom/google/mlkit/vision/barcode/common/Barcode;", "onTextDetected", "text", "Lcom/google/mlkit/vision/text/Text;", "parseIngredients", "parseRawOcrToIngredients", "resetOcrScanState", "resetScanState", "setCorrectionMode", "enabled", "setIngredientsForAnalysis", "updateCameraPermissionState", "state", "updateFrameSize", "size", "updateRecognizedIngredients", "CorrectionState", "OcrScanState", "ScanState", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ScanViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.ScanHistoryRepository scanHistoryRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FirestoreRepository firestoreRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.OcrCorrectionRepository ocrCorrectionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.util.CameraPermissionState> _cameraPermissionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.util.CameraPermissionState> cameraPermissionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState> _scanState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState> scanState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState> _ocrScanState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState> ocrScanState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<java.lang.String>> _recognizedIngredients = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> recognizedIngredients = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _rawOcrText = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> rawOcrText = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState> _correctionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState> correctionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _correctionMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> correctionMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.components.FrameSize> _frameSize = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.components.FrameSize> frameSize = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.SupportedLanguage> userLanguage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _ingredientsForAnalysis = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> ingredientsForAnalysis = null;
    
    @javax.inject.Inject()
    public ScanViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.ScanHistoryRepository scanHistoryRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirestoreRepository firestoreRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.OcrCorrectionRepository ocrCorrectionRepository) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.util.CameraPermissionState> getCameraPermissionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState> getScanState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState> getOcrScanState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> getRecognizedIngredients() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getRawOcrText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState> getCorrectionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getCorrectionMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.components.FrameSize> getFrameSize() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.data.model.SupportedLanguage> getUserLanguage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getIngredientsForAnalysis() {
        return null;
    }
    
    /**
     * Kamera izni durumunu günceller
     */
    public final void updateCameraPermissionState(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.util.CameraPermissionState state) {
    }
    
    /**
     * Kamera izni durumunu kontrol eder
     */
    public final void checkCameraPermission(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Barkod algılandığında çağrılır
     */
    public final void onBarcodeDetected(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.google.mlkit.vision.barcode.common.Barcode> barcodes) {
    }
    
    /**
     * Metin algılandığında çağrılır
     */
    public final void onTextDetected(@org.jetbrains.annotations.NotNull()
    com.google.mlkit.vision.text.Text text) {
    }
    
    /**
     * İçerikleri DeepSeek API ile düzeltir
     */
    private final void correctIngredients(java.util.List<java.lang.String> ingredients) {
    }
    
    /**
     * Metin içindeki içerikleri ayırır
     */
    private final java.util.List<java.lang.String> parseIngredients(java.lang.String text) {
        return null;
    }
    
    /**
     * Tarama durumunu sıfırlar
     */
    public final void resetScanState() {
    }
    
    /**
     * OCR tarama durumunu sıfırlar
     */
    public final void resetOcrScanState() {
    }
    
    /**
     * İçerik düzeltme modunu ayarlar
     */
    public final void setCorrectionMode(boolean enabled) {
    }
    
    /**
     * Ham OCR sonucunu içerik listesine dönüştürür
     */
    public final void parseRawOcrToIngredients() {
    }
    
    /**
     * Tanınan içerikleri günceller
     */
    public final void updateRecognizedIngredients(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> ingredients) {
    }
    
    /**
     * Kamera görüntüsünü yakala ve analiz et
     */
    public final void captureAndAnalyzeImage() {
    }
    
    /**
     * Çerçeve boyutunu günceller
     */
    public final void updateFrameSize(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.components.FrameSize size) {
    }
    
    /**
     * Analiz için içerikleri kaydeder
     */
    public final void setIngredientsForAnalysis(@org.jetbrains.annotations.NotNull()
    java.lang.String ingredients) {
    }
    
    /**
     * Analiz için kaydedilen içerikleri temizler
     */
    public final void clearIngredientsForAnalysis() {
    }
    
    /**
     * Mevcut kullanıcı ID'sini alır
     */
    private final java.lang.String getCurrentUserId() {
        return null;
    }
    
    /**
     * Düzeltme durumunu temsil eden sealed class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState;", "", "()V", "Correcting", "Error", "Idle", "Success", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Correcting;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Success;", "app_debug"})
    public static abstract class CorrectionState {
        
        private CorrectionState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Correcting;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState;", "()V", "app_debug"})
        public static final class Correcting extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.Correcting INSTANCE = null;
            
            private Correcting() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Error extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState;", "()V", "app_debug"})
        public static final class Idle extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.Idle INSTANCE = null;
            
            private Idle() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState$Success;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$CorrectionState;", "()V", "app_debug"})
        public static final class Success extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState.Success INSTANCE = null;
            
            private Success() {
            }
        }
    }
    
    /**
     * OCR tarama durumunu temsil eden sealed class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState;", "", "()V", "Error", "Idle", "Scanning", "Success", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Scanning;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Success;", "app_debug"})
    public static abstract class OcrScanState {
        
        private OcrScanState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Error extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState;", "()V", "app_debug"})
        public static final class Idle extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.Idle INSTANCE = null;
            
            private Idle() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Scanning;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState;", "()V", "app_debug"})
        public static final class Scanning extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.Scanning INSTANCE = null;
            
            private Scanning() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState$Success;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$OcrScanState;", "text", "", "(Ljava/lang/String;)V", "getText", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Success extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String text = null;
            
            public Success(@org.jetbrains.annotations.NotNull()
            java.lang.String text) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getText() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanState.Success copy(@org.jetbrains.annotations.NotNull()
            java.lang.String text) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
    
    /**
     * Tarama durumunu temsil eden sealed class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState;", "", "()V", "Error", "Idle", "Scanning", "Success", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Scanning;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Success;", "app_debug"})
    public static abstract class ScanState {
        
        private ScanState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Error extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Idle;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState;", "()V", "app_debug"})
        public static final class Idle extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.Idle INSTANCE = null;
            
            private Idle() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Scanning;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState;", "()V", "app_debug"})
        public static final class Scanning extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.Scanning INSTANCE = null;
            
            private Scanning() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState$Success;", "Lcom/healthyproducts/app/ui/viewmodel/ScanViewModel$ScanState;", "barcode", "", "(Ljava/lang/String;)V", "getBarcode", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Success extends com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String barcode = null;
            
            public Success(@org.jetbrains.annotations.NotNull()
            java.lang.String barcode) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getBarcode() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState.Success copy(@org.jetbrains.annotations.NotNull()
            java.lang.String barcode) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
}