package com.healthyproducts.app.data.repository

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.GsonBuilder
import com.healthyproducts.app.data.model.Sugar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * <PERSON>eker türleri işlemlerini yöneten repository sınıfı
 */
@Singleton
class SugarRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val TAG = "SugarRepository"
        private const val COLLECTION_NAME = "sugars"
    }

    private val _sugars = MutableStateFlow<List<Sugar>>(emptyList())
    val sugars: StateFlow<List<Sugar>> = _sugars.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    /**
     * Tüm şeker türlerini getirir
     */
    suspend fun getAllSugars(): Result<List<Sugar>> = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null

            Log.d(TAG, "Getting all sugars")

            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val sugarsList = snapshot.documents.mapNotNull { document ->
                document.toObject(Sugar::class.java)
            }

            Log.d(TAG, "Retrieved ${sugarsList.size} sugars")

            _sugars.value = sugarsList
            _isLoading.value = false

            Result.success(sugarsList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting sugars", e)
            _error.value = e.message
            _isLoading.value = false
            Result.failure(e)
        }
    }

    /**
     * Şeker türü ekler
     */
    suspend fun addSugar(sugar: Sugar): Result<Sugar> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding sugar: ${sugar.sugarId} - ${sugar.nameTr}")

            // Şeker türünü Firestore'a ekle
            val documentRef = firestore.collection(COLLECTION_NAME).document(sugar.sugarId)
            documentRef.set(sugar).await()

            // Şeker türünü yerel listeye ekle
            val currentList = _sugars.value.toMutableList()
            currentList.add(sugar)
            _sugars.value = currentList

            Log.d(TAG, "Sugar added successfully")

            Result.success(sugar)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding sugar", e)
            Result.failure(e)
        }
    }

    /**
     * Şeker türünü günceller
     */
    suspend fun updateSugar(sugar: Sugar): Result<Sugar> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating sugar: ${sugar.sugarId} - ${sugar.nameTr}")

            // Şeker türünü Firestore'da güncelle
            val documentRef = firestore.collection(COLLECTION_NAME).document(sugar.sugarId)
            documentRef.set(sugar).await()

            // Şeker türünü yerel listede güncelle
            val currentList = _sugars.value.toMutableList()
            val index = currentList.indexOfFirst { it.sugarId == sugar.sugarId }
            if (index != -1) {
                currentList[index] = sugar
                _sugars.value = currentList
            }

            Log.d(TAG, "Sugar updated successfully")

            Result.success(sugar)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating sugar", e)
            Result.failure(e)
        }
    }

    /**
     * Şeker türünü siler
     */
    suspend fun deleteSugar(sugarId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting sugar: $sugarId")

            // Şeker türünü Firestore'dan sil
            val documentRef = firestore.collection(COLLECTION_NAME).document(sugarId)
            documentRef.delete().await()

            // Şeker türünü yerel listeden sil
            val currentList = _sugars.value.toMutableList()
            val index = currentList.indexOfFirst { it.sugarId == sugarId }
            if (index != -1) {
                currentList.removeAt(index)
                _sugars.value = currentList
            }

            Log.d(TAG, "Sugar deleted successfully")

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting sugar", e)
            Result.failure(e)
        }
    }

    /**
     * Şeker türünü ID ile getirir
     */
    suspend fun getSugarById(sugarId: String): Result<Sugar?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting sugar by ID: $sugarId")

            // Önce yerel listede ara
            val localSugar = _sugars.value.find { it.sugarId == sugarId }
            if (localSugar != null) {
                Log.d(TAG, "Sugar found in local cache")
                return@withContext Result.success(localSugar)
            }

            // Yerel listede yoksa Firestore'dan getir
            val documentRef = firestore.collection(COLLECTION_NAME).document(sugarId)
            val document = documentRef.get().await()

            val sugar = document.toObject(Sugar::class.java)

            if (sugar != null) {
                Log.d(TAG, "Sugar found in Firestore")
            } else {
                Log.d(TAG, "Sugar not found")
            }

            Result.success(sugar)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting sugar by ID", e)
            Result.failure(e)
        }
    }

    /**
     * Şeker türlerini adına göre arar
     */
    suspend fun searchSugarsByName(query: String): Result<List<Sugar>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching sugars by name: $query")

            // Firestore'da tam metin araması yapılamadığı için tüm şeker türlerini getirip filtreliyoruz
            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val sugarsList = snapshot.documents.mapNotNull { document ->
                document.toObject(Sugar::class.java)
            }.filter { sugar ->
                sugar.nameTr.contains(query, ignoreCase = true) ||
                sugar.nameEn.contains(query, ignoreCase = true) ||
                sugar.sugarId.contains(query, ignoreCase = true)
            }

            Log.d(TAG, "Found ${sugarsList.size} sugars matching query")

            Result.success(sugarsList)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching sugars", e)
            Result.failure(e)
        }
    }

    /**
     * Şeker türünü kodu ile getirir
     */
    suspend fun getSugarByCode(code: String): Result<Sugar?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting sugar by code: $code")

            // Önce yerel listede ara
            val localSugar = _sugars.value.find { it.id == code || it.sugarId == code }
            if (localSugar != null) {
                Log.d(TAG, "Sugar found in local cache")
                return@withContext Result.success(localSugar)
            }

            // Yerel listede yoksa Firestore'dan getir
            val snapshot = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("id", code)
                .get()
                .await()

            val sugar = snapshot.documents.firstOrNull()?.toObject(Sugar::class.java)

            if (sugar != null) {
                Log.d(TAG, "Sugar found in Firestore by id")
                return@withContext Result.success(sugar)
            }

            // id ile bulunamadıysa sugarId ile dene
            val snapshot2 = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("sugarId", code)
                .get()
                .await()

            val sugar2 = snapshot2.documents.firstOrNull()?.toObject(Sugar::class.java)

            if (sugar2 != null) {
                Log.d(TAG, "Sugar found in Firestore by sugarId")
                return@withContext Result.success(sugar2)
            }

            // Doğrudan belge ID'si olarak da dene
            val documentRef = firestore.collection(COLLECTION_NAME).document(code)
            val document = documentRef.get().await()
            val sugar3 = document.toObject(Sugar::class.java)

            if (sugar3 != null) {
                Log.d(TAG, "Sugar found in Firestore by document ID")
                return@withContext Result.success(sugar3)
            }

            Log.d(TAG, "Sugar not found with code: $code")
            Result.success(null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting sugar by code", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dosyasından şeker türlerini yükler ve Firestore'a kaydeder
     */
    suspend fun importSugarsFromJson(context: Context): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Importing sugars from JSON file")

            // JSON dosyasını oku
            val jsonString = try {
                val inputStream = context.assets.open("json/sugarList.json")
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                inputStream.close()
                String(buffer, Charsets.UTF_8)
            } catch (e: IOException) {
                Log.e(TAG, "Error reading sugars JSON file", e)
                return@withContext Result.failure(e)
            }

            // JSON içeriğinin bir kısmını logla
            Log.d(TAG, "JSON content length: ${jsonString.length}")
            Log.d(TAG, "JSON content first 100 chars: ${jsonString.take(100)}")
            Log.d(TAG, "JSON content last 100 chars: ${jsonString.takeLast(100)}")

            // JSON'ı parse et
            val gson = GsonBuilder()
                .setLenient()
                .create()

            val jsonArray = JSONArray(jsonString)
            val sugars = mutableListOf<Sugar>()

            try {
                // JSON içeriğini logla
                Log.d(TAG, "JSON array length: ${jsonArray.length()}")

                // İlk birkaç JSON nesnesini logla
                for (i in 0 until minOf(3, jsonArray.length())) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    Log.d(TAG, "JSON object $i: ${jsonObject.toString().take(200)}...")
                }

                // Tüm JSON nesnelerini işle
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)

                    // Dizileri işle
                    val labelsTr = parseJsonArray(jsonObject.optJSONArray("labels_tr"))
                    val labelsEn = parseJsonArray(jsonObject.optJSONArray("labels_en"))
                    val functionalType = parseJsonArray(jsonObject.optJSONArray("functional_type"))

                    // Şeker nesnesini oluştur
                    val sugar = Sugar(
                        id = jsonObject.optString("id", ""),
                        sugarId = jsonObject.optString("id", ""),
                        nameTr = jsonObject.optString("name_tr", ""),
                        nameEn = jsonObject.optString("name_en", ""),
                        symbol = jsonObject.optString("symbol", ""),
                        descriptionTr = jsonObject.optString("description_tr", ""),
                        descriptionEn = jsonObject.optString("description_en", ""),
                        sourceTr = jsonObject.optString("source_tr", ""),
                        sourceEn = jsonObject.optString("source_en", ""),
                        glycemicIndex = jsonObject.optInt("glycemic_index", 0),
                        riskLevel = jsonObject.optInt("risk_level", 0),
                        labelsTr = parseJsonArray(jsonObject.optJSONArray("hidden_names_tr")), // JSON'da hidden_names_tr olarak geçiyor
                        labelsEn = parseJsonArray(jsonObject.optJSONArray("hidden_names_en")), // JSON'da hidden_names_en olarak geçiyor
                        notesTr = jsonObject.optString("notes_tr", ""),
                        notesEn = jsonObject.optString("notes_en", ""),
                        functionalType = functionalType
                    )

                    sugars.add(sugar)
                }

                // İlk birkaç şekerin içeriğini detaylı logla
                sugars.take(3).forEach { sugar ->
                    Log.d(TAG, "Parsed sugar: ${sugar.id}")
                    Log.d(TAG, "  sugarId: '${sugar.sugarId}'")
                    Log.d(TAG, "  nameTr: '${sugar.nameTr}'")
                    Log.d(TAG, "  nameEn: '${sugar.nameEn}'")
                    Log.d(TAG, "  descriptionTr: '${sugar.descriptionTr}'")
                    Log.d(TAG, "  symbol: '${sugar.symbol}'")
                    Log.d(TAG, "  labelsTr: ${sugar.labelsTr}")
                    Log.d(TAG, "  functionalType: ${sugar.functionalType}")
                }

                Log.d(TAG, "Parsed ${sugars.size} sugars from JSON")

                // Firestore'a kaydet
                var successCount = 0
                sugars.forEach { sugar ->
                    try {
                        // Sugar nesnesini Map'e dönüştür
                        val sugarMap = mapOf(
                            "id" to sugar.id,
                            "sugarId" to sugar.sugarId,
                            "name_tr" to sugar.nameTr,
                            "name_en" to sugar.nameEn,
                            "symbol" to sugar.symbol,
                            "description_tr" to sugar.descriptionTr,
                            "description_en" to sugar.descriptionEn,
                            "source_tr" to sugar.sourceTr,
                            "source_en" to sugar.sourceEn,
                            "glycemic_index" to sugar.glycemicIndex,
                            "risk_level" to sugar.riskLevel,
                            "labels_tr" to sugar.labelsTr,
                            "labels_en" to sugar.labelsEn,
                            "notes_tr" to sugar.notesTr,
                            "notes_en" to sugar.notesEn,
                            "functional_type" to sugar.functionalType
                        )

                        val documentRef = firestore.collection(COLLECTION_NAME).document(sugar.sugarId)

                        // Map içeriğini logla
                        Log.d(TAG, "Saving sugar to Firestore: ${sugar.sugarId}")
                        Log.d(TAG, "  Map content: ${sugarMap.entries.joinToString { "${it.key}='${it.value}'" }}")

                        documentRef.set(sugarMap).await()

                        // Kaydedilen veriyi kontrol et
                        val savedDoc = documentRef.get().await()
                        Log.d(TAG, "  Saved document: ${savedDoc.data?.entries?.joinToString { "${it.key}='${it.value}'" }}")

                        successCount++
                    } catch (e: Exception) {
                        Log.e(TAG, "Error importing sugar ${sugar.sugarId}", e)
                    }
                }

                Log.d(TAG, "Successfully imported $successCount out of ${sugars.size} sugars")

                Result.success(successCount)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing JSON", e)
                return@withContext Result.failure(e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error importing sugars from JSON", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dizisini String listesine dönüştürür
     */
    private fun parseJsonArray(jsonArray: JSONArray?): List<String> {
        if (jsonArray == null) return emptyList()

        val result = mutableListOf<String>()
        for (i in 0 until jsonArray.length()) {
            result.add(jsonArray.optString(i, ""))
        }
        return result
    }
}
