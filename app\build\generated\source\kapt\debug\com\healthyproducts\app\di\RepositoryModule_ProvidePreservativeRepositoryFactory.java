package com.healthyproducts.app.di;

import android.content.Context;
import com.google.firebase.firestore.FirebaseFirestore;
import com.healthyproducts.app.data.repository.PreservativeRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvidePreservativeRepositoryFactory implements Factory<PreservativeRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  private final Provider<Context> contextProvider;

  public RepositoryModule_ProvidePreservativeRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider, Provider<Context> contextProvider) {
    this.firestoreProvider = firestoreProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public PreservativeRepository get() {
    return providePreservativeRepository(firestoreProvider.get(), contextProvider.get());
  }

  public static RepositoryModule_ProvidePreservativeRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider, Provider<Context> contextProvider) {
    return new RepositoryModule_ProvidePreservativeRepositoryFactory(firestoreProvider, contextProvider);
  }

  public static PreservativeRepository providePreservativeRepository(FirebaseFirestore firestore,
      Context context) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.providePreservativeRepository(firestore, context));
  }
}
