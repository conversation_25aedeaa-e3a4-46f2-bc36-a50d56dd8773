package com.healthyproducts.app.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.ui.Modifier

/**
 * Clickable modifier extension
 * 
 * @param enabled Tıklamanın etkin olup olmadığı
 * @param onClick Tıklama işlemi
 */
fun Modifier.clickable(
    enabled: <PERSON><PERSON><PERSON>,
    onClick: () -> Unit
) = if (enabled) {
    this.then(clickable(onClick = onClick))
} else {
    this
}
