package com.healthyproducts.app.di;

/**
 * Repository sınıflarını sağlayan Hilt modülü
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0007J\u0018\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\fH\u0007J\u0018\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\fH\u0007J@\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u0019\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\n2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0007J\u0010\u0010#\u001a\u00020\"2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0018\u0010$\u001a\u00020%2\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010&\u001a\u00020\'H\u0007J\u0010\u0010(\u001a\u00020\u001e2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0018\u0010)\u001a\u00020 2\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\'H\u0007J\u0010\u0010*\u001a\u00020+2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010,\u001a\u00020\u001b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00a8\u0006-"}, d2 = {"Lcom/healthyproducts/app/di/RepositoryModule;", "", "()V", "provideAdditiveRepository", "Lcom/healthyproducts/app/data/repository/AdditiveRepository;", "firestore", "Lcom/google/firebase/firestore/FirebaseFirestore;", "provideAllergenRepository", "Lcom/healthyproducts/app/data/repository/AllergenRepository;", "provideFatRepository", "Lcom/healthyproducts/app/data/repository/FatRepository;", "provideFirebaseAuthRepository", "Lcom/healthyproducts/app/data/repository/FirebaseAuthRepository;", "auth", "Lcom/google/firebase/auth/FirebaseAuth;", "provideFirebaseStorageRepository", "Lcom/healthyproducts/app/data/repository/FirebaseStorageRepository;", "storage", "Lcom/google/firebase/storage/FirebaseStorage;", "authRepository", "provideFirestoreRepository", "Lcom/healthyproducts/app/data/repository/FirestoreRepository;", "provideFoodAnalysisService", "Lcom/healthyproducts/app/data/service/FoodAnalysisService;", "additiveRepository", "allergenRepository", "sugarRepository", "Lcom/healthyproducts/app/data/repository/SugarRepository;", "fatRepository", "intoleranceRepository", "Lcom/healthyproducts/app/data/repository/IntoleranceRepository;", "preservativeRepository", "Lcom/healthyproducts/app/data/repository/PreservativeRepository;", "certificateRepository", "Lcom/healthyproducts/app/data/repository/FoodCertificateRepository;", "provideFoodCertificateRepository", "provideImageUploadRepository", "Lcom/healthyproducts/app/data/repository/ImageUploadRepository;", "context", "Landroid/content/Context;", "provideIntoleranceRepository", "providePreservativeRepository", "provideProductRepository", "Lcom/healthyproducts/app/data/repository/ProductRepository;", "provideSugarRepository", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class RepositoryModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.healthyproducts.app.di.RepositoryModule INSTANCE = null;
    
    private RepositoryModule() {
        super();
    }
    
    /**
     * Firebase Authentication Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.FirebaseAuthRepository provideFirebaseAuthRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.auth.FirebaseAuth auth) {
        return null;
    }
    
    /**
     * Firestore Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.FirestoreRepository provideFirestoreRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirebaseAuthRepository authRepository) {
        return null;
    }
    
    /**
     * Firebase Storage Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.FirebaseStorageRepository provideFirebaseStorageRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.storage.FirebaseStorage storage, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FirebaseAuthRepository authRepository) {
        return null;
    }
    
    /**
     * Additive Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.AdditiveRepository provideAdditiveRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    /**
     * Allergen Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.AllergenRepository provideAllergenRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    /**
     * Sugar Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.SugarRepository provideSugarRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    /**
     * Fat Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.FatRepository provideFatRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    /**
     * Food Certificate Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.FoodCertificateRepository provideFoodCertificateRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    /**
     * Intolerance Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.IntoleranceRepository provideIntoleranceRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    /**
     * Preservative Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.PreservativeRepository providePreservativeRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Product Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.ProductRepository provideProductRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.firestore.FirebaseFirestore firestore) {
        return null;
    }
    
    /**
     * Image Upload Repository sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.repository.ImageUploadRepository provideImageUploadRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.storage.FirebaseStorage storage, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Food Analysis Service sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.service.FoodAnalysisService provideFoodAnalysisService(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.AdditiveRepository additiveRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.AllergenRepository allergenRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.SugarRepository sugarRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FatRepository fatRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.IntoleranceRepository intoleranceRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.PreservativeRepository preservativeRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FoodCertificateRepository certificateRepository) {
        return null;
    }
}