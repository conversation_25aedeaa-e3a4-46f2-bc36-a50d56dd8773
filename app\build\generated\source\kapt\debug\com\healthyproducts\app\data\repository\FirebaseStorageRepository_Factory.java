package com.healthyproducts.app.data.repository;

import com.google.firebase.storage.FirebaseStorage;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FirebaseStorageRepository_Factory implements Factory<FirebaseStorageRepository> {
  private final Provider<FirebaseStorage> storageProvider;

  private final Provider<FirebaseAuthRepository> authRepositoryProvider;

  public FirebaseStorageRepository_Factory(Provider<FirebaseStorage> storageProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    this.storageProvider = storageProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public FirebaseStorageRepository get() {
    return newInstance(storageProvider.get(), authRepositoryProvider.get());
  }

  public static FirebaseStorageRepository_Factory create(Provider<FirebaseStorage> storageProvider,
      Provider<FirebaseAuthRepository> authRepositoryProvider) {
    return new FirebaseStorageRepository_Factory(storageProvider, authRepositoryProvider);
  }

  public static FirebaseStorageRepository newInstance(FirebaseStorage storage,
      FirebaseAuthRepository authRepository) {
    return new FirebaseStorageRepository(storage, authRepository);
  }
}
