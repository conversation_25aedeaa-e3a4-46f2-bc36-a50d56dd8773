package com.healthyproducts.app.data.repository

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.GsonBuilder
import com.healthyproducts.app.data.model.Allergen
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Alerjen işlemlerini yöneten repository sınıfı
 */
@Singleton
class AllergenRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val TAG = "AllergenRepository"
        private const val COLLECTION_NAME = "allergens"
    }

    private val _allergens = MutableStateFlow<List<Allergen>>(emptyList())
    val allergens: StateFlow<List<Allergen>> = _allergens.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    /**
     * Tüm alerjenleri getirir
     */
    suspend fun getAllAllergens(): Result<List<Allergen>> = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null

            Log.d(TAG, "Getting all allergens")

            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val allergensList = snapshot.documents.mapNotNull { document ->
                document.toObject(Allergen::class.java)
            }

            Log.d(TAG, "Retrieved ${allergensList.size} allergens")

            _allergens.value = allergensList
            _isLoading.value = false

            Result.success(allergensList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting allergens", e)
            _error.value = e.message
            _isLoading.value = false
            Result.failure(e)
        }
    }

    /**
     * Alerjen ekler
     */
    suspend fun addAllergen(allergen: Allergen): Result<Allergen> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding allergen: ${allergen.allergenId} - ${allergen.nameTr}")

            // Alerjeni Firestore'a ekle
            val documentRef = firestore.collection(COLLECTION_NAME).document(allergen.allergenId)
            documentRef.set(allergen).await()

            // Alerjeni yerel listeye ekle
            val currentList = _allergens.value.toMutableList()
            currentList.add(allergen)
            _allergens.value = currentList

            Log.d(TAG, "Allergen added successfully")

            Result.success(allergen)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding allergen", e)
            Result.failure(e)
        }
    }

    /**
     * Alerjeni günceller
     */
    suspend fun updateAllergen(allergen: Allergen): Result<Allergen> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating allergen: ${allergen.allergenId} - ${allergen.nameTr}")

            // Alerjeni Firestore'da güncelle
            val documentRef = firestore.collection(COLLECTION_NAME).document(allergen.allergenId)
            documentRef.set(allergen).await()

            // Alerjeni yerel listede güncelle
            val currentList = _allergens.value.toMutableList()
            val index = currentList.indexOfFirst { it.allergenId == allergen.allergenId }
            if (index != -1) {
                currentList[index] = allergen
                _allergens.value = currentList
            }

            Log.d(TAG, "Allergen updated successfully")

            Result.success(allergen)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating allergen", e)
            Result.failure(e)
        }
    }

    /**
     * Alerjeni siler
     */
    suspend fun deleteAllergen(allergenId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting allergen: $allergenId")

            // Alerjeni Firestore'dan sil
            val documentRef = firestore.collection(COLLECTION_NAME).document(allergenId)
            documentRef.delete().await()

            // Alerjeni yerel listeden sil
            val currentList = _allergens.value.toMutableList()
            val index = currentList.indexOfFirst { it.allergenId == allergenId }
            if (index != -1) {
                currentList.removeAt(index)
                _allergens.value = currentList
            }

            Log.d(TAG, "Allergen deleted successfully")

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting allergen", e)
            Result.failure(e)
        }
    }

    /**
     * Alerjeni ID ile getirir
     */
    suspend fun getAllergenById(allergenId: String): Result<Allergen?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting allergen by ID: $allergenId")

            // Önce yerel listede ara
            val localAllergen = _allergens.value.find { it.allergenId == allergenId }
            if (localAllergen != null) {
                Log.d(TAG, "Allergen found in local cache")
                return@withContext Result.success(localAllergen)
            }

            // Yerel listede yoksa Firestore'dan getir
            val documentRef = firestore.collection(COLLECTION_NAME).document(allergenId)
            val document = documentRef.get().await()

            val allergen = document.toObject(Allergen::class.java)

            if (allergen != null) {
                Log.d(TAG, "Allergen found in Firestore")
            } else {
                Log.d(TAG, "Allergen not found")
            }

            Result.success(allergen)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting allergen by ID", e)
            Result.failure(e)
        }
    }

    /**
     * Alerjenleri adına göre arar
     */
    suspend fun searchAllergensByName(query: String): Result<List<Allergen>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching allergens by name: $query")

            // Firestore'da tam metin araması yapılamadığı için tüm alerjenleri getirip filtreliyoruz
            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val allergensList = snapshot.documents.mapNotNull { document ->
                document.toObject(Allergen::class.java)
            }.filter { allergen ->
                allergen.nameTr.contains(query, ignoreCase = true) ||
                allergen.nameEn.contains(query, ignoreCase = true) ||
                allergen.allergenId.contains(query, ignoreCase = true)
            }

            Log.d(TAG, "Found ${allergensList.size} allergens matching query")

            Result.success(allergensList)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching allergens", e)
            Result.failure(e)
        }
    }

    /**
     * Alerjeni kodu ile getirir
     */
    suspend fun getAllergenByCode(code: String): Result<Allergen?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting allergen by code: $code")

            // Önce yerel listede ara
            val localAllergen = _allergens.value.find { it.id == code || it.allergenId == code }
            if (localAllergen != null) {
                Log.d(TAG, "Allergen found in local cache")
                return@withContext Result.success(localAllergen)
            }

            // Yerel listede yoksa Firestore'dan getir
            val snapshot = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("id", code)
                .get()
                .await()

            val allergen = snapshot.documents.firstOrNull()?.toObject(Allergen::class.java)

            if (allergen != null) {
                Log.d(TAG, "Allergen found in Firestore by id")
                return@withContext Result.success(allergen)
            }

            // id ile bulunamadıysa allergenId ile dene
            val snapshot2 = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("allergenId", code)
                .get()
                .await()

            val allergen2 = snapshot2.documents.firstOrNull()?.toObject(Allergen::class.java)

            if (allergen2 != null) {
                Log.d(TAG, "Allergen found in Firestore by allergenId")
                return@withContext Result.success(allergen2)
            }

            // Doğrudan belge ID'si olarak da dene
            val documentRef = firestore.collection(COLLECTION_NAME).document(code)
            val document = documentRef.get().await()
            val allergen3 = document.toObject(Allergen::class.java)

            if (allergen3 != null) {
                Log.d(TAG, "Allergen found in Firestore by document ID")
                return@withContext Result.success(allergen3)
            }

            Log.d(TAG, "Allergen not found with code: $code")
            Result.success(null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting allergen by code", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dosyasından alerjenleri yükler ve Firestore'a kaydeder
     */
    suspend fun importAllergensFromJson(context: Context): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Importing allergens from JSON file")

            // JSON dosyasını oku
            val jsonString = try {
                val inputStream = context.assets.open("json/allergenList.json")
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                inputStream.close()
                String(buffer, Charsets.UTF_8)
            } catch (e: IOException) {
                Log.e(TAG, "Error reading allergens JSON file", e)
                return@withContext Result.failure(e)
            }

            // JSON içeriğinin bir kısmını logla
            Log.d(TAG, "JSON content length: ${jsonString.length}")
            Log.d(TAG, "JSON content first 100 chars: ${jsonString.take(100)}")
            Log.d(TAG, "JSON content last 100 chars: ${jsonString.takeLast(100)}")

            // JSON'ı parse et
            val gson = GsonBuilder()
                .setLenient()
                .create()

            val jsonArray = JSONArray(jsonString)
            val allergens = mutableListOf<Allergen>()

            try {
                // JSON içeriğini logla
                Log.d(TAG, "JSON array length: ${jsonArray.length()}")

                // İlk birkaç JSON nesnesini logla
                for (i in 0 until minOf(3, jsonArray.length())) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    Log.d(TAG, "JSON object $i: ${jsonObject.toString().take(200)}...")
                }

                // Tüm JSON nesnelerini işle
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)

                    // Vegan, halal, kosher durumlarını boolean'a çevir
                    val vegan = jsonObject.optBoolean("vegan", false)
                    val halal = jsonObject.optBoolean("halal", false)
                    val kosher = jsonObject.optBoolean("kosher", false)
                    val vegetarian = jsonObject.optBoolean("vegetarian", false)

                    // Dizileri işle
                    val hiddenNamesTr = parseJsonArray(jsonObject.optJSONArray("hidden_names_tr"))
                    val hiddenNamesEn = parseJsonArray(jsonObject.optJSONArray("hidden_names_en"))
                    val commonProductsTr = parseJsonArray(jsonObject.optJSONArray("common_products_tr"))
                    val commonProductsEn = parseJsonArray(jsonObject.optJSONArray("common_products_en"))
                    val functionalType = parseJsonArray(jsonObject.optJSONArray("functional_type"))
                    val veganStatuses = parseJsonArray(jsonObject.optJSONArray("vegan_statuses"))
                    val religiousStatuses = parseJsonArray(jsonObject.optJSONArray("religious_statuses"))

                    // Alerjen nesnesini oluştur
                    val allergen = Allergen(
                        id = jsonObject.optString("id", ""),
                        allergenId = jsonObject.optString("id", ""),
                        nameTr = jsonObject.optString("name_tr", ""),
                        nameEn = jsonObject.optString("name_en", ""),
                        symbol = jsonObject.optString("symbol", ""),
                        descriptionTr = jsonObject.optString("description_tr", ""),
                        descriptionEn = jsonObject.optString("description_en", ""),
                        hiddenNamesTr = hiddenNamesTr,
                        hiddenNamesEn = hiddenNamesEn,
                        riskLevel = jsonObject.optInt("risk_level", 0),
                        commonProductsTr = commonProductsTr,
                        commonProductsEn = commonProductsEn,
                        notesTr = jsonObject.optString("notes_tr", ""),
                        notesEn = jsonObject.optString("notes_en", ""),
                        functionalType = functionalType,
                        sourceTr = jsonObject.optString("source_tr", ""),
                        sourceEn = jsonObject.optString("source_en", ""),
                        veganStatuses = if (vegan) listOf("vegan") else veganStatuses,
                        religiousStatuses = buildReligiousStatuses(halal, kosher, religiousStatuses)
                    )

                    allergens.add(allergen)
                }

                // İlk birkaç alerjenin içeriğini detaylı logla
                allergens.take(3).forEach { allergen ->
                    Log.d(TAG, "Parsed allergen: ${allergen.id}")
                    Log.d(TAG, "  allergenId: '${allergen.allergenId}'")
                    Log.d(TAG, "  nameTr: '${allergen.nameTr}'")
                    Log.d(TAG, "  nameEn: '${allergen.nameEn}'")
                    Log.d(TAG, "  descriptionTr: '${allergen.descriptionTr}'")
                    Log.d(TAG, "  symbol: '${allergen.symbol}'")
                    Log.d(TAG, "  hiddenNamesTr: ${allergen.hiddenNamesTr}")
                    Log.d(TAG, "  functionalType: ${allergen.functionalType}")
                    Log.d(TAG, "  veganStatuses: ${allergen.veganStatuses}")
                    Log.d(TAG, "  religiousStatuses: ${allergen.religiousStatuses}")
                }

                Log.d(TAG, "Parsed ${allergens.size} allergens from JSON")

                // Firestore'a kaydet
                var successCount = 0
                allergens.forEach { allergen ->
                    try {
                        // Allergen nesnesini Map'e dönüştür
                        val allergenMap = mapOf(
                            "id" to allergen.id,
                            "allergenId" to allergen.allergenId,
                            "name_tr" to allergen.nameTr,
                            "name_en" to allergen.nameEn,
                            "symbol" to allergen.symbol,
                            "description_tr" to allergen.descriptionTr,
                            "description_en" to allergen.descriptionEn,
                            "hidden_names_tr" to allergen.hiddenNamesTr,
                            "hidden_names_en" to allergen.hiddenNamesEn,
                            "risk_level" to allergen.riskLevel,
                            "common_products_tr" to allergen.commonProductsTr,
                            "common_products_en" to allergen.commonProductsEn,
                            "notes_tr" to allergen.notesTr,
                            "notes_en" to allergen.notesEn,
                            "functional_type" to allergen.functionalType,
                            "source_tr" to allergen.sourceTr,
                            "source_en" to allergen.sourceEn,
                            "vegan_statuses" to allergen.veganStatuses,
                            "religious_statuses" to allergen.religiousStatuses
                        )

                        val documentRef = firestore.collection(COLLECTION_NAME).document(allergen.allergenId)

                        // Map içeriğini logla
                        Log.d(TAG, "Saving allergen to Firestore: ${allergen.allergenId}")
                        Log.d(TAG, "  Map content: ${allergenMap.entries.joinToString { "${it.key}='${it.value}'" }}")

                        documentRef.set(allergenMap).await()

                        // Kaydedilen veriyi kontrol et
                        val savedDoc = documentRef.get().await()
                        Log.d(TAG, "  Saved document: ${savedDoc.data?.entries?.joinToString { "${it.key}='${it.value}'" }}")

                        successCount++
                    } catch (e: Exception) {
                        Log.e(TAG, "Error importing allergen ${allergen.allergenId}", e)
                    }
                }

                Log.d(TAG, "Successfully imported $successCount out of ${allergens.size} allergens")

                Result.success(successCount)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing JSON", e)
                return@withContext Result.failure(e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error importing allergens from JSON", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dizisini String listesine dönüştürür
     */
    private fun parseJsonArray(jsonArray: JSONArray?): List<String> {
        if (jsonArray == null) return emptyList()

        val result = mutableListOf<String>()
        for (i in 0 until jsonArray.length()) {
            result.add(jsonArray.optString(i, ""))
        }
        return result
    }

    /**
     * Dini durumları oluşturur
     */
    private fun buildReligiousStatuses(halal: Boolean, kosher: Boolean, existingStatuses: List<String>): List<String> {
        val statuses = existingStatuses.toMutableList()

        if (halal && !statuses.contains("halal")) {
            statuses.add("halal")
        }

        if (kosher && !statuses.contains("kosher")) {
            statuses.add("kosher")
        }

        return statuses
    }
}
