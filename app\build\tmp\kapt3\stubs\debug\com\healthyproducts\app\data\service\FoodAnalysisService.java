package com.healthyproducts.app.data.service;

/**
 * Gıda analiz servisi
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u0000 \'2\u00020\u0001:\u0001\'B?\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J$\u0010\u0011\u001a\u00020\u00122\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00150\u00142\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\\\u0010\u0019\u001a\u00020\u001a2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00142\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001e0\u00142\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\u00142\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\"0\u00142\f\u0010#\u001a\b\u0012\u0004\u0012\u00020$0\u00142\f\u0010%\u001a\b\u0012\u0004\u0012\u00020&0\u0014H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/healthyproducts/app/data/service/FoodAnalysisService;", "", "additiveRepository", "Lcom/healthyproducts/app/data/repository/AdditiveRepository;", "allergenRepository", "Lcom/healthyproducts/app/data/repository/AllergenRepository;", "sugarRepository", "Lcom/healthyproducts/app/data/repository/SugarRepository;", "fatRepository", "Lcom/healthyproducts/app/data/repository/FatRepository;", "intoleranceRepository", "Lcom/healthyproducts/app/data/repository/IntoleranceRepository;", "preservativeRepository", "Lcom/healthyproducts/app/data/repository/PreservativeRepository;", "certificateRepository", "Lcom/healthyproducts/app/data/repository/FoodCertificateRepository;", "(Lcom/healthyproducts/app/data/repository/AdditiveRepository;Lcom/healthyproducts/app/data/repository/AllergenRepository;Lcom/healthyproducts/app/data/repository/SugarRepository;Lcom/healthyproducts/app/data/repository/FatRepository;Lcom/healthyproducts/app/data/repository/IntoleranceRepository;Lcom/healthyproducts/app/data/repository/PreservativeRepository;Lcom/healthyproducts/app/data/repository/FoodCertificateRepository;)V", "analyzeIngredients", "Lcom/healthyproducts/app/data/service/FoodAnalysisResult;", "ingredients", "", "", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "(Ljava/util/List;Lcom/healthyproducts/app/data/model/SupportedLanguage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "calculateOverallRiskLevel", "", "allergenWarnings", "Lcom/healthyproducts/app/data/service/AllergenWarning;", "sugarWarnings", "Lcom/healthyproducts/app/data/service/SugarWarning;", "fatWarnings", "Lcom/healthyproducts/app/data/service/FatWarning;", "intoleranceWarnings", "Lcom/healthyproducts/app/data/service/IntoleranceWarning;", "preservativeWarnings", "Lcom/healthyproducts/app/data/service/PreservativeWarning;", "additiveWarnings", "Lcom/healthyproducts/app/data/service/AdditiveWarning;", "Companion", "app_debug"})
public final class FoodAnalysisService {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.AdditiveRepository additiveRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.AllergenRepository allergenRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.SugarRepository sugarRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FatRepository fatRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.IntoleranceRepository intoleranceRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.PreservativeRepository preservativeRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.FoodCertificateRepository certificateRepository = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "FoodAnalysisService";
    @org.jetbrains.annotations.NotNull()
    public static final com.healthyproducts.app.data.service.FoodAnalysisService.Companion Companion = null;
    
    @javax.inject.Inject()
    public FoodAnalysisService(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.AdditiveRepository additiveRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.AllergenRepository allergenRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.SugarRepository sugarRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FatRepository fatRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.IntoleranceRepository intoleranceRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.PreservativeRepository preservativeRepository, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.FoodCertificateRepository certificateRepository) {
        super();
    }
    
    /**
     * İçerik listesini analiz eder ve risk değerlendirmesi yapar
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object analyzeIngredients(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> ingredients, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.healthyproducts.app.data.service.FoodAnalysisResult> $completion) {
        return null;
    }
    
    /**
     * Genel risk seviyesini hesaplar
     */
    private final int calculateOverallRiskLevel(java.util.List<com.healthyproducts.app.data.service.AllergenWarning> allergenWarnings, java.util.List<com.healthyproducts.app.data.service.SugarWarning> sugarWarnings, java.util.List<com.healthyproducts.app.data.service.FatWarning> fatWarnings, java.util.List<com.healthyproducts.app.data.service.IntoleranceWarning> intoleranceWarnings, java.util.List<com.healthyproducts.app.data.service.PreservativeWarning> preservativeWarnings, java.util.List<com.healthyproducts.app.data.service.AdditiveWarning> additiveWarnings) {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/healthyproducts/app/data/service/FoodAnalysisService$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}