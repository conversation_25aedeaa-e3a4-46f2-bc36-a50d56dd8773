package com.healthyproducts.app.di;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideContextFactory implements Factory<Context> {
  private final Provider<Context> appContextProvider;

  public AppModule_ProvideContextFactory(Provider<Context> appContextProvider) {
    this.appContextProvider = appContextProvider;
  }

  @Override
  public Context get() {
    return provideContext(appContextProvider.get());
  }

  public static AppModule_ProvideContextFactory create(Provider<Context> appContextProvider) {
    return new AppModule_ProvideContextFactory(appContextProvider);
  }

  public static Context provideContext(Context appContext) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideContext(appContext));
  }
}
