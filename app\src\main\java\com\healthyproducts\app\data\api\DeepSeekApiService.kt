package com.healthyproducts.app.data.api

import com.healthyproducts.app.data.model.DeepSeekRequest
import com.healthyproducts.app.data.model.DeepSeekResponse
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import java.util.concurrent.TimeUnit

/**
 * DeepSeek API servisi
 */
interface DeepSeekApiService {

    @POST("v1/chat/completions")
    suspend fun getCompletion(
        @Header("Authorization") authorization: String,
        @Body request: DeepSeekRequest
    ): DeepSeekResponse

    companion object {
        private const val BASE_URL = "https://api.deepseek.com/"
    }
}
