package com.healthyproducts.app.ui.screens.foodanalysis

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import android.util.Log
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.model.FoodPreferenceType
import com.healthyproducts.app.data.model.Preservative
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.navigation.Screen
import com.healthyproducts.app.ui.components.AddCustomItemDialog
import com.healthyproducts.app.ui.components.BackButton
import com.healthyproducts.app.ui.viewmodel.FoodAnalysisViewModel
import com.healthyproducts.app.ui.viewmodel.ImportStatus
import com.healthyproducts.app.ui.viewmodel.PreservativesState
import com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState
import com.healthyproducts.app.ui.viewmodel.UserViewModel
import kotlinx.coroutines.launch

/**
 * Koruyucular ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PreservativesScreen(
    navController: NavController,
    foodAnalysisViewModel: FoodAnalysisViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }

    val preservativesState by foodAnalysisViewModel.preservativesState.collectAsState()
    val userPreferences by userViewModel.userPreferences.collectAsState()
    val importStatus by foodAnalysisViewModel.importStatus.collectAsState()
    val userFoodPreferencesState by foodAnalysisViewModel.userFoodPreferencesState.collectAsState()
    val selectedFoodPreferences by foodAnalysisViewModel.selectedFoodPreferences.collectAsState()

    // Kullanıcı ID'si
    val userState by userViewModel.userState.collectAsState()
    val userId = if (userState is UserViewModel.UserState.LoggedIn) {
        (userState as UserViewModel.UserState.LoggedIn).user.id
    } else {
        ""
    }

    // Dil tercihi
    val language = SupportedLanguage.fromCode(userPreferences.language)

    // Arama metni
    var searchText by remember { mutableStateOf("") }

    // Özel koruyucu ekleme diyaloğu
    var showAddCustomDialog by remember { mutableStateOf(false) }

    // İçeri aktarma durumunu izle
    LaunchedEffect(importStatus) {
        when (importStatus) {
            is ImportStatus.Success -> {
                val count = (importStatus as ImportStatus.Success).count
                snackbarHostState.showSnackbar(
                    message = context.getString(R.string.import_success, count)
                )
                foodAnalysisViewModel.resetImportStatus()
            }
            is ImportStatus.Error -> {
                val message = (importStatus as ImportStatus.Error).message
                snackbarHostState.showSnackbar(
                    message = context.getString(R.string.import_error, message)
                )
                foodAnalysisViewModel.resetImportStatus()
            }
            else -> {}
        }
    }

    // Koruyucuları yükle
    LaunchedEffect(Unit) {
        Log.d("PreservativesScreen", "Loading preservatives")
        foodAnalysisViewModel.loadPreservatives()
    }

    // Kullanıcı tercihlerini yükle (kullanıcı ID'si değiştiğinde)
    LaunchedEffect(userId) {
        if (userId.isNotEmpty()) {
            foodAnalysisViewModel.loadUserFoodPreferencesByType(userId, FoodPreferenceType.PRESERVATIVE)
        }
    }

    // Debug bilgisi
    LaunchedEffect(preservativesState) {
        when (val state = preservativesState) {
            is PreservativesState.Loading -> {
                Log.d("PreservativesScreen", "State: Loading")
            }
            is PreservativesState.Success -> {
                Log.d("PreservativesScreen", "State: Success, items: ${state.preservatives.size}")
                state.preservatives.take(3).forEach {
                    Log.d("PreservativesScreen", "  Item: ${it.preservativeId} - ${it.nameTr}")
                }
            }
            is PreservativesState.Error -> {
                Log.e("PreservativesScreen", "State: Error - ${state.message}")
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.preservatives)) },
                navigationIcon = { BackButton(navController) }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        floatingActionButton = {
            // Kullanıcı giriş yapmışsa özel koruyucu ekleme butonu göster
            if (userId.isNotEmpty()) {
                FloatingActionButton(
                    onClick = {
                        // Özel koruyucu ekleme diyaloğunu göster
                        showAddCustomDialog = true
                    }
                ) {
                    Icon(Icons.Default.Add, contentDescription = stringResource(R.string.add_custom_preservative))
                }
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // Arama alanı
            TextField(
                value = searchText,
                onValueChange = { searchText = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                placeholder = { Text(text = stringResource(R.string.search)) },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "Ara"
                    )
                },
                singleLine = true
            )

            when (val state = preservativesState) {
                is PreservativesState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                is PreservativesState.Success -> {
                    val preservatives = state.preservatives
                    val filteredPreservatives = if (searchText.isBlank()) {
                        preservatives
                    } else {
                        preservatives.filter {
                            it.getName(language).contains(searchText, ignoreCase = true) ||
                                    it.getDescription(language).contains(searchText, ignoreCase = true)
                        }
                    }

                    if (filteredPreservatives.isEmpty()) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(text = stringResource(R.string.no_preservatives_found))
                        }
                    } else {
                        PreservativesList(
                            preservatives = filteredPreservatives,
                            language = language,
                            navController = navController,
                            selectedFoodPreferences = selectedFoodPreferences,
                            userFoodPreferencesState = userFoodPreferencesState,
                            userId = userId,
                            onSavePreference = { preservative, isSelected ->
                                if (userId.isNotEmpty()) {
                                    if (isSelected) {
                                        // Koruyucu tercihini kaydet
                                        foodAnalysisViewModel.saveUserFoodPreference(
                                            userId = userId,
                                            type = FoodPreferenceType.PRESERVATIVE,
                                            itemId = preservative.preservativeId,
                                            itemName = preservative.getName(language)
                                        )
                                    } else {
                                        // Koruyucu tercihini bul ve sil
                                        val preference = (userFoodPreferencesState as? UserFoodPreferencesState.Success)?.preferences?.find {
                                            it.itemId == preservative.preservativeId && it.type == FoodPreferenceType.PRESERVATIVE.value
                                        }

                                        if (preference != null) {
                                            foodAnalysisViewModel.deleteUserFoodPreference(
                                                preferenceId = preference.id,
                                                userId = userId,
                                                type = FoodPreferenceType.PRESERVATIVE
                                            )
                                        }
                                    }
                                } else {
                                    // Kullanıcı giriş yapmamışsa uyarı göster
                                    scope.launch {
                                        snackbarHostState.showSnackbar("Tercih eklemek için giriş yapmalısınız")
                                    }
                                }
                            }
                        )
                    }
                }
                is PreservativesState.Error -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(text = state.message)
                    }
                }
                else -> {
                    // Başlangıç durumu
                }
            }
        }

        // Özel koruyucu ekleme diyaloğu
        if (showAddCustomDialog) {
            AddCustomItemDialog(
                title = stringResource(R.string.add_custom_preservative),
                onDismiss = { showAddCustomDialog = false },
                onConfirm = { preservativeName ->
                    scope.launch {
                        foodAnalysisViewModel.addCustomFoodPreference(userId, FoodPreferenceType.PRESERVATIVE, preservativeName)
                        snackbarHostState.showSnackbar("Özel koruyucu eklendi: $preservativeName")
                    }
                }
            )
        }
    }
}

/**
 * Koruyucular listesi
 */
@Composable
fun PreservativesList(
    preservatives: List<Preservative>,
    language: SupportedLanguage,
    navController: NavController,
    selectedFoodPreferences: Map<String, Boolean>,
    userFoodPreferencesState: UserFoodPreferencesState,
    userId: String,
    onSavePreference: (Preservative, Boolean) -> Unit
) {
    LazyColumn(
        contentPadding = PaddingValues(16.dp)
    ) {
        items(preservatives) { preservative ->
            PreservativeItem(
                preservative = preservative,
                language = language,
                isSelected = selectedFoodPreferences[preservative.preservativeId] ?: false,
                onCheckedChange = { isChecked ->
                    onSavePreference(preservative, isChecked)
                },
                onClick = {
                    navController.navigate(Screen.PreservativeDetail.createRoute(preservative.preservativeId))
                }
            )
        }
    }
}

/**
 * Koruyucu öğesi
 */
@Composable
fun PreservativeItem(
    preservative: Preservative,
    language: SupportedLanguage,
    isSelected: Boolean = false,
    onCheckedChange: (Boolean) -> Unit = {},
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable(onClick = onClick)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Checkbox
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = onCheckedChange,
                    modifier = Modifier.padding(end = 8.dp)
                )

                // Emoji veya ikon
                if (preservative.symbol.isNotEmpty()) {
                    Text(
                        text = preservative.symbol,
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier.size(32.dp)
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                }

                // Koruyucu adı
                Text(
                    text = preservative.getName(language),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Koruyucu açıklaması
            Text(
                text = preservative.getDescription(language),
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Risk seviyesi
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.risk_level),
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = preservative.riskLevel.toString(),
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Divider(modifier = Modifier.padding(vertical = 8.dp))

            // Yaygın ürünler
            val commonProducts = preservative.getCommonProducts(language)
            if (commonProducts.isNotEmpty()) {
                Text(
                    text = stringResource(R.string.common_products),
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = commonProducts.joinToString(", "),
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
