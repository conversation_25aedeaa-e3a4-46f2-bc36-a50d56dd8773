package com.healthyproducts.app.data.repository;

/**
 * Tarama geçmişini yöneten repository sınıfı
 * Not: Şu an için mock veri kullan<PERSON><PERSON><PERSON><PERSON>, gerçek Firebase entegrasyonu daha sonra eklenecek
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001:\u0001\u001fB\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002JP\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b2\u0006\u0010\f\u001a\u00020\u00052\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0010\u001a\u00020\u0011H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\u0013J$\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u000b2\u0006\u0010\f\u001a\u00020\u0005H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0016\u0010\u0017J4\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\u00190\u000b2\u0006\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\u001b\u001a\u00020\u001cH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001d\u0010\u001eR\u001a\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u0007\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006 "}, d2 = {"Lcom/healthyproducts/app/data/repository/ScanHistoryRepository;", "", "()V", "mockProducts", "", "", "Lcom/healthyproducts/app/model/Product;", "mockScanHistory", "", "Lcom/healthyproducts/app/model/ScanHistory;", "addToScanHistory", "Lkotlin/Result;", "userId", "productId", "barcode", "imageUrl", "scanType", "Lcom/healthyproducts/app/model/ScanType;", "addToScanHistory-hUnOzRk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/healthyproducts/app/model/ScanType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearScanHistory", "", "clearScanHistory-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getScanHistory", "", "Lcom/healthyproducts/app/data/repository/ScanHistoryRepository$ScanHistoryWithProduct;", "limit", "", "getScanHistory-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "ScanHistoryWithProduct", "app_debug"})
public final class ScanHistoryRepository {
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.util.List<com.healthyproducts.app.model.ScanHistory>> mockScanHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.healthyproducts.app.model.Product> mockProducts = null;
    
    @javax.inject.Inject()
    public ScanHistoryRepository() {
        super();
    }
    
    /**
     * Tarama geçmişi ve ürün bilgisini birleştiren veri sınıfı
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0015"}, d2 = {"Lcom/healthyproducts/app/data/repository/ScanHistoryRepository$ScanHistoryWithProduct;", "", "scanHistory", "Lcom/healthyproducts/app/model/ScanHistory;", "product", "Lcom/healthyproducts/app/model/Product;", "(Lcom/healthyproducts/app/model/ScanHistory;Lcom/healthyproducts/app/model/Product;)V", "getProduct", "()Lcom/healthyproducts/app/model/Product;", "getScanHistory", "()Lcom/healthyproducts/app/model/ScanHistory;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class ScanHistoryWithProduct {
        @org.jetbrains.annotations.NotNull()
        private final com.healthyproducts.app.model.ScanHistory scanHistory = null;
        @org.jetbrains.annotations.Nullable()
        private final com.healthyproducts.app.model.Product product = null;
        
        public ScanHistoryWithProduct(@org.jetbrains.annotations.NotNull()
        com.healthyproducts.app.model.ScanHistory scanHistory, @org.jetbrains.annotations.Nullable()
        com.healthyproducts.app.model.Product product) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.healthyproducts.app.model.ScanHistory getScanHistory() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.healthyproducts.app.model.Product getProduct() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.healthyproducts.app.model.ScanHistory component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.healthyproducts.app.model.Product component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.healthyproducts.app.data.repository.ScanHistoryRepository.ScanHistoryWithProduct copy(@org.jetbrains.annotations.NotNull()
        com.healthyproducts.app.model.ScanHistory scanHistory, @org.jetbrains.annotations.Nullable()
        com.healthyproducts.app.model.Product product) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}