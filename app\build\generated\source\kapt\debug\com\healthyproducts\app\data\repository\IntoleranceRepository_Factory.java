package com.healthyproducts.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class IntoleranceRepository_Factory implements Factory<IntoleranceRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public IntoleranceRepository_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public IntoleranceRepository get() {
    return newInstance(firestoreProvider.get());
  }

  public static IntoleranceRepository_Factory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new IntoleranceRepository_Factory(firestoreProvider);
  }

  public static IntoleranceRepository newInstance(FirebaseFirestore firestore) {
    return new IntoleranceRepository(firestore);
  }
}
