package com.healthyproducts.app

import android.app.Application
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import com.healthyproducts.app.data.model.SupportedLanguage
import com.healthyproducts.app.data.preferences.LanguagePreferences
import com.healthyproducts.app.data.repository.FirestoreRepository
import com.healthyproducts.app.util.LocaleHelper
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Locale
import javax.inject.Inject

/**
 * Uygulama sınıfı
 */
@HiltAndroidApp
class HealthyProductsApp : Application() {

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    @Inject
    lateinit var firestoreRepository: FirestoreRepository

    override fun onCreate() {
        super.onCreate()

        // Firebase'i başlat
        initializeFirebase()

        // Dil ayarlarını başlat
        initializeLanguage()
    }

    /**
     * Firebase'i başlatma
     */
    private fun initializeFirebase() {
        try {
            // Firebase'i başlat
            FirebaseApp.initializeApp(this)

            // Firebase bağlantısını kontrol et
            val auth = FirebaseAuth.getInstance()
            Log.d("Firebase", "Firebase başarıyla başlatıldı. Kullanıcı giriş durumu: ${auth.currentUser != null}")
        } catch (e: Exception) {
            Log.e("Firebase", "Firebase başlatılırken hata oluştu", e)
        }
    }

    /**
     * Dil ayarlarını başlatma
     */
    private fun initializeLanguage() {
        try {
            // SharedPreferences'tan direkt oku (senkron)
            val sharedPrefs = getSharedPreferences("language_preferences", MODE_PRIVATE)
            val languageCode = sharedPrefs.getString("language_code", "en") ?: "en"  // Varsayılan İngilizce

            // Locale'i doğrudan ayarla
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            // Uygulama dilini AppCompatDelegate ile de ayarla
            val localeList = LocaleListCompat.forLanguageTags(languageCode)
            AppCompatDelegate.setApplicationLocales(localeList)

            // Dil değişikliğini logla
            Log.d("HealthyProductsApp", "Application locale set to: $languageCode")

            // Asenkron olarak Firestore güncelleme
            applicationScope.launch {
                updateFirestoreLanguage(languageCode)
            }

        } catch (e: Exception) {
            Log.e("HealthyProductsApp", "Error initializing language", e)
        }
    }

    /**
     * Firestore'daki dil ayarını günceller
     */
    private suspend fun updateFirestoreLanguage(languageCode: String) {
        try {
            // Kullanıcı oturum açmışsa, Firestore'daki dil ayarını güncelle
            val currentUser = FirebaseAuth.getInstance().currentUser
            if (currentUser != null) {
                try {
                    // Kullanıcı tercihlerini al
                    val userPreferences = firestoreRepository.userPreferences.value

                    // Dil ayarını güncelle
                    if (userPreferences.language != languageCode) {
                        // Mevcut AI model değerini logla ve koru
                        val currentAiModel = userPreferences.aiModel
                        Log.d("HealthyProductsApp", "Current AI model before language change: $currentAiModel")

                        // Sadece dil değerini güncelle, diğer değerleri koru
                        val updatedPreferences = userPreferences.copy(language = languageCode)

                        // AI model değerinin korunduğunu logla
                        Log.d("HealthyProductsApp", "Preserving AI model: ${updatedPreferences.aiModel}")

                        firestoreRepository.updateUserPreferences(updatedPreferences)

                        // Firestore'daki kullanıcı dilini güncelle
                        val language = LocaleHelper.Language.fromCode(languageCode)
                        val supportedLanguage = LocaleHelper.languageToSupportedLanguage(language)
                        firestoreRepository.updateUserLanguage(supportedLanguage)
                    }
                } catch (e: Exception) {
                    Log.e("HealthyProductsApp", "Error updating user language in Firestore", e)
                }
            }

            Log.d("HealthyProductsApp", "Language initialized to: $languageCode")
        } catch (e: Exception) {
            Log.e("HealthyProductsApp", "Error updating Firestore language", e)
        }
    }
}
