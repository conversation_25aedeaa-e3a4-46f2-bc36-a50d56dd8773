package com.healthyproducts.app.di;

import com.google.firebase.firestore.FirebaseFirestore;
import com.healthyproducts.app.data.repository.FatRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideFatRepositoryFactory implements Factory<FatRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public RepositoryModule_ProvideFatRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public FatRepository get() {
    return provideFatRepository(firestoreProvider.get());
  }

  public static RepositoryModule_ProvideFatRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new RepositoryModule_ProvideFatRepositoryFactory(firestoreProvider);
  }

  public static FatRepository provideFatRepository(FirebaseFirestore firestore) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideFatRepository(firestore));
  }
}
