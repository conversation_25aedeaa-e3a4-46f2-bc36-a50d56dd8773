package com.healthyproducts.app.data.repository

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.gson.GsonBuilder
import com.healthyproducts.app.data.model.Fat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Ya<PERSON> türleri işlemlerini yöneten repository sınıfı
 */
@Singleton
class FatRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val TAG = "FatRepository"
        private const val COLLECTION_NAME = "fats"
    }

    private val _fats = MutableStateFlow<List<Fat>>(emptyList())
    val fats: StateFlow<List<Fat>> = _fats.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    /**
     * Tüm yağ türlerini getirir
     */
    suspend fun getAllFats(): Result<List<Fat>> = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null

            Log.d(TAG, "Getting all fats")

            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val fatsList = snapshot.documents.mapNotNull { document ->
                document.toObject(Fat::class.java)
            }

            Log.d(TAG, "Retrieved ${fatsList.size} fats")

            _fats.value = fatsList
            _isLoading.value = false

            Result.success(fatsList)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting fats", e)
            _error.value = e.message
            _isLoading.value = false
            Result.failure(e)
        }
    }

    /**
     * Yağ türü ekler
     */
    suspend fun addFat(fat: Fat): Result<Fat> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding fat: ${fat.fatId} - ${fat.nameTr}")

            // Yağ türünü Firestore'a ekle
            val documentRef = firestore.collection(COLLECTION_NAME).document(fat.fatId)
            documentRef.set(fat).await()

            // Yağ türünü yerel listeye ekle
            val currentList = _fats.value.toMutableList()
            currentList.add(fat)
            _fats.value = currentList

            Log.d(TAG, "Fat added successfully")

            Result.success(fat)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding fat", e)
            Result.failure(e)
        }
    }

    /**
     * Yağ türünü günceller
     */
    suspend fun updateFat(fat: Fat): Result<Fat> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Updating fat: ${fat.fatId} - ${fat.nameTr}")

            // Yağ türünü Firestore'da güncelle
            val documentRef = firestore.collection(COLLECTION_NAME).document(fat.fatId)
            documentRef.set(fat).await()

            // Yağ türünü yerel listede güncelle
            val currentList = _fats.value.toMutableList()
            val index = currentList.indexOfFirst { it.fatId == fat.fatId }
            if (index != -1) {
                currentList[index] = fat
                _fats.value = currentList
            }

            Log.d(TAG, "Fat updated successfully")

            Result.success(fat)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating fat", e)
            Result.failure(e)
        }
    }

    /**
     * Yağ türünü siler
     */
    suspend fun deleteFat(fatId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting fat: $fatId")

            // Yağ türünü Firestore'dan sil
            val documentRef = firestore.collection(COLLECTION_NAME).document(fatId)
            documentRef.delete().await()

            // Yağ türünü yerel listeden sil
            val currentList = _fats.value.toMutableList()
            val index = currentList.indexOfFirst { it.fatId == fatId }
            if (index != -1) {
                currentList.removeAt(index)
                _fats.value = currentList
            }

            Log.d(TAG, "Fat deleted successfully")

            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting fat", e)
            Result.failure(e)
        }
    }

    /**
     * Yağ türünü ID ile getirir
     */
    suspend fun getFatById(fatId: String): Result<Fat?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting fat by ID: $fatId")

            // Önce yerel listede ara
            val localFat = _fats.value.find { it.fatId == fatId }
            if (localFat != null) {
                Log.d(TAG, "Fat found in local cache")
                return@withContext Result.success(localFat)
            }

            // Yerel listede yoksa Firestore'dan getir
            val documentRef = firestore.collection(COLLECTION_NAME).document(fatId)
            val document = documentRef.get().await()

            val fat = document.toObject(Fat::class.java)

            if (fat != null) {
                Log.d(TAG, "Fat found in Firestore")
            } else {
                Log.d(TAG, "Fat not found")
            }

            Result.success(fat)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting fat by ID", e)
            Result.failure(e)
        }
    }

    /**
     * Yağ türlerini adına göre arar
     */
    suspend fun searchFatsByName(query: String): Result<List<Fat>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Searching fats by name: $query")

            // Firestore'da tam metin araması yapılamadığı için tüm yağ türlerini getirip filtreliyoruz
            val snapshot = firestore.collection(COLLECTION_NAME)
                .get()
                .await()

            val fatsList = snapshot.documents.mapNotNull { document ->
                document.toObject(Fat::class.java)
            }.filter { fat ->
                fat.nameTr.contains(query, ignoreCase = true) ||
                fat.nameEn.contains(query, ignoreCase = true) ||
                fat.fatId.contains(query, ignoreCase = true)
            }

            Log.d(TAG, "Found ${fatsList.size} fats matching query")

            Result.success(fatsList)
        } catch (e: Exception) {
            Log.e(TAG, "Error searching fats", e)
            Result.failure(e)
        }
    }

    /**
     * Yağ türünü kodu ile getirir
     */
    suspend fun getFatByCode(code: String): Result<Fat?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting fat by code: $code")

            // Önce yerel listede ara
            val localFat = _fats.value.find { it.id == code || it.fatId == code }
            if (localFat != null) {
                Log.d(TAG, "Fat found in local cache")
                return@withContext Result.success(localFat)
            }

            // Yerel listede yoksa Firestore'dan getir
            val snapshot = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("id", code)
                .get()
                .await()

            val fat = snapshot.documents.firstOrNull()?.toObject(Fat::class.java)

            if (fat != null) {
                Log.d(TAG, "Fat found in Firestore by id")
                return@withContext Result.success(fat)
            }

            // id ile bulunamadıysa fatId ile dene
            val snapshot2 = firestore.collection(COLLECTION_NAME)
                .whereEqualTo("fatId", code)
                .get()
                .await()

            val fat2 = snapshot2.documents.firstOrNull()?.toObject(Fat::class.java)

            if (fat2 != null) {
                Log.d(TAG, "Fat found in Firestore by fatId")
                return@withContext Result.success(fat2)
            }

            // Doğrudan belge ID'si olarak da dene
            val documentRef = firestore.collection(COLLECTION_NAME).document(code)
            val document = documentRef.get().await()
            val fat3 = document.toObject(Fat::class.java)

            if (fat3 != null) {
                Log.d(TAG, "Fat found in Firestore by document ID")
                return@withContext Result.success(fat3)
            }

            Log.d(TAG, "Fat not found with code: $code")
            Result.success(null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting fat by code", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dosyasından yağ türlerini yükler ve Firestore'a kaydeder
     */
    suspend fun importFatsFromJson(context: Context): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Importing fats from JSON file")

            // JSON dosyasını oku
            val jsonString = try {
                val inputStream = context.assets.open("json/fatList.json")
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                inputStream.close()
                String(buffer, Charsets.UTF_8)
            } catch (e: IOException) {
                Log.e(TAG, "Error reading fats JSON file", e)
                return@withContext Result.failure(e)
            }

            // JSON içeriğinin bir kısmını logla
            Log.d(TAG, "JSON content length: ${jsonString.length}")
            Log.d(TAG, "JSON content first 100 chars: ${jsonString.take(100)}")
            Log.d(TAG, "JSON content last 100 chars: ${jsonString.takeLast(100)}")

            // JSON'ı parse et
            val gson = GsonBuilder()
                .setLenient()
                .create()

            val jsonArray = JSONArray(jsonString)
            val fats = mutableListOf<Fat>()

            try {
                // JSON içeriğini logla
                Log.d(TAG, "JSON array length: ${jsonArray.length()}")

                // İlk birkaç JSON nesnesini logla
                for (i in 0 until minOf(3, jsonArray.length())) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    Log.d(TAG, "JSON object $i: ${jsonObject.toString().take(200)}...")
                }

                // Tüm JSON nesnelerini işle
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)

                    // Dizileri işle
                    val labelsTr = parseJsonArray(jsonObject.optJSONArray("labels_tr"))
                    val labelsEn = parseJsonArray(jsonObject.optJSONArray("labels_en"))
                    val functionalType = parseJsonArray(jsonObject.optJSONArray("functional_type"))

                    // Yağ nesnesini oluştur
                    val fat = Fat(
                        id = jsonObject.optString("id", ""),
                        fatId = jsonObject.optString("id", ""),
                        nameTr = jsonObject.optString("name_tr", ""),
                        nameEn = jsonObject.optString("name_en", ""),
                        symbol = jsonObject.optString("symbol", ""),
                        descriptionTr = jsonObject.optString("description_tr", ""),
                        descriptionEn = jsonObject.optString("description_en", ""),
                        originTr = jsonObject.optString("source_tr", ""), // JSON'da source_tr olarak geçiyor
                        originEn = jsonObject.optString("source_en", ""), // JSON'da source_en olarak geçiyor
                        typeTr = jsonObject.optString("fatty_type_tr", ""), // JSON'da fatty_type_tr olarak geçiyor
                        typeEn = jsonObject.optString("fatty_type_en", ""), // JSON'da fatty_type_en olarak geçiyor
                        healthEffectTr = "", // JSON'da yok, boş bırakıyoruz
                        healthEffectEn = "", // JSON'da yok, boş bırakıyoruz
                        riskLevel = jsonObject.optInt("risk_level", 0),
                        labelsTr = parseJsonArray(jsonObject.optJSONArray("hidden_names_tr")), // JSON'da hidden_names_tr olarak geçiyor
                        labelsEn = parseJsonArray(jsonObject.optJSONArray("hidden_names_en")), // JSON'da hidden_names_en olarak geçiyor
                        notesTr = jsonObject.optString("notes_tr", ""),
                        notesEn = jsonObject.optString("notes_en", ""),
                        functionalType = functionalType
                    )

                    fats.add(fat)
                }

                // İlk birkaç yağın içeriğini detaylı logla
                fats.take(3).forEach { fat ->
                    Log.d(TAG, "Parsed fat: ${fat.id}")
                    Log.d(TAG, "  fatId: '${fat.fatId}'")
                    Log.d(TAG, "  nameTr: '${fat.nameTr}'")
                    Log.d(TAG, "  nameEn: '${fat.nameEn}'")
                    Log.d(TAG, "  descriptionTr: '${fat.descriptionTr}'")
                    Log.d(TAG, "  symbol: '${fat.symbol}'")
                    Log.d(TAG, "  labelsTr: ${fat.labelsTr}")
                    Log.d(TAG, "  functionalType: ${fat.functionalType}")
                }

                Log.d(TAG, "Parsed ${fats.size} fats from JSON")

                // Firestore'a kaydet
                var successCount = 0
                fats.forEach { fat ->
                    try {
                        // Fat nesnesini Map'e dönüştür
                        val fatMap = mapOf(
                            "id" to fat.id,
                            "fatId" to fat.fatId,
                            "name_tr" to fat.nameTr,
                            "name_en" to fat.nameEn,
                            "symbol" to fat.symbol,
                            "description_tr" to fat.descriptionTr,
                            "description_en" to fat.descriptionEn,
                            "origin_tr" to fat.originTr,
                            "origin_en" to fat.originEn,
                            "type_tr" to fat.typeTr,
                            "type_en" to fat.typeEn,
                            "health_effect_tr" to fat.healthEffectTr,
                            "health_effect_en" to fat.healthEffectEn,
                            "risk_level" to fat.riskLevel,
                            "labels_tr" to fat.labelsTr,
                            "labels_en" to fat.labelsEn,
                            "notes_tr" to fat.notesTr,
                            "notes_en" to fat.notesEn,
                            "functional_type" to fat.functionalType
                        )

                        val documentRef = firestore.collection(COLLECTION_NAME).document(fat.fatId)

                        // Map içeriğini logla
                        Log.d(TAG, "Saving fat to Firestore: ${fat.fatId}")
                        Log.d(TAG, "  Map content: ${fatMap.entries.joinToString { "${it.key}='${it.value}'" }}")

                        documentRef.set(fatMap).await()

                        // Kaydedilen veriyi kontrol et
                        val savedDoc = documentRef.get().await()
                        Log.d(TAG, "  Saved document: ${savedDoc.data?.entries?.joinToString { "${it.key}='${it.value}'" }}")

                        successCount++
                    } catch (e: Exception) {
                        Log.e(TAG, "Error importing fat ${fat.fatId}", e)
                    }
                }

                Log.d(TAG, "Successfully imported $successCount out of ${fats.size} fats")

                Result.success(successCount)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing JSON", e)
                return@withContext Result.failure(e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error importing fats from JSON", e)
            Result.failure(e)
        }
    }

    /**
     * JSON dizisini String listesine dönüştürür
     */
    private fun parseJsonArray(jsonArray: JSONArray?): List<String> {
        if (jsonArray == null) return emptyList()

        val result = mutableListOf<String>()
        for (i in 0 until jsonArray.length()) {
            result.add(jsonArray.optString(i, ""))
        }
        return result
    }
}
