{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values/values.xml", "map": [{"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,114,113,-1,112,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,110,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,111,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5676,5613,-1,5538,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5417,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5479,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,58,62,-1,74,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,61,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,58,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5730,5671,-1,5608,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5474,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5533,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,417,418,419,420,428,429,430,431,432,433,434,435,436,437,438,439,440,482,485,486,488,489,490,491,492,493,494,495,499,500,501,503,504,505,506,507,508,512,513,514,515,516,517,518,519,520,521,522,523,525,526,527,532,533,534,535,536,537,538,539,540,541,542,543,544,545,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,627,628,629,630,632,633,634,635,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,677,678,679,680,681,682,683,684,685,686,687,688,689,694,695,696,697,698,699,700,701,702,703,704,705,706,707,709,712,715,716,717,718,719,720,721,722,723,724,725,726", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24876,24916,24970,25038,25112,25170,25230,25306,25368,25430,25485,25541,25602,25662,25725,25790,25855,25911,25960,26029,26092,26140,26186,26249,26309,26357,26416,26479,26523,26681,26731,26785,26835,26873,27432,27573,27655,27697,27747,27813,27867,27941,28013,28087,28147,28213,28269,32912,33113,33175,33305,33365,33417,33479,33527,33644,33711,33776,34162,34204,34256,34376,34414,34470,34567,34607,34675,34930,34980,35018,35066,35122,35263,35326,35390,35454,35518,35569,35629,35773,35831,35894,36396,36458,36512,36580,36636,36694,36732,36794,36846,36914,36966,37036,37112,37168,37331,37397,37449,37521,37603,37662,37735,37809,37865,37911,37973,38026,38089,38142,38189,38229,38307,38371,42854,42902,42956,42994,43126,43190,43264,43321,43431,43471,43560,43608,43642,43684,43730,43778,43849,43926,43980,44048,44104,44162,44222,44452,44502,44556,44602,44672,44738,44810,44854,44894,44954,45004,45042,45104,45142,45196,45250,45312,45393,45442,45484,45651,45713,45759,45821,45897,45965,46033,46105,46175,46251,46319,46364,46411,46655,46702,46749,46800,46849,46906,46961,47026,47083,47138,47185,47242,47296,47338,47459,47633,47806,47846,47901,47961,48022,48081,48142,48182,48252,48306,48370,48414", "endColumns": "39,53,67,73,57,59,75,61,61,54,55,60,59,62,64,64,55,48,68,62,47,45,62,59,47,58,62,43,74,49,53,49,37,47,140,81,41,49,65,53,73,71,73,59,65,55,39,59,61,61,59,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,57,37,61,51,67,51,69,75,55,50,65,51,71,81,58,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,69,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,57,59,43,49,53,45,69,65,71,43,39,59,49,37,61,37,53,53,61,80,48,41,67,61,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,73,47,61,39,54,59,60,58,60,39,69,53,63,43,71", "endOffsets": "24911,24965,25033,25107,25165,25225,25301,25363,25425,25480,25536,25597,25657,25720,25785,25850,25906,25955,26024,26087,26135,26181,26244,26304,26352,26411,26474,26518,26593,26726,26780,26830,26868,26916,27568,27650,27692,27742,27808,27862,27936,28008,28082,28142,28208,28264,28304,32967,33170,33232,33360,33412,33474,33522,33639,33706,33771,33846,34199,34251,34313,34409,34465,34562,34602,34670,34710,34975,35013,35061,35117,35258,35321,35385,35449,35513,35564,35624,35686,35826,35889,35945,36453,36507,36575,36631,36689,36727,36789,36841,36909,36961,37031,37107,37163,37214,37392,37444,37516,37598,37657,37730,37804,37860,37906,37968,38021,38084,38137,38184,38224,38302,38366,38408,42897,42951,42989,43059,43185,43259,43316,43372,43466,43555,43603,43637,43679,43725,43773,43844,43921,43975,44043,44099,44157,44217,44261,44497,44551,44597,44667,44733,44805,44849,44889,44949,44999,45037,45099,45137,45191,45245,45307,45388,45437,45479,45547,45708,45754,45816,45892,45960,46028,46100,46170,46246,46314,46359,46406,46451,46697,46744,46795,46844,46901,46956,47021,47078,47133,47180,47237,47291,47333,47407,47502,47690,47841,47896,47956,48017,48076,48137,48177,48247,48301,48365,48409,48481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\981fd9293439b18a98e73358fb60a309\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3,2286", "startColumns": "4,4", "startOffsets": "202,153496", "endLines": "3,2288", "endColumns": "60,12", "endOffsets": "258,153636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,421,422,423,424,425,426,427,693,2124,2125,2130,2133,2138,2284,2285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "564,1590,1662,3001,3066,6278,6347,15105,15175,15243,15315,15385,15446,15520,16377,16438,16499,16561,16625,16687,16748,16816,16916,16976,17042,17115,17184,17241,17293,18020,18092,18168,18233,18292,18351,18411,18471,18531,18591,18651,18711,18771,18831,18891,18951,19010,19070,19130,19190,19250,19310,19370,19430,19490,19550,19610,19669,19729,19789,19848,19907,19966,20025,20084,20902,20937,21341,21396,21459,21514,21572,21630,21691,21754,21811,21862,21912,21973,22030,22096,22130,22165,22940,26921,26988,27060,27129,27198,27272,27344,46584,140830,140947,141214,141507,141774,153357,153429", "endLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,421,422,423,424,425,426,427,693,2124,2128,2130,2136,2138,2284,2285", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "619,1657,1745,3061,3127,6342,6405,15170,15238,15310,15380,15441,15515,15588,16433,16494,16556,16620,16682,16743,16811,16911,16971,17037,17110,17179,17236,17288,17350,18087,18163,18228,18287,18346,18406,18466,18526,18586,18646,18706,18766,18826,18886,18946,19005,19065,19125,19185,19245,19305,19365,19425,19485,19545,19605,19664,19724,19784,19843,19902,19961,20020,20079,20138,20932,20967,21391,21454,21509,21567,21625,21686,21749,21806,21857,21907,21968,22025,22091,22125,22160,22195,23005,26983,27055,27124,27193,27267,27339,27427,46650,140942,141143,141319,141703,141898,153424,153491"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "36,108,109,110,121,122,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "2069,7087,7134,7181,7925,7970,8136", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2106,7129,7176,7223,7965,8010,8173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ccfdf8fe6da9c4c34a4246aecbf96939\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "322", "startColumns": "4", "startOffsets": "20972", "endColumns": "52", "endOffsets": "21020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "310,312,313,316,318,351,441,442,496,497,502,546,547,631,636,653,654,676,690,691,692,708,710,711,1830,1846,1849", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20348,20472,20530,20708,20793,22503,28309,28374,33851,33917,34318,37219,37271,43064,43377,44348,44398,45605,46456,46502,46544,47412,47507,47543,118658,119638,119749", "endLines": "310,312,313,316,318,351,441,442,496,497,502,546,547,631,636,653,654,676,690,691,692,708,710,711,1832,1848,1852", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "20417,20525,20580,20754,20843,22551,28369,28423,33912,34013,34371,37266,37326,43121,43426,44393,44447,45646,46497,46539,46579,47454,47538,47628,118765,119744,119939"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2176", "startColumns": "4", "startOffsets": "144606", "endColumns": "91", "endOffsets": "144693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c36e1f051d57c59ccd6930c68000c21a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "346", "startColumns": "4", "startOffsets": "22232", "endColumns": "42", "endOffsets": "22270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\167b5a10bb8d261a1d1c21747e97c5f2\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "99,259,260,261,262,2129,2131,2132,2137,2139", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6410,17355,17408,17461,17514,141148,141324,141446,141708,141903", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "6494,17403,17456,17509,17562,141209,141441,141502,141769,141965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\14d03a95cc07898beae69a352dd60254\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "349", "startColumns": "4", "startOffsets": "22389", "endColumns": "49", "endOffsets": "22434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,675,727,728,729,730,731,732,740,741,745,749,760,765,771,778,782,786,791,795,799,803,807,811,815,821,825,831,835,841,845,850,854,857,861,867,871,877,881,887,890,894,898,902,906,910,911,912,913,916,919,922,925,929,930,931,932,933,936,938,940,942,947,948,952,958,962,963,965,977,978,982,988,992,993,994,998,1025,1029,1030,1034,1062,1234,1260,1431,1457,1488,1496,1502,1518,1540,1545,1550,1560,1569,1578,1582,1589,1608,1615,1616,1625,1628,1631,1635,1639,1643,1646,1647,1652,1657,1667,1672,1679,1685,1686,1689,1693,1698,1700,1702,1705,1708,1710,1714,1717,1724,1727,1730,1734,1736,1740,1742,1744,1746,1750,1758,1766,1778,1784,1793,1796,1807,1810,1811,1816,1817,1853,1922,1992,1993,2003,2012,2013,2015,2019,2022,2025,2028,2031,2034,2037,2040,2044,2047,2050,2053,2057,2060,2064,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2090,2092,2093,2094,2095,2096,2097,2098,2099,2101,2102,2104,2105,2107,2109,2110,2112,2113,2114,2115,2116,2117,2119,2120,2121,2122,2123,2140,2142,2144,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2160,2161,2162,2163,2164,2165,2166,2168,2172,2177,2178,2179,2180,2181,2182,2186,2187,2188,2189,2191,2193,2195,2197,2199,2200,2201,2202,2204,2206,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2222,2223,2224,2225,2227,2229,2230,2232,2233,2235,2237,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2252,2253,2254,2255,2257,2258,2259,2260,2261,2263,2265,2267,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "263,318,515,931,972,1027,1089,1153,1223,1284,1359,1435,1512,1750,1835,1917,1993,2111,2188,2266,2372,2478,2557,2886,2943,4925,4999,5074,5139,5205,5265,5326,5398,5471,5538,5606,5665,5724,5783,5842,5901,5955,6009,6062,6116,6170,6224,6499,6573,6652,6725,6799,6870,6942,7014,7228,7285,7343,7416,7490,7564,7639,7711,7784,7854,8015,8075,8178,8247,8316,8386,8460,8536,8600,8677,8753,8830,8895,8964,9041,9116,9185,9253,9330,9396,9457,9554,9619,9688,9787,9858,9917,9975,10032,10091,10155,10226,10298,10370,10442,10514,10581,10649,10717,10776,10839,10903,10993,11084,11144,11210,11277,11343,11413,11477,11530,11597,11658,11725,11838,11896,11959,12024,12089,12164,12237,12309,12353,12400,12446,12495,12556,12617,12678,12740,12804,12868,12932,12997,13060,13120,13181,13247,13306,13366,13428,13499,13559,15593,15679,15766,15856,15943,16031,16113,16196,16286,17567,17619,17677,17722,17788,17852,17909,17966,20143,20200,20248,20297,20759,21092,21139,21295,22200,22556,22620,22682,22742,23010,23084,23154,23232,23286,23356,23441,23489,23535,23596,23659,23725,23789,23860,23923,23988,24052,24113,24174,24226,24299,24373,24442,24517,24591,24665,24806,45552,48486,48564,48654,48742,48838,48928,49510,49599,49846,50127,50793,51078,51471,51948,52170,52392,52668,52895,53125,53355,53585,53815,54042,54461,54687,55112,55342,55770,55989,56272,56480,56611,56838,57264,57489,57916,58137,58562,58682,58958,59259,59583,59874,60188,60325,60456,60561,60803,60970,61174,61382,61653,61765,61877,61982,62099,62313,62459,62599,62685,63033,63121,63367,63785,64034,64116,64214,64871,64971,65223,65647,65902,65996,66085,66322,68346,68588,68690,68943,71099,81780,83296,93991,95519,97276,97902,98322,99583,100848,101104,101340,101887,102381,102986,103184,103764,105132,105507,105625,106163,106320,106516,106789,107045,107215,107356,107420,107785,108152,108828,109092,109430,109783,109877,110063,110369,110631,110756,110883,111122,111333,111452,111645,111822,112277,112458,112580,112839,112952,113139,113241,113348,113477,113752,114260,114756,115633,115927,116497,116646,117378,117550,117634,117970,118062,119944,125175,130546,130608,131186,131770,131861,131974,132203,132363,132515,132686,132852,133021,133188,133351,133594,133764,133937,134108,134382,134581,134786,135116,135200,135296,135392,135490,135590,135692,135794,135896,135998,136100,136200,136296,136408,136537,136660,136791,136922,137020,137134,137228,137368,137502,137598,137710,137810,137926,138022,138134,138234,138374,138510,138674,138804,138962,139112,139253,139397,139532,139644,139794,139922,140050,140186,140318,140448,140578,140690,141970,142116,142260,142398,142464,142554,142630,142734,142824,142926,143034,143142,143242,143322,143414,143512,143622,143674,143752,143858,143950,144054,144164,144286,144449,144698,144778,144878,144968,145078,145168,145409,145503,145609,145701,145801,145913,146027,146143,146259,146353,146467,146579,146681,146801,146923,147005,147109,147229,147355,147453,147547,147635,147747,147863,147985,148097,148272,148388,148474,148566,148678,148802,148869,148995,149063,149191,149335,149463,149532,149627,149742,149855,149954,150063,150174,150285,150386,150491,150591,150721,150812,150935,151029,151141,151227,151331,151427,151515,151633,151737,151841,151967,152055,152163,152263,152353,152463,152547,152649,152733,152787,152851,152957,153043,153153,153237", "endLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,675,727,728,729,730,731,739,740,744,748,752,764,770,777,781,785,790,794,798,802,806,810,814,820,824,830,834,840,844,849,853,856,860,866,870,876,880,886,889,893,897,901,905,909,910,911,912,915,918,921,924,928,929,930,931,932,935,937,939,941,946,947,951,957,961,962,964,976,977,981,987,991,992,993,997,1024,1028,1029,1033,1061,1233,1259,1430,1456,1487,1495,1501,1517,1539,1544,1549,1559,1568,1577,1581,1588,1607,1614,1615,1624,1627,1630,1634,1638,1642,1645,1646,1651,1656,1666,1671,1678,1684,1685,1688,1692,1697,1699,1701,1704,1707,1709,1713,1716,1723,1726,1729,1733,1735,1739,1741,1743,1745,1749,1757,1765,1777,1783,1792,1795,1806,1809,1810,1815,1816,1821,1921,1991,1992,2002,2011,2012,2014,2018,2021,2024,2027,2030,2033,2036,2039,2043,2046,2049,2052,2056,2059,2063,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2089,2091,2092,2093,2094,2095,2096,2097,2098,2100,2101,2103,2104,2106,2108,2109,2111,2112,2113,2114,2115,2116,2118,2119,2120,2121,2122,2123,2141,2143,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2159,2160,2161,2162,2163,2164,2165,2167,2171,2175,2177,2178,2179,2180,2181,2185,2186,2187,2188,2190,2192,2194,2196,2198,2199,2200,2201,2203,2205,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2221,2222,2223,2224,2226,2228,2229,2231,2232,2234,2236,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2251,2252,2253,2254,2256,2257,2258,2259,2260,2262,2264,2266,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "313,358,559,967,1022,1084,1148,1218,1279,1354,1430,1507,1585,1830,1912,1988,2064,2183,2261,2367,2473,2552,2632,2938,2996,4994,5069,5134,5200,5260,5321,5393,5466,5533,5601,5660,5719,5778,5837,5896,5950,6004,6057,6111,6165,6219,6273,6568,6647,6720,6794,6865,6937,7009,7082,7280,7338,7411,7485,7559,7634,7706,7779,7849,7920,8070,8131,8242,8311,8381,8455,8531,8595,8672,8748,8825,8890,8959,9036,9111,9180,9248,9325,9391,9452,9549,9614,9683,9782,9853,9912,9970,10027,10086,10150,10221,10293,10365,10437,10509,10576,10644,10712,10771,10834,10898,10988,11079,11139,11205,11272,11338,11408,11472,11525,11592,11653,11720,11833,11891,11954,12019,12084,12159,12232,12304,12348,12395,12441,12490,12551,12612,12673,12735,12799,12863,12927,12992,13055,13115,13176,13242,13301,13361,13423,13494,13554,13622,15674,15761,15851,15938,16026,16108,16191,16281,16372,17614,17672,17717,17783,17847,17904,17961,18015,20195,20243,20292,20343,20788,21134,21183,21336,22227,22615,22677,22737,22794,23079,23149,23227,23281,23351,23436,23484,23530,23591,23654,23720,23784,23855,23918,23983,24047,24108,24169,24221,24294,24368,24437,24512,24586,24660,24801,24871,45600,48559,48649,48737,48833,48923,49505,49594,49841,50122,50374,51073,51466,51943,52165,52387,52663,52890,53120,53350,53580,53810,54037,54456,54682,55107,55337,55765,55984,56267,56475,56606,56833,57259,57484,57911,58132,58557,58677,58953,59254,59578,59869,60183,60320,60451,60556,60798,60965,61169,61377,61648,61760,61872,61977,62094,62308,62454,62594,62680,63028,63116,63362,63780,64029,64111,64209,64866,64966,65218,65642,65897,65991,66080,66317,68341,68583,68685,68938,71094,81775,83291,93986,95514,97271,97897,98317,99578,100843,101099,101335,101882,102376,102981,103179,103759,105127,105502,105620,106158,106315,106511,106784,107040,107210,107351,107415,107780,108147,108823,109087,109425,109778,109872,110058,110364,110626,110751,110878,111117,111328,111447,111640,111817,112272,112453,112575,112834,112947,113134,113236,113343,113472,113747,114255,114751,115628,115922,116492,116641,117373,117545,117629,117965,118057,118335,125170,130541,130603,131181,131765,131856,131969,132198,132358,132510,132681,132847,133016,133183,133346,133589,133759,133932,134103,134377,134576,134781,135111,135195,135291,135387,135485,135585,135687,135789,135891,135993,136095,136195,136291,136403,136532,136655,136786,136917,137015,137129,137223,137363,137497,137593,137705,137805,137921,138017,138129,138229,138369,138505,138669,138799,138957,139107,139248,139392,139527,139639,139789,139917,140045,140181,140313,140443,140573,140685,140825,142111,142255,142393,142459,142549,142625,142729,142819,142921,143029,143137,143237,143317,143409,143507,143617,143669,143747,143853,143945,144049,144159,144281,144444,144601,144773,144873,144963,145073,145163,145404,145498,145604,145696,145796,145908,146022,146138,146254,146348,146462,146574,146676,146796,146918,147000,147104,147224,147350,147448,147542,147630,147742,147858,147980,148092,148267,148383,148469,148561,148673,148797,148864,148990,149058,149186,149330,149458,149527,149622,149737,149850,149949,150058,150169,150280,150381,150486,150586,150716,150807,150930,151024,151136,151222,151326,151422,151510,151628,151732,151836,151962,152050,152158,152258,152348,152458,152542,152644,152728,152782,152846,152952,153038,153148,153232,153352"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "498,524,528,529,530,531,652", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "34018,35691,35950,36054,36163,36283,44266", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "34157,35768,36049,36158,36278,36391,44343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\02b89f0ee9be1e0a15cf2905800b0f25\\transformed\\facebook-common-16.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2289,2294,2300,2311,2322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3381,3433,3521,3601,3680,3744,3821,3896,3963,4045,4126,4199,13940,14009,14088,14222,14296,14369,14442,14514,14587,14660,14725,14794,153641,153935,154285,154906,155537", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2293,2299,2310,2321,2324", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12", "endOffsets": "3428,3516,3596,3675,3739,3816,3891,3958,4040,4121,4194,4249,14004,14083,14147,14291,14364,14437,14509,14582,14655,14720,14789,14854,153930,154280,154901,155532,155711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1bf99d2dddf6b3f731ce95e33fee49d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "319,323", "startColumns": "4,4", "startOffsets": "20848,21025", "endColumns": "53,66", "endOffsets": "20897,21087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\856e9279244c37324ec12dc8b216b897\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "326,347", "startColumns": "4,4", "startOffsets": "21188,22275", "endColumns": "41,59", "endOffsets": "21225,22330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "43,44,45,46,207,208,487,509,510,511", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2637,2695,2761,2824,13627,13698,33237,34715,34782,34861", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2690,2756,2819,2881,13693,13765,33300,34777,34856,34925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b616a44a83a35cc1ff94b844e1245adb\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "20422", "endColumns": "49", "endOffsets": "20467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "215,225,226,227,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,2325,2326", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14152,14859,14941,15024,28428,28565,28680,28798,28893,28958,29026,29084,29156,29228,29325,29416,29490,29564,29677,29778,29841,30061,30226,30303,30385,30510,30596,155716,155802", "endLines": "215,225,226,227,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,2325,2333", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12", "endOffsets": "14217,14936,15019,15100,28560,28675,28793,28888,28953,29021,29079,29151,29223,29320,29411,29485,29559,29672,29773,29836,30056,30221,30298,30380,30505,30591,30715,155797,156175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e520bbb7f153fcbb4e273d864eff355d\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "22335", "endColumns": "53", "endOffsets": "22384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21c308ad6a5cb3ffbae3557cea5538ff\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "6,12", "startColumns": "4,4", "startOffsets": "363,624", "endLines": "9,19", "endColumns": "11,11", "endOffsets": "510,926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "713,714", "startColumns": "4,4", "startOffsets": "47695,47751", "endColumns": "55,54", "endOffsets": "47746,47801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5ccc5722ca8455978d22b35e0684d747\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "314,327,350", "startColumns": "4,4,4", "startOffsets": "20585,21230,22439", "endColumns": "56,64,63", "endOffsets": "20637,21290,22498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "357,566,567,568,569,570,571,572,573,574,575,578,579,580,581,582,583,584,585,586,587,588,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,1833,1843", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22867,38413,38501,38587,38668,38752,38821,38886,38969,39075,39161,39281,39335,39404,39465,39534,39623,39718,39792,39889,39982,40080,40229,40320,40408,40504,40602,40666,40734,40821,40915,40982,41054,41126,41227,41336,41412,41481,41529,41595,41659,41733,41790,41847,41919,41969,42023,42094,42165,42235,42304,42362,42438,42509,42583,42669,42719,42789,118770,119485", "endLines": "357,566,567,568,569,570,571,572,573,574,577,578,579,580,581,582,583,584,585,586,587,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,1842,1845", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "22935,38496,38582,38663,38747,38816,38881,38964,39070,39156,39276,39330,39399,39460,39529,39618,39713,39787,39884,39977,40075,40224,40315,40403,40499,40597,40661,40729,40816,40910,40977,41049,41121,41222,41331,41407,41476,41524,41590,41654,41728,41785,41842,41914,41964,42018,42089,42160,42230,42299,42357,42433,42504,42578,42664,42714,42784,42849,119480,119633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "67,68,69,70,71,72,73,74,466,467,468,469,470,471,472,473,475,476,477,478,479,480,481,483,484", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4254,4344,4424,4514,4604,4684,4765,4845,30720,30825,31006,31131,31238,31418,31541,31657,31927,32115,32220,32401,32526,32701,32849,32972,33034", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "4339,4419,4509,4599,4679,4760,4840,4920,30820,31001,31126,31233,31413,31536,31652,31755,32110,32215,32396,32521,32696,32844,32907,33029,33108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5092f4b0fc3229c87d6dc9ffd8fb80cd\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "20642", "endColumns": "65", "endOffsets": "20703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2cda5a1f9d8c110805098b8b6936f0e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "415", "startColumns": "4", "startOffsets": "26598", "endColumns": "82", "endOffsets": "26676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0632335579b593b6b56131d5dd003758\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,51,52,53,54,209,210,211,753,1822,1824,1827", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3132,3193,3255,3317,13770,13829,13886,50379,118340,118404,118530", "endLines": "2,51,52,53,54,209,210,211,759,1823,1826,1829", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "197,3188,3250,3312,3376,13824,13881,13935,50788,118399,118525,118653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "356,474", "startColumns": "4,4", "startOffsets": "22799,31760", "endColumns": "67,166", "endOffsets": "22862,31922"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.healthyproducts.app-mergeDebugResources-81:\\values\\values.xml", "map": [{"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "67,109,213,217,214,218,216,215,45,114,111,113,108,115,118,117,112,120,119,116,107,65,66,143,139,104,194,1,207,102,20,22,94,191,146,40,170,171,172,169,163,84,103,199,81,226,219,59,130,134,129,133,95,148,153,96,110,28,79,192,99,144,140,6,17,204,203,142,174,173,31,198,147,167,166,168,23,41,43,165,151,4,177,209,182,178,180,181,179,183,195,39,30,35,29,27,156,58,126,123,125,124,98,76,221,85,78,197,14,82,225,200,16,162,161,26,21,101,149,80,196,223,222,188,187,186,68,38,7,11,77,86,154,46,193,100,208,152,93,220,5,12,83,13,25,24,97,210,8,73,71,72,60,63,62,64,61,34,33,32,49,50,54,159,160,53,158,51,55,52,157,145,141,44,189,69,190,136,164,135,131,132,150,224,42,155,70,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3370,5166,10666,10930,10734,11004,10854,10792,2267,5449,5276,5388,5106,5504,5695,5630,5332,5829,5760,5567,5058,3261,3307,6938,6746,4990,9723,16,10400,4912,777,873,4587,9561,7102,1963,8463,8537,8609,8403,8044,4170,4950,9995,4016,11548,11064,2859,6223,6535,6158,6460,4629,7230,7472,4671,5220,1310,3930,9611,4798,6998,6794,178,695,10227,10164,6874,8747,8683,1552,9935,7168,8289,8226,8347,955,2017,2125,8170,7364,102,8832,10510,9148,8894,9002,9072,8946,9216,9773,1911,1480,1740,1407,1236,7664,2813,6070,5901,6007,5954,4751,3802,11188,4226,3888,9887,512,4078,11484,10055,638,7988,7948,1147,825,4878,7282,3970,9839,11343,11266,9419,9351,9295,3410,1851,226,342,3842,4290,7534,2329,9679,4838,10450,7422,4549,11126,140,396,4116,450,1066,1017,4709,10562,270,3718,3574,3650,2907,3113,3043,3185,2975,1695,1648,1603,2431,2478,2684,7842,7891,2629,7777,2525,2735,2582,7720,7048,6832,2193,9473,3468,9521,6663,8110,6602,6340,6399,7324,11414,2071,7600,3530,566", "endColumns": "39,53,67,73,57,59,75,61,61,54,55,60,59,62,64,64,55,48,68,62,47,45,62,59,47,43,49,53,49,37,47,81,41,49,65,53,73,71,73,59,65,55,39,59,61,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,57,37,61,51,67,51,69,75,55,50,65,51,71,81,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,57,59,43,53,45,69,65,71,43,39,59,49,37,61,37,53,53,61,80,48,41,67,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,73,47,61,39,54,59,60,58,60,39,69,53,63,43,71", "endOffsets": "3405,5215,10729,10999,10787,11059,10925,10849,2324,5499,5327,5444,5161,5562,5755,5690,5383,5873,5824,5625,5101,3302,3365,6993,6789,5029,9768,65,10445,4945,820,950,4624,9606,7163,2012,8532,8604,8678,8458,8105,4221,4985,10050,4073,11595,11121,2902,6335,6597,6218,6530,4666,7277,7529,4704,5271,1402,3965,9674,4833,7043,6827,221,746,10363,10222,6933,8806,8742,1598,9990,7225,8342,8284,8398,1012,2066,2188,8221,7417,135,8889,10557,9211,8941,9067,9143,8997,9262,9834,1958,1547,1817,1475,1305,7715,2854,6127,5949,6065,6002,4793,3837,11261,4285,3925,9930,561,4111,11543,10124,690,8039,7983,1231,868,4907,7319,4011,9882,11409,11338,9468,9414,9346,3463,1906,265,391,3883,4355,7595,2396,9718,4873,10505,7467,4582,11183,173,445,4165,507,1142,1061,4746,10625,311,3775,3645,3713,2970,3180,3108,3256,3038,1735,1690,1643,2473,2520,2730,7886,7943,2679,7837,2577,2785,2624,7772,7097,6869,2262,9516,3525,9556,6713,8165,6658,6394,6455,7359,11479,2120,7659,3569,633"}, "to": {"startLines": "386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,413,414,415,416,417,425,426,427,428,429,430,431,432,433,434,435,436,478,481,483,484,485,486,487,488,489,493,494,495,497,498,499,500,501,502,506,507,508,509,510,511,512,513,514,515,516,517,519,520,521,526,527,528,529,530,531,532,533,534,535,536,537,538,539,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,620,621,622,624,625,626,627,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,668,669,670,671,672,673,674,675,676,677,678,679,684,685,686,687,688,689,690,691,692,693,694,695,696,697,699,702,705,706,707,708,709,710,711,712,713,714,715,716", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24876,24916,24970,25038,25112,25170,25230,25306,25368,25430,25485,25541,25602,25662,25725,25790,25855,25911,25960,26029,26092,26140,26186,26249,26309,26357,26484,26534,26588,26638,26676,27235,27317,27359,27409,27475,27529,27603,27675,27749,27809,27875,27931,32574,32775,32905,32957,33019,33067,33184,33251,33316,33702,33744,33796,33916,33954,34010,34107,34147,34215,34470,34520,34558,34606,34662,34803,34866,34930,34994,35058,35109,35169,35313,35371,35434,35936,35998,36052,36120,36176,36234,36272,36334,36386,36454,36506,36576,36652,36708,36871,36937,36989,37061,37143,37216,37290,37346,37392,37454,37507,37570,37623,37670,37710,37788,37852,42335,42383,42437,42537,42601,42675,42732,42842,42882,42971,43019,43053,43095,43141,43189,43260,43337,43391,43459,43515,43573,43633,43863,43917,43963,44033,44099,44171,44215,44255,44315,44365,44403,44465,44503,44557,44611,44673,44754,44803,44845,45012,45058,45120,45196,45264,45332,45404,45474,45550,45618,45663,45710,45954,46001,46048,46099,46148,46205,46260,46325,46382,46437,46484,46541,46595,46637,46758,46932,47105,47145,47200,47260,47321,47380,47441,47481,47551,47605,47669,47713", "endColumns": "39,53,67,73,57,59,75,61,61,54,55,60,59,62,64,64,55,48,68,62,47,45,62,59,47,43,49,53,49,37,47,81,41,49,65,53,73,71,73,59,65,55,39,59,61,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,57,37,61,51,67,51,69,75,55,50,65,51,71,81,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,57,59,43,53,45,69,65,71,43,39,59,49,37,61,37,53,53,61,80,48,41,67,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,73,47,61,39,54,59,60,58,60,39,69,53,63,43,71", "endOffsets": "24911,24965,25033,25107,25165,25225,25301,25363,25425,25480,25536,25597,25657,25720,25785,25850,25906,25955,26024,26087,26135,26181,26244,26304,26352,26396,26529,26583,26633,26671,26719,27312,27354,27404,27470,27524,27598,27670,27744,27804,27870,27926,27966,32629,32832,32952,33014,33062,33179,33246,33311,33386,33739,33791,33853,33949,34005,34102,34142,34210,34250,34515,34553,34601,34657,34798,34861,34925,34989,35053,35104,35164,35226,35366,35429,35485,35993,36047,36115,36171,36229,36267,36329,36381,36449,36501,36571,36647,36703,36754,36932,36984,37056,37138,37211,37285,37341,37387,37449,37502,37565,37618,37665,37705,37783,37847,37889,42378,42432,42470,42596,42670,42727,42783,42877,42966,43014,43048,43090,43136,43184,43255,43332,43386,43454,43510,43568,43628,43672,43912,43958,44028,44094,44166,44210,44250,44310,44360,44398,44460,44498,44552,44606,44668,44749,44798,44840,44908,45053,45115,45191,45259,45327,45399,45469,45545,45613,45658,45705,45750,45996,46043,46094,46143,46200,46255,46320,46377,46432,46479,46536,46590,46632,46706,46801,46989,47140,47195,47255,47316,47375,47436,47476,47546,47600,47664,47708,47780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\981fd9293439b18a98e73358fb60a309\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2276,3076,3082", "startColumns": "4,4,4,4", "startOffsets": "202,152795,181067,181278", "endLines": "3,2278,3081,3165", "endColumns": "60,12,24,24", "endOffsets": "258,152935,181273,185789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,418,419,420,421,422,423,424,683,2114,2115,2120,2123,2128,2274,2275,2976,3038,3184,3217,3247,3280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "564,1590,1662,3001,3066,6278,6347,15105,15175,15243,15315,15385,15446,15520,16377,16438,16499,16561,16625,16687,16748,16816,16916,16976,17042,17115,17184,17241,17293,18020,18092,18168,18233,18292,18351,18411,18471,18531,18591,18651,18711,18771,18831,18891,18951,19010,19070,19130,19190,19250,19310,19370,19430,19490,19550,19610,19669,19729,19789,19848,19907,19966,20025,20084,20902,20937,21341,21396,21459,21514,21572,21630,21691,21754,21811,21862,21912,21973,22030,22096,22130,22165,22940,26724,26791,26863,26932,27001,27075,27147,45883,140129,140246,140513,140806,141073,152656,152728,176912,179409,186366,188097,189097,189779", "endLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,418,419,420,421,422,423,424,683,2114,2118,2120,2126,2128,2274,2275,2981,3047,3216,3237,3279,3285", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "619,1657,1745,3061,3127,6342,6405,15170,15238,15310,15380,15441,15515,15588,16433,16494,16556,16620,16682,16743,16811,16911,16971,17037,17110,17179,17236,17288,17350,18087,18163,18228,18287,18346,18406,18466,18526,18586,18646,18706,18766,18826,18886,18946,19005,19065,19125,19185,19245,19305,19365,19425,19485,19545,19605,19664,19724,19784,19843,19902,19961,20020,20079,20138,20932,20967,21391,21454,21509,21567,21625,21686,21749,21806,21857,21907,21968,22025,22091,22125,22160,22195,23005,26786,26858,26927,26996,27070,27142,27230,45949,140241,140442,140618,141002,141197,152723,152790,177110,179705,188092,188773,189774,189941"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "36,108,109,110,121,122,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "2069,7087,7134,7181,7925,7970,8136", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2106,7129,7176,7223,7965,8010,8173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ccfdf8fe6da9c4c34a4246aecbf96939\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "322,2428,3528,3531", "startColumns": "4,4,4,4", "startOffsets": "20972,159002,198000,198115", "endLines": "322,2434,3530,3533", "endColumns": "52,24,24,24", "endOffsets": "21020,159301,198110,198225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "310,312,313,316,318,351,437,438,490,491,496,540,541,623,628,645,646,667,680,681,682,698,700,701,1820,1836,1839", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20348,20472,20530,20708,20793,22503,27971,28036,33391,33457,33858,36759,36811,42475,42788,43759,43809,44966,45755,45801,45843,46711,46806,46842,117957,118937,119048", "endLines": "310,312,313,316,318,351,437,438,490,491,496,540,541,623,628,645,646,667,680,681,682,698,700,701,1822,1838,1842", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "20417,20525,20580,20754,20843,22551,28031,28085,33452,33553,33911,36806,36866,42532,42837,43804,43858,45007,45796,45838,45878,46753,46837,46927,118064,119043,119238"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "92", "endOffsets": "144"}, "to": {"startLines": "2166", "startColumns": "4", "startOffsets": "143905", "endColumns": "91", "endOffsets": "143992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c36e1f051d57c59ccd6930c68000c21a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "346", "startColumns": "4", "startOffsets": "22232", "endColumns": "42", "endOffsets": "22270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25ad31b9d5ee235be384e44c485c1169\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2445,2461,2467,3625,3641", "startColumns": "4,4,4,4,4", "startOffsets": "159823,160248,160426,200962,201373", "endLines": "2460,2466,2476,3640,3644", "endColumns": "24,24,24,24,24", "endOffsets": "160243,160421,160705,201368,201495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\167b5a10bb8d261a1d1c21747e97c5f2\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "99,259,260,261,262,2119,2121,2122,2127,2129", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6410,17355,17408,17461,17514,140447,140623,140745,141007,141202", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "6494,17403,17456,17509,17562,140508,140740,140801,141068,141264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\14d03a95cc07898beae69a352dd60254\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "349", "startColumns": "4", "startOffsets": "22389", "endColumns": "49", "endOffsets": "22434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,666,717,718,719,720,721,722,730,731,735,739,750,755,761,768,772,776,781,785,789,793,797,801,805,811,815,821,825,831,835,840,844,847,851,857,861,867,871,877,880,884,888,892,896,900,901,902,903,906,909,912,915,919,920,921,922,923,926,928,930,932,937,938,942,948,952,953,955,967,968,972,978,982,983,984,988,1015,1019,1020,1024,1052,1224,1250,1421,1447,1478,1486,1492,1508,1530,1535,1540,1550,1559,1568,1572,1579,1598,1605,1606,1615,1618,1621,1625,1629,1633,1636,1637,1642,1647,1657,1662,1669,1675,1676,1679,1683,1688,1690,1692,1695,1698,1700,1704,1707,1714,1717,1720,1724,1726,1730,1732,1734,1736,1740,1748,1756,1768,1774,1783,1786,1797,1800,1801,1806,1807,1843,1912,1982,1983,1993,2002,2003,2005,2009,2012,2015,2018,2021,2024,2027,2030,2034,2037,2040,2043,2047,2050,2054,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2080,2082,2083,2084,2085,2086,2087,2088,2089,2091,2092,2094,2095,2097,2099,2100,2102,2103,2104,2105,2106,2107,2109,2110,2111,2112,2113,2130,2132,2134,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2150,2151,2152,2153,2154,2155,2156,2158,2162,2167,2168,2169,2170,2171,2172,2176,2177,2178,2179,2181,2183,2185,2187,2189,2190,2191,2192,2194,2196,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2212,2213,2214,2215,2217,2219,2220,2222,2223,2225,2227,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2242,2243,2244,2245,2247,2248,2249,2250,2251,2253,2255,2257,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2324,2399,2402,2405,2408,2422,2435,2477,2480,2509,2536,2545,2609,2972,3010,3048,3166,3286,3310,3316,3335,3356,3480,3539,3545,3553,3559,3613,3645,3711,3731,3786,3798,3824", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "263,318,515,931,972,1027,1089,1153,1223,1284,1359,1435,1512,1750,1835,1917,1993,2111,2188,2266,2372,2478,2557,2886,2943,4925,4999,5074,5139,5205,5265,5326,5398,5471,5538,5606,5665,5724,5783,5842,5901,5955,6009,6062,6116,6170,6224,6499,6573,6652,6725,6799,6870,6942,7014,7228,7285,7343,7416,7490,7564,7639,7711,7784,7854,8015,8075,8178,8247,8316,8386,8460,8536,8600,8677,8753,8830,8895,8964,9041,9116,9185,9253,9330,9396,9457,9554,9619,9688,9787,9858,9917,9975,10032,10091,10155,10226,10298,10370,10442,10514,10581,10649,10717,10776,10839,10903,10993,11084,11144,11210,11277,11343,11413,11477,11530,11597,11658,11725,11838,11896,11959,12024,12089,12164,12237,12309,12353,12400,12446,12495,12556,12617,12678,12740,12804,12868,12932,12997,13060,13120,13181,13247,13306,13366,13428,13499,13559,15593,15679,15766,15856,15943,16031,16113,16196,16286,17567,17619,17677,17722,17788,17852,17909,17966,20143,20200,20248,20297,20759,21092,21139,21295,22200,22556,22620,22682,22742,23010,23084,23154,23232,23286,23356,23441,23489,23535,23596,23659,23725,23789,23860,23923,23988,24052,24113,24174,24226,24299,24373,24442,24517,24591,24665,24806,44913,47785,47863,47953,48041,48137,48227,48809,48898,49145,49426,50092,50377,50770,51247,51469,51691,51967,52194,52424,52654,52884,53114,53341,53760,53986,54411,54641,55069,55288,55571,55779,55910,56137,56563,56788,57215,57436,57861,57981,58257,58558,58882,59173,59487,59624,59755,59860,60102,60269,60473,60681,60952,61064,61176,61281,61398,61612,61758,61898,61984,62332,62420,62666,63084,63333,63415,63513,64170,64270,64522,64946,65201,65295,65384,65621,67645,67887,67989,68242,70398,81079,82595,93290,94818,96575,97201,97621,98882,100147,100403,100639,101186,101680,102285,102483,103063,104431,104806,104924,105462,105619,105815,106088,106344,106514,106655,106719,107084,107451,108127,108391,108729,109082,109176,109362,109668,109930,110055,110182,110421,110632,110751,110944,111121,111576,111757,111879,112138,112251,112438,112540,112647,112776,113051,113559,114055,114932,115226,115796,115945,116677,116849,116933,117269,117361,119243,124474,129845,129907,130485,131069,131160,131273,131502,131662,131814,131985,132151,132320,132487,132650,132893,133063,133236,133407,133681,133880,134085,134415,134499,134595,134691,134789,134889,134991,135093,135195,135297,135399,135499,135595,135707,135836,135959,136090,136221,136319,136433,136527,136667,136801,136897,137009,137109,137225,137321,137433,137533,137673,137809,137973,138103,138261,138411,138552,138696,138831,138943,139093,139221,139349,139485,139617,139747,139877,139989,141269,141415,141559,141697,141763,141853,141929,142033,142123,142225,142333,142441,142541,142621,142713,142811,142921,142973,143051,143157,143249,143353,143463,143585,143748,143997,144077,144177,144267,144377,144467,144708,144802,144908,145000,145100,145212,145326,145442,145558,145652,145766,145878,145980,146100,146222,146304,146408,146528,146654,146752,146846,146934,147046,147162,147284,147396,147571,147687,147773,147865,147977,148101,148168,148294,148362,148490,148634,148762,148831,148926,149041,149154,149253,149362,149473,149584,149685,149790,149890,150020,150111,150234,150328,150440,150526,150630,150726,150814,150932,151036,151140,151266,151354,151462,151562,151652,151762,151846,151948,152032,152086,152150,152256,152342,152452,152536,155479,158095,158213,158328,158408,158769,159306,160710,160788,162132,163493,163881,166724,176777,178039,179710,185794,189946,190697,190959,191474,191853,196131,198412,198641,198935,199150,200650,201500,204526,205270,207401,207741,209052", "endLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,666,717,718,719,720,721,729,730,734,738,742,754,760,767,771,775,780,784,788,792,796,800,804,810,814,820,824,830,834,839,843,846,850,856,860,866,870,876,879,883,887,891,895,899,900,901,902,905,908,911,914,918,919,920,921,922,925,927,929,931,936,937,941,947,951,952,954,966,967,971,977,981,982,983,987,1014,1018,1019,1023,1051,1223,1249,1420,1446,1477,1485,1491,1507,1529,1534,1539,1549,1558,1567,1571,1578,1597,1604,1605,1614,1617,1620,1624,1628,1632,1635,1636,1641,1646,1656,1661,1668,1674,1675,1678,1682,1687,1689,1691,1694,1697,1699,1703,1706,1713,1716,1719,1723,1725,1729,1731,1733,1735,1739,1747,1755,1767,1773,1782,1785,1796,1799,1800,1805,1806,1811,1911,1981,1982,1992,2001,2002,2004,2008,2011,2014,2017,2020,2023,2026,2029,2033,2036,2039,2042,2046,2049,2053,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2079,2081,2082,2083,2084,2085,2086,2087,2088,2090,2091,2093,2094,2096,2098,2099,2101,2102,2103,2104,2105,2106,2108,2109,2110,2111,2112,2113,2131,2133,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2149,2150,2151,2152,2153,2154,2155,2157,2161,2165,2167,2168,2169,2170,2171,2175,2176,2177,2178,2180,2182,2184,2186,2188,2189,2190,2191,2193,2195,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2211,2212,2213,2214,2216,2218,2219,2221,2222,2224,2226,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2241,2242,2243,2244,2246,2247,2248,2249,2250,2252,2254,2256,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2398,2401,2404,2407,2421,2427,2444,2479,2508,2535,2544,2608,2971,2975,3037,3075,3183,3309,3315,3321,3355,3479,3499,3544,3548,3558,3593,3624,3710,3730,3785,3797,3823,3830", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "313,358,559,967,1022,1084,1148,1218,1279,1354,1430,1507,1585,1830,1912,1988,2064,2183,2261,2367,2473,2552,2632,2938,2996,4994,5069,5134,5200,5260,5321,5393,5466,5533,5601,5660,5719,5778,5837,5896,5950,6004,6057,6111,6165,6219,6273,6568,6647,6720,6794,6865,6937,7009,7082,7280,7338,7411,7485,7559,7634,7706,7779,7849,7920,8070,8131,8242,8311,8381,8455,8531,8595,8672,8748,8825,8890,8959,9036,9111,9180,9248,9325,9391,9452,9549,9614,9683,9782,9853,9912,9970,10027,10086,10150,10221,10293,10365,10437,10509,10576,10644,10712,10771,10834,10898,10988,11079,11139,11205,11272,11338,11408,11472,11525,11592,11653,11720,11833,11891,11954,12019,12084,12159,12232,12304,12348,12395,12441,12490,12551,12612,12673,12735,12799,12863,12927,12992,13055,13115,13176,13242,13301,13361,13423,13494,13554,13622,15674,15761,15851,15938,16026,16108,16191,16281,16372,17614,17672,17717,17783,17847,17904,17961,18015,20195,20243,20292,20343,20788,21134,21183,21336,22227,22615,22677,22737,22794,23079,23149,23227,23281,23351,23436,23484,23530,23591,23654,23720,23784,23855,23918,23983,24047,24108,24169,24221,24294,24368,24437,24512,24586,24660,24801,24871,44961,47858,47948,48036,48132,48222,48804,48893,49140,49421,49673,50372,50765,51242,51464,51686,51962,52189,52419,52649,52879,53109,53336,53755,53981,54406,54636,55064,55283,55566,55774,55905,56132,56558,56783,57210,57431,57856,57976,58252,58553,58877,59168,59482,59619,59750,59855,60097,60264,60468,60676,60947,61059,61171,61276,61393,61607,61753,61893,61979,62327,62415,62661,63079,63328,63410,63508,64165,64265,64517,64941,65196,65290,65379,65616,67640,67882,67984,68237,70393,81074,82590,93285,94813,96570,97196,97616,98877,100142,100398,100634,101181,101675,102280,102478,103058,104426,104801,104919,105457,105614,105810,106083,106339,106509,106650,106714,107079,107446,108122,108386,108724,109077,109171,109357,109663,109925,110050,110177,110416,110627,110746,110939,111116,111571,111752,111874,112133,112246,112433,112535,112642,112771,113046,113554,114050,114927,115221,115791,115940,116672,116844,116928,117264,117356,117634,124469,129840,129902,130480,131064,131155,131268,131497,131657,131809,131980,132146,132315,132482,132645,132888,133058,133231,133402,133676,133875,134080,134410,134494,134590,134686,134784,134884,134986,135088,135190,135292,135394,135494,135590,135702,135831,135954,136085,136216,136314,136428,136522,136662,136796,136892,137004,137104,137220,137316,137428,137528,137668,137804,137968,138098,138256,138406,138547,138691,138826,138938,139088,139216,139344,139480,139612,139742,139872,139984,140124,141410,141554,141692,141758,141848,141924,142028,142118,142220,142328,142436,142536,142616,142708,142806,142916,142968,143046,143152,143244,143348,143458,143580,143743,143900,144072,144172,144262,144372,144462,144703,144797,144903,144995,145095,145207,145321,145437,145553,145647,145761,145873,145975,146095,146217,146299,146403,146523,146649,146747,146841,146929,147041,147157,147279,147391,147566,147682,147768,147860,147972,148096,148163,148289,148357,148485,148629,148757,148826,148921,149036,149149,149248,149357,149468,149579,149680,149785,149885,150015,150106,150229,150323,150435,150521,150625,150721,150809,150927,151031,151135,151261,151349,151457,151557,151647,151757,151841,151943,152027,152081,152145,152251,152337,152447,152531,152651,158090,158208,158323,158403,158764,158997,159818,160783,162127,163488,163876,166719,176772,176907,179404,181062,186361,190692,190954,191154,191848,196126,196732,198636,198787,199145,200228,200957,204521,205265,207396,207736,209047,209250"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,727", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "194,276,380,489,609,722,804"}, "to": {"startLines": "492,518,522,523,524,525,644", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "33558,35231,35490,35594,35703,35823,43677", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "33697,35308,35589,35698,35818,35931,43754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\02b89f0ee9be1e0a15cf2905800b0f25\\transformed\\facebook-common-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,49,54,60,71,82,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,202,290,370,449,513,590,665,732,814,895,968,1023,1092,1171,1235,1309,1382,1455,1527,1600,1673,1738,1807,4164,4458,4808,5429,6060,6239", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,53,59,70,81,84,112", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "197,285,365,444,508,585,660,727,809,890,963,1018,1087,1166,1230,1304,1377,1450,1522,1595,1668,1733,1802,1867,4453,4803,5424,6055,6234,7468"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2279,2284,2290,2301,2312,3831", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3381,3433,3521,3601,3680,3744,3821,3896,3963,4045,4126,4199,13940,14009,14088,14222,14296,14369,14442,14514,14587,14660,14725,14794,152940,153234,153584,154205,154836,209255", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2283,2289,2300,2311,2314,3858", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "3428,3516,3596,3675,3739,3816,3891,3958,4040,4121,4194,4249,14004,14083,14147,14291,14364,14437,14509,14582,14655,14720,14789,14854,153229,153579,154200,154831,155010,210484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1bf99d2dddf6b3f731ce95e33fee49d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "319,323", "startColumns": "4,4", "startOffsets": "20848,21025", "endColumns": "53,66", "endOffsets": "20897,21087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\856e9279244c37324ec12dc8b216b897\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "326,347", "startColumns": "4,4", "startOffsets": "21188,22275", "endColumns": "41,59", "endOffsets": "21225,22330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "43,44,45,46,207,208,482,503,504,505", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2637,2695,2761,2824,13627,13698,32837,34255,34322,34401", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2690,2756,2819,2881,13693,13765,32900,34317,34396,34465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b616a44a83a35cc1ff94b844e1245adb\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "20422", "endColumns": "49", "endOffsets": "20467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,38,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,175,257,340,421,558,673,791,886,951,1019,1077,1149,1221,1318,1409,1483,1557,1670,1771,1834,2054,2219,2296,2378,2503,2589,2713,2799,3177,3880", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,37,50,59", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "170,252,335,416,553,668,786,881,946,1014,1072,1144,1216,1313,1404,1478,1552,1665,1766,1829,2049,2214,2291,2373,2498,2584,2708,2794,3172,3875,4296"}, "to": {"startLines": "215,225,226,227,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,2315,2316,3859,3872", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14152,14859,14941,15024,28090,28227,28342,28460,28555,28620,28688,28746,28818,28890,28987,29078,29152,29226,29339,29440,29503,29723,29888,29965,30047,30172,30258,155015,155101,210489,211192", "endLines": "215,225,226,227,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,2315,2323,3871,3880", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "14217,14936,15019,15100,28222,28337,28455,28550,28615,28683,28741,28813,28885,28982,29073,29147,29221,29334,29435,29498,29718,29883,29960,30042,30167,30253,30377,155096,155474,211187,211608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e520bbb7f153fcbb4e273d864eff355d\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "22335", "endColumns": "53", "endOffsets": "22384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21c308ad6a5cb3ffbae3557cea5538ff\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "6,12,3549", "startColumns": "4,4,4", "startOffsets": "363,624,198792", "endLines": "9,19,3552", "endColumns": "11,11,24", "endOffsets": "510,926,198930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "703,704", "startColumns": "4,4", "startOffsets": "46994,47050", "endColumns": "55,54", "endOffsets": "47045,47100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b341f2bf9e26a9b9b6d9ac1cb5423684\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3500,3513,3519,3525,3534", "startColumns": "4,4,4,4,4", "startOffsets": "196737,197376,197620,197867,198230", "endLines": "3512,3518,3524,3527,3538", "endColumns": "24,24,24,24,24", "endOffsets": "197371,197615,197862,197995,198407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5ccc5722ca8455978d22b35e0684d747\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "314,327,350,3238,3243", "startColumns": "4,4,4,4,4", "startOffsets": "20585,21230,22439,188778,188948", "endLines": "314,327,350,3242,3246", "endColumns": "56,64,63,24,24", "endOffsets": "20637,21290,22498,188943,189092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "357,559,560,561,562,563,564,565,566,567,568,571,572,573,574,575,576,577,578,579,580,581,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,1823,1833", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22867,37894,37982,38068,38149,38233,38302,38367,38450,38556,38642,38762,38816,38885,38946,39015,39104,39199,39273,39370,39463,39561,39710,39801,39889,39985,40083,40147,40215,40302,40396,40463,40535,40607,40708,40817,40893,40962,41010,41076,41140,41214,41271,41328,41400,41450,41504,41575,41646,41716,41785,41843,41919,41990,42064,42150,42200,42270,118069,118784", "endLines": "357,559,560,561,562,563,564,565,566,567,570,571,572,573,574,575,576,577,578,579,580,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,1832,1835", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "22935,37977,38063,38144,38228,38297,38362,38445,38551,38637,38757,38811,38880,38941,39010,39099,39194,39268,39365,39458,39556,39705,39796,39884,39980,40078,40142,40210,40297,40391,40458,40530,40602,40703,40812,40888,40957,41005,41071,41135,41209,41266,41323,41395,41445,41499,41570,41641,41711,41780,41838,41914,41985,42059,42145,42195,42265,42330,118779,118932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "67,68,69,70,71,72,73,74,462,463,464,465,466,467,468,469,471,472,473,474,475,476,477,479,480,3322,3594", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4254,4344,4424,4514,4604,4684,4765,4845,30382,30487,30668,30793,30900,31080,31203,31319,31589,31777,31882,32063,32188,32363,32511,32634,32696,191159,200233", "endLines": "67,68,69,70,71,72,73,74,462,463,464,465,466,467,468,469,471,472,473,474,475,476,477,479,480,3334,3612", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4339,4419,4509,4599,4679,4760,4840,4920,30482,30663,30788,30895,31075,31198,31314,31417,31772,31877,32058,32183,32358,32506,32569,32691,32770,191469,200645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5092f4b0fc3229c87d6dc9ffd8fb80cd\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "20642", "endColumns": "65", "endOffsets": "20703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2cda5a1f9d8c110805098b8b6936f0e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "412", "startColumns": "4", "startOffsets": "26401", "endColumns": "82", "endOffsets": "26479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0632335579b593b6b56131d5dd003758\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "2,51,52,53,54,209,210,211,743,1812,1814,1817,2982", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3132,3193,3255,3317,13770,13829,13886,49678,117639,117703,117829,177115", "endLines": "2,51,52,53,54,209,210,211,749,1813,1816,1819,3009", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "197,3188,3250,3312,3376,13824,13881,13935,50087,117698,117824,117952,178034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "356,470", "startColumns": "4,4", "startOffsets": "22799,31422", "endColumns": "67,166", "endOffsets": "22862,31584"}}]}]}