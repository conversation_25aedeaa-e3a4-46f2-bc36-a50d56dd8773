package com.healthyproducts.app.di;

/**
 * A<PERSON> istekleri için gerekli servisleri sağlayan Hilt modülü
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007J\b\u0010\u000b\u001a\u00020\fH\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007J\u0010\u0010\u0011\u001a\u00020\b2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007J\b\u0010\u0012\u001a\u00020\u0004H\u0007J\b\u0010\u0013\u001a\u00020\u0004H\u0007J\b\u0010\u0014\u001a\u00020\nH\u0007J\b\u0010\u0015\u001a\u00020\u0016H\u0007J\u0018\u0010\u0017\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\f2\u0006\u0010\u0019\u001a\u00020\u0016H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/healthyproducts/app/di/NetworkModule;", "", "()V", "DEEPSEEK_BASE_URL", "", "provideAiService", "Lcom/healthyproducts/app/data/api/AiService;", "deepSeekApiService", "Lcom/healthyproducts/app/data/api/DeepSeekApiService;", "geminiApiService", "Lcom/healthyproducts/app/data/api/GeminiApiService;", "provideAuthInterceptor", "Lokhttp3/Interceptor;", "provideDeepSeekApi", "Lcom/healthyproducts/app/data/api/DeepSeekApi;", "okHttpClient", "Lokhttp3/OkHttpClient;", "provideDeepSeekApiService", "provideDeepSeekAuthorizationHeader", "provideDeepSeekBaseUrl", "provideGeminiApiService", "provideLoggingInterceptor", "Lokhttp3/logging/HttpLoggingInterceptor;", "provideOkHttpClient", "authInterceptor", "loggingInterceptor", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class NetworkModule {
    
    /**
     * DeepSeek API için temel URL
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEEPSEEK_BASE_URL = "https://api.deepseek.com/";
    @org.jetbrains.annotations.NotNull()
    public static final com.healthyproducts.app.di.NetworkModule INSTANCE = null;
    
    private NetworkModule() {
        super();
    }
    
    /**
     * OkHttpClient için bir interceptor sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.Interceptor provideAuthInterceptor() {
        return null;
    }
    
    /**
     * Loglama interceptor'ı sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.logging.HttpLoggingInterceptor provideLoggingInterceptor() {
        return null;
    }
    
    /**
     * OkHttpClient sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.OkHttpClient provideOkHttpClient(@org.jetbrains.annotations.NotNull()
    okhttp3.Interceptor authInterceptor, @org.jetbrains.annotations.NotNull()
    okhttp3.logging.HttpLoggingInterceptor loggingInterceptor) {
        return null;
    }
    
    /**
     * DeepSeek API için Retrofit sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.api.DeepSeekApi provideDeepSeekApi(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient) {
        return null;
    }
    
    /**
     * DeepSeek API Service için Retrofit sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.api.DeepSeekApiService provideDeepSeekApiService(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient) {
        return null;
    }
    
    /**
     * DeepSeek API Service için BASE_URL sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String provideDeepSeekBaseUrl() {
        return null;
    }
    
    /**
     * DeepSeek API için yetkilendirme başlığını sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String provideDeepSeekAuthorizationHeader() {
        return null;
    }
    
    /**
     * Gemini API Service sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.api.GeminiApiService provideGeminiApiService() {
        return null;
    }
    
    /**
     * AI Service sağlar
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.api.AiService provideAiService(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.api.DeepSeekApiService deepSeekApiService, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.api.GeminiApiService geminiApiService) {
        return null;
    }
}