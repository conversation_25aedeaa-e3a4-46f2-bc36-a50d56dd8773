package com.healthyproducts.app.ui.screens.auth;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001a\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a$\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u00a8\u0006\n"}, d2 = {"LoginScreen", "", "navController", "Landroidx/navigation/NavController;", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "SocialLoginButtons", "onGoogleClick", "Lkotlin/Function0;", "onFacebookClick", "app_debug"})
public final class LoginScreenKt {
    
    /**
     * Giriş ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void LoginScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * Sosyal medya ile giriş butonları
     */
    @androidx.compose.runtime.Composable()
    public static final void SocialLoginButtons(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onGoogleClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onFacebookClick) {
    }
}