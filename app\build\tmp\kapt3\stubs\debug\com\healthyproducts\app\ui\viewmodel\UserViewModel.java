package com.healthyproducts.app.ui.viewmodel;

/**
 * Kullanıcı işlemlerini yöneten ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001:\u0001\u001eB\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0012\u001a\u00020\u0011J\u0016\u0010\u0013\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0015J\u000e\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u0015J\u0006\u0010\u0019\u001a\u00020\u0011J\"\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u0015J\u000e\u0010\u001c\u001a\u00020\u00112\u0006\u0010\u001d\u001a\u00020\u0007R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u001f"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "Landroidx/lifecycle/ViewModel;", "userRepository", "Lcom/healthyproducts/app/data/repository/UserRepository;", "(Lcom/healthyproducts/app/data/repository/UserRepository;)V", "_userPreferences", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/healthyproducts/app/model/UserPreferences;", "_userState", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState;", "userPreferences", "Lkotlinx/coroutines/flow/StateFlow;", "getUserPreferences", "()Lkotlinx/coroutines/flow/StateFlow;", "userState", "getUserState", "checkCurrentUser", "", "refreshUserPreferences", "signIn", "email", "", "password", "signInWithGoogle", "token", "signOut", "signUp", "name", "updateUserPreferences", "preferences", "UserState", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class UserViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.healthyproducts.app.data.repository.UserRepository userRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState> _userState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState> userState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.healthyproducts.app.model.UserPreferences> _userPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.model.UserPreferences> userPreferences = null;
    
    @javax.inject.Inject()
    public UserViewModel(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.repository.UserRepository userRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState> getUserState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.healthyproducts.app.model.UserPreferences> getUserPreferences() {
        return null;
    }
    
    /**
     * Kullanıcı tercihlerini zorla yenile
     */
    public final void refreshUserPreferences() {
    }
    
    /**
     * Mevcut kullanıcıyı kontrol etme
     */
    public final void checkCurrentUser() {
    }
    
    /**
     * E-posta ve şifre ile kayıt olma
     */
    public final void signUp(@org.jetbrains.annotations.NotNull()
    java.lang.String email, @org.jetbrains.annotations.NotNull()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.String name) {
    }
    
    /**
     * E-posta ve şifre ile giriş yapma
     */
    public final void signIn(@org.jetbrains.annotations.NotNull()
    java.lang.String email, @org.jetbrains.annotations.NotNull()
    java.lang.String password) {
    }
    
    /**
     * Google ile giriş yapma
     */
    public final void signInWithGoogle(@org.jetbrains.annotations.NotNull()
    java.lang.String token) {
    }
    
    /**
     * Çıkış yapma
     */
    public final void signOut() {
    }
    
    /**
     * Kullanıcı tercihlerini güncelleme
     */
    public final void updateUserPreferences(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.model.UserPreferences preferences) {
    }
    
    /**
     * Kullanıcı durumunu temsil eden sealed class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState;", "", "()V", "Error", "Loading", "LoggedIn", "LoggedOut", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$LoggedIn;", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$LoggedOut;", "app_debug"})
    public static abstract class UserState {
        
        private UserState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$Error;", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
        public static final class Error extends com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$Loading;", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState;", "()V", "app_debug"})
        public static final class Loading extends com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.Loading INSTANCE = null;
            
            private Loading() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$LoggedIn;", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState;", "user", "Lcom/healthyproducts/app/model/User;", "(Lcom/healthyproducts/app/model/User;)V", "getUser", "()Lcom/healthyproducts/app/model/User;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
        public static final class LoggedIn extends com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState {
            @org.jetbrains.annotations.NotNull()
            private final com.healthyproducts.app.model.User user = null;
            
            public LoggedIn(@org.jetbrains.annotations.NotNull()
            com.healthyproducts.app.model.User user) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.model.User getUser() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.model.User component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.LoggedIn copy(@org.jetbrains.annotations.NotNull()
            com.healthyproducts.app.model.User user) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState$LoggedOut;", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel$UserState;", "()V", "app_debug"})
        public static final class LoggedOut extends com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState {
            @org.jetbrains.annotations.NotNull()
            public static final com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState.LoggedOut INSTANCE = null;
            
            private LoggedOut() {
            }
        }
    }
}