package com.healthyproducts.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthyproducts.app.data.repository.UserRepository
import com.healthyproducts.app.model.User
import com.healthyproducts.app.model.UserPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Kullanıcı işlemlerini yöneten ViewModel
 */
@HiltViewModel
class UserViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    // Kullanıcı durumu
    private val _userState = MutableStateFlow<UserState>(UserState.Loading)
    val userState: StateFlow<UserState> = _userState.asStateFlow()

    // Kullanıcı tercihleri
    private val _userPreferences = MutableStateFlow<UserPreferences>(UserPreferences())
    val userPreferences: StateFlow<UserPreferences> = _userPreferences.asStateFlow()

    init {
        android.util.Log.d("UserViewModel", "Initializing with default preferences: aiModel=${_userPreferences.value.aiModel}")

        // Mevcut kullanıcıyı kontrol et
        checkCurrentUser()
    }

    /**
     * Mevcut kullanıcıyı kontrol etme
     */
    fun checkCurrentUser() {
        viewModelScope.launch {
            _userState.value = UserState.Loading

            val currentUser = userRepository.getCurrentUser()

            if (currentUser != null) {
                _userState.value = UserState.LoggedIn(currentUser)

                // Kullanıcı tercihlerini getir
                userRepository.getUserPreferences(currentUser.id)
                    .onSuccess { preferences ->
                        android.util.Log.d("UserViewModel", "Retrieved user preferences: language=${preferences.language}")
                        _userPreferences.value = preferences
                    }
            } else {
                _userState.value = UserState.LoggedOut
            }
        }
    }

    /**
     * E-posta ve şifre ile kayıt olma
     */
    fun signUp(email: String, password: String, name: String? = null) {
        viewModelScope.launch {
            _userState.value = UserState.Loading

            userRepository.signUp(email, password, name)
                .onSuccess { user ->
                    _userState.value = UserState.LoggedIn(user)
                }
                .onFailure { error ->
                    _userState.value = UserState.Error(error.message ?: "Kayıt olma hatası")
                }
        }
    }

    /**
     * E-posta ve şifre ile giriş yapma
     */
    fun signIn(email: String, password: String) {
        viewModelScope.launch {
            _userState.value = UserState.Loading

            userRepository.signIn(email, password)
                .onSuccess { user ->
                    _userState.value = UserState.LoggedIn(user)

                    // Kullanıcı tercihlerini getir
                    userRepository.getUserPreferences(user.id)
                        .onSuccess { preferences ->
                            _userPreferences.value = preferences
                        }
                }
                .onFailure { error ->
                    _userState.value = UserState.Error(error.message ?: "Giriş yapma hatası")
                }
        }
    }

    /**
     * Google ile giriş yapma
     */
    fun signInWithGoogle(token: String) {
        viewModelScope.launch {
            _userState.value = UserState.Loading

            userRepository.signInWithGoogle(token)
                .onSuccess { user ->
                    _userState.value = UserState.LoggedIn(user)

                    // Kullanıcı tercihlerini getir
                    userRepository.getUserPreferences(user.id)
                        .onSuccess { preferences ->
                            _userPreferences.value = preferences
                        }
                }
                .onFailure { error ->
                    _userState.value = UserState.Error(error.message ?: "Google ile giriş yapma hatası")
                }
        }
    }

    /**
     * Çıkış yapma
     */
    fun signOut() {
        viewModelScope.launch {
            userRepository.signOut()
                .onSuccess {
                    _userState.value = UserState.LoggedOut
                    _userPreferences.value = UserPreferences()
                }
                .onFailure { error ->
                    _userState.value = UserState.Error(error.message ?: "Çıkış yapma hatası")
                }
        }
    }

    /**
     * Kullanıcı tercihlerini güncelleme
     */
    fun updateUserPreferences(preferences: UserPreferences) {
        viewModelScope.launch {
            val currentState = _userState.value

            android.util.Log.d("UserViewModel", "Updating preferences: aiModel=${preferences.aiModel}")

            if (currentState is UserState.LoggedIn) {
                // Önce yerel state'i güncelle
                _userPreferences.value = preferences
                android.util.Log.d("UserViewModel", "Local state updated: aiModel=${_userPreferences.value.aiModel}")

                // Sonra Firebase'e kaydet
                userRepository.updateUserPreferences(currentState.user.id, preferences)
                    .onSuccess { updatedPreferences ->
                        // Firebase'den gelen güncellenmiş tercihleri kullan
                        // AI model değerini kontrol et ve koru
                        if (updatedPreferences.aiModel != preferences.aiModel) {
                            android.util.Log.w("UserViewModel", "AI model mismatch: expected=${preferences.aiModel}, got=${updatedPreferences.aiModel}")
                            // Yerel değeri koru
                            val correctedPreferences = updatedPreferences.copy(aiModel = preferences.aiModel)
                            _userPreferences.value = correctedPreferences
                            // Firebase'i tekrar güncelle
                            userRepository.updateUserPreferences(currentState.user.id, correctedPreferences)
                                .onSuccess {
                                    android.util.Log.d("UserViewModel", "Corrected AI model in Firebase: ${correctedPreferences.aiModel}")
                                }
                        } else {
                            _userPreferences.value = updatedPreferences
                            android.util.Log.d("UserViewModel", "Firebase preferences updated: aiModel=${_userPreferences.value.aiModel}")
                        }
                    }
                    .onFailure { error ->
                        android.util.Log.e("UserViewModel", "Failed to update preferences", error)
                    }
            } else {
                android.util.Log.w("UserViewModel", "Cannot update preferences: user not logged in")
            }
        }
    }

    /**
     * Kullanıcı durumunu temsil eden sealed class
     */
    sealed class UserState {
        object Loading : UserState()
        data class LoggedIn(val user: User) : UserState()
        object LoggedOut : UserState()
        data class Error(val message: String) : UserState()
    }
}
