package com.healthyproducts.app.data.model;

/**
 * G<PERSON>da sertifikasını temsil eden veri sınıfı
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\bF\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u00c7\u0001\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0004\u0012\b\b\u0002\u0010\b\u001a\u00020\u0004\u0012\b\b\u0002\u0010\t\u001a\u00020\u0004\u0012\b\b\u0002\u0010\n\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0004\u0012\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\f\u00a2\u0006\u0002\u0010\u0016J\t\u0010@\u001a\u00020\u0004H\u00c6\u0003J\t\u0010A\u001a\u00020\u0004H\u00c6\u0003J\t\u0010B\u001a\u00020\u0004H\u00c6\u0003J\u000f\u0010C\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\t\u0010D\u001a\u00020\u0004H\u00c6\u0003J\t\u0010E\u001a\u00020\u0004H\u00c6\u0003J\t\u0010F\u001a\u00020\u0004H\u00c6\u0003J\t\u0010G\u001a\u00020\u0004H\u00c6\u0003J\u000f\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\t\u0010I\u001a\u00020\u0004H\u00c6\u0003J\t\u0010J\u001a\u00020\u0004H\u00c6\u0003J\t\u0010K\u001a\u00020\u0004H\u00c6\u0003J\t\u0010L\u001a\u00020\u0004H\u00c6\u0003J\t\u0010M\u001a\u00020\u0004H\u00c6\u0003J\t\u0010N\u001a\u00020\u0004H\u00c6\u0003J\u000f\u0010O\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\u000f\u0010P\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0003J\u00cb\u0001\u0010Q\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00042\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\u00042\b\b\u0002\u0010\t\u001a\u00020\u00042\b\b\u0002\u0010\n\u001a\u00020\u00042\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\b\b\u0002\u0010\u000e\u001a\u00020\u00042\b\b\u0002\u0010\u000f\u001a\u00020\u00042\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\b\b\u0002\u0010\u0011\u001a\u00020\u00042\b\b\u0002\u0010\u0012\u001a\u00020\u00042\b\b\u0002\u0010\u0013\u001a\u00020\u00042\b\b\u0002\u0010\u0014\u001a\u00020\u00042\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u00c6\u0001J\u0013\u0010R\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010U\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010X\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010Y\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010Z\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010[\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010\\\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010]\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010^\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u000e\u0010_\u001a\u00020\u00042\u0006\u0010V\u001a\u00020WJ\u0014\u0010`\u001a\b\u0012\u0004\u0012\u00020\u00040\f2\u0006\u0010V\u001a\u00020WJ\t\u0010a\u001a\u00020bH\u00d6\u0001J\t\u0010c\u001a\u00020\u0004H\u00d6\u0001R\u001a\u0010\u0005\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u001a\u0010\n\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u0018\"\u0004\b\u001c\u0010\u001aR\u001a\u0010\t\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u0018\"\u0004\b\u001e\u0010\u001aR \u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\fX\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\u0016\u0010\u0003\u001a\u00020\u00048\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0018R \u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\fX\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010 \"\u0004\b%\u0010\"R \u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\fX\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010 \"\u0004\b\'\u0010\"R\u001a\u0010\u0007\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010\u0018\"\u0004\b)\u0010\u001aR\u001a\u0010\u0006\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b*\u0010\u0018\"\u0004\b+\u0010\u001aR\u001a\u0010\u0014\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010\u0018\"\u0004\b-\u0010\u001aR\u001a\u0010\u0013\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010\u0018\"\u0004\b/\u0010\u001aR \u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\fX\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u0010 \"\u0004\b1\u0010\"R\u0017\u00102\u001a\b\u0012\u0004\u0012\u00020\u00040\f8F\u00a2\u0006\u0006\u001a\u0004\b3\u0010 R\u001a\u0010\u0012\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u0010\u0018\"\u0004\b5\u0010\u001aR\u001a\u0010\u0011\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b6\u0010\u0018\"\u0004\b7\u0010\u001aR\u001a\u0010\b\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b8\u0010\u0018\"\u0004\b9\u0010\u001aR\u001a\u0010\u000f\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b:\u0010\u0018\"\u0004\b;\u0010\u001aR\u001a\u0010\u000e\u001a\u00020\u0004X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b<\u0010\u0018\"\u0004\b=\u0010\u001aR\u0017\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00040\f8F\u00a2\u0006\u0006\u001a\u0004\b?\u0010 \u00a8\u0006d"}, d2 = {"Lcom/healthyproducts/app/data/model/FoodCertificate;", "", "()V", "id", "", "certificateId", "nameTr", "nameEn", "symbol", "descriptionTr", "descriptionEn", "issuerTr", "", "issuerEn", "validForTr", "validForEn", "region", "riskIfMissingTr", "riskIfMissingEn", "notesTr", "notesEn", "functionalType", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V", "getCertificateId", "()Ljava/lang/String;", "setCertificateId", "(Ljava/lang/String;)V", "getDescriptionEn", "setDescriptionEn", "getDescriptionTr", "setDescriptionTr", "getFunctionalType", "()Ljava/util/List;", "setFunctionalType", "(Ljava/util/List;)V", "getId", "getIssuerEn", "setIssuerEn", "getIssuerTr", "setIssuerTr", "getNameEn", "setNameEn", "getNameTr", "setNameTr", "getNotesEn", "setNotesEn", "getNotesTr", "setNotesTr", "getRegion", "setRegion", "religiousStatuses", "getReligiousStatuses", "getRiskIfMissingEn", "setRiskIfMissingEn", "getRiskIfMissingTr", "setRiskIfMissingTr", "getSymbol", "setSymbol", "getValidForEn", "setValidForEn", "getValidForTr", "setValidForTr", "veganStatuses", "getVeganStatuses", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "getAppliesTo", "language", "Lcom/healthyproducts/app/data/model/SupportedLanguage;", "getCertificationCriteria", "getCertificationProcess", "getCertificationValidity", "getCertifyingBody", "getDescription", "getName", "getNotes", "getRiskIfMissing", "getStandards", "hashCode", "", "toString", "app_debug"})
public final class FoodCertificate {
    @com.google.firebase.firestore.DocumentId()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String certificateId;
    @com.google.gson.annotations.SerializedName(value = "name_tr")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameTr;
    @com.google.gson.annotations.SerializedName(value = "name_en")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String nameEn;
    @com.google.gson.annotations.SerializedName(value = "symbol")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String symbol;
    @com.google.gson.annotations.SerializedName(value = "description_tr")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionTr;
    @com.google.gson.annotations.SerializedName(value = "description_en")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String descriptionEn;
    @com.google.gson.annotations.SerializedName(value = "issuer_tr")
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> issuerTr;
    @com.google.gson.annotations.SerializedName(value = "issuer_en")
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> issuerEn;
    @com.google.gson.annotations.SerializedName(value = "valid_for_tr")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String validForTr;
    @com.google.gson.annotations.SerializedName(value = "valid_for_en")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String validForEn;
    @com.google.gson.annotations.SerializedName(value = "region")
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> region;
    @com.google.gson.annotations.SerializedName(value = "risk_if_missing_tr")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String riskIfMissingTr;
    @com.google.gson.annotations.SerializedName(value = "risk_if_missing_en")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String riskIfMissingEn;
    @com.google.gson.annotations.SerializedName(value = "notes_tr")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesTr;
    @com.google.gson.annotations.SerializedName(value = "notes_en")
    @org.jetbrains.annotations.NotNull()
    private java.lang.String notesEn;
    @com.google.gson.annotations.SerializedName(value = "functional_type")
    @org.jetbrains.annotations.NotNull()
    private java.util.List<java.lang.String> functionalType;
    
    public FoodCertificate(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String certificateId, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> issuerTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> issuerEn, @org.jetbrains.annotations.NotNull()
    java.lang.String validForTr, @org.jetbrains.annotations.NotNull()
    java.lang.String validForEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> region, @org.jetbrains.annotations.NotNull()
    java.lang.String riskIfMissingTr, @org.jetbrains.annotations.NotNull()
    java.lang.String riskIfMissingEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCertificateId() {
        return null;
    }
    
    public final void setCertificateId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_tr")
    public final void setNameTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNameEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "name_en")
    public final void setNameEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSymbol() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "symbol")
    public final void setSymbol(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_tr")
    public final void setDescriptionTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescriptionEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "description_en")
    public final void setDescriptionEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "issuer_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getIssuerTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "issuer_tr")
    public final void setIssuerTr(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "issuer_en")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getIssuerEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "issuer_en")
    public final void setIssuerEn(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "valid_for_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValidForTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "valid_for_tr")
    public final void setValidForTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "valid_for_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValidForEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "valid_for_en")
    public final void setValidForEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "region")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRegion() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "region")
    public final void setRegion(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_if_missing_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRiskIfMissingTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_if_missing_tr")
    public final void setRiskIfMissingTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_if_missing_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRiskIfMissingEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "risk_if_missing_en")
    public final void setRiskIfMissingEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesTr() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_tr")
    public final void setNotesTr(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotesEn() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "notes_en")
    public final void setNotesEn(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getFunctionalType() {
        return null;
    }
    
    @com.google.firebase.firestore.PropertyName(value = "functional_type")
    public final void setFunctionalType(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> p0) {
    }
    
    public FoodCertificate() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getStandards(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAppliesTo(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRiskIfMissing(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNotes(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCertifyingBody(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCertificationCriteria(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCertificationProcess(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCertificationValidity(@org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.data.model.SupportedLanguage language) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getVeganStatuses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getReligiousStatuses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component17() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.healthyproducts.app.data.model.FoodCertificate copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String certificateId, @org.jetbrains.annotations.NotNull()
    java.lang.String nameTr, @org.jetbrains.annotations.NotNull()
    java.lang.String nameEn, @org.jetbrains.annotations.NotNull()
    java.lang.String symbol, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionTr, @org.jetbrains.annotations.NotNull()
    java.lang.String descriptionEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> issuerTr, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> issuerEn, @org.jetbrains.annotations.NotNull()
    java.lang.String validForTr, @org.jetbrains.annotations.NotNull()
    java.lang.String validForEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> region, @org.jetbrains.annotations.NotNull()
    java.lang.String riskIfMissingTr, @org.jetbrains.annotations.NotNull()
    java.lang.String riskIfMissingEn, @org.jetbrains.annotations.NotNull()
    java.lang.String notesTr, @org.jetbrains.annotations.NotNull()
    java.lang.String notesEn, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> functionalType) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}