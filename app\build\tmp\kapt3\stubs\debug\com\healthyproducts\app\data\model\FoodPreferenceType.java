package com.healthyproducts.app.data.model;

/**
 * <PERSON><PERSON><PERSON><PERSON>ının gıda tercihleri türleri
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/healthyproducts/app/data/model/FoodPreferenceType;", "", "value", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "ALLERGEN", "FAT", "SUGAR", "CERTIFICATE", "PRESERVATIVE", "app_debug"})
public enum FoodPreferenceType {
    /*public static final*/ ALLERGEN /* = new ALLERGEN(null) */,
    /*public static final*/ FAT /* = new FAT(null) */,
    /*public static final*/ SUGAR /* = new SUGAR(null) */,
    /*public static final*/ CERTIFICATE /* = new CERTIFICATE(null) */,
    /*public static final*/ PRESERVATIVE /* = new PRESERVATIVE(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String value = null;
    
    FoodPreferenceType(java.lang.String value) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValue() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.healthyproducts.app.data.model.FoodPreferenceType> getEntries() {
        return null;
    }
}