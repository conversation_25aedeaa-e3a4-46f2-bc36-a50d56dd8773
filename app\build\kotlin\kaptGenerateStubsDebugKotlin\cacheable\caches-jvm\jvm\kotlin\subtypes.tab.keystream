android.app.Application#androidx.activity.ComponentActivity*com.healthyproducts.app.data.api.AiServicekotlin.Enum2kotlinx.serialization.internal.GeneratedSerializer)com.healthyproducts.app.navigation.Screenandroidx.lifecycle.ViewModel5com.healthyproducts.app.ui.screens.admin.UploadStatusCcom.healthyproducts.app.ui.viewmodel.AdditiveViewModel.ImportStatusFcom.healthyproducts.app.ui.viewmodel.FavoritesViewModel.FavoritesState3com.healthyproducts.app.ui.viewmodel.AllergensState.com.healthyproducts.app.ui.viewmodel.FatsState0com.healthyproducts.app.ui.viewmodel.SugarsState6com.healthyproducts.app.ui.viewmodel.CertificatesState1com.healthyproducts.app.ui.viewmodel.ImportStatus2com.healthyproducts.app.ui.viewmodel.AnalysisState7com.healthyproducts.app.ui.viewmodel.PreservativesState6com.healthyproducts.app.ui.viewmodel.PreservativeState=com.healthyproducts.app.ui.viewmodel.UserFoodPreferencesState#androidx.lifecycle.AndroidViewModelJcom.healthyproducts.app.ui.viewmodel.ScanHistoryViewModel.ScanHistoryState<com.healthyproducts.app.ui.viewmodel.ScanViewModel.ScanState?com.healthyproducts.app.ui.viewmodel.ScanViewModel.OcrScanStateBcom.healthyproducts.app.ui.viewmodel.ScanViewModel.CorrectionState<com.healthyproducts.app.ui.viewmodel.UserViewModel.UserState+androidx.camera.core.ImageAnalysis.Analyzer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             