{"logs": [{"outputFile": "com.healthyproducts.app-mergeDebugResources-81:/values/values.xml", "map": [{"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "67,109,248,213,217,214,218,216,215,263,262,45,114,111,113,108,115,118,117,112,120,119,116,107,241,65,66,143,139,242,238,237,104,236,194,1,207,102,255,254,20,232,22,94,191,146,40,245,170,171,172,169,264,163,267,84,103,199,81,234,230,226,219,59,130,134,129,133,95,148,153,96,110,28,79,192,99,144,140,6,17,204,203,142,174,173,31,198,147,167,166,168,23,41,43,165,240,151,4,270,269,177,209,182,178,180,181,179,183,195,39,30,35,235,29,27,156,58,126,123,125,124,98,76,221,85,78,197,14,82,233,225,200,16,162,161,26,21,101,149,80,196,223,222,188,187,186,244,68,251,38,256,250,257,253,252,7,247,229,11,77,86,154,46,193,100,208,152,93,220,249,5,258,12,83,13,25,24,97,210,265,231,8,73,71,72,60,63,62,64,61,34,33,32,49,50,54,159,160,53,158,51,55,52,157,145,141,243,246,44,189,266,69,190,136,164,135,131,132,268,150,259,224,239,42,155,70,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3370,5166,12875,10666,10930,10734,11004,10854,10792,13727,13663,2267,5449,5276,5388,5106,5504,5695,5630,5332,5829,5760,5567,5058,12467,3261,3307,6938,6746,12523,12288,12225,4990,12150,9723,16,10400,4912,13313,13228,777,11818,873,4587,9561,7102,1963,12693,8463,8537,8609,8403,13775,8044,13947,4170,4950,9995,4016,12029,11696,11548,11064,2859,6223,6535,6158,6460,4629,7230,7472,4671,5220,1310,3930,9611,4798,6998,6794,178,695,10227,10164,6874,8747,8683,1552,9935,7168,8289,8226,8347,955,2017,2125,8170,12403,7364,102,14155,14078,8832,10510,9148,8894,9002,9072,8946,9216,9773,1911,1480,1740,12091,1407,1236,7664,2813,6070,5901,6007,5954,4751,3802,11188,4226,3888,9887,512,4078,11959,11484,10055,638,7988,7948,1147,825,4878,7282,3970,9839,11343,11266,9419,9351,9295,12629,3410,13035,1851,13381,12981,13449,13160,13091,226,12807,11646,342,3842,4290,7534,2329,9679,4838,10450,7422,4549,11126,12927,140,13538,396,4116,450,1066,1017,4709,10562,13829,11756,270,3718,3574,3650,2907,3113,3043,3185,2975,1695,1648,1603,2431,2478,2684,7842,7891,2629,7777,2525,2735,2582,7720,7048,6832,12579,12755,2193,9473,13897,3468,9521,6663,8110,6602,6340,6399,14015,7324,13588,11414,12347,2071,7600,3530,566", "endColumns": "39,53,51,67,73,57,59,75,61,47,63,61,54,55,60,59,62,64,64,55,48,68,62,47,55,45,62,59,47,55,58,62,43,74,49,53,49,37,67,84,47,140,81,41,49,65,53,61,73,71,73,59,53,65,67,55,39,59,61,61,59,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,63,57,37,68,76,61,51,67,51,69,75,55,50,65,51,71,81,58,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,69,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,63,57,55,59,67,53,88,67,68,43,67,49,53,45,69,65,71,43,39,59,49,37,61,53,37,49,53,53,61,80,48,41,67,67,61,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,49,51,73,47,49,61,39,54,59,60,58,60,62,39,47,69,55,53,63,43,71", "endOffsets": "3405,5215,12922,10729,10999,10787,11059,10925,10849,13770,13722,2324,5499,5327,5444,5161,5562,5755,5690,5383,5873,5824,5625,5101,12518,3302,3365,6993,6789,12574,12342,12283,5029,12220,9768,65,10445,4945,13376,13308,820,11954,950,4624,9606,7163,2012,12750,8532,8604,8678,8458,13824,8105,14010,4221,4985,10050,4073,12086,11751,11595,11121,2902,6335,6597,6218,6530,4666,7277,7529,4704,5271,1402,3965,9674,4833,7043,6827,221,746,10363,10222,6933,8806,8742,1598,9990,7225,8342,8284,8398,1012,2066,2188,8221,12462,7417,135,14219,14150,8889,10557,9211,8941,9067,9143,8997,9262,9834,1958,1547,1817,12145,1475,1305,7715,2854,6127,5949,6065,6002,4793,3837,11261,4285,3925,9930,561,4111,12024,11543,10124,690,8039,7983,1231,868,4907,7319,4011,9882,11409,11338,9468,9414,9346,12688,3463,13086,1906,13444,13030,13533,13223,13155,265,12870,11691,391,3883,4355,7595,2396,9718,4873,10505,7467,4582,11183,12976,173,13583,445,4165,507,1142,1061,4746,10625,13892,11813,311,3775,3645,3713,2970,3180,3108,3256,3038,1735,1690,1643,2473,2520,2730,7886,7943,2679,7837,2577,2785,2624,7772,7097,6869,12624,12802,2262,9516,13942,3525,9556,6713,8165,6658,6394,6455,14073,7359,13631,11479,12398,2120,7659,3569,633"}, "to": {"startLines": "386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,421,422,423,424,425,426,427,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,492,495,496,498,499,500,501,502,503,504,505,509,510,511,513,514,515,516,517,518,522,523,524,525,526,527,528,529,530,531,532,533,535,536,537,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,640,641,642,643,645,646,647,648,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,699,701,702,703,704,705,706,707,708,709,710,711,712,713,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,735,737,739,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24876,24916,24970,25022,25090,25164,25222,25282,25358,25420,25468,25532,25594,25649,25705,25766,25826,25889,25954,26019,26075,26124,26193,26256,26304,26360,26406,26469,26529,26577,26633,26692,26755,26799,26957,27007,27061,27111,27149,27217,27302,27861,28002,28084,28126,28176,28242,28296,28358,28432,28504,28578,28638,28692,28758,28826,28882,33525,33726,33788,33918,33978,34030,34092,34140,34257,34324,34389,34775,34817,34869,34989,35027,35083,35180,35220,35288,35543,35593,35631,35679,35735,35876,35939,36003,36067,36131,36182,36242,36386,36444,36507,37009,37071,37125,37193,37249,37313,37371,37409,37478,37555,37617,37669,37737,37789,37859,37935,37991,38154,38220,38272,38344,38426,38485,38558,38632,38688,38734,38796,38849,38912,38965,39012,39052,39130,39194,43677,43725,43779,43817,43949,44013,44087,44144,44254,44294,44383,44431,44465,44507,44553,44601,44672,44749,44803,44871,44927,44991,45049,45105,45165,45233,45287,45376,45444,45513,45743,45811,45861,45915,45961,46031,46097,46169,46213,46253,46313,46363,46401,46463,46517,46555,46605,46659,46713,46775,46856,46905,46947,47068,47182,47244,47290,47352,47428,47496,47564,47636,47706,47782,47850,47895,47942,48186,48233,48280,48331,48380,48437,48492,48557,48614,48669,48716,48773,48827,48869,48919,48971,49092,49176,49316,49489,49529,49584,49644,49705,49764,49825,49888,49928,49976,50046,50102,50156,50220,50264", "endColumns": "39,53,51,67,73,57,59,75,61,47,63,61,54,55,60,59,62,64,64,55,48,68,62,47,55,45,62,59,47,55,58,62,43,74,49,53,49,37,67,84,47,140,81,41,49,65,53,61,73,71,73,59,53,65,67,55,39,59,61,61,59,51,61,47,116,66,64,74,41,51,61,37,55,96,39,67,39,49,37,47,55,140,62,63,63,63,50,59,61,57,62,55,61,53,67,55,63,57,37,68,76,61,51,67,51,69,75,55,50,65,51,71,81,58,72,73,55,45,61,52,62,52,46,39,77,63,41,47,53,37,69,63,73,56,55,39,88,47,33,41,45,47,70,76,53,67,55,63,57,55,59,67,53,88,67,68,43,67,49,53,45,69,65,71,43,39,59,49,37,61,53,37,49,53,53,61,80,48,41,67,67,61,45,61,75,67,67,71,69,75,67,44,46,44,46,46,50,48,56,54,64,56,54,46,56,53,41,49,51,73,47,49,61,39,54,59,60,58,60,62,39,47,69,55,53,63,43,71", "endOffsets": "24911,24965,25017,25085,25159,25217,25277,25353,25415,25463,25527,25589,25644,25700,25761,25821,25884,25949,26014,26070,26119,26188,26251,26299,26355,26401,26464,26524,26572,26628,26687,26750,26794,26869,27002,27056,27106,27144,27212,27297,27345,27997,28079,28121,28171,28237,28291,28353,28427,28499,28573,28633,28687,28753,28821,28877,28917,33580,33783,33845,33973,34025,34087,34135,34252,34319,34384,34459,34812,34864,34926,35022,35078,35175,35215,35283,35323,35588,35626,35674,35730,35871,35934,35998,36062,36126,36177,36237,36299,36439,36502,36558,37066,37120,37188,37244,37308,37366,37404,37473,37550,37612,37664,37732,37784,37854,37930,37986,38037,38215,38267,38339,38421,38480,38553,38627,38683,38729,38791,38844,38907,38960,39007,39047,39125,39189,39231,43720,43774,43812,43882,44008,44082,44139,44195,44289,44378,44426,44460,44502,44548,44596,44667,44744,44798,44866,44922,44986,45044,45100,45160,45228,45282,45371,45439,45508,45552,45806,45856,45910,45956,46026,46092,46164,46208,46248,46308,46358,46396,46458,46512,46550,46600,46654,46708,46770,46851,46900,46942,47010,47131,47239,47285,47347,47423,47491,47559,47631,47701,47777,47845,47890,47937,47982,48228,48275,48326,48375,48432,48487,48552,48609,48664,48711,48768,48822,48864,48914,48966,49040,49135,49221,49373,49524,49579,49639,49700,49759,49820,49883,49923,49971,50041,50097,50151,50215,50259,50331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\981fd9293439b18a98e73358fb60a309\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2316,3116,3122", "startColumns": "4,4,4,4", "startOffsets": "202,155346,183618,183829", "endLines": "3,2318,3121,3205", "endColumns": "60,12,24,24", "endOffsets": "258,155486,183824,188340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94669d66a91321d208dab1f867a16831\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,428,429,430,431,432,433,434,717,2154,2155,2160,2163,2168,2314,2315,3016,3078,3224,3257,3287,3320", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "564,1590,1662,3001,3066,6278,6347,15105,15175,15243,15315,15385,15446,15520,16377,16438,16499,16561,16625,16687,16748,16816,16916,16976,17042,17115,17184,17241,17293,18020,18092,18168,18233,18292,18351,18411,18471,18531,18591,18651,18711,18771,18831,18891,18951,19010,19070,19130,19190,19250,19310,19370,19430,19490,19550,19610,19669,19729,19789,19848,19907,19966,20025,20084,20902,20937,21341,21396,21459,21514,21572,21630,21691,21754,21811,21862,21912,21973,22030,22096,22130,22165,22940,27350,27417,27489,27558,27627,27701,27773,48115,142680,142797,143064,143357,143624,155207,155279,179463,181960,188917,190648,191648,192330", "endLines": "11,30,31,49,50,97,98,228,229,230,231,232,233,234,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,320,321,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,358,428,429,430,431,432,433,434,717,2154,2158,2160,2166,2168,2314,2315,3021,3087,3256,3277,3319,3325", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "619,1657,1745,3061,3127,6342,6405,15170,15238,15310,15380,15441,15515,15588,16433,16494,16556,16620,16682,16743,16811,16911,16971,17037,17110,17179,17236,17288,17350,18087,18163,18228,18287,18346,18406,18466,18526,18586,18646,18706,18766,18826,18886,18946,19005,19065,19125,19185,19245,19305,19365,19425,19485,19545,19605,19664,19724,19784,19843,19902,19961,20020,20079,20138,20932,20967,21391,21454,21509,21567,21625,21686,21749,21806,21857,21907,21968,22025,22091,22125,22160,22195,23005,27412,27484,27553,27622,27696,27768,27856,48181,142792,142993,143169,143553,143748,155274,155341,179661,182256,190643,191324,192325,192492"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "36,108,109,110,121,122,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "2069,7087,7134,7181,7925,7970,8136", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2106,7129,7176,7223,7965,8010,8173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ccfdf8fe6da9c4c34a4246aecbf96939\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "322,2468,3568,3571", "startColumns": "4,4,4,4", "startOffsets": "20972,161553,200551,200666", "endLines": "322,2474,3570,3573", "endColumns": "52,24,24,24", "endOffsets": "21020,161852,200661,200776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe1178617600db5fd0c80d7e37655d6c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "310,312,313,316,318,351,451,452,506,507,512,559,560,644,649,673,674,700,714,715,716,734,736,738,1860,1876,1879", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20348,20472,20530,20708,20793,22503,28922,28987,34464,34530,34931,38042,38094,43887,44200,45639,45689,47136,47987,48033,48075,49045,49140,49226,120508,121488,121599", "endLines": "310,312,313,316,318,351,451,452,506,507,512,559,560,644,649,673,674,700,714,715,716,734,736,738,1862,1878,1882", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "20417,20525,20580,20754,20843,22551,28982,29036,34525,34626,34984,38089,38149,43944,44249,45684,45738,47177,48028,48070,48110,49087,49171,49311,120615,121594,121789"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "92", "endOffsets": "144"}, "to": {"startLines": "2206", "startColumns": "4", "startOffsets": "146456", "endColumns": "91", "endOffsets": "146543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c36e1f051d57c59ccd6930c68000c21a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "346", "startColumns": "4", "startOffsets": "22232", "endColumns": "42", "endOffsets": "22270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25ad31b9d5ee235be384e44c485c1169\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2485,2501,2507,3665,3681", "startColumns": "4,4,4,4,4", "startOffsets": "162374,162799,162977,203513,203924", "endLines": "2500,2506,2516,3680,3684", "endColumns": "24,24,24,24,24", "endOffsets": "162794,162972,163256,203919,204046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\167b5a10bb8d261a1d1c21747e97c5f2\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "99,259,260,261,262,2159,2161,2162,2167,2169", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6410,17355,17408,17461,17514,142998,143174,143296,143558,143753", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "6494,17403,17456,17509,17562,143059,143291,143352,143619,143815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\14d03a95cc07898beae69a352dd60254\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "349", "startColumns": "4", "startOffsets": "22389", "endColumns": "49", "endOffsets": "22434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeba2917d45d1138de91ba9d1c8482e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,698,757,758,759,760,761,762,770,771,775,779,790,795,801,808,812,816,821,825,829,833,837,841,845,851,855,861,865,871,875,880,884,887,891,897,901,907,911,917,920,924,928,932,936,940,941,942,943,946,949,952,955,959,960,961,962,963,966,968,970,972,977,978,982,988,992,993,995,1007,1008,1012,1018,1022,1023,1024,1028,1055,1059,1060,1064,1092,1264,1290,1461,1487,1518,1526,1532,1548,1570,1575,1580,1590,1599,1608,1612,1619,1638,1645,1646,1655,1658,1661,1665,1669,1673,1676,1677,1682,1687,1697,1702,1709,1715,1716,1719,1723,1728,1730,1732,1735,1738,1740,1744,1747,1754,1757,1760,1764,1766,1770,1772,1774,1776,1780,1788,1796,1808,1814,1823,1826,1837,1840,1841,1846,1847,1883,1952,2022,2023,2033,2042,2043,2045,2049,2052,2055,2058,2061,2064,2067,2070,2074,2077,2080,2083,2087,2090,2094,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2120,2122,2123,2124,2125,2126,2127,2128,2129,2131,2132,2134,2135,2137,2139,2140,2142,2143,2144,2145,2146,2147,2149,2150,2151,2152,2153,2170,2172,2174,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2190,2191,2192,2193,2194,2195,2196,2198,2202,2207,2208,2209,2210,2211,2212,2216,2217,2218,2219,2221,2223,2225,2227,2229,2230,2231,2232,2234,2236,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2252,2253,2254,2255,2257,2259,2260,2262,2263,2265,2267,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2282,2283,2284,2285,2287,2288,2289,2290,2291,2293,2295,2297,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2364,2439,2442,2445,2448,2462,2475,2517,2520,2549,2576,2585,2649,3012,3050,3088,3206,3326,3350,3356,3375,3396,3520,3579,3585,3593,3599,3653,3685,3751,3771,3826,3838,3864", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "263,318,515,931,972,1027,1089,1153,1223,1284,1359,1435,1512,1750,1835,1917,1993,2111,2188,2266,2372,2478,2557,2886,2943,4925,4999,5074,5139,5205,5265,5326,5398,5471,5538,5606,5665,5724,5783,5842,5901,5955,6009,6062,6116,6170,6224,6499,6573,6652,6725,6799,6870,6942,7014,7228,7285,7343,7416,7490,7564,7639,7711,7784,7854,8015,8075,8178,8247,8316,8386,8460,8536,8600,8677,8753,8830,8895,8964,9041,9116,9185,9253,9330,9396,9457,9554,9619,9688,9787,9858,9917,9975,10032,10091,10155,10226,10298,10370,10442,10514,10581,10649,10717,10776,10839,10903,10993,11084,11144,11210,11277,11343,11413,11477,11530,11597,11658,11725,11838,11896,11959,12024,12089,12164,12237,12309,12353,12400,12446,12495,12556,12617,12678,12740,12804,12868,12932,12997,13060,13120,13181,13247,13306,13366,13428,13499,13559,15593,15679,15766,15856,15943,16031,16113,16196,16286,17567,17619,17677,17722,17788,17852,17909,17966,20143,20200,20248,20297,20759,21092,21139,21295,22200,22556,22620,22682,22742,23010,23084,23154,23232,23286,23356,23441,23489,23535,23596,23659,23725,23789,23860,23923,23988,24052,24113,24174,24226,24299,24373,24442,24517,24591,24665,24806,47015,50336,50414,50504,50592,50688,50778,51360,51449,51696,51977,52643,52928,53321,53798,54020,54242,54518,54745,54975,55205,55435,55665,55892,56311,56537,56962,57192,57620,57839,58122,58330,58461,58688,59114,59339,59766,59987,60412,60532,60808,61109,61433,61724,62038,62175,62306,62411,62653,62820,63024,63232,63503,63615,63727,63832,63949,64163,64309,64449,64535,64883,64971,65217,65635,65884,65966,66064,66721,66821,67073,67497,67752,67846,67935,68172,70196,70438,70540,70793,72949,83630,85146,95841,97369,99126,99752,100172,101433,102698,102954,103190,103737,104231,104836,105034,105614,106982,107357,107475,108013,108170,108366,108639,108895,109065,109206,109270,109635,110002,110678,110942,111280,111633,111727,111913,112219,112481,112606,112733,112972,113183,113302,113495,113672,114127,114308,114430,114689,114802,114989,115091,115198,115327,115602,116110,116606,117483,117777,118347,118496,119228,119400,119484,119820,119912,121794,127025,132396,132458,133036,133620,133711,133824,134053,134213,134365,134536,134702,134871,135038,135201,135444,135614,135787,135958,136232,136431,136636,136966,137050,137146,137242,137340,137440,137542,137644,137746,137848,137950,138050,138146,138258,138387,138510,138641,138772,138870,138984,139078,139218,139352,139448,139560,139660,139776,139872,139984,140084,140224,140360,140524,140654,140812,140962,141103,141247,141382,141494,141644,141772,141900,142036,142168,142298,142428,142540,143820,143966,144110,144248,144314,144404,144480,144584,144674,144776,144884,144992,145092,145172,145264,145362,145472,145524,145602,145708,145800,145904,146014,146136,146299,146548,146628,146728,146818,146928,147018,147259,147353,147459,147551,147651,147763,147877,147993,148109,148203,148317,148429,148531,148651,148773,148855,148959,149079,149205,149303,149397,149485,149597,149713,149835,149947,150122,150238,150324,150416,150528,150652,150719,150845,150913,151041,151185,151313,151382,151477,151592,151705,151804,151913,152024,152135,152236,152341,152441,152571,152662,152785,152879,152991,153077,153181,153277,153365,153483,153587,153691,153817,153905,154013,154113,154203,154313,154397,154499,154583,154637,154701,154807,154893,155003,155087,158030,160646,160764,160879,160959,161320,161857,163261,163339,164683,166044,166432,169275,179328,180590,182261,188345,192497,193248,193510,194025,194404,198682,200963,201192,201486,201701,203201,204051,207077,207821,209952,210292,211603", "endLines": "4,5,10,20,21,22,23,24,25,26,27,28,29,32,33,34,35,37,38,39,40,41,42,47,48,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,123,124,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,235,236,237,238,239,240,241,242,243,263,264,265,266,267,268,269,270,306,307,308,309,317,324,325,328,345,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,698,757,758,759,760,761,769,770,774,778,782,794,800,807,811,815,820,824,828,832,836,840,844,850,854,860,864,870,874,879,883,886,890,896,900,906,910,916,919,923,927,931,935,939,940,941,942,945,948,951,954,958,959,960,961,962,965,967,969,971,976,977,981,987,991,992,994,1006,1007,1011,1017,1021,1022,1023,1027,1054,1058,1059,1063,1091,1263,1289,1460,1486,1517,1525,1531,1547,1569,1574,1579,1589,1598,1607,1611,1618,1637,1644,1645,1654,1657,1660,1664,1668,1672,1675,1676,1681,1686,1696,1701,1708,1714,1715,1718,1722,1727,1729,1731,1734,1737,1739,1743,1746,1753,1756,1759,1763,1765,1769,1771,1773,1775,1779,1787,1795,1807,1813,1822,1825,1836,1839,1840,1845,1846,1851,1951,2021,2022,2032,2041,2042,2044,2048,2051,2054,2057,2060,2063,2066,2069,2073,2076,2079,2082,2086,2089,2093,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2119,2121,2122,2123,2124,2125,2126,2127,2128,2130,2131,2133,2134,2136,2138,2139,2141,2142,2143,2144,2145,2146,2148,2149,2150,2151,2152,2153,2171,2173,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2189,2190,2191,2192,2193,2194,2195,2197,2201,2205,2207,2208,2209,2210,2211,2215,2216,2217,2218,2220,2222,2224,2226,2228,2229,2230,2231,2233,2235,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2251,2252,2253,2254,2256,2258,2259,2261,2262,2264,2266,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2281,2282,2283,2284,2286,2287,2288,2289,2290,2292,2294,2296,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2438,2441,2444,2447,2461,2467,2484,2519,2548,2575,2584,2648,3011,3015,3077,3115,3223,3349,3355,3361,3395,3519,3539,3584,3588,3598,3633,3664,3750,3770,3825,3837,3863,3870", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "313,358,559,967,1022,1084,1148,1218,1279,1354,1430,1507,1585,1830,1912,1988,2064,2183,2261,2367,2473,2552,2632,2938,2996,4994,5069,5134,5200,5260,5321,5393,5466,5533,5601,5660,5719,5778,5837,5896,5950,6004,6057,6111,6165,6219,6273,6568,6647,6720,6794,6865,6937,7009,7082,7280,7338,7411,7485,7559,7634,7706,7779,7849,7920,8070,8131,8242,8311,8381,8455,8531,8595,8672,8748,8825,8890,8959,9036,9111,9180,9248,9325,9391,9452,9549,9614,9683,9782,9853,9912,9970,10027,10086,10150,10221,10293,10365,10437,10509,10576,10644,10712,10771,10834,10898,10988,11079,11139,11205,11272,11338,11408,11472,11525,11592,11653,11720,11833,11891,11954,12019,12084,12159,12232,12304,12348,12395,12441,12490,12551,12612,12673,12735,12799,12863,12927,12992,13055,13115,13176,13242,13301,13361,13423,13494,13554,13622,15674,15761,15851,15938,16026,16108,16191,16281,16372,17614,17672,17717,17783,17847,17904,17961,18015,20195,20243,20292,20343,20788,21134,21183,21336,22227,22615,22677,22737,22794,23079,23149,23227,23281,23351,23436,23484,23530,23591,23654,23720,23784,23855,23918,23983,24047,24108,24169,24221,24294,24368,24437,24512,24586,24660,24801,24871,47063,50409,50499,50587,50683,50773,51355,51444,51691,51972,52224,52923,53316,53793,54015,54237,54513,54740,54970,55200,55430,55660,55887,56306,56532,56957,57187,57615,57834,58117,58325,58456,58683,59109,59334,59761,59982,60407,60527,60803,61104,61428,61719,62033,62170,62301,62406,62648,62815,63019,63227,63498,63610,63722,63827,63944,64158,64304,64444,64530,64878,64966,65212,65630,65879,65961,66059,66716,66816,67068,67492,67747,67841,67930,68167,70191,70433,70535,70788,72944,83625,85141,95836,97364,99121,99747,100167,101428,102693,102949,103185,103732,104226,104831,105029,105609,106977,107352,107470,108008,108165,108361,108634,108890,109060,109201,109265,109630,109997,110673,110937,111275,111628,111722,111908,112214,112476,112601,112728,112967,113178,113297,113490,113667,114122,114303,114425,114684,114797,114984,115086,115193,115322,115597,116105,116601,117478,117772,118342,118491,119223,119395,119479,119815,119907,120185,127020,132391,132453,133031,133615,133706,133819,134048,134208,134360,134531,134697,134866,135033,135196,135439,135609,135782,135953,136227,136426,136631,136961,137045,137141,137237,137335,137435,137537,137639,137741,137843,137945,138045,138141,138253,138382,138505,138636,138767,138865,138979,139073,139213,139347,139443,139555,139655,139771,139867,139979,140079,140219,140355,140519,140649,140807,140957,141098,141242,141377,141489,141639,141767,141895,142031,142163,142293,142423,142535,142675,143961,144105,144243,144309,144399,144475,144579,144669,144771,144879,144987,145087,145167,145259,145357,145467,145519,145597,145703,145795,145899,146009,146131,146294,146451,146623,146723,146813,146923,147013,147254,147348,147454,147546,147646,147758,147872,147988,148104,148198,148312,148424,148526,148646,148768,148850,148954,149074,149200,149298,149392,149480,149592,149708,149830,149942,150117,150233,150319,150411,150523,150647,150714,150840,150908,151036,151180,151308,151377,151472,151587,151700,151799,151908,152019,152130,152231,152336,152436,152566,152657,152780,152874,152986,153072,153176,153272,153360,153478,153582,153686,153812,153900,154008,154108,154198,154308,154392,154494,154578,154632,154696,154802,154888,154998,155082,155202,160641,160759,160874,160954,161315,161548,162369,163334,164678,166039,166427,169270,179323,179458,181955,183613,188912,193243,193505,193705,194399,198677,199283,201187,201338,201696,202779,203508,207072,207816,209947,210287,211598,211801"}}, {"source": "F:\\Software Development\\augment-projects\\Healthy Product\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,727", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "194,276,380,489,609,722,804"}, "to": {"startLines": "508,534,538,539,540,541,672", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "34631,36304,36563,36667,36776,36896,45557", "endColumns": "143,81,103,108,119,112,81", "endOffsets": "34770,36381,36662,36771,36891,37004,45634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\02b89f0ee9be1e0a15cf2905800b0f25\\transformed\\facebook-common-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,49,54,60,71,82,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,202,290,370,449,513,590,665,732,814,895,968,1023,1092,1171,1235,1309,1382,1455,1527,1600,1673,1738,1807,4164,4458,4808,5429,6060,6239", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,53,59,70,81,84,112", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "197,285,365,444,508,585,660,727,809,890,963,1018,1087,1166,1230,1304,1377,1450,1522,1595,1668,1733,1802,1867,4453,4803,5424,6055,6234,7468"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2319,2324,2330,2341,2352,3871", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3381,3433,3521,3601,3680,3744,3821,3896,3963,4045,4126,4199,13940,14009,14088,14222,14296,14369,14442,14514,14587,14660,14725,14794,155491,155785,156135,156756,157387,211806", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,212,213,214,216,217,218,219,220,221,222,223,224,2323,2329,2340,2351,2354,3898", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "3428,3516,3596,3675,3739,3816,3891,3958,4040,4121,4194,4249,14004,14083,14147,14291,14364,14437,14509,14582,14655,14720,14789,14854,155780,156130,156751,157382,157561,213035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1bf99d2dddf6b3f731ce95e33fee49d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "319,323", "startColumns": "4,4", "startOffsets": "20848,21025", "endColumns": "53,66", "endOffsets": "20897,21087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\856e9279244c37324ec12dc8b216b897\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "326,347", "startColumns": "4,4", "startOffsets": "21188,22275", "endColumns": "41,59", "endOffsets": "21225,22330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46b0b167eaae9071f6eb78a60488f486\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "43,44,45,46,207,208,497,519,520,521", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2637,2695,2761,2824,13627,13698,33850,35328,35395,35474", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2690,2756,2819,2881,13693,13765,33913,35390,35469,35538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b616a44a83a35cc1ff94b844e1245adb\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "20422", "endColumns": "49", "endOffsets": "20467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\82a7a8f16b09c6b5659ed497e86f10d2\\transformed\\facebook-login-16.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,38,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,175,257,340,421,558,673,791,886,951,1019,1077,1149,1221,1318,1409,1483,1557,1670,1771,1834,2054,2219,2296,2378,2503,2589,2713,2799,3177,3880", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,37,50,59", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "170,252,335,416,553,668,786,881,946,1014,1072,1144,1216,1313,1404,1478,1552,1665,1766,1829,2049,2214,2291,2373,2498,2584,2708,2794,3172,3875,4296"}, "to": {"startLines": "215,225,226,227,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,2355,2356,3899,3912", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14152,14859,14941,15024,29041,29178,29293,29411,29506,29571,29639,29697,29769,29841,29938,30029,30103,30177,30290,30391,30454,30674,30839,30916,30998,31123,31209,157566,157652,213040,213743", "endLines": "215,225,226,227,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,2355,2363,3911,3920", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "14217,14936,15019,15100,29173,29288,29406,29501,29566,29634,29692,29764,29836,29933,30024,30098,30172,30285,30386,30449,30669,30834,30911,30993,31118,31204,31328,157647,158025,213738,214159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e520bbb7f153fcbb4e273d864eff355d\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "22335", "endColumns": "53", "endOffsets": "22384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21c308ad6a5cb3ffbae3557cea5538ff\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "6,12,3589", "startColumns": "4,4,4", "startOffsets": "363,624,201343", "endLines": "9,19,3592", "endColumns": "11,11,24", "endOffsets": "510,926,201481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd362f2df44c951726e528b041211eb2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "740,741", "startColumns": "4,4", "startOffsets": "49378,49434", "endColumns": "55,54", "endOffsets": "49429,49484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b341f2bf9e26a9b9b6d9ac1cb5423684\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3540,3553,3559,3565,3574", "startColumns": "4,4,4,4,4", "startOffsets": "199288,199927,200171,200418,200781", "endLines": "3552,3558,3564,3567,3578", "endColumns": "24,24,24,24,24", "endOffsets": "199922,200166,200413,200546,200958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5ccc5722ca8455978d22b35e0684d747\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "314,327,350,3278,3283", "startColumns": "4,4,4,4,4", "startOffsets": "20585,21230,22439,191329,191499", "endLines": "314,327,350,3282,3286", "endColumns": "56,64,63,24,24", "endOffsets": "20637,21290,22498,191494,191643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a42896bc1e72ea84c886f88868497142\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "357,579,580,581,582,583,584,585,586,587,588,591,592,593,594,595,596,597,598,599,600,601,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,1863,1873", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22867,39236,39324,39410,39491,39575,39644,39709,39792,39898,39984,40104,40158,40227,40288,40357,40446,40541,40615,40712,40805,40903,41052,41143,41231,41327,41425,41489,41557,41644,41738,41805,41877,41949,42050,42159,42235,42304,42352,42418,42482,42556,42613,42670,42742,42792,42846,42917,42988,43058,43127,43185,43261,43332,43406,43492,43542,43612,120620,121335", "endLines": "357,579,580,581,582,583,584,585,586,587,590,591,592,593,594,595,596,597,598,599,600,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,1872,1875", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "22935,39319,39405,39486,39570,39639,39704,39787,39893,39979,40099,40153,40222,40283,40352,40441,40536,40610,40707,40800,40898,41047,41138,41226,41322,41420,41484,41552,41639,41733,41800,41872,41944,42045,42154,42230,42299,42347,42413,42477,42551,42608,42665,42737,42787,42841,42912,42983,43053,43122,43180,43256,43327,43401,43487,43537,43607,43672,121330,121483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea199b5bc61986cf0429ca4ca01f9df\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "67,68,69,70,71,72,73,74,476,477,478,479,480,481,482,483,485,486,487,488,489,490,491,493,494,3362,3634", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4254,4344,4424,4514,4604,4684,4765,4845,31333,31438,31619,31744,31851,32031,32154,32270,32540,32728,32833,33014,33139,33314,33462,33585,33647,193710,202784", "endLines": "67,68,69,70,71,72,73,74,476,477,478,479,480,481,482,483,485,486,487,488,489,490,491,493,494,3374,3652", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4339,4419,4509,4599,4679,4760,4840,4920,31433,31614,31739,31846,32026,32149,32265,32368,32723,32828,33009,33134,33309,33457,33520,33642,33721,194020,203196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5092f4b0fc3229c87d6dc9ffd8fb80cd\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "20642", "endColumns": "65", "endOffsets": "20703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2cda5a1f9d8c110805098b8b6936f0e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "420", "startColumns": "4", "startOffsets": "26874", "endColumns": "82", "endOffsets": "26952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0632335579b593b6b56131d5dd003758\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "2,51,52,53,54,209,210,211,783,1852,1854,1857,3022", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3132,3193,3255,3317,13770,13829,13886,52229,120190,120254,120380,179666", "endLines": "2,51,52,53,54,209,210,211,789,1853,1856,1859,3049", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "197,3188,3250,3312,3376,13824,13881,13935,52638,120249,120375,120503,180585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\21ddb6e5847bd1a05683bb0c448269e9\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "356,484", "startColumns": "4,4", "startOffsets": "22799,32373", "endColumns": "67,166", "endOffsets": "22862,32535"}}]}]}