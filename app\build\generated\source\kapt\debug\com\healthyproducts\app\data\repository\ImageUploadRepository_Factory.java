package com.healthyproducts.app.data.repository;

import android.content.Context;
import com.google.firebase.storage.FirebaseStorage;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ImageUploadRepository_Factory implements Factory<ImageUploadRepository> {
  private final Provider<FirebaseStorage> storageProvider;

  private final Provider<Context> contextProvider;

  public ImageUploadRepository_Factory(Provider<FirebaseStorage> storageProvider,
      Provider<Context> contextProvider) {
    this.storageProvider = storageProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public ImageUploadRepository get() {
    return newInstance(storageProvider.get(), contextProvider.get());
  }

  public static ImageUploadRepository_Factory create(Provider<FirebaseStorage> storageProvider,
      Provider<Context> contextProvider) {
    return new ImageUploadRepository_Factory(storageProvider, contextProvider);
  }

  public static ImageUploadRepository newInstance(FirebaseStorage storage, Context context) {
    return new ImageUploadRepository(storage, context);
  }
}
