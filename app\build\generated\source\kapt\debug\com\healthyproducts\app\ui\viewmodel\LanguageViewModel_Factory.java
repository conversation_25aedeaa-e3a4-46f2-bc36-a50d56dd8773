package com.healthyproducts.app.ui.viewmodel;

import android.app.Application;
import com.healthyproducts.app.data.repository.FirestoreRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LanguageViewModel_Factory implements Factory<LanguageViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<FirestoreRepository> firestoreRepositoryProvider;

  public LanguageViewModel_Factory(Provider<Application> applicationProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    this.applicationProvider = applicationProvider;
    this.firestoreRepositoryProvider = firestoreRepositoryProvider;
  }

  @Override
  public LanguageViewModel get() {
    return newInstance(applicationProvider.get(), firestoreRepositoryProvider.get());
  }

  public static LanguageViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<FirestoreRepository> firestoreRepositoryProvider) {
    return new LanguageViewModel_Factory(applicationProvider, firestoreRepositoryProvider);
  }

  public static LanguageViewModel newInstance(Application application,
      FirestoreRepository firestoreRepository) {
    return new LanguageViewModel(application, firestoreRepository);
  }
}
