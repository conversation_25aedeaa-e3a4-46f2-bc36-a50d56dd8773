package com.healthyproducts.app.ui.screens.settings;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\n\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a4\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\fH\u0007\u001a.\u0010\r\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0010H\u0007\u001a\u001a\u0010\u0011\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0007\u001a,\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\fH\u0007\u001aV\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u00162\u0006\u0010\u001e\u001a\u00020\u00162\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\f2\b\b\u0002\u0010\u001f\u001a\u00020\u0003H\u0002\u00a8\u0006 "}, d2 = {"SettingsCategory", "", "title", "", "SettingsContent", "paddingValues", "Landroidx/compose/foundation/layout/PaddingValues;", "userPreferences", "Lcom/healthyproducts/app/model/UserPreferences;", "navController", "Landroidx/navigation/NavController;", "onPreferencesChanged", "Lkotlin/Function1;", "SettingsItem", "value", "onClick", "Lkotlin/Function0;", "SettingsScreen", "userViewModel", "Lcom/healthyproducts/app/ui/viewmodel/UserViewModel;", "SettingsSwitch", "checked", "", "onCheckedChange", "updatePreferences", "showHalalAnalysis", "showVeganAnalysis", "showKosherAnalysis", "showHarmfulAnalysis", "showUnhealthyAnalysis", "darkMode", "aiModel", "app_debug"})
public final class SettingsScreenKt {
    
    /**
     * Ayarlar ekranı
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SettingsScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.ui.viewmodel.UserViewModel userViewModel) {
    }
    
    /**
     * Ayarlar ekranı içeriği
     */
    @androidx.compose.runtime.Composable()
    public static final void SettingsContent(@org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.layout.PaddingValues paddingValues, @org.jetbrains.annotations.NotNull()
    com.healthyproducts.app.model.UserPreferences userPreferences, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.healthyproducts.app.model.UserPreferences, kotlin.Unit> onPreferencesChanged) {
    }
    
    /**
     * Ayarlar kategorisi başlığı
     */
    @androidx.compose.runtime.Composable()
    public static final void SettingsCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String title) {
    }
    
    /**
     * Ayarlar öğesi (başlık ve değer)
     */
    @androidx.compose.runtime.Composable()
    public static final void SettingsItem(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.String value, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * Ayarlar switch öğesi
     */
    @androidx.compose.runtime.Composable()
    public static final void SettingsSwitch(@org.jetbrains.annotations.NotNull()
    java.lang.String title, boolean checked, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onCheckedChange) {
    }
    
    /**
     * Kullanıcı tercihlerini günceller
     */
    private static final void updatePreferences(boolean showHalalAnalysis, boolean showVeganAnalysis, boolean showKosherAnalysis, boolean showHarmfulAnalysis, boolean showUnhealthyAnalysis, boolean darkMode, kotlin.jvm.functions.Function1<? super com.healthyproducts.app.model.UserPreferences, kotlin.Unit> onPreferencesChanged, java.lang.String aiModel) {
    }
}