package com.healthyproducts.app.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.healthyproducts.app.R
import com.healthyproducts.app.navigation.Screen

/**
 * Alt navigasyon çubuğu öğelerini tanımlayan veri sınıfı
 */
data class BottomNavItem(
    val screen: Screen,
    val iconVector: androidx.compose.ui.graphics.vector.ImageVector,
    val labelResId: Int
)

/**
 * Alt navigasyon çubuğu öğeleri listesi
 */
val bottomNavItems = listOf(
    BottomNavItem(
        screen = Screen.Home,
        iconVector = Icons.Default.Home,
        labelResId = R.string.home
    ),
    BottomNavItem(
        screen = Screen.Scan,
        iconVector = Icons.Default.QrCodeScanner,
        labelResId = R.string.scan
    ),
    BottomNavItem(
        screen = Screen.Favorites,
        iconVector = Icons.Default.Favorite,
        labelResId = R.string.favorites
    ),
    BottomNavItem(
        screen = Screen.Profile,
        iconVector = Icons.Default.Person,
        labelResId = R.string.profile
    )
)

/**
 * Alt navigasyon çubuğu bileşeni
 */
@Composable
fun BottomNavBar(navController: NavController) {
    NavigationBar {
        val navBackStackEntry by navController.currentBackStackEntryAsState()
        val currentDestination = navBackStackEntry?.destination
        
        bottomNavItems.forEach { item ->
            NavigationBarItem(
                icon = { Icon(item.iconVector, contentDescription = null) },
                label = { Text(stringResource(item.labelResId)) },
                selected = currentDestination?.hierarchy?.any { it.route == item.screen.route } == true,
                onClick = {
                    navController.navigate(item.screen.route) {
                        // Pop up to the start destination of the graph to
                        // avoid building up a large stack of destinations
                        popUpTo(navController.graph.findStartDestination().id) {
                            saveState = true
                        }
                        // Avoid multiple copies of the same destination when
                        // reselecting the same item
                        launchSingleTop = true
                        // Restore state when reselecting a previously selected item
                        restoreState = true
                    }
                }
            )
        }
    }
}
