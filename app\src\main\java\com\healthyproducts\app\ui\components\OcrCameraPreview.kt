package com.healthyproducts.app.ui.components

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.graphics.Matrix
import android.util.Size
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import java.util.concurrent.Executor
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * OCR kamera önizleme bileşeni
 */
@Composable
fun OcrCameraPreview(
    onTextDetected: (text: com.google.mlkit.vision.text.Text) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val configuration = LocalConfiguration.current

    // Kamera sağlayıcısı
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val previewView = remember { PreviewView(context) }

    // Görüntü yakalama
    val imageCapture = remember {
        ImageCapture.Builder()
            .setTargetResolution(Size(1280, 720))
            .build()
    }

    // Metin tanıyıcı
    val textRecognizer = remember { TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS) }

    // Dikdörtgen boyutları
    val screenWidth = configuration.screenWidthDp.dp
    val frameWidth = screenWidth * 0.8f
    val frameHeight = frameWidth * 0.5f

    val frameWidthPx = with(LocalDensity.current) { frameWidth.toPx() }
    val frameHeightPx = with(LocalDensity.current) { frameHeight.toPx() }
    val screenWidthPx = with(LocalDensity.current) { screenWidth.toPx() }
    val screenHeightPx = with(LocalDensity.current) { configuration.screenHeightDp.dp.toPx() }

    // Dikdörtgenin konumu
    val frameX = (screenWidthPx - frameWidthPx) / 2
    val frameY = (screenHeightPx - frameHeightPx) / 2

    // Kamera sağlayıcısını başlat
    LaunchedEffect(previewView) {
        val cameraProvider = suspendCoroutine<ProcessCameraProvider> { continuation ->
            cameraProviderFuture.addListener(
                {
                    continuation.resume(cameraProviderFuture.get())
                },
                ContextCompat.getMainExecutor(context)
            )
        }

        // Kamera seçici (arka kamera)
        val cameraSelector = CameraSelector.Builder()
            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
            .build()

        // Önizleme
        val preview = Preview.Builder().build().also {
            it.setSurfaceProvider(previewView.surfaceProvider)
        }

        try {
            // Mevcut kamera kullanımını temizle
            cameraProvider.unbindAll()

            // Kamera kullanımını bağla
            cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageCapture
            )
        } catch (e: Exception) {
            // Kamera başlatma hatası
        }
    }

    // Görüntü yakalama ve analiz fonksiyonu
    val captureListener = remember {
        object : ImageCapture.OnImageCapturedCallback() {
            override fun onCaptureSuccess(image: ImageProxy) {
                // Görüntüyü bitmap'e dönüştür
                val bitmap = image.toBitmap()
                val rotatedBitmap = rotateBitmap(bitmap, image.imageInfo.rotationDegrees.toFloat())

                // Dikdörtgen içindeki alanı kes
                val croppedBitmap = cropBitmap(
                    rotatedBitmap,
                    frameX.toInt(),
                    frameY.toInt(),
                    frameWidthPx.toInt(),
                    frameHeightPx.toInt()
                )

                // OCR işlemi
                val inputImage = InputImage.fromBitmap(croppedBitmap, 0)
                textRecognizer.process(inputImage)
                    .addOnSuccessListener { text ->
                        if (text.text.isNotEmpty()) {
                            // İçerik/bileşenler kısmını bulmaya çalış
                            val processedText = extractIngredientsSection(text.text)
                            // Eğer içerik bölümü bulunamazsa, tüm metni kullan
                            val finalText = if (processedText.isNotEmpty()) processedText else text.text

                            // Yeni bir Text nesnesi oluştur (sadece içerik kısmıyla)
                            val newText = com.google.mlkit.vision.text.Text(finalText, text.textBlocks)
                            onTextDetected(newText)
                        }
                    }
                    .addOnFailureListener {
                        // OCR hatası
                    }

                // Görüntüyü kapat
                image.close()
            }

            override fun onError(exception: ImageCaptureException) {
                // Görüntü yakalama hatası
            }
        }
    }

    // Görüntü yakalama işlemi için SharedPreferences'ı dinle
    DisposableEffect(Unit) {
        val mainExecutor = ContextCompat.getMainExecutor(context)

        // Tara düğmesine basıldığında görüntü yakalama
        val sharedPrefs = context.getSharedPreferences("OCR_PREFS", Context.MODE_PRIVATE)
        val listener = SharedPreferences.OnSharedPreferenceChangeListener { prefs, key ->
            if (key == "CAPTURE_IMAGE" && prefs.getBoolean(key, false)) {
                // Görüntü yakala
                captureImage(imageCapture, mainExecutor, captureListener)
                // Bayrağı sıfırla
                prefs.edit().putBoolean(key, false).apply()
            }
        }

        // Listener'ı kaydet
        sharedPrefs.registerOnSharedPreferenceChangeListener(listener)

        // ViewModel'den captureAndAnalyzeImage çağrıldığında
        context.getSharedPreferences("OCR_PREFS", Context.MODE_PRIVATE)
            .edit()
            .putBoolean("CAMERA_READY", true)
            .apply()

        onDispose {
            sharedPrefs.unregisterOnSharedPreferenceChangeListener(listener)
            context.getSharedPreferences("OCR_PREFS", Context.MODE_PRIVATE)
                .edit()
                .putBoolean("CAMERA_READY", false)
                .apply()
        }
    }

    // Kamera önizleme görünümü
    AndroidView(
        factory = { previewView },
        modifier = Modifier.fillMaxSize()
    )
}

/**
 * Görüntü yakalama ve analiz fonksiyonu
 */
fun captureImage(
    imageCapture: ImageCapture,
    executor: Executor,
    callback: ImageCapture.OnImageCapturedCallback
) {
    imageCapture.takePicture(executor, callback)
}

/**
 * ImageProxy'yi Bitmap'e dönüştürme
 */
fun ImageProxy.toBitmap(): Bitmap {
    val buffer = planes[0].buffer
    val bytes = ByteArray(buffer.remaining())
    buffer.get(bytes)
    return android.graphics.BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
}

/**
 * Bitmap'i döndürme
 */
fun rotateBitmap(bitmap: Bitmap, degrees: Float): Bitmap {
    val matrix = Matrix()
    matrix.postRotate(degrees)
    return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
}

/**
 * Bitmap'i kesme
 */
fun cropBitmap(bitmap: Bitmap, x: Int, y: Int, width: Int, height: Int): Bitmap {
    // Sınırları kontrol et
    val safeX = x.coerceIn(0, bitmap.width - 1)
    val safeY = y.coerceIn(0, bitmap.height - 1)
    val safeWidth = width.coerceAtMost(bitmap.width - safeX)
    val safeHeight = height.coerceAtMost(bitmap.height - safeY)

    return Bitmap.createBitmap(bitmap, safeX, safeY, safeWidth, safeHeight)
}

/**
 * OCR sonucundan içerik/bileşenler kısmını çıkarma
 * Farklı dillerde "içerikler", "içindekiler", "ingredients" gibi anahtar kelimeleri arar
 */
fun extractIngredientsSection(text: String): String {
    // Farklı dillerde içerik/bileşenler için anahtar kelimeler
    val ingredientKeywords = listOf(
        "içindekiler", "içerikler", "bileşenler", "ingredients", "ingrediente", "ingredienti",
        "ingrédients", "zutaten", "składniki", "ingredienser", "ingrediënten", "összetevők"
    )

    // Satırlara böl
    val lines = text.split("\n")

    // İçerik bölümünün başlangıç ve bitiş indekslerini bul
    var startIndex = -1
    var endIndex = -1

    // İçerik bölümünün başlangıcını bul
    for (i in lines.indices) {
        val line = lines[i].trim().lowercase()

        // Anahtar kelimelerden birini içeriyor mu kontrol et
        if (ingredientKeywords.any { keyword -> line.contains(keyword) }) {
            startIndex = i
            break
        }
    }

    // İçerik bölümü bulunamadıysa boş string döndür
    if (startIndex == -1) {
        return ""
    }

    // İçerik bölümünün sonunu bul (bir sonraki başlık veya metin bitimi)
    endIndex = lines.size - 1
    for (i in startIndex + 1 until lines.size) {
        val line = lines[i].trim()

        // Boş satır veya yeni bir başlık (büyük harflerle yazılmış kısa metin) bulundu mu?
        if (line.isEmpty() || (line.uppercase() == line && line.length < 30 && !line.contains(":"))) {
            endIndex = i - 1
            break
        }
    }

    // İçerik bölümünü birleştir ve döndür
    return lines.subList(startIndex, endIndex + 1).joinToString("\n")
}
