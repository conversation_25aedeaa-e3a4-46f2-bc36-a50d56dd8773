package com.healthyproducts.app.util

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import com.healthyproducts.app.data.model.SupportedLanguage
import java.util.Locale

/**
 * Dil ayarlarını yönetmek için yardımcı sınıf
 */
object LocaleHelper {

    /**
     * Desteklenen diller
     */
    enum class Language(val code: String, val displayName: String) {
        ENGLISH("en", "English"),
        TURKISH("tr", "Türkçe");

        companion object {
            fun fromCode(code: String): Language {
                return values().find { it.code == code } ?: ENGLISH
            }
        }
    }

    /**
     * Uygulama dilini değiştirme
     */
    fun setLocale(languageCode: String) {
        try {
            // Locale'i ayarla
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            // AppCompatDelegate ile uygulama dilini ayarla
            val localeList = LocaleListCompat.forLanguageTags(languageCode)
            AppCompatDelegate.setApplicationLocales(localeList)

            Log.d("LocaleHelper", "Locale set to: $languageCode")
        } catch (e: Exception) {
            Log.e("LocaleHelper", "Error setting locale", e)
        }
    }

    /**
     * Mevcut dili alma
     */
    fun getCurrentLocale(): Language {
        val locales = AppCompatDelegate.getApplicationLocales()
        if (locales.isEmpty) {
            return getSystemLocale()
        }

        val languageCode = locales.get(0)?.language ?: "en"
        return Language.fromCode(languageCode)
    }

    /**
     * Sistem dilini alma
     */
    fun getSystemLocale(): Language {
        // Sistem dilini al
        val locale = Locale.getDefault()

        return Language.fromCode(locale.language)
    }

    /**
     * Belirli bir dil için yapılandırma oluşturma
     */
    fun getLocalizedConfiguration(language: Language): Configuration {
        val locale = Locale(language.code)
        Locale.setDefault(locale)

        val config = Configuration()
        config.setLocale(locale)

        return config
    }

    /**
     * Context'i belirli bir dil için yapılandırma
     */
    fun getLocalizedContext(context: Context, language: Language): Context {
        val config = getLocalizedConfiguration(language)
        return context.createConfigurationContext(config)
    }

    /**
     * Language sınıfını SupportedLanguage sınıfına dönüştürür
     */
    fun languageToSupportedLanguage(language: Language): SupportedLanguage {
        return when (language) {
            Language.ENGLISH -> SupportedLanguage.ENGLISH
            Language.TURKISH -> SupportedLanguage.TURKISH
        }
    }

    /**
     * SupportedLanguage sınıfını Language sınıfına dönüştürür
     */
    fun supportedLanguageToLanguage(supportedLanguage: SupportedLanguage): Language {
        return when (supportedLanguage) {
            SupportedLanguage.ENGLISH -> Language.ENGLISH
            SupportedLanguage.TURKISH -> Language.TURKISH
        }
    }
}
