package com.healthyproducts.app.ui.screens.product

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material3.Card
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.healthyproducts.app.R
import com.healthyproducts.app.data.repository.FavoritesRepository
import com.healthyproducts.app.model.Ingredient
import com.healthyproducts.app.model.IngredientStatus
import com.healthyproducts.app.model.Product
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import androidx.compose.runtime.rememberCoroutineScope
import java.util.Date

/**
 * Ürün detay ekranı
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductDetailScreen(
    productId: String,
    navController: NavController
) {
    // Mock ürün verisi - gerçek uygulamada bu veri veritabanından gelecek
    val product = remember {
        // productId parametresi aslında taranan barkod değeri
        Product(
            id = productId,
            barcode = productId, // Barkod değerini doğrudan kullan
            name = "Örnek Ürün (Barkod: $productId)",
            brand = "Örnek Marka",
            ingredients = listOf(
                Ingredient(
                    id = "1",
                    name = "Su",
                    status = listOf(IngredientStatus.HALAL, IngredientStatus.VEGAN)
                ),
                Ingredient(
                    id = "2",
                    name = "Şeker",
                    status = listOf(IngredientStatus.HALAL, IngredientStatus.VEGAN)
                ),
                Ingredient(
                    id = "3",
                    name = "Aroma",
                    status = listOf(IngredientStatus.SUSPICIOUS)
                ),
                Ingredient(
                    id = "4",
                    name = "Koruyucu",
                    status = listOf(IngredientStatus.HARMFUL, IngredientStatus.UNHEALTHY)
                )
            ),
            createdAt = Date()
        )
    }

    // Favori durumu
    var isFavorite by remember { mutableStateOf(false) }
    val favoritesRepository = remember { FavoritesRepository() }
    val coroutineScope = rememberCoroutineScope()

    // Favori durumunu kontrol et
    LaunchedEffect(Unit) {
        withContext(Dispatchers.IO) {
            // Mock kullanıcı ID'si
            val userId = "mock_user_id"
            favoritesRepository.isFavorite(userId, productId)
                .onSuccess { isFavorite = it }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.product_details)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Geri"
                        )
                    }
                },
                actions = {
                    // Favori butonu
                    IconButton(
                        onClick = {
                            // Mock kullanıcı ID'si
                            val userId = "mock_user_id"

                            coroutineScope.launch {
                                if (isFavorite) {
                                    favoritesRepository.removeFromFavorites(userId, productId)
                                        .onSuccess { isFavorite = false }
                                } else {
                                    favoritesRepository.addToFavorites(userId, productId)
                                        .onSuccess { isFavorite = true }
                                }
                            }
                        }
                    ) {
                        Icon(
                            imageVector = if (isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                            contentDescription = if (isFavorite) stringResource(R.string.remove_from_favorites) else stringResource(R.string.add_to_favorites),
                            tint = if (isFavorite) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // Ürün adı ve markası
            Text(
                text = product.name,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            if (product.brand != null) {
                Text(
                    text = product.brand,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Barkod
            if (product.barcode != null) {
                Text(
                    text = "Barkod: ${product.barcode}",
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(16.dp))
            }

            // Helal durumu
            StatusCard(
                title = stringResource(R.string.halal_status),
                status = if (product.isHalal()) stringResource(R.string.status_halal) else stringResource(R.string.status_haram),
                isPositive = product.isHalal()
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Vegan durumu
            StatusCard(
                title = stringResource(R.string.vegan_status),
                status = if (product.isVegan()) stringResource(R.string.status_vegan) else stringResource(R.string.status_not_vegan),
                isPositive = product.isVegan()
            )

            Spacer(modifier = Modifier.height(16.dp))

            // İçindekiler başlığı
            Text(
                text = stringResource(R.string.ingredients),
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            // İçindekiler listesi
            product.ingredients.forEach { ingredient ->
                IngredientItem(ingredient = ingredient)
                Divider(modifier = Modifier.padding(vertical = 8.dp))
            }

            // Zararlı içerikler varsa göster
            if (product.hasHarmfulIngredients()) {
                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = stringResource(R.string.harmful_ingredients),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.error
                )

                Spacer(modifier = Modifier.height(8.dp))

                product.ingredients
                    .filter { IngredientStatus.HARMFUL in it.status }
                    .forEach { ingredient ->
                        Text(
                            text = "• ${ingredient.name}",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
            }

            // Şüpheli içerikler varsa göster
            if (product.hasSuspiciousIngredients()) {
                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = stringResource(R.string.suspicious_ingredients),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.tertiary
                )

                Spacer(modifier = Modifier.height(8.dp))

                product.ingredients
                    .filter { IngredientStatus.SUSPICIOUS in it.status }
                    .forEach { ingredient ->
                        Text(
                            text = "• ${ingredient.name}",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.tertiary
                        )
                    }
            }
        }
    }
}

/**
 * Durum kartı bileşeni
 */
@Composable
fun StatusCard(
    title: String,
    status: String,
    isPositive: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = status,
                style = MaterialTheme.typography.bodyLarge,
                color = if (isPositive) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

/**
 * İçerik öğesi bileşeni
 */
@Composable
fun IngredientItem(
    ingredient: Ingredient
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // İçerik adı
        Text(
            text = ingredient.name,
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(1f)
        )

        Spacer(modifier = Modifier.width(8.dp))

        // İçerik durumu göstergeleri
        ingredient.status.forEach { status ->
            val color = when (status) {
                IngredientStatus.HALAL -> MaterialTheme.colorScheme.primary
                IngredientStatus.HARAM -> MaterialTheme.colorScheme.error
                IngredientStatus.SUSPICIOUS -> MaterialTheme.colorScheme.tertiary
                IngredientStatus.VEGAN -> MaterialTheme.colorScheme.secondary
                IngredientStatus.HARMFUL -> MaterialTheme.colorScheme.error
                IngredientStatus.UNHEALTHY -> MaterialTheme.colorScheme.error
            }

            val text = when (status) {
                IngredientStatus.HALAL -> "H"
                IngredientStatus.HARAM -> "X"
                IngredientStatus.SUSPICIOUS -> "?"
                IngredientStatus.VEGAN -> "V"
                IngredientStatus.HARMFUL -> "!"
                IngredientStatus.UNHEALTHY -> "U"
            }

            Card(
                modifier = Modifier
                    .size(24.dp)
                    .padding(horizontal = 2.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(color),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = text,
                        color = MaterialTheme.colorScheme.onPrimary,
                        style = MaterialTheme.typography.labelSmall
                    )
                }
            }
        }
    }
}
